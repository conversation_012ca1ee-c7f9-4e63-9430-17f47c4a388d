# Guía para Ejecutar Tests en Rayuela

Esta guía explica cómo ejecutar los diferentes tipos de tests en Rayuela localmente.

## Requisitos Previos

Asegúrate de tener instaladas las siguientes dependencias:

```bash
pip install pytest pytest-asyncio pytest-cov httpx fastapi locust
```

## Estructura de Tests

Rayuela tiene varios tipos de tests:

- **Tests Unitarios**: En `tests/unit/`
- **Tests de Integración**: En `tests/integration/`
- **Tests End-to-End**: En `tests/e2e/`
- **Tests de Carga**: En `tests/load/`

## Scripts para Ejecutar Tests

Hemos creado varios scripts para facilitar la ejecución de tests:

### 1. Ejecutar Todos los Tests

Para ejecutar todos los tipos de tests en secuencia:

```bash
python -m scripts.run_all_test_types
```

Opciones disponibles:
- `--verbose` o `-v`: Mostrar salida detallada
- `--coverage`: Ejecutar con cobertura de código
- `--skip-unit`: Omitir tests unitarios
- `--skip-integration`: Omitir tests de integración
- `--skip-e2e`: Omitir tests end-to-end
- `--skip-load`: Omitir tests de carga
- `--skip-multi-tenancy`: Omitir tests de multi-tenancy
- `--load-users`: Número de usuarios para tests de carga (default: 10)
- `--load-time`: Tiempo de ejecución para tests de carga en segundos (default: 30)

### 2. Ejecutar Tests Unitarios

```bash
python -m scripts.run_all_tests unit
```

Opciones disponibles:
- `--coverage`: Ejecutar con cobertura de código
- `--verbose` o `-v`: Mostrar salida detallada
- `--pattern` o `-p`: Patrón para filtrar tests (ej: 'test_api_key')

### 3. Ejecutar Tests de Integración

```bash
python -m scripts.run_all_tests integration
```

Opciones disponibles:
- `--coverage`: Ejecutar con cobertura de código
- `--verbose` o `-v`: Mostrar salida detallada
- `--pattern` o `-p`: Patrón para filtrar tests (ej: 'test_multi_tenancy')
- `--setup-db`: Configurar base de datos de prueba antes de ejecutar tests

### 4. Ejecutar Tests End-to-End

```bash
python -m scripts.run_all_tests e2e
```

Opciones disponibles:
- `--verbose` o `-v`: Mostrar salida detallada

### 5. Ejecutar Tests de Carga

```bash
python -m scripts.run_load_tests
```

Opciones disponibles:
- `--host`: Host para el servidor (default: localhost)
- `--port`: Puerto para el servidor (default: 8001)
- `--users`: Número de usuarios concurrentes (default: 10)
- `--spawn-rate`: Tasa de creación de usuarios por segundo (default: 1)
- `--time`: Tiempo de ejecución en segundos (default: 60)
- `--tags`: Tags para filtrar tests (separados por comas)
- `--headless`: Ejecutar en modo headless (sin interfaz web)
- `--no-api`: No iniciar el servidor API (asume que ya está en ejecución)

### 6. Ejecutar Tests de Multi-Tenancy

```bash
python -m scripts.test_multi_tenancy
```

Opciones disponibles:
- `--verbose` o `-v`: Mostrar salida detallada
- `--skip-setup`: Omitir la configuración de la base de datos
- `--test-type`: Tipo de test a ejecutar (basic, security, celery, all)

### 7. Ejecutar un Test Específico

Para ejecutar un test específico sin depender del conftest.py global:

```bash
python -m scripts.run_direct_test <ruta_al_archivo_de_test>
```

Ejemplo:
```bash
python -m scripts.run_direct_test tests/unit/core/security/test_api_key_fixed.py
```

### 8. Ejecutar Tests de Integración Específicos

Para ejecutar un test de integración específico:

```bash
python -m scripts.run_integration_tests --path <ruta_al_archivo_de_test>
```

Opciones disponibles:
- `--verbose` o `-v`: Mostrar salida detallada
- `--skip-setup`: Omitir la configuración de la base de datos

Ejemplo:
```bash
python -m scripts.run_integration_tests --path tests/integration/test_multi_tenancy.py
```

### 9. Ejecutar Tests de Carga

Para ejecutar tests de carga con Locust:

```bash
python -m scripts.run_load_test
```

Opciones disponibles:
- `--host`: Host del servidor API (default: localhost)
- `--port`: Puerto del servidor API (default: 8001)
- `--users`: Número de usuarios concurrentes (default: 10)
- `--spawn-rate`: Tasa de creación de usuarios por segundo (default: 1)
- `--time`: Tiempo de ejecución en segundos (default: 60)
- `--headless`: Ejecutar en modo headless (sin interfaz web)
- `--no-api`: No iniciar el servidor API (asume que ya está en ejecución)

Ejemplo:
```bash
python -m scripts.run_load_test --users 20 --time 30 --headless
```

## Configuración de la Base de Datos de Prueba

Para configurar la base de datos de prueba manualmente:

```bash
python -m scripts.init_db_simple --db-name rayuela_test
```

## Ejecución con pytest Directamente

También puedes usar pytest directamente:

```bash
# Ejecutar todos los tests unitarios
pytest tests/unit/

# Ejecutar un test específico
pytest tests/unit/core/security/test_api_key.py

# Ejecutar con cobertura
pytest tests/unit/ --cov=src --cov-report=term --cov-report=html
```

## Solución de Problemas

Si encuentras problemas al ejecutar los tests, verifica:

1. Que todas las dependencias estén instaladas
2. Que la base de datos de prueba esté configurada correctamente
3. Que los servicios necesarios (PostgreSQL, Redis) estén en ejecución

Para problemas específicos con los tests de multi-tenancy, asegúrate de que las políticas RLS estén correctamente configuradas en la base de datos.
