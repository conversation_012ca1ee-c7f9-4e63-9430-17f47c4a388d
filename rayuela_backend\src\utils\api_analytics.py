"""
API Analytics Service for tracking detailed API usage metrics.
"""

import time
import json
import statistics
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.dialects.postgresql import insert

from src.db.models.account_usage_metrics import AccountUsageMetrics, EndpointMetrics
from src.utils.base_logger import log_info, log_error


class APIAnalyticsService:
    """
    Service for tracking and analyzing API usage metrics.
    This service provides methods to track API calls, response times, and errors,
    and to calculate various metrics based on this data.
    """

    def __init__(self, db: AsyncSession, account_id: int):
        self.db = db
        self.account_id = account_id
        self._response_times_buffer: Dict[str, List[float]] = (
            {}
        )  # Buffer for calculating percentiles
        self._buffer_size = (
            100  # Number of response times to keep in buffer per endpoint
        )

    async def track_api_call(
        self,
        endpoint: str,
        method: str,
        response_time: float,
        status_code: int,
        is_error: bool = False,
    ) -> bool:
        """
        Track an API call with its response time and status.

        Args:
            endpoint: The API endpoint path
            method: The HTTP method (GET, POST, etc.)
            response_time: The response time in seconds
            status_code: The HTTP status code
            is_error: Whether this call resulted in an error

        Returns:
            True if tracking was successful, False otherwise
        """
        try:
            # Update the endpoint key for storage
            endpoint_key = f"{method}:{endpoint}"

            # 1. Update account-level metrics
            await self._update_account_metrics(endpoint_key, response_time, is_error)

            # 2. Update endpoint-specific metrics
            await self._update_endpoint_metrics(
                endpoint, method, response_time, is_error
            )

            # 3. Buffer the response time for percentile calculations
            self._buffer_response_time(endpoint_key, response_time)

            # 4. Log the API call for monitoring
            log_info(
                f"API call tracked",
                {
                    "account_id": self.account_id,
                    "endpoint": endpoint,
                    "method": method,
                    "response_time": response_time,
                    "status_code": status_code,
                    "is_error": is_error,
                },
            )

            return True
        except Exception as e:
            log_error(f"Error tracking API call: {str(e)}", exc_info=True)
            return False

    async def _update_account_metrics(
        self, endpoint_key: str, response_time: float, is_error: bool
    ) -> None:
        """
        Update account-level metrics for an API call.

        Args:
            endpoint_key: The endpoint identifier (method:path)
            response_time: The response time in seconds
            is_error: Whether this call resulted in an error
        """
        try:
            # Get current metrics or create if not exists
            stmt = select(AccountUsageMetrics).where(
                AccountUsageMetrics.account_id == self.account_id
            )
            result = await self.db.execute(stmt)
            metrics = result.scalar_one_or_none()

            if not metrics:
                # Create new metrics record
                metrics = AccountUsageMetrics(
                    account_id=self.account_id,
                    api_calls_count=1,
                    endpoint_usage={endpoint_key: 1},
                    avg_response_time=response_time,
                    error_count=1 if is_error else 0,
                    error_rate=1.0 if is_error else 0.0,
                    error_types={} if not is_error else {"status_code": 1},
                    billing_period_api_calls=1,
                )
                self.db.add(metrics)
            else:
                # Update existing metrics
                metrics.api_calls_count += 1
                metrics.billing_period_api_calls += 1

                # Update endpoint usage
                endpoint_usage = metrics.endpoint_usage or {}
                endpoint_usage[endpoint_key] = endpoint_usage.get(endpoint_key, 0) + 1
                metrics.endpoint_usage = endpoint_usage

                # Update response time metrics
                old_avg = metrics.avg_response_time or 0
                metrics.avg_response_time = (
                    (old_avg * (metrics.api_calls_count - 1)) + response_time
                ) / metrics.api_calls_count

                # Update error metrics
                if is_error:
                    metrics.error_count += 1
                    error_types = metrics.error_types or {}
                    error_types["status_code"] = error_types.get("status_code", 0) + 1
                    metrics.error_types = error_types

                metrics.error_rate = metrics.error_count / metrics.api_calls_count

            # Commit changes
            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            log_error(f"Error updating account metrics: {str(e)}", exc_info=True)
            raise

    async def _update_endpoint_metrics(
        self, endpoint: str, method: str, response_time: float, is_error: bool
    ) -> None:
        """
        Update endpoint-specific metrics for an API call.

        Args:
            endpoint: The API endpoint path
            method: The HTTP method
            response_time: The response time in seconds
            is_error: Whether this call resulted in an error
        """
        try:
            # Use PostgreSQL's upsert functionality for atomic updates
            stmt = insert(EndpointMetrics).values(
                account_id=self.account_id,
                endpoint=endpoint,
                method=method,
                call_count=1,
                avg_response_time=response_time,
                min_response_time=response_time,
                max_response_time=response_time,
                error_count=1 if is_error else 0,
                error_rate=1.0 if is_error else 0.0,
                last_called_at=datetime.now(timezone.utc),
            )

            # On conflict, update the metrics
            stmt = stmt.on_conflict_do_update(
                index_elements=["account_id", "endpoint", "method"],
                set_={
                    "call_count": EndpointMetrics.call_count + 1,
                    "avg_response_time": (
                        EndpointMetrics.avg_response_time * EndpointMetrics.call_count
                        + response_time
                    )
                    / (EndpointMetrics.call_count + 1),
                    "min_response_time": func.least(
                        EndpointMetrics.min_response_time, response_time
                    ),
                    "max_response_time": func.greatest(
                        EndpointMetrics.max_response_time, response_time
                    ),
                    "error_count": EndpointMetrics.error_count + (1 if is_error else 0),
                    "error_rate": (EndpointMetrics.error_count + (1 if is_error else 0))
                    / (EndpointMetrics.call_count + 1),
                    "last_called_at": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc),
                },
            )

            await self.db.execute(stmt)
            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            log_error(f"Error updating endpoint metrics: {str(e)}", exc_info=True)
            raise

    def _buffer_response_time(self, endpoint_key: str, response_time: float) -> None:
        """
        Buffer response times for calculating percentiles.

        Args:
            endpoint_key: The endpoint identifier
            response_time: The response time to buffer
        """
        if endpoint_key not in self._response_times_buffer:
            self._response_times_buffer[endpoint_key] = []

        buffer = self._response_times_buffer[endpoint_key]
        buffer.append(response_time)

        # Keep buffer size limited
        if len(buffer) > self._buffer_size:
            buffer.pop(0)

    async def calculate_percentiles(self) -> None:
        """
        Calculate response time percentiles from the buffered data and update the database.
        This should be called periodically to update p95 and p99 metrics.
        """
        try:
            for endpoint_key, times in self._response_times_buffer.items():
                if len(times) < 10:  # Need enough data for meaningful percentiles
                    continue

                # Calculate percentiles
                p95 = statistics.quantiles(times, n=20)[19]  # 95th percentile
                p99 = (
                    statistics.quantiles(times, n=100)[99]
                    if len(times) >= 100
                    else times[-1]
                )  # 99th percentile or max

                # Extract method and endpoint
                method, endpoint = endpoint_key.split(":", 1)

                # Update endpoint metrics
                stmt = (
                    update(EndpointMetrics)
                    .where(
                        (EndpointMetrics.account_id == self.account_id)
                        & (EndpointMetrics.endpoint == endpoint)
                        & (EndpointMetrics.method == method)
                    )
                    .values(
                        p95_response_time=p95,
                        p99_response_time=p99,
                        updated_at=datetime.now(timezone.utc),
                    )
                )

                await self.db.execute(stmt)

            # Update account-level metrics with overall percentiles
            all_times = [
                t for times in self._response_times_buffer.values() for t in times
            ]
            if len(all_times) >= 10:
                p95 = statistics.quantiles(all_times, n=20)[19]
                p99 = (
                    statistics.quantiles(all_times, n=100)[99]
                    if len(all_times) >= 100
                    else all_times[-1]
                )

                stmt = (
                    update(AccountUsageMetrics)
                    .where(AccountUsageMetrics.account_id == self.account_id)
                    .values(
                        p95_response_time=p95,
                        p99_response_time=p99,
                        updated_at=datetime.now(timezone.utc),
                    )
                )

                await self.db.execute(stmt)

            await self.db.commit()

        except Exception as e:
            await self.db.rollback()
            log_error(f"Error calculating percentiles: {str(e)}", exc_info=True)

    async def get_account_metrics(self) -> Dict[str, Any]:
        """
        Get the current account metrics.

        Returns:
            A dictionary with the account metrics
        """
        try:
            stmt = select(AccountUsageMetrics).where(
                AccountUsageMetrics.account_id == self.account_id
            )
            result = await self.db.execute(stmt)
            metrics = result.scalar_one_or_none()

            if not metrics:
                return {
                    "account_id": self.account_id,
                    "api_calls_count": 0,
                    "error_rate": 0.0,
                    "avg_response_time": 0.0,
                }

            return {
                "account_id": metrics.account_id,
                "api_calls_count": metrics.api_calls_count,
                "storage_used": metrics.storage_used,
                "endpoint_usage": metrics.endpoint_usage,
                "training_count": metrics.training_count,
                "recommendation_count": metrics.recommendation_count,
                "avg_response_time": metrics.avg_response_time,
                "p95_response_time": metrics.p95_response_time,
                "p99_response_time": metrics.p99_response_time,
                "error_count": metrics.error_count,
                "error_rate": metrics.error_rate,
                "error_types": metrics.error_types,
                "last_reset": (
                    metrics.last_reset.isoformat() if metrics.last_reset else None
                ),
                "updated_at": (
                    metrics.updated_at.isoformat() if metrics.updated_at else None
                ),
                "billing_period_api_calls": metrics.billing_period_api_calls,
            }

        except Exception as e:
            log_error(f"Error getting account metrics: {str(e)}", exc_info=True)
            return {"account_id": self.account_id, "error": str(e)}

    async def get_endpoint_metrics(
        self, endpoint: Optional[str] = None, method: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Get metrics for specific endpoints or all endpoints.

        Args:
            endpoint: Optional endpoint path to filter by
            method: Optional HTTP method to filter by

        Returns:
            A list of dictionaries with endpoint metrics
        """
        try:
            query = select(EndpointMetrics).where(
                EndpointMetrics.account_id == self.account_id
            )

            if endpoint:
                query = query.where(EndpointMetrics.endpoint == endpoint)

            if method:
                query = query.where(EndpointMetrics.method == method)

            result = await self.db.execute(query)
            metrics_list = result.scalars().all()

            return [
                {
                    "account_id": metrics.account_id,
                    "endpoint": metrics.endpoint,
                    "method": metrics.method,
                    "call_count": metrics.call_count,
                    "avg_response_time": metrics.avg_response_time,
                    "p95_response_time": metrics.p95_response_time,
                    "p99_response_time": metrics.p99_response_time,
                    "min_response_time": metrics.min_response_time,
                    "max_response_time": metrics.max_response_time,
                    "error_count": metrics.error_count,
                    "error_rate": metrics.error_rate,
                    "last_called_at": (
                        metrics.last_called_at.isoformat()
                        if metrics.last_called_at
                        else None
                    ),
                    "updated_at": (
                        metrics.updated_at.isoformat() if metrics.updated_at else None
                    ),
                }
                for metrics in metrics_list
            ]

        except Exception as e:
            log_error(f"Error getting endpoint metrics: {str(e)}", exc_info=True)
            return []
