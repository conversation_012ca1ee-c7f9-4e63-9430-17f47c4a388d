"""
Test for the robust_upsert method in BaseRepository.
"""

import pytest
from sqlalchemy.ext.asyncio import AsyncSession
from src.db.repositories.base import BaseRepository
from src.db import models


@pytest.mark.asyncio
async def test_robust_upsert(db_session: AsyncSession):
    """Test that robust_upsert correctly inserts and updates records."""
    # Create a repository for testing
    repo = BaseRepository(db_session, account_id=1, model=models.Product)

    # Test data
    products = [
        {
            "id": 1,
            "name": "Test Product 1",
            "description": "Description 1",
            "price": 10.0,
            "category": "Test Category",
        },
        {
            "id": 2,
            "name": "Test Product 2",
            "description": "Description 2",
            "price": 20.0,
            "category": "Test Category",
        },
    ]

    # Insert products
    inserted = await repo.robust_upsert(products, index_elements=["account_id", "id"])
    assert len(inserted) == 2
    assert inserted[0].name == "Test Product 1"
    assert inserted[1].name == "Test Product 2"

    # Update products
    updated_products = [
        {
            "id": 1,
            "name": "Updated Product 1",
            "price": 15.0,
        },
        {
            "id": 2,
            "name": "Updated Product 2",
            "price": 25.0,
        },
    ]

    updated = await repo.robust_upsert(
        updated_products, index_elements=["account_id", "id"]
    )
    assert len(updated) == 2
    assert updated[0].name == "Updated Product 1"
    assert updated[0].price == 15.0
    assert updated[0].description == "Description 1"  # Should not be changed
    assert updated[1].name == "Updated Product 2"
    assert updated[1].price == 25.0
    assert updated[1].description == "Description 2"  # Should not be changed
