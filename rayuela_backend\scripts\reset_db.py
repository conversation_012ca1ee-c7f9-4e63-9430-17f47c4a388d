"""
Script para resetear la base de datos.
Este script elimina y recrea la base de datos, y luego aplica las migraciones.
"""

import asyncio
from src.utils.base_logger import logger

# Importar las funciones de db_manage_utils
from scripts.db_manage_utils import _reset_database, _run_alembic_upgrade


async def reset_database():
    """
    Elimina y recrea la base de datos, y luego aplica las migraciones.
    
    ADVERTENCIA: Esta función elimina TODOS los datos.
    Solo debe usarse en entornos de desarrollo local.
    """
    logger.warning("ADVERTENCIA: Esta función elimina TODOS los datos.")
    logger.warning("Solo debe usarse en entornos de desarrollo local.")
    
    try:
        # Resetear la base de datos
        reset_success = await _reset_database()
        if reset_success:
            # Aplicar migraciones
            await _run_alembic_upgrade()
            logger.info("Base de datos reseteada y migraciones aplicadas correctamente.")
            return True
        else:
            logger.error("Error al resetear la base de datos.")
            return False
    except Exception as e:
        logger.error(f"Error reseteando la base de datos: {e}")
        return False


if __name__ == "__main__":
    # Si se ejecuta directamente, resetear la base de datos
    logger.info("Reseteando base de datos...")
    result = asyncio.run(reset_database())
    if result:
        logger.info("Base de datos reseteada correctamente.")
    else:
        logger.error("Error reseteando la base de datos.")
