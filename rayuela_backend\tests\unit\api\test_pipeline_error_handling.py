"""
Tests for error handling in pipeline endpoints.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import HTT<PERSON>Exception
from datetime import datetime, timezone

from src.api.v1.endpoints.pipeline import train_models, get_training_status
from src.core.exceptions import LimitExceededError, ResourceNotFoundError


class TestPipelineErrorHandling:
    """Tests for error handling in pipeline endpoints."""
    
    @pytest.mark.asyncio
    async def test_train_models_api_limit_exceeded(self):
        """Test train_models when API limit is exceeded."""
        # Setup mocks
        mock_request = AsyncMock()
        mock_request.json.return_value = {"param": "value"}
        
        mock_account = MagicMock()
        mock_account.account_id = 1
        
        mock_db = AsyncMock()
        mock_db.begin = AsyncMock().__aenter__.return_value
        
        mock_limit_service = AsyncMock()
        mock_limit_service.validate_api_call_limit.side_effect = LimitExceededError("API limit exceeded")
        
        # Execute and assert
        with pytest.raises(HTTPException) as excinfo:
            await train_models(
                request=mock_request,
                account=mock_account,
                db=mock_db,
                limit_service=mock_limit_service
            )
        
        # Verify exception details
        assert excinfo.value.status_code == 429
        assert "Model training limit exceeded" in excinfo.value.detail
        
        # Verify method calls
        mock_limit_service.validate_api_call_limit.assert_called_once_with("model_training")
        mock_limit_service.validate_training_frequency.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_train_models_training_frequency_exceeded(self):
        """Test train_models when training frequency is exceeded."""
        # Setup mocks
        mock_request = AsyncMock()
        mock_request.json.return_value = {"param": "value"}
        
        mock_account = MagicMock()
        mock_account.account_id = 1
        
        mock_db = AsyncMock()
        mock_db.begin = AsyncMock().__aenter__.return_value
        
        mock_limit_service = AsyncMock()
        mock_limit_service.validate_api_call_limit.return_value = None
        mock_limit_service.validate_training_frequency.side_effect = LimitExceededError(
            "Training frequency limit exceeded"
        )
        
        # Execute and assert
        with pytest.raises(HTTPException) as excinfo:
            await train_models(
                request=mock_request,
                account=mock_account,
                db=mock_db,
                limit_service=mock_limit_service
            )
        
        # Verify exception details
        assert excinfo.value.status_code == 429
        assert "Training frequency limit exceeded" in excinfo.value.detail
        
        # Verify method calls
        mock_limit_service.validate_api_call_limit.assert_called_once_with("model_training")
        mock_limit_service.validate_training_frequency.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_train_models_database_error(self):
        """Test train_models when database error occurs."""
        # Setup mocks
        mock_request = AsyncMock()
        mock_request.json.return_value = {"param": "value"}
        
        mock_account = MagicMock()
        mock_account.account_id = 1
        
        mock_db = AsyncMock()
        mock_db.begin.side_effect = Exception("Database error")
        
        mock_limit_service = AsyncMock()
        mock_limit_service.validate_api_call_limit.return_value = None
        mock_limit_service.validate_training_frequency.return_value = None
        
        # Execute and assert
        with pytest.raises(HTTPException) as excinfo:
            await train_models(
                request=mock_request,
                account=mock_account,
                db=mock_db,
                limit_service=mock_limit_service
            )
        
        # Verify exception details
        assert excinfo.value.status_code == 500
        assert "Database error" in excinfo.value.detail
        
        # Verify method calls
        mock_limit_service.validate_api_call_limit.assert_called_once_with("model_training")
        mock_limit_service.validate_training_frequency.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_training_status_service_error(self):
        """Test get_training_status when recommendation service error occurs."""
        # Setup mocks
        mock_account = MagicMock()
        mock_account.account_id = 1
        
        mock_db = AsyncMock()
        
        # Mock recommendation_service.get_metrics
        with patch('src.api.v1.endpoints.pipeline.recommendation_service.get_metrics', 
                  side_effect=Exception("Service error")):
            
            # Execute and assert
            with pytest.raises(HTTPException) as excinfo:
                await get_training_status(
                    account=mock_account,
                    db=mock_db
                )
            
            # Verify exception details
            assert excinfo.value.status_code == 500
            assert "Service error" in excinfo.value.detail
    
    @pytest.mark.asyncio
    async def test_get_training_status_resource_not_found(self):
        """Test get_training_status when resource not found."""
        # Setup mocks
        mock_account = MagicMock()
        mock_account.account_id = 1
        
        mock_db = AsyncMock()
        
        # Mock recommendation_service.get_metrics
        with patch('src.api.v1.endpoints.pipeline.recommendation_service.get_metrics', 
                  side_effect=ResourceNotFoundError("Model not found")):
            
            # Execute and assert
            with pytest.raises(HTTPException) as excinfo:
                await get_training_status(
                    account=mock_account,
                    db=mock_db
                )
            
            # Verify exception details
            assert excinfo.value.status_code == 500
            assert "Model not found" in excinfo.value.detail
