from sqlalchemy import (
    Column,
    Integer,
    String,
    DateTime,
    ForeignKey,
    DECIMAL,
    Enum,
    Text,
    Index,
    func,
    PrimaryKeyConstraint,
    ForeignKeyConstraint,
    UniqueConstraint,
)
from sqlalchemy.orm import relationship
from datetime import datetime

from src.db.base import Base
from src.db.enums import OrderStatus


class Order(Base):
    """
    Modelo para representar órdenes de compra.
    """

    __tablename__ = "orders"

    account_id = Column(Integer, ForeignKey("accounts.account_id"), primary_key=True)
    id = Column(Integer, primary_key=True)
    order_number = Column(String(50), nullable=False)
    user_id = Column(Integer, nullable=False, index=True)
    status = Column(
        Enum(OrderStatus), default=OrderStatus.PENDING, nullable=False, index=True
    )

    # Información de la orden
    total_amount = Column(DECIMAL(10, 2), nullable=False)
    currency = Column(String(3), default="USD", nullable=False)

    # Información de envío
    shipping_address = Column(Text, nullable=True)
    billing_address = Column(Text, nullable=True)

    # Fechas importantes
    order_date = Column(
        DateTime(timezone=True), default=func.now(), nullable=False, index=True
    )
    confirmed_at = Column(DateTime(timezone=True), nullable=True)
    shipped_at = Column(DateTime(timezone=True), nullable=True)
    delivered_at = Column(DateTime(timezone=True), nullable=True)
    canceled_at = Column(DateTime(timezone=True), nullable=True)

    # Metadatos adicionales
    notes = Column(Text, nullable=True)
    payment_method = Column(String(50), nullable=True)
    tracking_number = Column(String(100), nullable=True)

    # Timestamps estándar
    created_at = Column(
        DateTime(timezone=True), default=func.now(), server_default=func.now()
    )
    updated_at = Column(
        DateTime(timezone=True),
        default=func.now(),
        server_default=func.now(),
        onupdate=func.now(),
    )

    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        # CRITICAL: Unique constraint for order_number per account (US-SEC-004)
        # This ensures order numbers are unique within each tenant, preventing data integrity issues
        UniqueConstraint("account_id", "order_number", name="uq_order_account_number"),
        # Composite FK for user_id to ensure tenant isolation
        ForeignKeyConstraint(
            ["user_id", "account_id"],
            ["end_users.user_id", "end_users.account_id"],
            ondelete="CASCADE",
            name="fk_order_end_user",
        ),
        # Note: idx_order_account_order_number is redundant since UniqueConstraint creates an index
        Index("idx_order_account_status", "account_id", "status"),
        Index("idx_order_account_date", "account_id", "order_date"),
    )

    # Relaciones
    account = relationship("Account", back_populates="orders")
    end_user = relationship(
        "EndUser",
        foreign_keys=[user_id, account_id],
        primaryjoin="and_(Order.user_id==EndUser.user_id, Order.account_id==EndUser.account_id)",
        back_populates="orders",
    )
    order_items = relationship(
        "OrderItem", back_populates="order", cascade="all, delete-orphan"
    )

    def __repr__(self):
        return f"<Order(id={self.id}, order_number='{self.order_number}', status='{self.status}', total_amount={self.total_amount})>"
