"""add_subscription_tracking_fields

Revision ID: 60753112de16
Revises: f2d345c271dd
Create Date: 2025-04-21 22:52:38.697372

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '60753112de16'
down_revision: Union[str, None] = 'f2d345c271dd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to add subscription tracking fields."""
    # Add new fields to the subscriptions table
    op.add_column('subscriptions', sa.Column('monthly_api_calls_used', sa.Integer(), nullable=False, server_default='0'))
    op.add_column('subscriptions', sa.Column('storage_used', sa.BigInteger(), nullable=False, server_default='0'))
    op.add_column('subscriptions', sa.Column('last_reset_date', sa.DateTime(timezone=True), nullable=True))
    op.add_column('subscriptions', sa.Column('available_models', sa.JSON(), nullable=True))

    # Add comment to explain the purpose of the new fields - one statement per execute call
    op.execute("COMMENT ON COLUMN subscriptions.monthly_api_calls_used IS 'Number of API calls used in the current month';")
    op.execute("COMMENT ON COLUMN subscriptions.storage_used IS 'Amount of storage used in bytes';")
    op.execute("COMMENT ON COLUMN subscriptions.last_reset_date IS 'Date when the monthly API calls counter was last reset';")
    op.execute("COMMENT ON COLUMN subscriptions.available_models IS 'List of models available for this subscription';")

    # Initialize last_reset_date to the current date for existing subscriptions
    op.execute("UPDATE subscriptions SET last_reset_date = NOW() WHERE last_reset_date IS NULL;")


def downgrade() -> None:
    """Downgrade schema to remove subscription tracking fields."""
    # Remove the new fields from the subscriptions table
    op.drop_column('subscriptions', 'available_models')
    op.drop_column('subscriptions', 'last_reset_date')
    op.drop_column('subscriptions', 'storage_used')
    op.drop_column('subscriptions', 'monthly_api_calls_used')
