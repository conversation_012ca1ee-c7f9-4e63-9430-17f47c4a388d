# 🎯 Rayuela - Plataforma Multi-Tenant

Una plataforma SaaS multi-tenant construida con FastAPI, Next.js y desplegada en Google Cloud Platform.

## 🌐 **Aplicación en Vivo**

| Servicio | URL | Estado |
|----------|-----|--------|
| 🖥️ **Frontend** | https://rayuela-frontend-lrw7xazcbq-uc.a.run.app | ✅ Funcionando |
| 🔧 **Backend API** | https://rayuela-backend-lrw7xazcbq-uc.a.run.app | ✅ Funcionando |
| 📡 **API Docs** | https://rayuela-api-lrw7xazcbq-uc.a.run.app | ✅ Funcionando |

### 🏥 **Health Check**
```bash
curl https://rayuela-backend-lrw7xazcbq-uc.a.run.app/health
# Response: {"status":"healthy"}
```

## 🏗️ **Arquitectura**

### **Backend (FastAPI)**
- **Framework:** FastAPI con Python 3.12
- **Base de datos:** PostgreSQL en Cloud SQL
- **Cache:** Redis en Memorystore  
- **Storage:** Cloud Storage
- **Deployment:** Cloud Run con auto-scaling

### **Frontend (Next.js)**
- **Framework:** Next.js con TypeScript
- **Deployment:** Cloud Run
- **CDN:** Google Cloud CDN

### **CI/CD Pipeline**
- **Platform:** Google Cloud Build
- **Tests:** Unit tests, integration tests, security scans
- **Quality:** Black, isort, flake8, bandit
- **Deployment:** Automated deployment on push to main

## 🚀 **Quick Start**

### **Para Desarrolladores**

1. **Clonar el repositorio:**
```bash
git clone https://github.com/marcelo-vera/rayuela.git
cd rayuela
```

2. **Backend setup:**
```bash
cd rayuela_backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

3. **Frontend setup:**
```bash
cd rayuela_frontend
npm install
npm run dev
```

### **Para Deployment**

El deployment es automático via CI/CD. Solo haz push a `main`:

```bash
git add .
git commit -m "feat: your changes"
git push origin main
```

El pipeline automáticamente:
- ✅ Ejecuta tests y verificaciones de calidad
- ✅ Construye imágenes Docker  
- ✅ Despliega a Cloud Run
- ✅ Verifica la salud de los servicios

## 📁 **Estructura del Proyecto**

```
rayuela/
├── 📄 README.md                    # Este archivo
├── 🔧 cloudbuild.yaml             # Pipeline CI/CD principal
├── 🚀 cloudbuild-deploy-production.yaml  # Pipeline de deployment rápido
├── 🙈 .gitignore                  # Archivos ignorados por Git
│
├── 📖 docs/                       # Documentación
│   ├── DEPLOYMENT_GUIDE.md        # Guía completa de deployment
│   ├── ARCHITECTURE.md            # Documentación de arquitectura
│   ├── ci_cd_pipeline.md          # Documentación del CI/CD
│   ├── CLOUD_RUN_DEPLOYMENT.md    # Deployment específico en Cloud Run
│   ├── CUSTOM_DOMAIN_SETUP.md     # Configuración de dominio personalizado
│   ├── MULTI_TENANCY_SECURITY_GUIDE.md  # Guía de seguridad multi-tenant
│   └── ERROR_CODES.md             # Documentación de códigos de error
│
├── 🛠️ scripts/                    # Scripts de utilidad
│   ├── deploy-production.sh       # Script de deployment manual
│   ├── verify-deployment.sh       # Verificación completa del deployment
│   ├── setup-monitoring.sh        # Configuración de monitoreo
│   ├── setup-custom-domain.sh     # Configuración de dominio personalizado
│   └── update-frontend-config.sh  # Actualización de configuración del frontend
│
├── ⚙️ config/                     # Archivos de configuración
│   └── config-production.env.example  # Ejemplo de variables de entorno
│
├── 🐍 rayuela_backend/            # Backend FastAPI
│   ├── src/                       # Código fuente
│   ├── tests/                     # Tests unitarios e integración
│   ├── Dockerfile                 # Imagen Docker del backend
│   ├── requirements.txt           # Dependencias de producción
│   ├── requirements-dev.txt       # Dependencias de desarrollo
│   └── docker-compose.test.yml    # Docker Compose para tests
│
└── ⚛️ rayuela_frontend/            # Frontend Next.js
    ├── src/                       # Código fuente React/Next.js
    ├── public/                    # Assets públicos
    ├── Dockerfile                 # Imagen Docker del frontend
    ├── package.json               # Dependencias Node.js
    └── next.config.js             # Configuración de Next.js
```

## 🛠️ **Scripts Disponibles**

| Script | Descripción | Uso |
|--------|-------------|-----|
| `scripts/deploy-production.sh` | Deployment manual a producción | `./scripts/deploy-production.sh --direct` |
| `scripts/verify-deployment.sh` | Verificación completa del deployment | `./scripts/verify-deployment.sh` |
| `scripts/setup-monitoring.sh` | Configurar alertas y dashboards | `./scripts/setup-monitoring.sh` |
| `scripts/setup-custom-domain.sh` | Configurar dominio personalizado | `./scripts/setup-custom-domain.sh yourdomain.com` |

## 🔧 **Comandos Útiles**

### **Monitoreo y Logs**
```bash
# Ver servicios desplegados
gcloud run services list --region=us-central1

# Ver logs del backend
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=rayuela-backend" --limit=10

# Ver estado de builds
gcloud builds list --limit=5

# Verificación completa
./scripts/verify-deployment.sh
```

### **Desarrollo Local**
```bash
# Tests del backend
cd rayuela_backend
pytest tests/ -v

# Formatear código
black src/ tests/
isort src/ tests/
flake8 src/ tests/

# Tests del frontend
cd rayuela_frontend
npm test
npm run lint
```

## 🏗️ **Infraestructura GCP**

### **Servicios Utilizados:**
- **Cloud Run:** Hosting de containers serverless
- **Cloud SQL:** Base de datos PostgreSQL administrada
- **Memorystore:** Redis administrado para cache
- **Cloud Storage:** Almacenamiento de archivos
- **Cloud Build:** CI/CD pipeline
- **Artifact Registry:** Registry de imágenes Docker
- **Secret Manager:** Gestión de secretos
- **Cloud Monitoring:** Monitoreo y alertas

### **Configuración Actual:**
- **Proyecto:** `lateral-insight-461112-g9`
- **Región:** `us-central1`
- **PostgreSQL IP:** `*************`
- **Redis IP:** `*************`

## 📊 **Monitoring y Logs**

- **Cloud Monitoring:** https://console.cloud.google.com/monitoring
- **Cloud Logging:** https://console.cloud.google.com/logs
- **Cloud Run Console:** https://console.cloud.google.com/run

## 🔒 **Seguridad**

- ✅ **Multi-tenancy:** Aislamiento completo de datos por tenant
- ✅ **Row Level Security (RLS):** Políticas de seguridad a nivel de base de datos
- ✅ **Secret Management:** Credenciales en Secret Manager
- ✅ **Security Scans:** Bandit, safety, pip-audit en CI/CD
- ✅ **HTTPS:** SSL/TLS en todos los endpoints

## 📚 **Documentación Adicional**

- [📖 Guía de Deployment](docs/DEPLOYMENT_GUIDE.md)
- [🏗️ Documentación de Arquitectura](docs/ARCHITECTURE.md)
- [🔄 CI/CD Pipeline](docs/ci_cd_pipeline.md)
- [🌐 Cloud Run Deployment](docs/CLOUD_RUN_DEPLOYMENT.md)
- [🔒 Guía de Seguridad Multi-Tenant](docs/MULTI_TENANCY_SECURITY_GUIDE.md)

## 🤝 **Contribución**

1. Fork el proyecto
2. Crea una rama para tu feature (`git checkout -b feature/amazing-feature`)
3. Commit tus cambios (`git commit -m 'feat: add amazing feature'`)
4. Push a la rama (`git push origin feature/amazing-feature`)
5. Abre un Pull Request

## 📄 **Licencia**

Este proyecto está bajo la Licencia MIT. Ver el archivo `LICENSE` para más detalles.

## 🆘 **Soporte**

Si tienes problemas:

1. **Revisa la documentación** en el directorio `docs/`
2. **Ejecuta verificación:** `./scripts/verify-deployment.sh`
3. **Revisa logs:** Ver comandos útiles arriba
4. **Consulta issues:** GitHub Issues del proyecto

---

## 🎯 **Status del Proyecto**

**✅ PROYECTO COMPLETAMENTE DESPLEGADO Y FUNCIONANDO**

- **Último deployment:** Build ID `d0ce8739-1f24-4cb5-ab79-40c9b069b015`
- **Fecha:** 30 de Mayo, 2025
- **Status:** ✅ Producción estable
- **Uptime:** 24/7 con auto-scaling

¡La aplicación está lista para usar! 🎉
