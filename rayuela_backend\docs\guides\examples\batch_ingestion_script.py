#!/usr/bin/env python
"""
Script de ejemplo para la ingesta masiva de datos en Rayuela.

Este script lee datos de archivos CSV y los envía al endpoint de ingesta masiva.
"""

import csv
import json
import time
import argparse
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime


def read_csv_file(file_path: str) -> List[Dict[str, Any]]:
    """
    Lee un archivo CSV y devuelve una lista de diccionarios.

    Args:
        file_path: Ruta al archivo CSV

    Returns:
        Lista de diccionarios con los datos del CSV
    """
    data = []
    with open(file_path, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            # Convertir valores numéricos
            for key, value in row.items():
                if key in ["price", "average_rating", "value"]:
                    try:
                        row[key] = float(value)
                    except (ValueError, TypeError):
                        pass
                elif key in ["num_ratings", "inventory_count"]:
                    try:
                        row[key] = int(value)
                    except (ValueError, TypeError):
                        pass
            data.append(row)
    return data


def prepare_payload(
    users_file: Optional[str] = None,
    products_file: Optional[str] = None,
    interactions_file: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Prepara el payload para la ingesta masiva.

    Args:
        users_file: Ruta al archivo CSV de usuarios
        products_file: Ruta al archivo CSV de productos
        interactions_file: Ruta al archivo CSV de interacciones

    Returns:
        Diccionario con el payload para la ingesta masiva
    """
    payload = {}

    # Cargar usuarios
    if users_file:
        users = read_csv_file(users_file)
        payload["users"] = users

    # Cargar productos
    if products_file:
        products = read_csv_file(products_file)
        payload["products"] = products

    # Cargar interacciones
    if interactions_file:
        interactions = read_csv_file(interactions_file)

        # Convertir external_id a id para interacciones
        # Nota: En un caso real, necesitarías mapear los external_id a los id reales
        # Este es solo un ejemplo simplificado
        for interaction in interactions:
            if "end_user_external_id" in interaction:
                interaction["end_user_id"] = interaction.pop("end_user_external_id")
            if "product_external_id" in interaction:
                interaction["product_id"] = interaction.pop("product_external_id")

        payload["interactions"] = interactions

    return payload


def send_batch_request(
    api_url: str, api_key: str, payload: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Envía una solicitud de ingesta masiva a la API.

    Args:
        api_url: URL base de la API
        api_key: API Key para autenticación
        payload: Payload para la ingesta masiva

    Returns:
        Respuesta de la API
    """
    headers = {"X-API-Key": api_key, "Content-Type": "application/json"}

    response = requests.post(
        f"{api_url}/ingestion/batch", headers=headers, json=payload
    )

    if response.status_code != 202:
        raise Exception(
            f"Error en la solicitud: {response.status_code} - {response.text}"
        )

    return response.json()


def check_job_status(api_url: str, api_key: str, job_id: int) -> Dict[str, Any]:
    """
    Verifica el estado de un trabajo de ingesta masiva.

    Args:
        api_url: URL base de la API
        api_key: API Key para autenticación
        job_id: ID del trabajo de ingesta

    Returns:
        Estado del trabajo
    """
    headers = {"X-API-Key": api_key}

    response = requests.get(f"{api_url}/ingestion/batch/{job_id}", headers=headers)

    if response.status_code != 200:
        raise Exception(
            f"Error al verificar el estado: {response.status_code} - {response.text}"
        )

    return response.json()


def monitor_job(
    api_url: str,
    api_key: str,
    job_id: int,
    check_interval: int = 5,
    max_checks: int = 20,
) -> None:
    """
    Monitorea el estado de un trabajo de ingesta masiva hasta que se complete o falle.

    Args:
        api_url: URL base de la API
        api_key: API Key para autenticación
        job_id: ID del trabajo de ingesta
        check_interval: Intervalo entre verificaciones (en segundos)
        max_checks: Número máximo de verificaciones
    """
    checks = 0
    completed = False

    print(f"Monitoreando trabajo de ingesta (ID: {job_id})...")

    while checks < max_checks and not completed:
        try:
            status = check_job_status(api_url, api_key, job_id)

            job_status = status.get("status", "")
            progress = status.get("progress_percentage", 0)

            print(f"Estado: {job_status} - Progreso: {progress:.1f}%")

            if job_status in ["completed", "failed"]:
                completed = True

                if job_status == "completed":
                    processed = status.get("processed_count", {})
                    print("\n¡Ingesta completada exitosamente!")
                    print(f"Usuarios procesados: {processed.get('users', 0)}")
                    print(f"Productos procesados: {processed.get('products', 0)}")
                    print(
                        f"Interacciones procesadas: {processed.get('interactions', 0)}"
                    )
                    print(f"Total procesado: {processed.get('total', 0)}")
                    print(f"Errores: {processed.get('errors', 0)}")
                else:
                    error_msg = status.get("error_message", "Error desconocido")
                    print(f"\n¡Ingesta fallida! Error: {error_msg}")

            if not completed:
                time.sleep(check_interval)
                checks += 1

        except Exception as e:
            print(f"Error al monitorear el trabajo: {str(e)}")
            time.sleep(check_interval)
            checks += 1

    if not completed:
        print(
            "\nSe alcanzó el límite de verificaciones. El trabajo puede seguir en progreso."
        )
        print(f"Verifique manualmente el estado con el ID: {job_id}")


def main():
    """Función principal del script."""
    parser = argparse.ArgumentParser(
        description="Script de ingesta masiva para Rayuela"
    )
    parser.add_argument(
        "--api-url", required=True, help="URL base de la API de Rayuela"
    )
    parser.add_argument("--api-key", required=True, help="API Key para autenticación")
    parser.add_argument("--users", help="Ruta al archivo CSV de usuarios")
    parser.add_argument("--products", help="Ruta al archivo CSV de productos")
    parser.add_argument("--interactions", help="Ruta al archivo CSV de interacciones")
    parser.add_argument(
        "--monitor", action="store_true", help="Monitorear el estado del trabajo"
    )

    args = parser.parse_args()

    if not (args.users or args.products or args.interactions):
        parser.error(
            "Debe proporcionar al menos un archivo de datos (usuarios, productos o interacciones)"
        )

    try:
        # Preparar payload
        print("Preparando datos para la ingesta...")
        payload = prepare_payload(args.users, args.products, args.interactions)

        # Mostrar resumen
        print("\nResumen de datos a ingestar:")
        if "users" in payload:
            print(f"- Usuarios: {len(payload['users'])}")
        if "products" in payload:
            print(f"- Productos: {len(payload['products'])}")
        if "interactions" in payload:
            print(f"- Interacciones: {len(payload['interactions'])}")

        # Enviar solicitud
        print("\nEnviando solicitud de ingesta masiva...")
        response = send_batch_request(args.api_url, args.api_key, payload)

        # Mostrar respuesta
        job_id = response.get("job_id")
        print(f"\nSolicitud enviada exitosamente. Job ID: {job_id}")
        print(f"Estado inicial: {response.get('status')}")
        print(f"Usuarios a procesar: {response.get('total_users', 0)}")
        print(f"Productos a procesar: {response.get('total_products', 0)}")
        print(f"Interacciones a procesar: {response.get('total_interactions', 0)}")

        # Monitorear estado si se solicita
        if args.monitor and job_id:
            print("\n")
            monitor_job(args.api_url, args.api_key, job_id)

    except Exception as e:
        print(f"\nError: {str(e)}")


if __name__ == "__main__":
    main()
