import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from unittest.mock import patch, MagicMock

from src.db.schemas import ExplanationLevel, ExplanationReason, DetailedExplanation, ExplanationEvidence
from src.ml_pipeline.explanation_generator import ExplanationGenerator
from src.ml_pipeline.serving_engine import ServingEngine


@pytest.mark.asyncio
async def test_get_recommendation_explanation(
    client: AsyncClient, db_session: AsyncSession, test_account, test_user, test_product
):
    """Test para verificar que el endpoint de explicación detallada funciona correctamente."""
    
    # Crear un mock para la explicación detallada
    mock_explanation = DetailedExplanation(
        primary_reason=ExplanationReason.SIMILAR_ITEMS,
        secondary_reasons=[ExplanationReason.CATEGORY_AFFINITY],
        confidence=0.85,
        text_explanation="Recomendado porque es similar a productos que te han gustado.",
        evidence=[
            ExplanationEvidence(
                type="similar_product",
                id=456,
                name="Producto similar",
                relevance=0.9
            ),
            ExplanationEvidence(
                type="category",
                name="electrónicos",
                relevance=0.75
            )
        ],
        source="hybrid"
    )
    
    # Mockear el método generate_explanation del ExplanationGenerator en lugar del servicio centralizado
    with patch.object(
        ExplanationGenerator, 'generate_explanation', return_value=mock_explanation
    ) as mock_get_explanation:
        
        # Realizar la petición al endpoint
        response = await client.get(
            f"/api/v1/recommendations/explain/{test_user.id}/{test_product.id}",
            headers={"Authorization": f"Bearer {test_account.api_key}"}
        )
        
        # Verificar que la respuesta es correcta
        assert response.status_code == 200
        data = response.json()
        
        # Verificar que la explicación tiene la estructura correcta
        assert data["primary_reason"] == ExplanationReason.SIMILAR_ITEMS
        assert ExplanationReason.CATEGORY_AFFINITY in data["secondary_reasons"]
        assert data["confidence"] == 0.85
        assert "similar" in data["text_explanation"].lower()
        assert len(data["evidence"]) == 2
        assert data["source"] == "hybrid"
        
        # Verificar que se llamó al método correcto (la verificación exacta de parámetros puede cambiar)
        mock_get_explanation.assert_called_once()


@pytest.mark.asyncio
async def test_personalized_recommendations_with_explanation_level(
    client: AsyncClient, db_session: AsyncSession, test_account, test_user
):
    """Test para verificar que el parámetro explanation_level funciona correctamente."""
    
    # Mockear el método get_candidate_recommendations del ServingEngine en lugar del servicio centralizado
    with patch.object(
        ServingEngine, 'get_candidate_recommendations'
    ) as mock_get_recommendations:
        # Configurar el mock para devolver una respuesta vacía
        mock_get_recommendations.return_value = []
        
        # Realizar la petición al endpoint con explanation_level=detailed
        response = await client.post(
            "/api/v1/recommendations/personalized/query",
            json={
                "user_id": test_user.id,
                "include_explanation": True,
                "explanation_level": "detailed"
            },
            headers={"Authorization": f"Bearer {test_account.api_key}"}
        )
        
        # Verificar que la respuesta es correcta
        assert response.status_code == 200
        
        # La verificación exacta de parámetros puede necesitar ajustes según la refactorización real
        assert mock_get_recommendations.called
        
        # Realizar la petición al endpoint con explanation_level=simple
        response = await client.post(
            "/api/v1/recommendations/personalized/query",
            json={
                "user_id": test_user.id,
                "include_explanation": True,
                "explanation_level": "simple"
            },
            headers={"Authorization": f"Bearer {test_account.api_key}"}
        )
        
        # Verificar que la respuesta es correcta
        assert response.status_code == 200
        
        # La verificación exacta de parámetros puede necesitar ajustes según la refactorización real
        assert mock_get_recommendations.called
