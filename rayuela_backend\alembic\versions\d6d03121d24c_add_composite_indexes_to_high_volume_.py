"""add_composite_indexes_to_high_volume_tables

Revision ID: d6d03121d24c
Revises: e3477f6a4cd5
Create Date: 2025-04-17 03:26:45.547507

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'd6d03121d24c'
down_revision: Union[str, None] = 'e3477f6a4cd5'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Agregar índices compuestos para la tabla Interaction
    op.create_index(
        'idx_interaction_account_type_timestamp',
        'interactions',
        ['account_id', 'interaction_type', 'timestamp'],
        unique=False
    )
    op.create_index(
        'idx_interaction_account_user_product_timestamp',
        'interactions',
        ['account_id', 'end_user_id', 'product_id', 'timestamp'],
        unique=False
    )

    # Agregar índices compuestos para la tabla AuditLog
    op.create_index(
        'idx_audit_account_entity_created',
        'audit_logs',
        ['account_id', 'entity_type', 'entity_id', 'created_at'],
        unique=False
    )
    op.create_index(
        'idx_audit_account_performer_created',
        'audit_logs',
        ['account_id', 'performed_by', 'created_at'],
        unique=False
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Eliminar índices compuestos de la tabla AuditLog
    op.drop_index('idx_audit_account_performer_created', table_name='audit_logs')
    op.drop_index('idx_audit_account_entity_created', table_name='audit_logs')

    # Eliminar índices compuestos de la tabla Interaction
    op.drop_index('idx_interaction_account_user_product_timestamp', table_name='interactions')
    op.drop_index('idx_interaction_account_type_timestamp', table_name='interactions')
