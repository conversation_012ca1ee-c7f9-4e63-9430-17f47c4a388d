# Índices en la Base de Datos

Este documento explica la estrategia de indexación utilizada en nuestra base de datos y las mejores prácticas para mantener un rendimiento óptimo.

## Cambios Recientes

### Eliminación de Índices Redundantes

Se han realizado los siguientes cambios para optimizar los índices en la base de datos:

1. **Eliminación del índice redundante `idx_account_api_key_hash`**:
   - Este índice era redundante porque ya existe una restricción de unicidad (`UniqueConstraint`) sobre las mismas columnas (`account_id`, `api_key_hash`).
   - En PostgreSQL, una restricción UNIQUE crea automáticamente un índice, por lo que el índice explícito era innecesario.

2. **Corrección del índice `idx_system_user_email`**:
   - Se ha corregido el índice para que incluya tanto `account_id` como `email`, lo que es más apropiado para un entorno multi-tenant.
   - Esto asegura que las búsquedas por email dentro de un tenant específico sean eficientes.

3. **Eliminación de la restricción incorrecta `uq_system_user_id_email`**:
   - Esta restricción incluía `id` que ya es parte de la clave primaria, lo que la hacía redundante e incorrecta.
   - La unicidad de email por tenant se mantiene a través del índice `idx_system_user_email`.

Para aplicar estos cambios a una base de datos existente, ejecuta:

```bash
# Si tienes alembic instalado globalmente
alembic upgrade head

# O si prefieres usar Python directamente
python -m alembic upgrade head

# Si estás usando Docker
docker-compose exec api alembic upgrade head
```

## Estrategia de Indexación

Nuestra estrategia de indexación se basa en los siguientes principios:

### 1. Índices para Claves Primarias y Foráneas

- Las claves primarias tienen índices automáticos en PostgreSQL.
- Las claves foráneas deben tener índices para mejorar el rendimiento de las operaciones JOIN.

### 2. Índices para Consultas Frecuentes

- Creamos índices para las columnas que se utilizan frecuentemente en cláusulas WHERE, ORDER BY o GROUP BY.
- Priorizamos las consultas que se ejecutan con mayor frecuencia o que son críticas para el rendimiento.

### 3. Índices Compuestos para Entornos Multi-tenant

- En un entorno multi-tenant, casi todas las consultas incluyen `account_id`.
- Utilizamos índices compuestos que comienzan con `account_id` para mejorar el rendimiento de las consultas filtradas por tenant.
- Ejemplos: `idx_interaction_account_user`, `idx_product_account_category`.

### 4. Índices para Restricciones de Unicidad

- Las restricciones UNIQUE y PRIMARY KEY crean automáticamente índices en PostgreSQL.
- No es necesario crear índices explícitos para las columnas que ya tienen restricciones de unicidad.

## Índices Existentes

### Tabla `accounts`

- `account_id`: Índice de clave primaria (automático)
- `uq_api_key_hash_global`: Índice único global en `api_key_hash` (seguridad mejorada)
- `mercadopago_customer_id`: Índice simple

**Nota de Seguridad**: El índice `uq_api_key_hash_global` garantiza que cada hash de API key sea único en todo el sistema, previniendo vulnerabilidades de acceso cruzado entre tenants (US-SEC-003).

### Tabla `system_users`

- (`account_id`, `id`): Índice de clave primaria (automático)
- `idx_system_user_email`: Índice compuesto en (`account_id`, `email`)
- `idx_system_user_account`: Índice en `account_id`

### Tabla `interactions`

- (`account_id`, `id`): Índice de clave primaria (automático)
- `idx_interaction_account_user`: Índice compuesto en (`account_id`, `end_user_id`)
- `idx_interaction_account_product`: Índice compuesto en (`account_id`, `product_id`)
- `idx_interaction_account_timestamp`: Índice compuesto en (`account_id`, `timestamp`)

### Tabla `products`

- (`account_id`, `id`): Índice de clave primaria (automático)
- `idx_product_account_category`: Índice compuesto en (`account_id`, `category`)

## Mejores Prácticas

### 1. Evitar Índices Redundantes

- No crear índices explícitos para columnas que ya tienen restricciones UNIQUE o PRIMARY KEY.
- No crear múltiples índices que comiencen con las mismas columnas en el mismo orden.

### 2. Considerar el Orden de las Columnas en Índices Compuestos

- El orden de las columnas en un índice compuesto es crucial para su eficacia.
- Las columnas más utilizadas en cláusulas WHERE deben ir primero, seguidas de las columnas utilizadas en ORDER BY.
- En un entorno multi-tenant, `account_id` suele ir primero porque casi todas las consultas lo filtran.

### 3. Monitorear el Uso de Índices

- Utilizar `EXPLAIN ANALYZE` para verificar si las consultas están utilizando los índices esperados.
- Revisar periódicamente los índices no utilizados y considerar eliminarlos.

### 4. Equilibrar Índices y Rendimiento de Escritura

- Cada índice mejora el rendimiento de lectura pero degrada el rendimiento de escritura.
- Evaluar el equilibrio entre rendimiento de lectura y escritura según las necesidades de la aplicación.

### 5. Particionamiento

- Nuestra base de datos utiliza particionamiento por `account_id` para mejorar el rendimiento en un entorno multi-tenant.
- Los índices locales en cada partición son más pequeños y eficientes que los índices globales.

## Consideraciones para PostgreSQL

### 1. Índices Implícitos

- PostgreSQL crea automáticamente índices para restricciones PRIMARY KEY y UNIQUE.
- No es necesario crear índices explícitos para estas columnas.

### 2. Tipos de Índices

- B-tree: El tipo de índice predeterminado, adecuado para la mayoría de los casos.
- GIN: Útil para columnas JSON o arrays.
- GIST: Útil para datos geoespaciales o búsqueda de texto completo.
- Hash: Útil para igualdades exactas.

### 3. Índices Parciales

- Los índices parciales incluyen solo un subconjunto de filas y pueden ser más eficientes que los índices completos.
- Útiles para columnas con distribución sesgada (por ejemplo, columnas booleanas donde la mayoría de los valores son FALSE).

### 4. Índices de Expresión

- Los índices pueden crearse en expresiones, no solo en columnas.
- Útiles para consultas que utilizan funciones o expresiones en la cláusula WHERE.

## Conclusión

Una estrategia de indexación bien diseñada es crucial para el rendimiento de la base de datos. Al seguir estas mejores prácticas y revisar periódicamente los índices, podemos mantener un rendimiento óptimo a medida que la aplicación crece.
