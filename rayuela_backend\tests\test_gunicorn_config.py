import os
import sys
import pytest
from unittest.mock import patch

# Add the project root to the path to import gunicorn_conf
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))


class TestGunicornConfig:
    """Tests for Gunicorn configuration security features."""

    def test_production_access_log_format_excludes_sensitive_data(self):
        """Test that production access log format excludes query parameters and sensitive headers."""
        with patch.dict(os.environ, {"ENV": "production"}):
            # Re-import to get the updated configuration
            import importlib
            import gunicorn_conf
            importlib.reload(gunicorn_conf)
            
            # Production format should exclude query parameters (%(q)s) and sensitive headers
            expected_format = '%(h)s %(l)s %(u)s %(t)s "%(m)s %(U)s %(H)s" %(s)s %(b)s %(L)s'
            assert gunicorn_conf.access_log_format == expected_format
            
            # Verify it doesn't contain sensitive format specifiers
            assert "%(q)s" not in gunicorn_conf.access_log_format  # Query parameters
            assert "%(f)s" not in gunicorn_conf.access_log_format  # Referer header
            assert "%(a)s" not in gunicorn_conf.access_log_format  # User agent
            assert "%(r)s" not in gunicorn_conf.access_log_format  # Full request line

    def test_development_access_log_format_includes_debug_info(self):
        """Test that development access log format includes debugging information."""
        with patch.dict(os.environ, {"ENV": "development"}):
            # Re-import to get the updated configuration
            import importlib
            import gunicorn_conf
            importlib.reload(gunicorn_conf)
            
            # Development format should include more details for debugging
            expected_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s'
            assert gunicorn_conf.access_log_format == expected_format
            
            # Verify it contains debugging format specifiers
            assert "%(r)s" in gunicorn_conf.access_log_format  # Full request line
            assert "%(f)s" in gunicorn_conf.access_log_format  # Referer header
            assert "%(a)s" in gunicorn_conf.access_log_format  # User agent

    def test_default_environment_uses_development_format(self):
        """Test that when ENV is not set, development format is used."""
        with patch.dict(os.environ, {}, clear=True):
            # Remove ENV if it exists
            if "ENV" in os.environ:
                del os.environ["ENV"]
            
            # Re-import to get the updated configuration
            import importlib
            import gunicorn_conf
            importlib.reload(gunicorn_conf)
            
            # Should default to development format
            expected_format = '%(h)s %(l)s %(u)s %(t)s "%(r)s" %(s)s %(b)s "%(f)s" "%(a)s" %(L)s'
            assert gunicorn_conf.access_log_format == expected_format

    def test_on_starting_hook_logs_environment(self):
        """Test that the on_starting hook logs the environment and security status."""
        import gunicorn_conf
        
        # Mock server object
        class MockServer:
            class MockLog:
                def __init__(self):
                    self.info_calls = []
                
                def info(self, message):
                    self.info_calls.append(message)
            
            def __init__(self):
                self.log = self.MockLog()
        
        mock_server = MockServer()
        
        # Test production environment
        with patch.dict(os.environ, {"ENV": "production"}):
            gunicorn_conf.on_starting(mock_server)
            
            assert any("production environment" in call for call in mock_server.log.info_calls)
            assert any("sensitive data sanitization enabled" in call for call in mock_server.log.info_calls)
        
        # Test development environment
        mock_server = MockServer()
        with patch.dict(os.environ, {"ENV": "development"}):
            gunicorn_conf.on_starting(mock_server)
            
            assert any("development environment" in call for call in mock_server.log.info_calls)
            # Should not mention sanitization in development
            assert not any("sensitive data sanitization enabled" in call for call in mock_server.log.info_calls)

    def test_access_log_format_components(self):
        """Test that access log format components are correctly defined."""
        with patch.dict(os.environ, {"ENV": "production"}):
            import importlib
            import gunicorn_conf
            importlib.reload(gunicorn_conf)
            
            format_str = gunicorn_conf.access_log_format
            
            # Verify essential components are present
            essential_components = [
                "%(h)s",  # Remote host
                "%(t)s",  # Time
                "%(m)s",  # Method
                "%(U)s",  # URL path (without query)
                "%(s)s",  # Status code
                "%(L)s"   # Request time
            ]
            
            for component in essential_components:
                assert component in format_str, f"Missing essential component: {component}"

    def test_security_documentation_present(self):
        """Test that security documentation is present in the configuration file."""
        import gunicorn_conf
        
        # Read the source file to check for security documentation
        config_file_path = os.path.join(os.path.dirname(__file__), '..', 'gunicorn_conf.py')
        with open(config_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Verify security documentation is present
        assert "Security Note:" in content
        assert "sensitive data" in content.lower()
        assert "query parameters" in content.lower()
        assert "API keys" in content.lower()
