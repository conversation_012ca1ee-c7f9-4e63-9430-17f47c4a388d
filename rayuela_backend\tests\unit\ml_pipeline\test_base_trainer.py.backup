"""
Tests for the BaseTrainer class.
"""
import pytest
import numpy as np
import pandas as pd
from typing import Dict, Any, List
from unittest.mock import MagicMock, patch
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from src.ml_pipeline.base_trainer import BaseTrainer
from src.core.exceptions import TrainingError
from src.db.models import (
    Product,
    EndUser,
    Interaction,
    ModelMetadata
)


# Create a concrete implementation of BaseTrainer for testing
class ConcreteTrainer(BaseTrainer):
    """Concrete implementation of BaseTrainer for testing."""
    
    def prepare_data(self, data: pd.DataFrame) -> tuple:
        """Prepara los datos para el entrenamiento."""
        train_data = data.sample(frac=0.8, random_state=42)
        test_data = data.drop(train_data.index)
        return train_data, test_data
    
    def train_model(self, data: pd.DataFrame, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Entrena el modelo con los datos proporcionados."""
        train_data, test_data = self.prepare_data(data)
        
        # Simulate model training
        self.model = MagicMock()
        self.model.get_params.return_value = params or {"default": "params"}
        
        # Calculate metrics
        metrics = self._calculate_metrics(test_data)
        
        return {
            "model": self.model,
            "metrics": metrics
        }
    
    def _calculate_metrics(self, test_data: pd.DataFrame, **kwargs) -> Dict[str, float]:
        """Calcula métricas de rendimiento del modelo."""
        return {
            "accuracy": 0.85,
            "precision": 0.82,
            "recall": 0.79
        }
    
    def get_required_columns(self) -> List[str]:
        """Retorna las columnas requeridas para el entrenamiento."""
        return ["user_id", "item_id", "rating"]


class TestBaseTrainer:
    """Tests for the BaseTrainer class."""
    
    @pytest.fixture
    def trainer(self):
        """Create a ConcreteTrainer instance for testing."""
        return ConcreteTrainer(account_id=1)
    
    @pytest.fixture
    def sample_data(self):
        """Create sample data for testing."""
        return pd.DataFrame({
            "user_id": [1, 1, 2, 2, 3],
            "item_id": [101, 102, 101, 103, 102],
            "rating": [5.0, 3.0, 2.0, 4.0, 1.0]
        })
    
    @pytest.fixture
    def invalid_data(self):
        """Create invalid data for testing."""
        return pd.DataFrame({
            "user_id": [1, 1, 2],
            "item_id": [101, 102, 101]
            # Missing 'rating' column
        })
    
    @pytest.fixture
    def empty_data(self):
        """Create empty data for testing."""
        return pd.DataFrame()
    
    def test_validate_data_valid(self, trainer, sample_data):
        """Test validate_data with valid data."""
        result = trainer.validate_data(sample_data)
        assert result is True
    
    def test_validate_data_invalid_missing_columns(self, trainer, invalid_data):
        """Test validate_data with invalid data (missing columns)."""
        result = trainer.validate_data(invalid_data)
        assert result is False
    
    def test_validate_data_empty(self, trainer, empty_data):
        """Test validate_data with empty data."""
        result = trainer.validate_data(empty_data)
        assert result is False
    
    def test_validate_data_exception(self, trainer):
        """Test validate_data with exception."""
        # Pass None instead of DataFrame to trigger exception
        result = trainer.validate_data(None)
        assert result is False
    
    def test_get_model_parameters_no_model(self, trainer):
        """Test get_model_parameters with no model."""
        result = trainer.get_model_parameters()
        assert result == {}
    
    def test_get_model_parameters_with_model(self, trainer):
        """Test get_model_parameters with model."""
        # Set up mock model
        trainer.model = MagicMock()
        trainer.model.get_params.return_value = {"param1": "value1", "param2": "value2"}
        
        result = trainer.get_model_parameters()
        assert result == {"param1": "value1", "param2": "value2"}
        trainer.model.get_params.assert_called_once()
    
    def test_save_model_no_model(self, trainer):
        """Test save_model with no model."""
        with pytest.raises(ValueError) as excinfo:
            trainer.save_model("path/to/model")
        assert "No hay modelo para guardar" in str(excinfo.value)
    
    def test_save_model_with_model(self, trainer):
        """Test save_model with model."""
        # Set up mock model
        trainer.model = MagicMock()
        
        trainer.save_model("path/to/model")
        trainer.model.save.assert_called_once_with("path/to/model")
    
    def test_load_model(self, trainer):
        """Test load_model."""
        # Set up mock model
        trainer.model = MagicMock()
        
        trainer.load_model("path/to/model")
        trainer.model.load.assert_called_once_with("path/to/model")
    
    @pytest.mark.asyncio
    async def test_train_success(self, trainer, sample_data):
        """Test train method success case."""
        # Mock train_model to avoid actual implementation
        with patch.object(trainer, 'train_model') as mock_train_model:
            mock_train_model.return_value = {
                "model": "mock_model",
                "metrics": {"accuracy": 0.85}
            }
            
            result = await trainer.train(sample_data, {"param1": "value1"})
            
            assert result == {"model": "mock_model", "metrics": {"accuracy": 0.85}}
            mock_train_model.assert_called_once_with(sample_data, {"param1": "value1"})
    
    @pytest.mark.asyncio
    async def test_train_invalid_data(self, trainer, invalid_data):
        """Test train method with invalid data."""
        with pytest.raises(ValueError) as excinfo:
            await trainer.train(invalid_data)
        
        assert "Datos inválidos para entrenamiento" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_train_exception_in_train_model(self, trainer, sample_data):
        """Test train method with exception in train_model."""
        # Mock train_model to raise exception
        with patch.object(trainer, 'train_model') as mock_train_model:
            mock_train_model.side_effect = Exception("Training error")
            
            with pytest.raises(Exception) as excinfo:
                await trainer.train(sample_data)
            
            assert "Training error" in str(excinfo.value)
            mock_train_model.assert_called_once_with(sample_data, None)
    
    def test_concrete_implementation(self, trainer, sample_data):
        """Test concrete implementation of BaseTrainer."""
        # Test prepare_data
        train_data, test_data = trainer.prepare_data(sample_data)
        assert len(train_data) == 4  # 80% of 5 = 4
        assert len(test_data) == 1   # 20% of 5 = 1
        
        # Test train_model
        result = trainer.train_model(sample_data, {"param1": "value1"})
        assert "model" in result
        assert "metrics" in result
        assert result["metrics"]["accuracy"] == 0.85
        
        # Test _calculate_metrics
        metrics = trainer._calculate_metrics(test_data)
        assert metrics["accuracy"] == 0.85
        assert metrics["precision"] == 0.82
        assert metrics["recall"] == 0.79
        
        # Test get_required_columns
        columns = trainer.get_required_columns()
        assert columns == ["user_id", "item_id", "rating"]


class MockTrainer(BaseTrainer):
    """Implementación mock del entrenador base para testing."""
    def __init__(self, db_session: AsyncSession, account_id: int):
        super().__init__(db_session, account_id)
        self.model = None
    
    def prepare_data(self, interactions: pd.DataFrame) -> tuple:
        """Preparar datos para entrenamiento."""
        # Simular preparación de datos
        return np.array([1, 2, 3]), np.array([1, 0, 1])
    
    def train_model(self, X: np.ndarray, y: np.ndarray) -> None:
        """Entrenar modelo."""
        # Simular entrenamiento
        self.model = "trained_model"
    
    def evaluate_model(self, X: np.ndarray, y: np.ndarray) -> dict:
        """Evaluar modelo."""
        # Simular evaluación
        return {
            "accuracy": 0.85,
            "precision": 0.82,
            "recall": 0.88,
            "f1": 0.85
        }
    
    def save_model(self, path: str) -> None:
        """Guardar modelo."""
        # Simular guardado
        pass
    
    def load_model(self, path: str) -> None:
        """Cargar modelo."""
        # Simular carga
        self.model = "loaded_model"

@pytest.fixture
async def trainer(db_session: AsyncSession):
    """Fixture para crear una instancia de MockTrainer."""
    return MockTrainer(db_session, account_id=1)

@pytest.fixture
async def sample_data(db_session: AsyncSession):
    """Fixture para crear datos de muestra."""
    # Crear usuarios
    users = []
    for i in range(10):
        user = EndUser(
            account_id=1,
            email=f"user{i}@example.com",
            name=f"User {i}"
        )
        db_session.add(user)
        users.append(user)
    
    # Crear productos
    products = []
    for i in range(20):
        product = Product(
            account_id=1,
            name=f"Product {i}",
            description=f"Description {i}",
            price=10.0
        )
        db_session.add(product)
        products.append(product)
    
    await db_session.commit()
    
    # Crear interacciones
    interactions = []
    for user in users:
        for product in products[:5]:  # Cada usuario interactúa con 5 productos
            interaction = Interaction(
                account_id=1,
                end_user_id=user.id,
                product_id=product.id,
                interaction_type="view",
                timestamp=datetime.utcnow() - timedelta(days=np.random.randint(0, 30))
            )
            db_session.add(interaction)
            interactions.append(interaction)
    
    await db_session.commit()
    return users, products, interactions

async def test_validate_training_data(trainer: MockTrainer, sample_data):
    """Test para validar datos de entrenamiento."""
    users, products, interactions = sample_data
    
    # Verificar que los datos son válidos
    is_valid = await trainer.validate_training_data()
    assert is_valid is True
    
    # Eliminar todas las interacciones
    for interaction in interactions:
        await trainer.db_session.delete(interaction)
    await trainer.db_session.commit()
    
    # Verificar que los datos ya no son válidos
    is_valid = await trainer.validate_training_data()
    assert is_valid is False

async def test_prepare_training_data(trainer: MockTrainer, sample_data):
    """Test para preparar datos de entrenamiento."""
    users, products, interactions = sample_data
    
    # Preparar datos
    X, y = await trainer.prepare_training_data()
    
    # Verificar que los datos tienen el formato correcto
    assert isinstance(X, np.ndarray)
    assert isinstance(y, np.ndarray)
    assert len(X) == len(y)

async def test_train(trainer: MockTrainer, sample_data):
    """Test para entrenar el modelo."""
    users, products, interactions = sample_data
    
    # Entrenar modelo
    metrics = await trainer.train()
    
    # Verificar que el modelo se entrenó correctamente
    assert trainer.model == "trained_model"
    assert isinstance(metrics, dict)
    assert "accuracy" in metrics
    assert "precision" in metrics
    assert "recall" in metrics
    assert "f1" in metrics

async def test_train_with_insufficient_data(trainer: MockTrainer, db_session: AsyncSession):
    """Test para entrenar con datos insuficientes."""
    # Intentar entrenar sin datos
    with pytest.raises(TrainingError):
        await trainer.train()

async def test_save_and_load_model(trainer: MockTrainer, sample_data, tmp_path):
    """Test para guardar y cargar el modelo."""
    users, products, interactions = sample_data
    
    # Entrenar modelo
    await trainer.train()
    
    # Guardar modelo
    model_path = tmp_path / "model.pkl"
    await trainer.save_model(str(model_path))
    
    # Crear nuevo trainer
    new_trainer = MockTrainer(trainer.db_session, trainer.account_id)
    
    # Cargar modelo
    await new_trainer.load_model(str(model_path))
    
    # Verificar que el modelo se cargó correctamente
    assert new_trainer.model == "loaded_model"

async def test_evaluate(trainer: MockTrainer, sample_data):
    """Test para evaluar el modelo."""
    users, products, interactions = sample_data
    
    # Entrenar modelo
    await trainer.train()
    
    # Evaluar modelo
    metrics = await trainer.evaluate()
    
    # Verificar métricas
    assert isinstance(metrics, dict)
    assert "accuracy" in metrics
    assert "precision" in metrics
    assert "recall" in metrics
    assert "f1" in metrics
    assert all(0 <= v <= 1 for v in metrics.values())

async def test_get_training_history(trainer: MockTrainer, sample_data):
    """Test para obtener historial de entrenamiento."""
    users, products, interactions = sample_data
    
    # Entrenar modelo varias veces
    for _ in range(3):
        await trainer.train()
    
    # Obtener historial
    history = await trainer.get_training_history()
    
    # Verificar historial
    assert len(history) == 3
    for entry in history:
        assert isinstance(entry, ModelMetadata)
        assert entry.account_id == trainer.account_id
        assert entry.model_type == "mock"
        assert entry.status == "completed"
        assert isinstance(entry.training_date, datetime)
        assert isinstance(entry.metrics, dict)

async def test_cleanup_old_models(trainer: MockTrainer, sample_data):
    """Test para limpiar modelos antiguos."""
    users, products, interactions = sample_data
    
    # Entrenar modelo varias veces
    for _ in range(5):
        await trainer.train()
    
    # Limpiar modelos antiguos
    await trainer.cleanup_old_models(keep_last=2)
    
    # Verificar que solo quedan los últimos 2 modelos
    history = await trainer.get_training_history()
    assert len(history) == 2
