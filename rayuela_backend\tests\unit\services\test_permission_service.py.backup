import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from src.services.permission_service import PermissionService
from src.core.exceptions import PermissionDeniedError
from src.db.models import (
    SystemUser,
    Role,
    Permission,
    SystemUserRole,
    role_permissions
)

@pytest.fixture
async def permission_service(db_session: AsyncSession):
    """Fixture para crear una instancia de PermissionService."""
    return PermissionService(db_session, account_id=1)

@pytest.fixture
async def test_roles(db_session: AsyncSession):
    """Fixture para crear roles de prueba."""
    roles = {
        "admin": Role(
            name="admin",
            description="Administrador del sistema"
        ),
        "manager": Role(
            name="manager",
            description="Gerente de cuenta"
        ),
        "user": Role(
            name="user",
            description="Usuario regular"
        )
    }
    
    for role in roles.values():
        db_session.add(role)
    await db_session.commit()
    return roles

@pytest.fixture
async def test_permissions(db_session: AsyncSession):
    """Fixture para crear permisos de prueba."""
    permissions = {
        "manage_users": Permission(
            name="manage_users",
            description="Gestionar usuarios"
        ),
        "manage_products": Permission(
            name="manage_products",
            description="Gestionar productos"
        ),
        "view_analytics": Permission(
            name="view_analytics",
            description="Ver analíticas"
        ),
        "train_models": Permission(
            name="train_models",
            description="Entrenar modelos"
        )
    }
    
    for permission in permissions.values():
        db_session.add(permission)
    await db_session.commit()
    return permissions

@pytest.fixture
async def test_users(db_session: AsyncSession, test_roles):
    """Fixture para crear usuarios de prueba."""
    users = {
        "admin": SystemUser(
            email="<EMAIL>",
            name="Admin User",
            is_active=True
        ),
        "manager": SystemUser(
            email="<EMAIL>",
            name="Manager User",
            is_active=True
        ),
        "user": SystemUser(
            email="<EMAIL>",
            name="Regular User",
            is_active=True
        )
    }
    
    for user in users.values():
        db_session.add(user)
    await db_session.commit()
    
    # Asignar roles a usuarios
    user_roles = [
        SystemUserRole(user_id=users["admin"].id, role_id=test_roles["admin"].id),
        SystemUserRole(user_id=users["manager"].id, role_id=test_roles["manager"].id),
        SystemUserRole(user_id=users["user"].id, role_id=test_roles["user"].id)
    ]
    
    for user_role in user_roles:
        db_session.add(user_role)
    await db_session.commit()
    
    return users

async def test_assign_role_to_user(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_users,
    test_roles
):
    """Test para asignar un rol a un usuario."""
    # Asignar rol de manager al usuario regular
    await permission_service.assign_role_to_user(
        user_id=test_users["user"].id,
        role_id=test_roles["manager"].id
    )
    
    # Verificar que el usuario tiene el rol asignado
    user_roles = await permission_service.get_user_roles(test_users["user"].id)
    assert len(user_roles) == 2
    assert any(role.id == test_roles["manager"].id for role in user_roles)

async def test_remove_role_from_user(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_users,
    test_roles
):
    """Test para remover un rol de un usuario."""
    # Remover rol de user del usuario regular
    await permission_service.remove_role_from_user(
        user_id=test_users["user"].id,
        role_id=test_roles["user"].id
    )
    
    # Verificar que el usuario ya no tiene el rol
    user_roles = await permission_service.get_user_roles(test_users["user"].id)
    assert len(user_roles) == 0

async def test_assign_permission_to_role(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_roles,
    test_permissions
):
    """Test para asignar un permiso a un rol."""
    # Asignar permiso de manage_products al rol de manager
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["manage_products"].id
    )
    
    # Verificar que el rol tiene el permiso asignado
    role_permissions = await permission_service.get_role_permissions(test_roles["manager"].id)
    assert len(role_permissions) == 1
    assert role_permissions[0].id == test_permissions["manage_products"].id

async def test_remove_permission_from_role(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_roles,
    test_permissions
):
    """Test para remover un permiso de un rol."""
    # Asignar permiso primero
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["manage_products"].id
    )
    
    # Remover el permiso
    await permission_service.remove_permission_from_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["manage_products"].id
    )
    
    # Verificar que el rol ya no tiene el permiso
    role_permissions = await permission_service.get_role_permissions(test_roles["manager"].id)
    assert len(role_permissions) == 0

async def test_check_user_permission(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_users,
    test_roles,
    test_permissions
):
    """Test para verificar si un usuario tiene un permiso específico."""
    # Asignar permiso de manage_products al rol de manager
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["manage_products"].id
    )
    
    # Verificar que el manager tiene el permiso
    has_permission = await permission_service.check_user_permission(
        user_id=test_users["manager"].id,
        permission_name="manage_products"
    )
    assert has_permission is True
    
    # Verificar que el usuario regular no tiene el permiso
    has_permission = await permission_service.check_user_permission(
        user_id=test_users["user"].id,
        permission_name="manage_products"
    )
    assert has_permission is False

async def test_validate_user_permission(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_users,
    test_roles,
    test_permissions
):
    """Test para validar si un usuario tiene un permiso específico."""
    # Asignar permiso de manage_products al rol de manager
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["manage_products"].id
    )
    
    # Verificar que el manager puede ejecutar la acción
    await permission_service.validate_user_permission(
        user_id=test_users["manager"].id,
        permission_name="manage_products"
    )
    
    # Verificar que el usuario regular no puede ejecutar la acción
    with pytest.raises(PermissionDeniedError):
        await permission_service.validate_user_permission(
            user_id=test_users["user"].id,
            permission_name="manage_products"
        )

async def test_get_user_permissions(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_users,
    test_roles,
    test_permissions
):
    """Test para obtener todos los permisos de un usuario."""
    # Asignar permisos al rol de manager
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["manage_products"].id
    )
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["view_analytics"].id
    )
    
    # Obtener permisos del manager
    permissions = await permission_service.get_user_permissions(test_users["manager"].id)
    assert len(permissions) == 2
    assert any(p.name == "manage_products" for p in permissions)
    assert any(p.name == "view_analytics" for p in permissions)

async def test_get_role_permissions(
    permission_service: PermissionService,
    db_session: AsyncSession,
    test_roles,
    test_permissions
):
    """Test para obtener todos los permisos de un rol."""
    # Asignar permisos al rol de manager
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["manage_products"].id
    )
    await permission_service.assign_permission_to_role(
        role_id=test_roles["manager"].id,
        permission_id=test_permissions["view_analytics"].id
    )
    
    # Obtener permisos del rol manager
    permissions = await permission_service.get_role_permissions(test_roles["manager"].id)
    assert len(permissions) == 2
    assert any(p.name == "manage_products" for p in permissions)
    assert any(p.name == "view_analytics" for p in permissions) 