"""
Decoradores para la gestión de caché.
"""
from functools import wraps
from fastapi import Request, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from src.db.session import get_db
from src.services.cache_service import CacheService
from src.utils.base_logger import log_info, log_error


def invalidate_cache_after(account_id_param: str = None, user_id_param: str = None):
    """
    Decorador para invalidar la caché después de que un endpoint se ejecute exitosamente.
    
    Args:
        account_id_param: Nombre del parámetro que contiene el account_id (opcional)
        user_id_param: Nombre del parámetro que contiene el user_id (opcional)
        
    Returns:
        Decorador que invalida la caché después de la ejecución exitosa del endpoint
    """
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, request: Request = None, db: AsyncSession = Depends(get_db), **kwargs):
            # Ejecutar la función original
            result = await func(*args, request=request, db=db, **kwargs)
            
            # Solo invalidar la caché si la función se ejecutó exitosamente
            try:
                # Crear servicio de caché
                cache_service = CacheService()
                
                # Obtener account_id y user_id de los parámetros si se especificaron
                account_id = kwargs.get(account_id_param) if account_id_param else None
                user_id = kwargs.get(user_id_param) if user_id_param else None
                
                # Si tenemos account_id y user_id, invalidar la caché específica
                if account_id and user_id:
                    await cache_service.invalidate_user_cache(account_id, user_id)
                    log_info(f"Caché invalidada por decorador para user_id={user_id}, account_id={account_id}")
                # Si solo tenemos account_id, invalidar toda la caché de la cuenta
                elif account_id:
                    await cache_service.invalidate_account_cache(account_id)
                    log_info(f"Caché de cuenta invalidada por decorador para account_id={account_id}")
                # Si no tenemos ninguno, intentar invalidar desde la solicitud
                elif request:
                    await cache_service.invalidate_from_request(request, db)
                    log_info("Caché invalidada por decorador desde la solicitud")
                else:
                    log_error("No se pudo invalidar la caché: faltan parámetros necesarios")
            except Exception as e:
                # No interrumpir el flujo principal si hay un error
                log_error(f"Error invalidando caché en decorador: {str(e)}")
            
            # Devolver el resultado original
            return result
        
        return wrapper
    
    return decorator
