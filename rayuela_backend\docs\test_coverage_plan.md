# Plan de Mejora de Cobertura de Pruebas

Este documento describe el plan para aumentar la cobertura de pruebas en el proyecto, con un enfoque en la capa de servicios, caminos condicionales complejos, manejo de errores y componentes del pipeline de ML.

## Objetivos

1. Aumentar la cobertura general de pruebas al 80%
2. Lograr una cobertura del 90% en la capa de servicios
3. <PERSON>eg<PERSON>r que todos los caminos condicionales complejos estén cubiertos
4. Mejorar la cobertura de pruebas en el pipeline de ML al 85%
5. Implementar pruebas exhaustivas para el manejo de errores

## Áreas Prioritarias

### 1. Capa de Servicios

La capa de servicios contiene la lógica de negocio principal y debe tener una alta cobertura de pruebas:

| Servicio | Archivos | Prioridad | Estado |
|----------|----------|-----------|--------|
| Account Service | `src/services/account_service.py` | Alta | Pendiente |
| Analytics Service | `src/services/analytics_service.py` | Media | Pendiente |
| Cache Service | `src/services/cache_service.py` | Media | Pendiente |
| Data Ingestion Service | `src/services/data_ingestion_service.py` | Alta | Pendiente |
| Interaction Service | `src/services/interaction_service.py` | Alta | Pendiente |
| Limit Service | `src/services/limit_service.py` | Media | Pendiente |
| Permission Service | `src/services/permission_service.py` | Alta | Pendiente |
| Subscription Service | `src/services/subscription_service.py` | Alta | Pendiente |

### 2. Pipeline de ML

Los componentes del pipeline de ML son críticos para la funcionalidad principal:

| Componente | Archivos | Prioridad | Estado |
|------------|----------|-----------|--------|
| Base Trainer | `src/ml_pipeline/base_trainer.py` | Alta | Pendiente |
| Collaborative Trainer | `src/ml_pipeline/collaborative_trainer.py` | Alta | Pendiente |
| Content Trainer | `src/ml_pipeline/content_trainer.py` | Alta | Pendiente |
| Evaluation | `src/ml_pipeline/evaluation.py` | Media | Pendiente |
| Metrics Tracker | `src/ml_pipeline/metrics_tracker.py` | Media | Pendiente |
| Model Artifact Manager | `src/ml_pipeline/model_artifact_manager.py` | Alta | Pendiente |

### 3. Caminos Condicionales Complejos

Identificar y probar caminos condicionales complejos en los endpoints y servicios:

- Lógica de autorización y permisos
- Validación de límites y cuotas
- Procesamiento de datos en lote
- Manejo de transacciones
- Lógica de caché

### 4. Manejo de Errores

Asegurar que todos los caminos de manejo de errores estén cubiertos:

- Excepciones HTTP
- Errores de base de datos
- Errores de validación
- Errores en el pipeline de ML
- Errores de integración con servicios externos

## Estrategia de Implementación

### Fase 1: Configuración y Análisis

1. ✅ Configurar herramientas de cobertura (pytest-cov)
2. ✅ Crear script para ejecutar pruebas con cobertura
3. Ejecutar pruebas existentes con cobertura para establecer línea base
4. Identificar áreas con baja cobertura

### Fase 2: Implementación de Pruebas Unitarias

1. Crear pruebas unitarias para la capa de servicios
2. Implementar pruebas para caminos condicionales complejos
3. Añadir pruebas para el manejo de errores
4. Desarrollar pruebas para el pipeline de ML

### Fase 3: Implementación de Pruebas de Integración

1. Crear pruebas de integración para flujos críticos
2. Implementar pruebas para interacciones entre servicios
3. Desarrollar pruebas para el pipeline de ML completo

### Fase 4: Monitoreo y Mantenimiento

1. Configurar CI/CD para ejecutar pruebas con cobertura
2. Establecer umbrales mínimos de cobertura
3. Implementar revisiones de cobertura en el proceso de PR

## Plantillas de Pruebas

### Plantilla para Pruebas de Servicios

```python
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from src.services.example_service import ExampleService

class TestExampleService:
    @pytest_asyncio.fixture
    async def service(self):
        # Configurar mocks y dependencias
        mock_db = AsyncMock()
        mock_repo = AsyncMock()
        
        # Crear instancia del servicio
        service = ExampleService(db=mock_db)
        service._repo = mock_repo
        
        return service, mock_db, mock_repo
    
    @pytest.mark.asyncio
    async def test_method_success_case(self, service):
        # Desempaquetar fixtures
        service, mock_db, mock_repo = service
        
        # Configurar comportamiento de mocks
        mock_repo.get_by_id.return_value = {"id": 1, "name": "Test"}
        
        # Ejecutar método a probar
        result = await service.get_item(1)
        
        # Verificar resultado
        assert result["id"] == 1
        assert result["name"] == "Test"
        
        # Verificar que se llamaron los métodos esperados
        mock_repo.get_by_id.assert_called_once_with(1)
    
    @pytest.mark.asyncio
    async def test_method_error_case(self, service):
        # Desempaquetar fixtures
        service, mock_db, mock_repo = service
        
        # Configurar comportamiento de mocks para simular error
        mock_repo.get_by_id.side_effect = Exception("Database error")
        
        # Ejecutar método a probar y verificar que lanza la excepción esperada
        with pytest.raises(Exception) as excinfo:
            await service.get_item(1)
        
        # Verificar mensaje de error
        assert "Database error" in str(excinfo.value)
```

### Plantilla para Pruebas del Pipeline de ML

```python
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
import numpy as np
import pandas as pd
from src.ml_pipeline.example_trainer import ExampleTrainer

class TestExampleTrainer:
    @pytest.fixture
    def sample_data(self):
        # Crear datos de muestra para pruebas
        return pd.DataFrame({
            "user_id": [1, 1, 2, 2, 3],
            "item_id": [101, 102, 101, 103, 102],
            "rating": [5.0, 3.0, 2.0, 4.0, 1.0]
        })
    
    @pytest.fixture
    def trainer(self, sample_data):
        # Configurar mocks y dependencias
        mock_db = AsyncMock()
        
        # Crear instancia del trainer
        trainer = ExampleTrainer()
        trainer.data = sample_data
        
        return trainer, mock_db
    
    @pytest.mark.asyncio
    async def test_train_model(self, trainer, sample_data):
        # Desempaquetar fixtures
        trainer, mock_db = trainer
        
        # Ejecutar método a probar
        model = await trainer.train(factors=10, iterations=5)
        
        # Verificar que el modelo se entrenó correctamente
        assert model is not None
        assert hasattr(model, "predict")
        
        # Verificar predicciones
        pred = model.predict(1, 103)
        assert isinstance(pred, float)
    
    @pytest.mark.asyncio
    async def test_evaluate_model(self, trainer, sample_data):
        # Desempaquetar fixtures
        trainer, mock_db = trainer
        
        # Entrenar modelo
        model = await trainer.train(factors=10, iterations=5)
        
        # Ejecutar método a probar
        metrics = trainer.evaluate(model, sample_data)
        
        # Verificar métricas
        assert "rmse" in metrics
        assert "mae" in metrics
        assert 0 <= metrics["rmse"] <= 5.0
        assert 0 <= metrics["mae"] <= 5.0
```

## Próximos Pasos

1. Ejecutar pruebas existentes con cobertura para establecer línea base
2. Implementar pruebas para los servicios de mayor prioridad
3. Desarrollar pruebas para los componentes críticos del pipeline de ML
4. Revisar y mejorar la cobertura de caminos condicionales complejos
5. Implementar pruebas para el manejo de errores
