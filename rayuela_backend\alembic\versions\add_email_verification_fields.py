"""add_email_verification_fields

Revision ID: add_email_verification_fields
Revises: f2d345c271dd
Create Date: 2023-11-15 12:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_email_verification_fields'
down_revision: Union[str, None] = 'f2d345c271dd'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Add email verification fields to system_users table
    op.add_column('system_users', sa.Column('email_verified', sa.<PERSON>(), nullable=True, server_default='false'))
    op.add_column('system_users', sa.Column('verification_token', sa.String(length=255), nullable=True))
    op.add_column('system_users', sa.Column('verification_token_expires_at', sa.DateTime(timezone=True), nullable=True))


def downgrade() -> None:
    # Remove email verification fields from system_users table
    op.drop_column('system_users', 'email_verified')
    op.drop_column('system_users', 'verification_token')
    op.drop_column('system_users', 'verification_token_expires_at')
