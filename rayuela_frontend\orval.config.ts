import { defineConfig } from 'orval';
import path from 'path';

const backendUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

export default defineConfig({
  rayuelaApi: {
    output: {
      mode: 'split',
      target: 'src/lib/generated',
      schemas: 'src/lib/generated/schemas',
      client: 'axios',
      prettier: true,
      override: {
        mutator: {
          path: 'src/lib/generated/api-client.ts',
          name: 'customInstance',
        },
        operations: {
          // Personalizar nombres de operaciones si es necesario
        },
        mock: false, // Desactivar generación de mocks
      },
    },
    input: {
      target: path.resolve(process.cwd(), 'src/lib/openapi/openapi.json'),
    },
    hooks: {
      afterAllFilesWrite: 'prettier --write src/lib/generated',
    },
  },
}); 