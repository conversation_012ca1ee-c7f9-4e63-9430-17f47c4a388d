"""add_global_unique_email_constraint

Revision ID: 1d65dfd484b3
Revises: af19ff6c9c1a
Create Date: 2025-04-21 03:35:56.592821

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1d65dfd484b3'
down_revision: Union[str, None] = 'af19ff6c9c1a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to add global unique email constraint."""
    # For partitioned tables in PostgreSQL, unique constraints must include all partitioning columns
    # Since we can't create a truly global unique constraint on email alone, we'll need to handle this at the application level

    # We'll keep the existing index but make it unique to enforce uniqueness within each account
    op.drop_index('idx_system_user_email', table_name='system_users')
    op.create_index('idx_system_user_email', 'system_users', ['account_id', 'email'], unique=True)

    # We'll also add a comment to the table to indicate that email should be globally unique
    op.execute("COMMENT ON TABLE system_users IS 'Emails should be globally unique across all accounts. This is enforced at the application level.'")


def downgrade() -> None:
    """Downgrade schema to revert global unique email constraint."""
    # Remove the comment from the table
    op.execute("COMMENT ON TABLE system_users IS NULL")

    # Drop the unique index and recreate it as non-unique
    op.drop_index('idx_system_user_email', table_name='system_users')
    op.create_index('idx_system_user_email', 'system_users', ['account_id', 'email'], unique=False)
