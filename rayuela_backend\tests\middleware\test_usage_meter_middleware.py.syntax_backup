"""
Tests for the UsageMeterMiddleware.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import FastAP<PERSON>, Request, Response, HTTPException
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.middleware.usage_meter_middleware import UsageMeterMiddleware
from src.services.usage_meter_service import UsageMeterService
from src.core.exceptions import RateLimitExceededError, LimitExceededError
from src.db.models import Account, Subscription
from src.db.enums import SubscriptionPlan


@pytest.fixture
def mock_db():
    """Mock database session."""
    db = AsyncMock()
    db.commit = AsyncMock()
    db.rollback = AsyncMock()
    db.execute = AsyncMock()
    return db


@pytest.fixture
def mock_redis():
    """Mock Redis connection."""
    redis = AsyncMock()
    redis.incr = AsyncMock(return_value=1)
    redis.get = AsyncMock(return_value=None)
    redis.set = AsyncMock()
    redis.expire = AsyncMock()
    return redis


@pytest.fixture
def mock_usage_meter_service():
    """Mock UsageMeterService."""
    service = AsyncMock(spec=UsageMeterService)
    service.check_rate_limit = AsyncMock(return_value=True)
    service.check_monthly_limit = AsyncMock(return_value=True)
    service.increment_api_call = AsyncMock(return_value=1)
    service.get_redis = AsyncMock(return_value=AsyncMock())
    return service


@pytest.fixture
def app():
    """Create a FastAPI app with the middleware."""
    app = FastAPI()
    app.add_middleware(UsageMeterMiddleware)

    @app.get("/api/v1/test")
@pytest.mark.asyncio
    async def test_endpoint():
        return {"message": "Test endpoint"}

    @app.get("/api/v1/auth/token")
    async def auth_endpoint():
        return {"message": "Auth endpoint"}

    @app.get("/docs")
    async def docs_endpoint():
        return {"message": "Docs endpoint"}

    return app


@pytest.fixture
def client(app):
    """Create a test client."""
    return AsyncClient(app=app, base_url="http://test")


@pytest.mark.asyncio
async def test_middleware_excluded_path(client):
    """Test that excluded paths are not processed by the middleware."""
    # Arrange & Act
    response = client.get("/docs")

    # Assert
    assert response.status_code == 200
    assert response.json() == {"message": "Docs endpoint"}


@pytest.mark.asyncio
async def test_middleware_auth_path(client):
    """Test that auth paths are not processed by the middleware."""
    # Arrange & Act
    response = client.get("/api/v1/auth/token")

    # Assert
    assert response.status_code == 200
    assert response.json() == {"message": "Auth endpoint"}


@pytest.mark.asyncio
async def test_middleware_missing_api_key(client):
    """Test that protected paths require an API key."""
    # Arrange & Act
    response = client.get("/api/v1/test")

    # Assert
    assert response.status_code == 401
    assert "API Key is required" in response.json()["detail"]


@pytest.mark.asyncio
async def test_middleware_with_api_key(client, mock_db):
    """Test that protected paths with a valid API key are processed."""
    # Arrange
    api_key = "test_api_key"
    
    # Mock account and subscription
    mock_account = MagicMock(spec=Account)
    mock_account.account_id = 123
    mock_account.is_active = True
    
    mock_subscription = MagicMock(spec=Subscription)
    mock_subscription.plan_type = SubscriptionPlan.STARTER
    mock_subscription.api_calls_limit = 1000
    mock_subscription.is_active = True
    mock_subscription.monthly_api_calls_used = 500
    
    # Mock database queries
    mock_account_result = MagicMock()
    mock_account_result.scalars.return_value.first.return_value = mock_account
    
    mock_subscription_result = MagicMock()
    mock_subscription_result.scalars.return_value.first.return_value = mock_subscription
    
    mock_db.execute.side_effect = [mock_account_result, mock_subscription_result]
    
    # Mock UsageMeterService
    with patch("src.middleware.usage_meter_middleware.get_db", return_value=AsyncMock(__anext__=AsyncMock(return_value=mock_db))), \
         patch("src.middleware.usage_meter_middleware.UsageMeterService") as mock_service_class:
        
        mock_service = AsyncMock()
        mock_service.check_rate_limit = AsyncMock(return_value=True)
        mock_service.check_monthly_limit = AsyncMock(return_value=True)
        mock_service.increment_api_call = AsyncMock(return_value=501)
        mock_service.get_redis = AsyncMock(return_value=AsyncMock())
        mock_service._get_monthly_counter_key = MagicMock(return_value="monthly_counter:123:2023-01")
        
        mock_service_class.return_value = mock_service
        
        # Act
        response = client.get("/api/v1/test", headers={"X-API-Key": api_key})
        
        # Assert
        assert response.status_code == 200
        assert response.json() == {"message": "Test endpoint"}
        
        # Verify service calls
        mock_service.check_rate_limit.assert_called_once()
        mock_service.check_monthly_limit.assert_called_once()
        mock_service.increment_api_call.assert_called_once()
        
        # Verify headers
        assert "X-RateLimit-Limit" in response.headers
        assert "X-RateLimit-Remaining" in response.headers
        assert "X-RateLimit-Reset" in response.headers
        assert response.headers["X-RateLimit-Limit"] == "1000"


@pytest.mark.asyncio
async def test_middleware_rate_limit_exceeded(client, mock_db):
    """Test that rate limit exceeded returns 429."""
    # Arrange
    api_key = "test_api_key"
    
    # Mock account and subscription
    mock_account = MagicMock(spec=Account)
    mock_account.account_id = 123
    mock_account.is_active = True
    
    mock_subscription = MagicMock(spec=Subscription)
    mock_subscription.plan_type = SubscriptionPlan.STARTER
    mock_subscription.api_calls_limit = 1000
    mock_subscription.is_active = True
    
    # Mock database queries
    mock_account_result = MagicMock()
    mock_account_result.scalars.return_value.first.return_value = mock_account
    
    mock_subscription_result = MagicMock()
    mock_subscription_result.scalars.return_value.first.return_value = mock_subscription
    
    mock_db.execute.side_effect = [mock_account_result, mock_subscription_result]
    
    # Mock UsageMeterService
    with patch("src.middleware.usage_meter_middleware.get_db", return_value=AsyncMock(__anext__=AsyncMock(return_value=mock_db))), \
         patch("src.middleware.usage_meter_middleware.UsageMeterService") as mock_service_class:
        
        mock_service = AsyncMock()
        mock_service.check_rate_limit = AsyncMock(side_effect=RateLimitExceededError("Rate limit exceeded"))
        
        mock_service_class.return_value = mock_service
        
        # Act
        response = client.get("/api/v1/test", headers={"X-API-Key": api_key})
        
        # Assert
        assert response.status_code == 429
        assert "Rate limit exceeded" in response.json()["detail"]
        assert "Retry-After" in response.headers
