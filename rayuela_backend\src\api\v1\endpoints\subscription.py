from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.deps import get_db, get_subscription_service, get_current_account
from src.core.rate_limit_config import RateLimitConfig
from src.db.repositories import AccountRepository
from src.db.schemas.subscription import (
    SubscriptionUpdate,
    Subscription as SubscriptionResponse,
)
from src.services import SubscriptionService
from src.utils.base_logger import log_info, log_error
from src.db.models import Account

router = APIRouter()


@router.put("/{account_id}/subscription", response_model=SubscriptionResponse)
async def update_subscription(
    account_id: int,
    subscription_update: SubscriptionUpdate,
    current_account: Account = Depends(get_current_account),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    db: AsyncSession = Depends(get_db),
):
    """
    Actualiza la suscripción de una cuenta y su configuración de rate limits.
    """
    try:
        # Verificar que el account_id en la ruta coincide con el account_id del token/API Key
        if current_account.account_id != account_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to update this subscription",
            )

        # Iniciar transacción explícita para garantizar atomicidad
        async with db.begin():
            # Obtener la suscripción actual
            subscription = await subscription_service.subscription_repo.get_by_account(account_id)

            if not subscription:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Subscription not found",
                )

            # Actualizar suscripción en la base de datos usando el servicio
            subscription = await subscription_service.update_subscription(
                subscription, subscription_update.model_dump(exclude_unset=True)
            )

            # Obtener la API key de la cuenta
            account_repo = AccountRepository(db)
            account = await account_repo.get_by_id(account_id)

            if not account:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Account not found",
                )

        # Actualizar configuración de rate limits en Redis (fuera de la transacción de BD)
        # Usamos el api_key_prefix para identificar la API key en los logs
        api_key_prefix = getattr(account, "api_key_prefix", "unknown")

        # Crear una instancia de RateLimitConfig (no necesita parámetros)
        rate_limit_config = RateLimitConfig()

        # Invalidar la configuración de rate limits para esta cuenta
        await rate_limit_config.invalidate_config(account_id=account_id)

        log_info(f"Rate limits invalidated for account {account_id} with API key prefix {api_key_prefix}")

        log_info(f"Subscription and rate limits updated for account {account_id}")
        return subscription

    except HTTPException:
        # El rollback se realiza automáticamente en caso de excepción
        raise
    except Exception as e:
        # El rollback se realiza automáticamente en caso de excepción
        log_error(f"Error updating subscription for account {account_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not update subscription",
        )
