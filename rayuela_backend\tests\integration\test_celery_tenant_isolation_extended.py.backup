"""
Extended integration tests for tenant isolation in Celery tasks.
"""
import pytest
import pytest_asyncio
from unittest.mock import patch

from src.db.models import Account
from src.utils.tenant_context import run_with_tenant_context
from src.workers.celery_tasks import train_model_for_job


@pytest_asyncio.fixture
async def test_accounts(db_session):
    """Create test accounts for tenant isolation tests."""
    # Create two test accounts with minimal required fields
    account1 = Account()
    account1.account_id = 1001  # Use a specific ID for testing

    account2 = Account()
    account2.account_id = 1002  # Use a specific ID for testing

    db_session.add_all([account1, account2])
    await db_session.commit()

    # Refresh to get the IDs
    await db_session.refresh(account1)
    await db_session.refresh(account2)

    return [account1, account2]


@pytest.fixture
def mock_async_to_sync():
    """Mock for async_to_sync function."""
    with patch("src.workers.celery_tasks.async_to_sync") as mock:
        yield mock


class TestCeleryTenantIsolationExtended:
    """Extended tests for tenant isolation in Celery tasks."""

    @pytest.mark.asyncio
    async def test_cleanup_old_interactions_isolation(self, test_accounts, mock_async_to_sync):
        """Test that cleanup_old_interactions respects tenant isolation."""
        account1, account2 = test_accounts

        # Configure the mock to return a success result
        mock_result = {"success": True, "deleted_count": 5, "expected_count": 5}
        mock_async_to_sync.return_value = mock_result

        # Mock the Celery task to avoid the missing self parameter issue
        with patch("src.workers.celery_tasks.cleanup_old_interactions") as mock_task:
            mock_task.return_value = mock_result

            # Call the function
            mock_task(
                days_to_keep=30,
                account_id=account1.account_id,
                batch_size=100
            )

        # Verify that run_with_tenant_context was called with the correct account_id
        mock_async_to_sync.assert_called_with(
            run_with_tenant_context
        )

        # Get the kwargs passed to run_with_tenant_context
        _, kwargs = mock_async_to_sync.call_args

        # Verify that the account_id was passed correctly
        assert kwargs.get('account_id') == account1.account_id

        # Reset the mock
        mock_async_to_sync.reset_mock()

        # Mock the Celery task for account 2
        with patch("src.workers.celery_tasks.cleanup_old_interactions") as mock_task:
            mock_task.return_value = mock_result

            # Call the function
            mock_task(
                days_to_keep=30,
                account_id=account2.account_id,
                batch_size=100
            )

        # Verify that run_with_tenant_context was called with the correct account_id
        mock_async_to_sync.assert_called_with(
            run_with_tenant_context
        )

        # Get the kwargs passed to run_with_tenant_context
        _, kwargs = mock_async_to_sync.call_args

        # Verify that the account_id was passed correctly
        assert kwargs.get('account_id') == account2.account_id

    @pytest.mark.asyncio
    async def test_cleanup_old_audit_logs_isolation(self, test_accounts, mock_async_to_sync):
        """Test that cleanup_old_audit_logs respects tenant isolation."""
        account1, account2 = test_accounts

        # Configure the mock to return a success result
        mock_result = {"success": True, "deleted_count": 5, "expected_count": 5}
        mock_async_to_sync.return_value = mock_result

        # Mock the Celery task for account 1
        with patch("src.workers.celery_tasks.cleanup_old_audit_logs") as mock_task:
            mock_task.return_value = mock_result

            # Call the function
            mock_task(
                days_to_keep=90,
                account_id=account1.account_id,
                batch_size=100
            )

        # Verify that run_with_tenant_context was called with the correct account_id
        mock_async_to_sync.assert_called_with(
            run_with_tenant_context
        )

        # Get the kwargs passed to run_with_tenant_context
        _, kwargs = mock_async_to_sync.call_args

        # Verify that the account_id was passed correctly
        assert kwargs.get('account_id') == account1.account_id

        # Reset the mock
        mock_async_to_sync.reset_mock()

        # Mock the Celery task for account 2
        with patch("src.workers.celery_tasks.cleanup_old_audit_logs") as mock_task:
            mock_task.return_value = mock_result

            # Call the function
            mock_task(
                days_to_keep=90,
                account_id=account2.account_id,
                batch_size=100
            )

        # Verify that run_with_tenant_context was called with the correct account_id
        mock_async_to_sync.assert_called_with(
            run_with_tenant_context
        )

        # Get the kwargs passed to run_with_tenant_context
        _, kwargs = mock_async_to_sync.call_args

        # Verify that the account_id was passed correctly
        assert kwargs.get('account_id') == account2.account_id

    @pytest.mark.asyncio
    async def test_train_model_isolation(self, test_accounts, mock_async_to_sync):
        """Test that train_model respects tenant isolation."""
        account1, account2 = test_accounts

        # Configure the mock to return a success result
        mock_result = {"success": True, "model_id": 1}
        mock_async_to_sync.return_value = mock_result

        # Call the Celery task for account 1
        training_data = {
            "model_type": "collaborative",
            "parameters": {
                "factors": 10,
                "iterations": 5
            }
        }

        # Mock the Celery task for account 1
        with patch("src.workers.celery_tasks.train_model") as mock_task:
            mock_task.return_value = mock_result

            # Call the function
            mock_task(
                account_id=account1.account_id,
                data=training_data
            )

        # Verify that run_with_tenant_context was called with the correct account_id
        mock_async_to_sync.assert_called_with(
            run_with_tenant_context
        )

        # Get the kwargs passed to run_with_tenant_context
        _, kwargs = mock_async_to_sync.call_args

        # Verify that the account_id was passed correctly
        assert kwargs.get('account_id') == account1.account_id

        # Reset the mock
        mock_async_to_sync.reset_mock()

        # Mock the Celery task for account 2
        with patch("src.workers.celery_tasks.train_model") as mock_task:
            mock_task.return_value = mock_result

            # Call the function
            mock_task(
                account_id=account2.account_id,
                data=training_data
            )

        # Verify that run_with_tenant_context was called with the correct account_id
        mock_async_to_sync.assert_called_with(
            run_with_tenant_context
        )

        # Get the kwargs passed to run_with_tenant_context
        _, kwargs = mock_async_to_sync.call_args

        # Verify that the account_id was passed correctly
        assert kwargs.get('account_id') == account2.account_id

    @pytest.mark.asyncio
    async def test_train_model_for_job_isolation(self, test_accounts, mock_async_to_sync):
        """Test that train_model_for_job respects tenant isolation."""
        account1, account2 = test_accounts

        # Configure the mock to return a success result
        mock_result = {"success": True, "model_id": 1}
        mock_async_to_sync.return_value = mock_result

        # Call the Celery task for account 1
        # In a real test, we would create a TrainingJob first
        job_id1 = 1

        # Mock the train_model_for_job function
        with patch("src.workers.celery_tasks.train_model_for_job") as mock_train:
            mock_train.return_value = mock_result

            # Call the function
            train_model_for_job(
                account_id=account1.account_id,
                job_id=job_id1
            )

            # Verify it was called with the correct parameters
            mock_train.assert_called_with(
                account_id=account1.account_id,
                job_id=job_id1
            )

        # Reset the mock
        mock_async_to_sync.reset_mock()

        # Call the Celery task for account 2
        job_id2 = 2

        # Mock the train_model_for_job function
        with patch("src.workers.celery_tasks.train_model_for_job") as mock_train:
            mock_train.return_value = mock_result

            # Call the function
            train_model_for_job(
                account_id=account2.account_id,
                job_id=job_id2
            )

            # Verify it was called with the correct parameters
            mock_train.assert_called_with(
                account_id=account2.account_id,
                job_id=job_id2
            )