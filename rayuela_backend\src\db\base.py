from typing import Any
from sqlalchemy.orm import as_declarative, declared_attr
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.ext.asyncio import AsyncAttrs


@as_declarative()
class Base:
    """Clase base para todos los modelos"""

    id: Any
    __name__: str

    # Generate __tablename__ automatically
    @declared_attr
    def __tablename__(cls) -> str:
        return cls.__name__.lower()

    # Configuración para permitir redefinición de tablas
    __table_args__ = {"extend_existing": True}
