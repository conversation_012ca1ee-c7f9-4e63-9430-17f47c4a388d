from typing import Dict, Any, List, Tuple, Optional, Set, Union
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession
from src.db import schemas
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.utils.base_logger import log_info, log_error, log_warning


class ModelLoader:
    """
    Clase responsable de cargar modelos de recomendación.
    """
    
    def __init__(self, artifact_manager: ModelArtifactManager):
        self.artifact_manager = artifact_manager
        
    async def load_model_artifacts(
        self,
        db: AsyncSession,
        account_id: int,
        model_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        Carga los artefactos del modelo para una cuenta.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_type: Tipo de modelo ("collaborative", "content")
            
        Returns:
            Artefactos del modelo o None si no existen
        """
        artifacts = await self.artifact_manager.get_artifacts(
            db=db, account_id=account_id, model_type=model_type
        )
        
        if not artifacts:
            log_warning(f"No se encontraron artefactos para account_id={account_id}, model_type={model_type}")
            return None
            
        return artifacts


class CollaborativeRecommender:
    """
    Clase para generar recomendaciones basadas en filtrado colaborativo.
    """
    
    async def get_recommendations(
        self,
        collab_artifacts: Dict[str, Any],
        user_id: int,
        n_recommendations: int,
        excluded_items: Optional[Set[int]] = None
    ) -> List[Dict[str, Any]]:
        """
        Obtiene recomendaciones colaborativas para un usuario.
        
        Args:
            collab_artifacts: Artefactos del modelo colaborativo
            user_id: ID del usuario
            n_recommendations: Número de recomendaciones a generar
            excluded_items: Conjunto de ítems a excluir
            
        Returns:
            Lista de recomendaciones
        """
        if not collab_artifacts or "model" not in collab_artifacts:
            log_warning(f"Artefactos del modelo colaborativo no válidos para user_id={user_id}")
            return []
            
        model = collab_artifacts["model"]
        
        # Verificar si el usuario existe en el modelo
        if not hasattr(model, "user_factors") or user_id not in model.user_factors:
            log_warning(f"Usuario {user_id} no encontrado en el modelo colaborativo")
            return []
            
        user_factors = model.user_factors[user_id]
        
        # Calcular scores para todos los ítems
        item_scores = []
        
        for item_id, item_factors in model.item_factors.items():
            # Excluir ítems si es necesario
            if excluded_items and item_id in excluded_items:
                continue
                
            # Calcular score como producto escalar de factores
            score = np.dot(user_factors, item_factors)
            
            # Obtener metadatos del ítem si están disponibles
            item_metadata = collab_artifacts.get("item_metadata", {}).get(str(item_id), {})
            
            item_scores.append({
                "item_id": item_id,
                "score": float(score),
                "model_type": "collaborative",
                **item_metadata
            })
            
        # Ordenar por score descendente y limitar al número solicitado
        item_scores.sort(key=lambda x: x["score"], reverse=True)
        
        log_info(f"Generadas {len(item_scores[:n_recommendations])} recomendaciones colaborativas para user_id={user_id}")
        
        return item_scores[:n_recommendations]


class ContentRecommender:
    """
    Clase para generar recomendaciones basadas en contenido.
    """
    
    async def get_recommendations(
        self,
        content_artifacts: Dict[str, Any],
        user_id: int,
        n_recommendations: int,
        excluded_items: Optional[Set[int]] = None
    ) -> List[Dict[str, Any]]:
        """
        Obtiene recomendaciones basadas en contenido para un usuario.
        
        Args:
            content_artifacts: Artefactos del modelo basado en contenido
            user_id: ID del usuario
            n_recommendations: Número de recomendaciones a generar
            excluded_items: Conjunto de ítems a excluir
            
        Returns:
            Lista de recomendaciones
        """
        if not content_artifacts or "model" not in content_artifacts:
            log_warning(f"Artefactos del modelo de contenido no válidos para user_id={user_id}")
            return []
            
        model = content_artifacts["model"]
        
        # Verificar si el usuario existe en el modelo
        if not hasattr(model, "user_profiles") or user_id not in model.user_profiles:
            log_warning(f"Usuario {user_id} no encontrado en el modelo de contenido")
            return []
            
        user_profile = model.user_profiles[user_id]
        
        # Calcular scores para todos los ítems
        item_scores = []
        
        for item_id, item_vector in model.item_vectors.items():
            # Excluir ítems si es necesario
            if excluded_items and item_id in excluded_items:
                continue
                
            # Calcular score como producto escalar de factores
            score = np.dot(user_profile, item_vector)
            
            # Obtener metadatos del ítem si están disponibles
            item_metadata = content_artifacts.get("item_metadata", {}).get(str(item_id), {})
            
            item_scores.append({
                "item_id": item_id,
                "score": float(score),
                "model_type": "content",
                "is_semantic_model": content_artifacts.get("is_semantic_model", False),
                **item_metadata
            })
            
        # Ordenar por score descendente y limitar al número solicitado
        item_scores.sort(key=lambda x: x["score"], reverse=True)
        
        log_info(f"Generadas {len(item_scores[:n_recommendations])} recomendaciones basadas en contenido para user_id={user_id}")
        
        return item_scores[:n_recommendations]


class HybridRecommender:
    """
    Clase para combinar recomendaciones colaborativas y basadas en contenido.
    """
    
    def combine_recommendations(
        self,
        collab_recs: List[Dict[str, Any]],
        content_recs: List[Dict[str, Any]],
        n_recommendations: int,
        user_id: Optional[int] = None,
        user_history: Optional[Dict[int, float]] = None,
        is_new_user: bool = False,
        strategy: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Combina recomendaciones colaborativas y basadas en contenido.
        
        Args:
            collab_recs: Recomendaciones colaborativas
            content_recs: Recomendaciones basadas en contenido
            n_recommendations: Número de recomendaciones a generar
            user_id: ID del usuario
            user_history: Historial de interacciones del usuario
            is_new_user: Si el usuario es nuevo
            strategy: Estrategia de combinación
            
        Returns:
            Lista combinada de recomendaciones
        """
        # Si no hay recomendaciones de uno de los tipos, devolver el otro
        if not collab_recs:
            return content_recs[:n_recommendations]
        if not content_recs:
            return collab_recs[:n_recommendations]
            
        # Calcular pesos dinámicos según la estrategia
        collab_weight, content_weight = self._calculate_weights(
            collab_recs, content_recs, user_history, is_new_user, strategy
        )
        
        # Normalizar pesos
        total_weight = collab_weight + content_weight
        if total_weight > 0:
            collab_norm = collab_weight / total_weight
            content_norm = content_weight / total_weight
        else:
            collab_norm = content_norm = 0.5
            
        # Convertir listas a diccionarios para acceso rápido
        collab_dict = {r["item_id"]: r for r in collab_recs}
        content_dict = {r["item_id"]: r for r in content_recs}
        
        # Obtener todos los item_ids únicos
        all_item_ids = set(collab_dict.keys()).union(set(content_dict.keys()))
        
        # Calcular scores combinados
        combined_recs = []
        
        for item_id in all_item_ids:
            # Obtener scores de cada modelo (0 si no existe)
            collab_score = collab_dict.get(item_id, {}).get("score", 0.0)
            content_score = content_dict.get(item_id, {}).get("score", 0.0)
            
            # Calcular score combinado
            combined_score = (collab_score * collab_norm) + (content_score * content_norm)
            
            # Combinar metadatos
            item_data = {}
            
            if item_id in collab_dict:
                item_data.update(collab_dict[item_id])
            if item_id in content_dict:
                # No sobrescribir item_id y model_type
                temp_data = content_dict[item_id].copy()
                temp_data.pop("item_id", None)
                temp_data.pop("model_type", None)
                temp_data.pop("score", None)
                item_data.update(temp_data)
                
            # Actualizar con datos híbridos
            item_data.update({
                "item_id": item_id,
                "score": combined_score,
                "model_type": "hybrid",
                "collab_score": collab_score,
                "content_score": content_score,
                "collab_weight": collab_norm,
                "content_weight": content_norm
            })
            
            combined_recs.append(item_data)
            
        # Ordenar por score combinado descendente
        combined_recs.sort(key=lambda x: x["score"], reverse=True)
        
        log_info(f"Generadas {len(combined_recs[:n_recommendations])} recomendaciones híbridas")
        
        return combined_recs[:n_recommendations]
        
    def _calculate_weights(
        self,
        collab_recs: List[Dict[str, Any]],
        content_recs: List[Dict[str, Any]],
        user_history: Optional[Dict[int, float]] = None,
        is_new_user: bool = False,
        strategy: Optional[str] = None
    ) -> Tuple[float, float]:
        """
        Calcula los pesos para combinar los modelos según la estrategia.
        
        Args:
            collab_recs: Recomendaciones colaborativas
            content_recs: Recomendaciones basadas en contenido
            user_history: Historial de interacciones del usuario
            is_new_user: Si el usuario es nuevo
            strategy: Estrategia de combinación
            
        Returns:
            Tuple[float, float]: Pesos para modelos colaborativo y de contenido
        """
        # Estrategias predefinidas
        strategy_weights = {
            "balanced": (0.5, 0.5),  # Balance equitativo
            "maximize_engagement": (0.7, 0.3),  # Priorizar colaborativo que suele tener mejor CTR
            "promote_new_arrivals": (0.3, 0.7),  # Priorizar contenido para promocionar nuevos ítems
            "discover_diverse": (0.2, 0.8)  # Máxima diversidad
        }
        
        # Ajustes por tipo de usuario
        base_weights = strategy_weights.get(strategy, (0.5, 0.5))
        
        collab_weight, content_weight = base_weights
        
        # Para usuarios nuevos, priorizar contenido pues no tienen suficiente historial
        if is_new_user:
            collab_weight *= 0.5
            content_weight *= 1.5
            
        # Ajustar por historial: usuarios con mucho historial suelen responder mejor al colaborativo
        if user_history and len(user_history) > 10:
            collab_weight *= min(2.0, 1.0 + (len(user_history) / 100))
            
        # Ajustar por calidad de las recomendaciones
        if collab_recs and content_recs:
            avg_collab_score = sum(r.get("score", 0) for r in collab_recs) / len(collab_recs)
            avg_content_score = sum(r.get("score", 0) for r in content_recs) / len(content_recs)
            
            # Si hay una diferencia significativa, ajustar los pesos
            if avg_collab_score > avg_content_score * 1.5:
                collab_weight *= 1.2
            elif avg_content_score > avg_collab_score * 1.5:
                content_weight *= 1.2
                
        return collab_weight, content_weight


class ServingEngine:
    """
    Motor principal para obtener recomendaciones personalizadas.
    """
    
    def __init__(self, model_artifact_manager: ModelArtifactManager):
        self.model_loader = ModelLoader(model_artifact_manager)
        self.collab_recommender = CollaborativeRecommender()
        self.content_recommender = ContentRecommender()
        self.hybrid_recommender = HybridRecommender()
        
    async def get_candidate_recommendations(
        self,
        db: AsyncSession,
        account_id: int,
        user_id: int,
        n_recommendations: int = 10,
        recommendation_type: str = "hybrid",
        excluded_items: Optional[Set[int]] = None,
        strategy: Optional[str] = None,
        user_history: Optional[Dict[int, float]] = None,
        is_new_user: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Obtiene recomendaciones candidatas para un usuario.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            user_id: ID del usuario
            n_recommendations: Número de recomendaciones
            recommendation_type: Tipo de recomendación ("user", "item", "hybrid")
            excluded_items: Ítems a excluir
            strategy: Estrategia de recomendación
            user_history: Historial de interacciones del usuario
            is_new_user: Si el usuario es nuevo
            
        Returns:
            Lista de recomendaciones candidatas
        """
        log_info(f"Obteniendo recomendaciones {recommendation_type} para user_id={user_id}")
        
        # Inicializar
        collab_recs = []
        content_recs = []
        
        # Cargar artefactos según el tipo de recomendación
        if recommendation_type in ["user", "hybrid"]:
            collab_artifacts = await self.model_loader.load_model_artifacts(
                db=db, account_id=account_id, model_type="collaborative"
            )
            
            if collab_artifacts:
                collab_recs = await self.collab_recommender.get_recommendations(
                    collab_artifacts=collab_artifacts,
                    user_id=user_id,
                    n_recommendations=n_recommendations,
                    excluded_items=excluded_items
                )
                
        if recommendation_type in ["item", "hybrid"]:
            content_artifacts = await self.model_loader.load_model_artifacts(
                db=db, account_id=account_id, model_type="content"
            )
            
            if content_artifacts:
                content_recs = await self.content_recommender.get_recommendations(
                    content_artifacts=content_artifacts,
                    user_id=user_id,
                    n_recommendations=n_recommendations,
                    excluded_items=excluded_items
                )
                
        # Generar recomendaciones según el tipo
        if recommendation_type == "user":
            return collab_recs
        elif recommendation_type == "item":
            return content_recs
        else:  # hybrid
            return self.hybrid_recommender.combine_recommendations(
                collab_recs=collab_recs,
                content_recs=content_recs,
                n_recommendations=n_recommendations,
                user_id=user_id,
                user_history=user_history,
                is_new_user=is_new_user,
                strategy=strategy
            )