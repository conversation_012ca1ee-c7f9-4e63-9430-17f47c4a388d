# Configuración de Producción para Rayuela Backend

# Environment
ENV=production

# Base de datos PostgreSQL
DB_HOST=*************
DB_PORT=5432
DB_USER=rayuela_user
DB_NAME=rayuela_prod
# DB_PASSWORD se obtiene de Secret Manager

# Redis
REDIS_HOST=*************
REDIS_PORT=6379
# REDIS_PASSWORD se obtiene de Secret Manager

# Seguridad
# SECRET_KEY se obtiene de Secret Manager

# GCP Configuration
GCP_PROJECT_ID=your-project-id
GCP_REGION=us-central1

# Configuración de aplicación
DEBUG=False
ALLOWED_HOSTS=*
CORS_ALLOWED_ORIGINS=https://rayuela-frontend-*.run.app

# Logging
LOG_LEVEL=INFO

# Gunicorn Optimization Settings (optimized for startup costs)
GUNICORN_WORKERS=4
GUNICORN_WORKER_CONNECTIONS=200
GUNICORN_TIMEOUT=120

# Workers Celery
CELERY_BROKER_URL=redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/0
CELERY_RESULT_BACKEND=redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/0

# Base de datos URL (se construye automáticamente)
DATABASE_URL=postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}

# Health check
HEALTH_CHECK_PATH=/health 