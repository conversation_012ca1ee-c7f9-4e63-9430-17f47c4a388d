"""
Endpoints for subscription usage information.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any

from src.core.deps import (
    get_current_active_user,
    get_db,
    get_subscription_service,
    get_storage_tracker_service,
)
from src.db.models import SystemUser
from src.services import SubscriptionService
from src.services.storage_tracker_service import StorageTrackerService
from src.utils.base_logger import log_info, log_error

router = APIRouter()


@router.get("/usage", response_model=Dict[str, Any])
async def get_subscription_usage(
    current_user: SystemUser = Depends(get_current_active_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    storage_tracker: StorageTrackerService = Depends(get_storage_tracker_service),
    db: AsyncSession = Depends(get_db),
):
    """
    Get subscription usage information for the current account.
    
    Returns:
        Dict with subscription usage information including:
        - API calls used and limit
        - Storage used and limit
        - Available models
        - Reset date for API calls counter
    """
    try:
        # Get subscription
        subscription = await subscription_service._subscription_repo.get_by_account(
            current_user.account_id
        )
        
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active subscription found",
            )
            
        # Get storage usage
        storage_used = await storage_tracker.get_current_storage_usage()
        
        # Format the response
        usage_info = {
            "subscription": {
                "plan": subscription.plan_type,
                "is_active": subscription.is_active,
                "expires_at": subscription.expires_at,
            },
            "api_calls": {
                "used": subscription.monthly_api_calls_used,
                "limit": subscription.api_calls_limit,
                "percentage": round(
                    (subscription.monthly_api_calls_used / subscription.api_calls_limit) * 100
                    if subscription.api_calls_limit > 0
                    else 0,
                    2,
                ),
                "reset_date": subscription.last_reset_date,
            },
            "storage": {
                "used_bytes": storage_used,
                "used_mb": round(storage_used / (1024 * 1024), 2),
                "limit_bytes": subscription.storage_limit,
                "limit_mb": round(subscription.storage_limit / (1024 * 1024), 2),
                "percentage": round(
                    (storage_used / subscription.storage_limit) * 100
                    if subscription.storage_limit > 0
                    else 0,
                    2,
                ),
            },
            "features": {
                "available_models": subscription.available_models or [],
                "training_frequency": str(subscription.training_frequency) if subscription.training_frequency else "manual",
            },
        }
        
        log_info(f"Retrieved subscription usage for account {current_user.account_id}")
        return usage_info
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error retrieving subscription usage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving subscription usage",
        )
