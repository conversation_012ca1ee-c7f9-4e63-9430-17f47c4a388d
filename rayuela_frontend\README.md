# Rayuela Frontend - Panel de Administración

Este proyecto contiene el panel de administración para el sistema de recomendación Rayuela. Está construido con Next.js, React y Tailwind CSS.

## Características

- **Autenticación de usuarios:** Sistema completo de inicio de sesión y registro para administradores de cuentas
- **Gestión de productos:** Interfaz para cargar, editar y eliminar productos
- **Gestión de usuarios finales:** Visualización y administración de usuarios finales
- **Monitoreo de interacciones:** Visualización de interacciones entre usuarios y productos
- **Configuración de modelos:** Interfaz para entrenar y configurar modelos de recomendación
- **Análisis de rendimiento:** Paneles de métricas y análisis de recomendaciones
- **Gestión de suscripciones:** Visualización y administración del plan de suscripción

## Tecnologías Utilizadas

- **Next.js:** Framework de React para renderizado del lado del servidor
- **React:** Biblioteca para construir interfaces de usuario
- **Tailwind CSS:** Framework CSS utilitario para el diseño
- **SWR:** Para el manejo eficiente de solicitudes HTTP y caché
- **Chart.js:** Para visualización de datos y métricas
- **NextAuth.js:** Para autenticación de usuarios

## Inicio Rápido

### Requisitos previos

- Node.js 18 o superior
- npm o yarn
- API de Rayuela en funcionamiento (backend)

### Instalación

1. Clonar el repositorio:
   ```bash
   git clone https://github.com/tu-organizacion/rayuela.git
   cd rayuela/rayuela_frontend
   ```

2. Instalar dependencias:
   ```bash
   npm install
   # o
   yarn install
   ```

3. Configurar variables de entorno:
   Crea un archivo `.env.local` con:
   ```
   NEXT_PUBLIC_API_BASE_URL=http://localhost:8001
   ```

4. Iniciar servidor de desarrollo:
   ```bash
   npm run dev
   # o
   yarn dev
   ```

5. Abrir [http://localhost:3000](http://localhost:3000) en el navegador

## Estructura del Proyecto

```
rayuela_frontend/
├── docs/               # Documentación específica del frontend
├── public/             # Archivos estáticos
├── src/
│   ├── app/            # Rutas y páginas (Next.js App Router)
│   ├── components/     # Componentes reutilizables
│   ├── hooks/          # Custom hooks
│   ├── lib/            # Utilidades y configuración
│   └── styles/         # Estilos globales
├── .env.example        # Ejemplo de variables de entorno
└── README.md           # Este archivo
```

## Documentación adicional

Para más información sobre funcionalidades específicas, consulta:

- [Sincronización de Planes y Límites](docs/SYNC_PLANS.md): Información sobre cómo se sincroniza la información de planes entre backend y frontend.

## Desarrollo

### Convenciones de código

- Utilizamos ESLint y Prettier para mantener un estilo de código consistente
- Componentes funcionales con hooks
- Tipado con TypeScript para todos los componentes y funciones
- Naming: camelCase para variables y PascalCase para componentes

### Pruebas

```bash
npm run test
# o
yarn test
```

## Despliegue

La aplicación está configurada para desplegarse en Vercel:

```bash
npm run build
# o
yarn build
```

Para más detalles sobre el despliegue en producción, consulta la [documentación de CI/CD](../docs/ci_cd_pipeline.md).
