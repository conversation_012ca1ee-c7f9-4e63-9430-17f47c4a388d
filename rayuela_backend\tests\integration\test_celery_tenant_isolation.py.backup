"""
Integration tests for tenant isolation in Celery tasks.
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from datetime import datetime, timezone, timedelta
import uuid
import json

from src.db.models import (
    Account,
    Product,
    EndUser,
    Interaction,
    TrainingJob,
    BatchIngestionJob
)
from src.utils.tenant_context import run_with_tenant_context
from tests.integration.utils.mock_celery import MockCeleryTaskRegistry
from src.workers.celery_tasks import (
    _train_model_async,
    _train_model_for_job_async,
    _process_batch_data_async
)
from src.services.batch_data_storage_service import BatchDataStorageService


@pytest.fixture
def mock_storage_service(monkeypatch):
    """Mock for BatchDataStorageService."""
    class MockBatchDataStorageService:
        async def retrieve_batch_data(self, file_path):
            # Return test data based on the file path
            account_id = int(file_path.split("account_")[1].split("/")[0])
            return {
                "users": [
                    {"external_id": f"user_{account_id}_1", "name": f"User {account_id} 1"},
                    {"external_id": f"user_{account_id}_2", "name": f"User {account_id} 2"}
                ],
                "products": [
                    {"external_id": f"product_{account_id}_1", "name": f"Product {account_id} 1"},
                    {"external_id": f"product_{account_id}_2", "name": f"Product {account_id} 2"}
                ],
                "interactions": [
                    {"user_external_id": f"user_{account_id}_1", "product_external_id": f"product_{account_id}_1", "type": "view"},
                    {"user_external_id": f"user_{account_id}_2", "product_external_id": f"product_{account_id}_2", "type": "purchase"}
                ]
            }

        async def delete_batch_data(self, file_path):
            # Just return success
            return True

    # Create an instance of the mock
    mock_service = MockBatchDataStorageService()

    # Patch the BatchDataStorageService class
    monkeypatch.setattr("src.services.batch_data_storage_service.BatchDataStorageService", lambda: mock_service)

    return mock_service


@pytest_asyncio.fixture
async def test_accounts(db_session):
    """Create test accounts for tenant isolation tests."""
    # Create two test accounts
    account1 = Account(
        name="Test Account 1",
        email="<EMAIL>",
        password_hash="dummy_hash",
        api_key_hash="dummy_api_key_hash",
        api_key_prefix="test1",
        api_key_last_chars="123456"
    )
    account2 = Account(
        name="Test Account 2",
        email="<EMAIL>",
        password_hash="dummy_hash",
        api_key_hash="dummy_api_key_hash",
        api_key_prefix="test2",
        api_key_last_chars="654321"
    )

    db_session.add_all([account1, account2])
    await db_session.commit()

    # Refresh to get the IDs
    await db_session.refresh(account1)
    await db_session.refresh(account2)

    return [account1, account2]


class TestCeleryTenantIsolation:
    """Tests for tenant isolation in Celery tasks."""

    @pytest.mark.asyncio
    async def test_train_model_tenant_isolation(self, db_session, test_accounts):
        """Test that train_model respects tenant isolation."""
        account1, account2 = test_accounts

        # Create training data for account 1
        training_data = {
            "model_type": "collaborative",
            "parameters": {
                "factors": 10,
                "iterations": 5
            }
        }

        # Run the training function for account 1
        await run_with_tenant_context(
            account_id=account1.id,
            async_func=_train_model_async,
            db=db_session,
            data=training_data
        )

        # Query training jobs for account 1
        stmt = select(TrainingJob).where(TrainingJob.account_id == account1.id)
        result = await db_session.execute(stmt)
        account1_jobs = result.scalars().all()

        # Query training jobs for account 2
        stmt = select(TrainingJob).where(TrainingJob.account_id == account2.id)
        result = await db_session.execute(stmt)
        account2_jobs = result.scalars().all()

        # Verify that only account 1 has training jobs
        assert len(account1_jobs) == 1
        assert len(account2_jobs) == 0

        # Verify that the training job has the correct account_id
        assert account1_jobs[0].account_id == account1.id

    @pytest.mark.asyncio
    async def test_process_batch_data_tenant_isolation(self, db_session, test_accounts, mock_storage_service):
        """Test that process_batch_data respects tenant isolation."""
        account1, account2 = test_accounts

        # Create a batch job for account 1
        batch_job1 = BatchIngestionJob(
            account_id=account1.id,
            status="pending",
            created_at=datetime.now(timezone.utc),
            data_file_path=f"test/account_{account1.id}/job_1.json"
        )
        db_session.add(batch_job1)
        await db_session.commit()
        await db_session.refresh(batch_job1)

        # Process batch data for account 1
        await run_with_tenant_context(
            account_id=account1.id,
            async_func=_process_batch_data_async,
            db=db_session,
            job_id=batch_job1.id,
            file_path=batch_job1.data_file_path,
            storage_service=mock_storage_service
        )

        # Query products for account 1
        stmt = select(Product).where(Product.account_id == account1.id)
        result = await db_session.execute(stmt)
        account1_products = result.scalars().all()

        # Query products for account 2
        stmt = select(Product).where(Product.account_id == account2.id)
        result = await db_session.execute(stmt)
        account2_products = result.scalars().all()

        # Verify that only account 1 has products
        assert len(account1_products) > 0
        assert len(account2_products) == 0

        # Verify that the products have the correct account_id
        for product in account1_products:
            assert product.account_id == account1.id
            assert product.external_id.startswith(f"product_{account1.id}")

    @pytest.mark.asyncio
    async def test_cleanup_interactions_tenant_isolation(self, db_session, test_accounts):
        """Test that cleanup_old_interactions respects tenant isolation."""
        account1, account2 = test_accounts

        # Create interactions for both accounts
        # Account 1: 2 recent, 2 old
        # Account 2: 2 recent, 2 old
        now = datetime.now(timezone.utc)
        old_date = now - timedelta(days=200)  # Older than the default 180 days

        # Create users and products for both accounts
        user1_1 = EndUser(account_id=account1.id, external_id="user1_1", name="User 1-1")
        user1_2 = EndUser(account_id=account1.id, external_id="user1_2", name="User 1-2")
        product1_1 = Product(account_id=account1.id, external_id="product1_1", name="Product 1-1")
        product1_2 = Product(account_id=account1.id, external_id="product1_2", name="Product 1-2")

        user2_1 = EndUser(account_id=account2.id, external_id="user2_1", name="User 2-1")
        user2_2 = EndUser(account_id=account2.id, external_id="user2_2", name="User 2-2")
        product2_1 = Product(account_id=account2.id, external_id="product2_1", name="Product 2-1")
        product2_2 = Product(account_id=account2.id, external_id="product2_2", name="Product 2-2")

        db_session.add_all([user1_1, user1_2, product1_1, product1_2, user2_1, user2_2, product2_1, product2_2])
        await db_session.commit()

        # Create interactions
        interaction1_1_recent = Interaction(
            account_id=account1.id,
            user_id=user1_1.id,
            product_id=product1_1.id,
            type="view",
            created_at=now
        )
        interaction1_2_recent = Interaction(
            account_id=account1.id,
            user_id=user1_2.id,
            product_id=product1_2.id,
            type="purchase",
            created_at=now
        )
        interaction1_1_old = Interaction(
            account_id=account1.id,
            user_id=user1_1.id,
            product_id=product1_2.id,
            type="view",
            created_at=old_date
        )
        interaction1_2_old = Interaction(
            account_id=account1.id,
            user_id=user1_2.id,
            product_id=product1_1.id,
            type="purchase",
            created_at=old_date
        )

        interaction2_1_recent = Interaction(
            account_id=account2.id,
            user_id=user2_1.id,
            product_id=product2_1.id,
            type="view",
            created_at=now
        )
        interaction2_2_recent = Interaction(
            account_id=account2.id,
            user_id=user2_2.id,
            product_id=product2_2.id,
            type="purchase",
            created_at=now
        )
        interaction2_1_old = Interaction(
            account_id=account2.id,
            user_id=user2_1.id,
            product_id=product2_2.id,
            type="view",
            created_at=old_date
        )
        interaction2_2_old = Interaction(
            account_id=account2.id,
            user_id=user2_2.id,
            product_id=product2_1.id,
            type="purchase",
            created_at=old_date
        )

        db_session.add_all([
            interaction1_1_recent, interaction1_2_recent, interaction1_1_old, interaction1_2_old,
            interaction2_1_recent, interaction2_2_recent, interaction2_1_old, interaction2_2_old
        ])
        await db_session.commit()

        # Import the cleanup function
        from src.utils.maintenance import cleanup_old_interactions as cleanup_interactions_async

        # Run cleanup for account 1 only
        await run_with_tenant_context(
            account_id=account1.id,
            async_func=cleanup_interactions_async,
            days_to_keep=180,
            batch_size=10
        )

        # Query interactions for both accounts
        stmt = select(Interaction).where(Interaction.account_id == account1.id)
        result = await db_session.execute(stmt)
        account1_interactions = result.scalars().all()

        stmt = select(Interaction).where(Interaction.account_id == account2.id)
        result = await db_session.execute(stmt)
        account2_interactions = result.scalars().all()

        # Verify that account 1 has only recent interactions (old ones were deleted)
        assert len(account1_interactions) == 2
        for interaction in account1_interactions:
            assert (now - interaction.created_at).days < 180

        # Verify that account 2 still has all interactions (both recent and old)
        assert len(account2_interactions) == 4
