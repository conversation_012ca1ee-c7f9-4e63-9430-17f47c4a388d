# Pipeline CI/CD Mejorado para Producción

## 🚀 Resumen de Mejoras Implementadas

El pipeline de CI/CD ha sido significativamente mejorado para incluir:

- ✅ **Migraciones automáticas de Alembic** antes del deployment
- ✅ **Gestión segura de secretos** desde Google Secret Manager
- ✅ **Trazabilidad completa** usando COMMIT_SHA
- ✅ **Tests críticos bloqueantes** de multi-tenancy y atomicidad
- ✅ **Verificación de conectividad** de base de datos
- ✅ **Configuración de producción robusta**

## 🔧 Arquitectura del Pipeline

### Flujo Completo del Pipeline

```mermaid
graph TD
    A[0. Tests Básicos] --> B[1. Setup Environment]
    B --> C[2. Verify Secrets]
    C --> D[3. CRITICAL: Multi-Tenancy Tests]
    D --> E[4. Build Backend]
    D --> F[5. Build Frontend]
    E --> G[6. Push Backend]
    F --> H[7. Push Frontend]
    G --> I[8. CRITICAL: Run Migrations]
    I --> J[9. Deploy Backend]
    J --> K[10. Get Backend URL]
    K --> L[11. Deploy Frontend]
    L --> M[12. Health Checks]
    M --> N[13. Setup Monitoring]
```

### Pasos Críticos y Bloqueantes

1. **Tests de Multi-Tenancy (Paso 3)**: BLOQUEA si hay violaciones de seguridad
2. **Migraciones de Alembic (Paso 8)**: BLOQUEA si las migraciones fallan
3. **Health Checks (Paso 12)**: BLOQUEA si los servicios no responden

## 🔐 Gestión de Secretos

### Secretos Requeridos

El pipeline requiere los siguientes secretos en Google Secret Manager:

#### Base de Datos
- `POSTGRES_USER` - Usuario de PostgreSQL
- `POSTGRES_PASSWORD` - Contraseña de PostgreSQL
- `POSTGRES_SERVER` - Servidor de PostgreSQL
- `POSTGRES_PORT` - Puerto de PostgreSQL (usualmente 5432)
- `POSTGRES_DB` - Nombre de la base de datos

#### Redis
- `REDIS_HOST` - Host de Redis
- `REDIS_PORT` - Puerto de Redis (usualmente 6379)
- `REDIS_URL` - URL completa de Redis
- `REDIS_PASSWORD` - Contraseña de Redis

#### Aplicación
- `SECRET_KEY` - Clave secreta de la aplicación (32+ caracteres)
- `MERCADOPAGO_ACCESS_TOKEN` - Token de MercadoPago
- `GCS_BUCKET_NAME` - Bucket de Google Cloud Storage

### Configuración de Secretos

```bash
# Ejecutar script de configuración
./scripts/setup-production-secrets.sh

# O crear manualmente
echo "valor_secreto" | gcloud secrets create SECRET_NAME --data-file=-
```

## 🗄️ Migraciones Automáticas

### Características

- **Ejecución Automática**: Las migraciones se ejecutan automáticamente antes del deployment
- **Verificación de Conectividad**: Se verifica la conexión a la base de datos antes de migrar
- **Rollback Automático**: Si las migraciones fallan, el deployment se cancela
- **Trazabilidad**: Se registra el estado de las migraciones

### Proceso de Migración

```bash
# 1. Cargar configuración desde Secret Manager
export POSTGRES_USER=$(gcloud secrets versions access latest --secret="POSTGRES_USER")
# ... otros secretos

# 2. Verificar conectividad
python -c "import asyncpg; asyncio.run(test_connection())"

# 3. Ejecutar migraciones
alembic -c alembic.ini upgrade head

# 4. Verificar estado
alembic -c alembic.ini current
```

## 📊 Trazabilidad y Versionado

### Uso de COMMIT_SHA

- **Imágenes Docker**: Etiquetadas con `$COMMIT_SHA` para trazabilidad completa
- **Deployments**: Cada deployment está vinculado a un commit específico
- **Rollbacks**: Fácil identificación de versiones para rollback

### Información de Deployment

```bash
# Información almacenada en cada deployment
DEPLOYMENT_TIMESTAMP=2024-01-15 10:30:00 UTC
COMMIT_SHA=abc123def456
BUILD_ID=12345
BACKEND_URL=https://rayuela-backend-xyz.run.app
FRONTEND_URL=https://rayuela-frontend-xyz.run.app
```

## 🔒 Seguridad y Mejores Prácticas

### Secretos
- ✅ Todos los secretos almacenados en Secret Manager
- ✅ No hay credenciales hardcodeadas en el código
- ✅ Acceso controlado por IAM

### Service Accounts
- ✅ Service Accounts dedicadas para backend y frontend
- ✅ Principio de menor privilegio
- ✅ Separación de responsabilidades

### Red
- ✅ VPC Connector para acceso seguro a base de datos
- ✅ Comunicación encriptada
- ✅ Firewall rules apropiadas

## 🚀 Ejecución del Pipeline

### Prerequisitos

1. **Configurar Secretos**:
   ```bash
   ./scripts/setup-production-secrets.sh
   ```

2. **Verificar Permisos**:
   ```bash
   # Cloud Build Service Account debe tener:
   # - roles/secretmanager.secretAccessor
   # - roles/cloudsql.client
   # - roles/run.admin
   ```

3. **Configurar Base de Datos**:
   - Cloud SQL instance configurada
   - Firewall rules para Cloud Build
   - Usuario y base de datos creados

### Ejecutar Pipeline

```bash
# Deployment automático desde main/production branch
git push origin main

# O ejecutar manualmente
gcloud builds submit --config=cloudbuild.yaml
```

### Monitoreo

```bash
# Ver logs del build
gcloud builds log BUILD_ID

# Ver estado de servicios
gcloud run services list --region=us-central1

# Ver logs de aplicación
gcloud run services logs read rayuela-backend --region=us-central1
```

## 🔧 Configuración Avanzada

### Service Accounts Dedicadas

```bash
# Crear Service Account para backend
gcloud iam service-accounts create rayuela-backend-sa \
    --display-name="Rayuela Backend Service Account"

# Crear Service Account para frontend
gcloud iam service-accounts create rayuela-frontend-sa \
    --display-name="Rayuela Frontend Service Account"

# Otorgar permisos necesarios
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:rayuela-backend-sa@$PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/cloudsql.client"
```

### VPC Connector

```bash
# Crear VPC Connector para acceso seguro a base de datos
gcloud compute networks vpc-access connectors create rayuela-vpc-connector \
    --region=us-central1 \
    --subnet=default \
    --subnet-project=$PROJECT_ID \
    --min-instances=2 \
    --max-instances=3
```

## 📈 Métricas y Monitoreo

### Tiempo de Ejecución
- **Tests Críticos**: ~10-15 minutos
- **Builds**: ~5-10 minutos
- **Migraciones**: ~2-5 minutos
- **Deployments**: ~3-5 minutos
- **Total**: ~20-35 minutos

### Criterios de Éxito
- ✅ 100% de tests críticos pasan
- ✅ Migraciones se ejecutan sin errores
- ✅ Health checks pasan
- ✅ Servicios responden correctamente

## 🚨 Troubleshooting

### Errores Comunes

1. **Secretos Faltantes**:
   ```bash
   # Error: Secret not found
   # Solución: Ejecutar setup-production-secrets.sh
   ```

2. **Migraciones Fallan**:
   ```bash
   # Error: Database connection failed
   # Solución: Verificar configuración de Cloud SQL y firewall
   ```

3. **Tests de Multi-Tenancy Fallan**:
   ```bash
   # Error: Tenant isolation violation
   # Solución: Revisar código y políticas RLS
   ```

### Logs Útiles

```bash
# Ver logs de migraciones
gcloud builds log BUILD_ID --filter="steps.name:run-migrations"

# Ver logs de tests críticos
gcloud builds log BUILD_ID --filter="steps.name:integration-multi-tenancy-tests"

# Ver logs de deployment
gcloud builds log BUILD_ID --filter="steps.name:deploy-backend"
```

## 🎯 Próximos Pasos

1. **Configurar Alertas**: Implementar alertas para fallos de pipeline
2. **Métricas Avanzadas**: Dashboards de performance y disponibilidad
3. **Rollback Automático**: Implementar rollback automático en caso de fallos
4. **Environments**: Configurar staging environment
5. **Blue-Green Deployment**: Implementar deployment sin downtime
