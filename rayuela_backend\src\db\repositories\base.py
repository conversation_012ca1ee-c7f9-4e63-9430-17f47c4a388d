"""
Clases base para los repositorios.
"""

from sqlalchemy import and_, or_, text, func, select, insert
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.dialects.postgresql import insert as pg_insert
from typing import List, Optional, TypeVar, Generic, Type, Dict, Any
from datetime import datetime, timezone
from fastapi import HTTPException
from abc import ABC, abstractmethod
from src.db import models, schemas, enums
from src.db.enums import OrderDirection
from src.utils.base_logger import log_info, log_error, log_warning
from src.core.tenant_context import get_current_tenant_id


ModelType = TypeVar("ModelType", bound=models.Base)
CreateSchemaType = TypeVar("CreateSchemaType")
UpdateSchemaType = TypeVar("UpdateSchemaType")


class IRepository(ABC):
    """Interfaz base para repositorios"""

    @abstractmethod
    async def get_by_id(self, id: int) -> Optional[ModelType]:
        raise NotImplementedError

    @abstractmethod
    async def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        raise NotImplementedError

    @abstractmethod
    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        raise NotImplementedError

    @abstractmethod
    async def update(self, id: int, obj_in: UpdateSchemaType) -> ModelType:
        raise NotImplementedError

    @abstractmethod
    async def delete(self, id: int) -> bool:
        raise NotImplementedError


class BaseRepository(
    IRepository, Generic[ModelType, CreateSchemaType, UpdateSchemaType]
):
    def __init__(
        self,
        db: AsyncSession,
        model: Type[ModelType] = None,
    ):
        self.db = db
        self.model = model
        self.is_tenant_scoped = hasattr(self.model, "account_id")

    @property
    def account_id(self) -> Optional[int]:
        """
        Obtiene el account_id del contexto actual.
        Esto garantiza que cada solicitud/thread tenga su propio tenant aislado.
        """
        return get_current_tenant_id()

    async def _validate_tenant_access(self):
        """Validar que se tenga un account_id asignado si el modelo lo requiere."""
        if self.is_tenant_scoped and self.account_id is None:
            raise ValueError(
                f"Account ID is required for operations on {self.model.__name__}"
            )

    async def prepare_soft_delete(self, id: int) -> Optional[ModelType]:
        """Prepara el objeto para soft delete. Retorna el objeto o None."""
        obj = await self.get_by_id(id)
        if obj and hasattr(obj, "is_active"):
            obj.is_active = False
            if hasattr(obj, "deleted_at"):
                obj.deleted_at = datetime.now(timezone.utc)
            log_info(
                f"Object {self.model.__name__} ID {id} prepared for soft delete, awaiting commit."
            )
            return obj
        return None

    def _add_tenant_filter(self, query):
        """Agregar filtro de tenant a la consulta si aplica."""
        if self.is_tenant_scoped:
            if self.account_id is None:
                # Este caso indica un error grave si estamos accediendo a datos tenant-specific sin un account_id
                log_error(
                    f"ALERTA DE SEGURIDAD: Intento de acceso a {self.model.__name__} sin account_id"
                )
                raise ValueError(
                    f"Account ID es obligatorio para operaciones en tabla tenant-scoped {self.model.__name__}"
                )

            # Siempre aplicar el filtro si la tabla es tenant-scoped
            return query.filter(self.model.account_id == self.account_id)
        return query

    async def get_by_filters(
        self,
        filters: Dict[str, Any],
        skip: int = 0,
        limit: int = 100,
        order_by: Optional[str] = None,
        order_direction: OrderDirection = OrderDirection.DESC,
        include_deleted: bool = False,
    ) -> List[ModelType]:
        """Búsqueda genérica mejorada con más opciones de filtrado y ordenamiento."""
        query = select(self.model)

        # Aplicar filtros
        for field, value in filters.items():
            if not hasattr(self.model, field):
                continue

            if isinstance(value, list):
                query = query.filter(getattr(self.model, field).in_(value))
            else:
                query = query.filter(getattr(self.model, field) == value)

        # Filtrar por tenant
        query = self._add_tenant_filter(query)

        # Excluir elementos eliminados (soft delete) si aplica
        if not include_deleted:
            if hasattr(self.model, "is_active"):
                query = query.filter(self.model.is_active == True)
            if hasattr(self.model, "deleted_at"):
                query = query.filter(self.model.deleted_at == None)

        # Aplicar ordenamiento
        if order_by and hasattr(self.model, order_by):
            order_attr = getattr(self.model, order_by)
            if order_direction == OrderDirection.DESC:
                query = query.order_by(order_attr.desc())
            else:
                query = query.order_by(order_attr.asc())
        elif hasattr(self.model, "created_at"):
            # Ordenamiento por defecto
            if order_direction == OrderDirection.DESC:
                query = query.order_by(self.model.created_at.desc())
            else:
                query = query.order_by(self.model.created_at.asc())

        # Aplicar paginación
        query = query.offset(skip).limit(limit)

        try:
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching objects by filters", e)

    async def get_by_id(self, id: int) -> Optional[ModelType]:
        """Obtener un objeto por su ID."""
        try:
            await self._validate_tenant_access()

            # Si el modelo tiene account_id, usar clave compuesta o solo account_id según corresponda
            if self.is_tenant_scoped and self.account_id is not None:
                # Verificar si el modelo tiene una PK compuesta
                if len(self.model.__table__.primary_key.columns) > 1:
                    obj = await self.db.get(self.model, (self.account_id, id))
                else:
                    # Si la PK es solo account_id, usar el account_id directamente
                    obj = await self.db.get(self.model, self.account_id)
            else:
                obj = await self.db.get(self.model, id)

            return obj
        except SQLAlchemyError as e:
            await self._handle_error(f"fetching {self.model.__name__} by ID", e)

    async def get_all(self, skip: int = 0, limit: int = 100) -> List[ModelType]:
        """Obtener todos los objetos con paginación."""
        try:
            await self._validate_tenant_access()

            query = select(self.model)
            query = self._add_tenant_filter(query)

            # Excluir elementos eliminados (soft delete) si aplica
            if hasattr(self.model, "is_active"):
                query = query.filter(self.model.is_active == True)
            if hasattr(self.model, "deleted_at"):
                query = query.filter(self.model.deleted_at == None)

            query = query.offset(skip).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error(f"fetching all {self.model.__name__}", e)

    async def create(self, obj_in: CreateSchemaType) -> ModelType:
        """Crear un nuevo objeto."""
        try:
            await self._validate_tenant_access()

            # Convertir a diccionario si es un modelo Pydantic
            if hasattr(obj_in, "dict"):
                obj_data = obj_in.dict()
            else:
                obj_data = obj_in

            # Agregar account_id si el modelo lo requiere
            if self.is_tenant_scoped and self.account_id is not None:
                obj_data["account_id"] = self.account_id

            # Crear el objeto
            db_obj = self.model(**obj_data)
            self.db.add(db_obj)
            await self.db.flush()
            await self.db.refresh(db_obj)

            return db_obj
        except SQLAlchemyError as e:
            await self._handle_error(f"creating {self.model.__name__}", e)

    async def update(self, id: int, obj_in: UpdateSchemaType) -> Optional[ModelType]:
        """Actualizar un objeto existente."""
        try:
            await self._validate_tenant_access()

            # Obtener el objeto existente
            db_obj = await self.get_by_id(id)
            if not db_obj:
                return None

            # Convertir a diccionario si es un modelo Pydantic
            if hasattr(obj_in, "dict"):
                update_data = obj_in.dict(exclude_unset=True)
            else:
                update_data = obj_in

            # Actualizar los campos
            for field, value in update_data.items():
                if hasattr(db_obj, field):
                    setattr(db_obj, field, value)

            await self.db.flush()
            await self.db.refresh(db_obj)

            return db_obj
        except SQLAlchemyError as e:
            await self._handle_error(f"updating {self.model.__name__}", e)

    async def soft_delete(self, id: int) -> bool:
        """Eliminar un objeto mediante soft delete."""
        try:
            await self._validate_tenant_access()
            obj = await self.prepare_soft_delete(id)
            return obj is not None
        except SQLAlchemyError as e:
            await self._handle_error(f"soft deleting {self.model.__name__}", e)
            return False

    async def hard_delete(self, id: int) -> bool:
        """Eliminar un objeto mediante hard delete."""
        try:
            await self._validate_tenant_access()
            obj = await self.get_by_id(id)
            if not obj:
                return False
            await self.db.delete(obj)
            return True
        except SQLAlchemyError as e:
            await self._handle_error(f"hard deleting {self.model.__name__}", e)
            return False

    async def delete(self, id: int) -> bool:
        """Eliminar un objeto (intenta soft delete primero, si no es posible hace hard delete)."""
        try:
            # Intentar soft delete primero
            if await self.soft_delete(id):
                return True

            # Si no se pudo hacer soft delete, intentar hard delete
            return await self.hard_delete(id)
        except SQLAlchemyError as e:
            await self._handle_error(f"deleting {self.model.__name__}", e)
            return False

    async def _handle_error(self, operation: str, error: Exception) -> None:
        """Manejar errores de base de datos de manera consistente."""
        log_error(f"Database error while {operation}: {str(error)}")
        if isinstance(error, SQLAlchemyError):
            raise HTTPException(
                status_code=500,
                detail=f"Database error occurred while {operation}",
            )
        raise error

    async def bulk_create(self, objects: List[CreateSchemaType]) -> List[ModelType]:
        """Crear múltiples objetos en una sola operación."""
        try:
            await self._validate_tenant_access()

            db_objects = []
            for obj_in in objects:
                # Convertir a diccionario si es un modelo Pydantic
                if hasattr(obj_in, "dict"):
                    obj_data = obj_in.dict()
                else:
                    obj_data = obj_in

                # Agregar account_id si el modelo lo requiere
                if self.is_tenant_scoped and self.account_id is not None:
                    obj_data["account_id"] = self.account_id

                # Crear el objeto
                db_obj = self.model(**obj_data)
                self.db.add(db_obj)
                db_objects.append(db_obj)

            await self.db.flush()

            # Refrescar todos los objetos
            for db_obj in db_objects:
                await self.db.refresh(db_obj)

            return db_objects
        except SQLAlchemyError as e:
            await self._handle_error(f"bulk creating {self.model.__name__}", e)

    async def robust_upsert(
        self, objects: List[Dict[str, Any]], index_elements: List[str]
    ) -> List[ModelType]:
        """
        Insertar o actualizar múltiples objetos de forma robusta usando PostgreSQL's
        insert().on_conflict_do_update() para manejar condiciones de carrera de forma
        confiable y eficiente.

        Args:
            objects: Lista de diccionarios con los datos a insertar/actualizar
            index_elements: Lista de nombres de columnas que forman la restricción única

        Returns:
            Lista de objetos insertados o actualizados
        """
        try:
            await self._validate_tenant_access()

            # Si no hay objetos, retornar lista vacía
            if not objects:
                return []

            # Agregar account_id a cada objeto si el modelo lo requiere
            if self.is_tenant_scoped and self.account_id is not None:
                for obj in objects:
                    obj["account_id"] = self.account_id

            # Crear statement de inserción
            stmt = pg_insert(self.model).values(objects)

            # Definir columnas a actualizar en caso de conflicto
            update_cols = {}
            for col in stmt.excluded.keys():
                # No actualizar claves primarias ni created_at
                if col not in index_elements and col != "created_at":
                    update_cols[col] = getattr(stmt.excluded, col)

            # Si hay columnas para actualizar
            if update_cols:
                stmt = stmt.on_conflict_do_update(
                    index_elements=index_elements,  # Columnas que definen el conflicto
                    set_=update_cols,  # Columnas a actualizar
                )
            else:
                # Si solo se proporcionan columnas únicas, no hacer nada en caso de conflicto
                stmt = stmt.on_conflict_do_nothing(index_elements=index_elements)

            # Agregar cláusula returning para obtener los objetos insertados/actualizados
            stmt = stmt.returning(self.model)

            # Ejecutar la consulta
            result = await self.db.execute(stmt)
            return result.scalars().all()

        except SQLAlchemyError as e:
            await self._handle_error(f"robust upserting {self.model.__name__}", e)

    async def upsert(
        self, objects: List[Dict[str, Any]], unique_columns: List[str]
    ) -> List[ModelType]:
        """
        Insertar o actualizar múltiples objetos basados en columnas únicas.
        Utiliza el método robust_upsert para una implementación más eficiente.

        Args:
            objects: Lista de diccionarios con los datos a insertar/actualizar
            unique_columns: Lista de nombres de columnas que identifican un registro único

        Returns:
            Lista de objetos insertados o actualizados
        """
        try:
            # Usar la implementación robusta
            return await self.robust_upsert(objects, unique_columns)
        except SQLAlchemyError as e:
            await self._handle_error(f"upserting {self.model.__name__}", e)

    async def patch(self, id: int, obj_in: Dict[str, Any]) -> Optional[ModelType]:
        """Actualización parcial de un objeto"""
        db_obj = await self.get_by_id(id)
        if not db_obj:
            return None

        for field, value in obj_in.items():
            if hasattr(db_obj, field):
                setattr(db_obj, field, value)

        await self.db.refresh(db_obj)
        return db_obj

    async def count_by_filters(self, filters: Dict[str, Any]) -> int:
        """Contar registros que coinciden con los filtros dados"""
        query = select(func.count()).select_from(self.model)

        for field, value in filters.items():
            if hasattr(self.model, field):
                query = query.filter(getattr(self.model, field) == value)

        query = self._add_tenant_filter(query)
        result = await self.db.execute(query)
        return result.scalar_one()
