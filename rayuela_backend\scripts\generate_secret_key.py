#!/usr/bin/env python
"""
Script para generar una clave secreta segura y actualizar los archivos de entorno.

Uso:
    python -m scripts.generate_secret_key [--env ENV] [--length LENGTH] [--update]

Argumentos:
    --env ENV: Entorno para el que generar la clave (development, staging, production)
    --length LENGTH: Longitud en bytes de la clave a generar (por defecto: 64)
    --update: Actualizar el archivo .env.{ENV} con la nueva clave

Ejemplos:
    # Generar una clave y mostrarla
    python -m scripts.generate_secret_key

    # Generar una clave para producción y mostrarla
    python -m scripts.generate_secret_key --env production

    # Generar una clave para desarrollo y actualizar .env.development
    python -m scripts.generate_secret_key --env development --update
"""
import os
import sys
import re
import argparse
from pathlib import Path

# Añadir el directorio raíz al path para importar módulos del proyecto
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.core.security.key_generator import generate_secret_key


def update_env_file(env_file: str, secret_key: str) -> bool:
    """
    Actualiza la clave secreta en un archivo de entorno.

    Args:
        env_file: Ruta al archivo de entorno
        secret_key: Nueva clave secreta

    Returns:
        True si se actualizó correctamente, False en caso contrario
    """
    if not os.path.exists(env_file):
        print(f"Error: El archivo {env_file} no existe.")
        return False

    # Leer el archivo
    with open(env_file, "r", encoding="utf-8") as f:
        content = f.read()

    # Buscar la línea SECRET_KEY
    secret_key_pattern = re.compile(r"^SECRET_KEY\s*=.*$", re.MULTILINE)

    if secret_key_pattern.search(content):
        # Reemplazar la línea existente
        new_content = secret_key_pattern.sub(f"SECRET_KEY={secret_key}", content)
    else:
        # Añadir la línea al final
        new_content = content.rstrip() + f"\n\n# Security\nSECRET_KEY={secret_key}\n"

    # Escribir el archivo
    with open(env_file, "w", encoding="utf-8") as f:
        f.write(new_content)

    return True


def main():
    parser = argparse.ArgumentParser(
        description="Genera una clave secreta segura para JWT"
    )
    parser.add_argument(
        "--env",
        choices=["development", "staging", "production"],
        default="development",
        help="Entorno para el que generar la clave",
    )
    parser.add_argument(
        "--length", type=int, default=64, help="Longitud en bytes de la clave a generar"
    )
    parser.add_argument(
        "--update",
        action="store_true",
        help="Actualizar el archivo .env.{ENV} con la nueva clave",
    )

    args = parser.parse_args()

    # Generar la clave
    secret_key = generate_secret_key(args.length)

    # Mostrar la clave
    print(f"\nClave secreta generada para entorno {args.env}:")
    print("-" * 80)
    print(secret_key)
    print("-" * 80)

    # Actualizar el archivo de entorno si se solicitó
    if args.update:
        env_file = f".env.{args.env}"
        if update_env_file(env_file, secret_key):
            print(f"\nArchivo {env_file} actualizado con la nueva clave secreta.")
        else:
            print(f"\nNo se pudo actualizar el archivo {env_file}.")

    # Instrucciones adicionales según el entorno
    if args.env == "production":
        print(
            "\nIMPORTANTE: En producción, guarda esta clave en Google Cloud Secret Manager:"
        )
        print("gcloud secrets create jwt-secret-key --replication-policy=automatic")
        print(
            f"echo -n '{secret_key}' | gcloud secrets versions add jwt-secret-key --data-file=-"
        )
        print("\nLuego, configura Cloud Run para usar este secreto:")
        print("--set-secrets=SECRET_KEY=jwt-secret-key:latest")
    else:
        print(
            f"\nPara usar esta clave en desarrollo, asegúrate de que esté en tu archivo {env_file}"
        )
        if not args.update:
            print(f"Puedes actualizar el archivo manualmente o ejecutar:")
            print(f"python -m scripts.generate_secret_key --env {args.env} --update")


if __name__ == "__main__":
    main()
