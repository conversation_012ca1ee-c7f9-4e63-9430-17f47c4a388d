"""
Tests para verificar la unicidad global de email en el registro de cuentas.
"""

import pytest
from unittest.mock import AsyncMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException

from src.services.auth_service import AuthService
from src.db.models import SystemUser, Account
from src.db.schemas import AccountCreate, SystemUserCreate


@pytest.fixture
def mock_db():
    """Mock de la sesión de base de datos."""
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def auth_service(mock_db):
    """Instancia del servicio de autenticación con DB mockeada."""
    return AuthService(mock_db)


class TestGlobalEmailUniqueness:
    """Tests para verificar la unicidad global de email."""

    @pytest.mark.asyncio
    async def test_check_global_email_uniqueness_unique_email(
        self, auth_service, mock_db
    ):
        """Test que verifica que un email único retorna True."""
        # Arrange
        email = "<EMAIL>"
        mock_scalars = AsyncMock()
        mock_scalars.first.return_value = None
        mock_result = AsyncMock()
        mock_result.scalars.return_value = mock_scalars
        mock_db.execute.return_value = mock_result

        # Act
        result = await auth_service._check_global_email_uniqueness(email)

        # Assert
        assert result is True
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_check_global_email_uniqueness_existing_email(
        self, auth_service, mock_db
    ):
        """Test que verifica que un email existente retorna False."""
        # Arrange
        email = "<EMAIL>"
        existing_user = SystemUser(id=1, email=email, account_id=1)
        mock_scalars = AsyncMock()
        mock_scalars.first.return_value = existing_user
        mock_result = AsyncMock()
        mock_result.scalars.return_value = mock_scalars
        mock_db.execute.return_value = mock_result

        # Act
        result = await auth_service._check_global_email_uniqueness(email)

        # Assert
        assert result is False
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_register_account_with_existing_email_fails(
        self, auth_service, mock_db
    ):
        """Test que verifica que el registro falla con email existente."""
        # Arrange
        account_name = "Test Company"
        email = "<EMAIL>"
        password = "securepassword123"

        # Mock para verificación de unicidad (email ya existe)
        existing_user = SystemUser(id=1, email=email, account_id=2)
        mock_scalars = AsyncMock()
        mock_scalars.first.return_value = existing_user
        mock_result = AsyncMock()
        mock_result.scalars.return_value = mock_scalars
        mock_db.execute.return_value = mock_result
        mock_db.begin.return_value.__aenter__ = AsyncMock()
        mock_db.begin.return_value.__aexit__ = AsyncMock()

        # Act & Assert
        with pytest.raises(HTTPException) as exc_info:
            await auth_service.register_account(account_name, email, password)

        assert exc_info.value.status_code == 400
        assert "Email already exists globally" in exc_info.value.detail

    @pytest.mark.asyncio
    async def test_login_searches_globally_for_user(self, auth_service, mock_db):
        """Test que verifica que el login busca usuarios globalmente."""
        # Arrange
        email = "<EMAIL>"
        password = "password123"

        # Mock para usuario encontrado globalmente
        mock_user = SystemUser(
            id=1,
            email=email,
            account_id=1,
            hashed_password="hashed_password",
            is_active=True,
        )
        mock_scalars = AsyncMock()
        mock_scalars.first.return_value = mock_user
        mock_result = AsyncMock()
        mock_result.scalars.return_value = mock_scalars
        mock_db.execute.return_value = mock_result

        with patch(
            "src.core.security.verify_password", return_value=True
        ) as mock_verify, patch(
            "src.core.security.create_access_token", return_value="test_token"
        ) as mock_create_token, patch.object(
            auth_service, "_get_active_subscription", return_value=None
        ):

            # Act
            result = await auth_service.login(email, password)

            # Assert
            assert result["access_token"] == "test_token"
            assert result["account_id"] == 1
            assert result["is_admin"] == mock_user.is_admin

            # Verificar que se buscó globalmente (sin filtro de account_id)
            mock_db.execute.assert_called_once()
            call_args = mock_db.execute.call_args[0][0]
            # La consulta debe ser para SystemUser.email == email sin filtro de account_id
            assert "SystemUser.email" in str(call_args)
