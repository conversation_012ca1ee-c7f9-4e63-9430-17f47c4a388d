"""
Tests para el endpoint de webhook de Mercado Pago.
"""

import pytest
import json
import hmac
import hashlib
from unittest.mock import patch, MagicMock, AsyncMock
from fastapi import FastAPI
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.v1.endpoints.billing import router as billing_router
from src.core.config import settings


@pytest.fixture
def app():
    """Fixture para crear una aplicación FastAPI con el router de billing."""
    app = FastAPI()
    app.include_router(billing_router, prefix="/billing")
    return app


@pytest.fixture
def client(app):
    """Fixture para crear un cliente de prueba."""
    return AsyncClient(app=app, base_url="http://test")


@pytest.fixture
def mock_db():
    """Fixture para mockear la sesión de base de datos."""
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def mock_subscription_service():
    """Fixture para mockear el servicio de suscripción."""
    with patch("src.api.v1.endpoints.billing.SubscriptionService") as mock:
        mock_instance = mock.return_value
        mock_instance.handle_mercadopago_payment_approved = AsyncMock()
        mock_instance.handle_mercadopago_payment_failed = AsyncMock()
        mock_instance.handle_mercadopago_subscription_created = AsyncMock()
        mock_instance.handle_mercadopago_subscription_paused = AsyncMock()
        mock_instance.handle_mercadopago_subscription_cancelled = AsyncMock()
        return mock_instance


@pytest.fixture
def mock_mercadopago_service():
    """Fixture para mockear el servicio de Mercado Pago."""
    with patch("src.api.v1.endpoints.billing.mercadopago_service") as mock:
        mock.sdk.payment.return_value.get.return_value = {"response": {"status": "approved"}}
        mock.sdk.preapproval.return_value.get.return_value = {"response": {"status": "authorized"}}
        return mock


@pytest.fixture
def mock_verify_signature():
    """Fixture para mockear la verificación de firma."""
    with patch("src.utils.mercadopago_utils.verify_mercadopago_signature", new_callable=AsyncMock) as mock:
        mock.return_value = True
        return mock


def generate_signature(data_id, request_id, timestamp, secret):
    """Genera una firma válida para las pruebas."""
    template = f"id:{data_id};request-id:{request_id};ts:{timestamp};"
    signature = hmac.new(
        secret.encode(),
        template.encode(),
        hashlib.sha256
    ).hexdigest()
    return f"ts={timestamp},v1={signature}"


@pytest.mark.asyncio
async def test_mercadopago_webhook_payment_approved(
    client, mock_db, mock_subscription_service, mock_mercadopago_service, mock_verify_signature
):
    """Test para el webhook de Mercado Pago con un pago aprobado."""
    # Arrange
    payment_id = "123456789"
    data_id = "payment_123456789"
    request_id = "test_request_id"
    timestamp = "1704908010"
    
    # Generar una firma válida
    signature = generate_signature(data_id, request_id, timestamp, "test_secret")
    
    # Crear el payload
    payload = {
        "type": "payment",
        "data": {
            "id": payment_id
        }
    }
    
    # Act
    with patch("src.api.v1.endpoints.billing.get_db", return_value=mock_db):
        response = client.post(
            f"/billing/webhook/mercadopago?data.id={data_id}",
            json=payload,
            headers={
                "x-signature": signature,
                "x-request-id": request_id
            }
        )
    
    # Assert
    assert response.status_code == 200
    assert response.json() == {"status": "success"}
    
    # Verificar que se llamó a la verificación de firma
    mock_verify_signature.assert_called_once()
    
    # Verificar que se obtuvo el pago
    mock_mercadopago_service.sdk.payment().get.assert_called_once_with(payment_id)
    
    # Verificar que se procesó el pago aprobado
    mock_subscription_service.handle_mercadopago_payment_approved.assert_called_once()


@pytest.mark.asyncio
async def test_mercadopago_webhook_payment_rejected(
    client, mock_db, mock_subscription_service, mock_mercadopago_service, mock_verify_signature
):
    """Test para el webhook de Mercado Pago con un pago rechazado."""
    # Arrange
    payment_id = "123456789"
    data_id = "payment_123456789"
    request_id = "test_request_id"
    timestamp = "1704908010"
    
    # Generar una firma válida
    signature = generate_signature(data_id, request_id, timestamp, "test_secret")
    
    # Configurar el mock para devolver un pago rechazado
    mock_mercadopago_service.sdk.payment().get.return_value = {"response": {"status": "rejected"}}
    
    # Crear el payload
    payload = {
        "type": "payment",
        "data": {
            "id": payment_id
        }
    }
    
    # Act
    with patch("src.api.v1.endpoints.billing.get_db", return_value=mock_db):
        response = client.post(
            f"/billing/webhook/mercadopago?data.id={data_id}",
            json=payload,
            headers={
                "x-signature": signature,
                "x-request-id": request_id
            }
        )
    
    # Assert
    assert response.status_code == 200
    assert response.json() == {"status": "success"}
    
    # Verificar que se llamó a la verificación de firma
    mock_verify_signature.assert_called_once()
    
    # Verificar que se obtuvo el pago
    mock_mercadopago_service.sdk.payment().get.assert_called_once_with(payment_id)
    
    # Verificar que se procesó el pago rechazado
    mock_subscription_service.handle_mercadopago_payment_failed.assert_called_once()


@pytest.mark.asyncio
async def test_mercadopago_webhook_subscription_created(
    client, mock_db, mock_subscription_service, mock_mercadopago_service, mock_verify_signature
):
    """Test para el webhook de Mercado Pago con una suscripción creada."""
    # Arrange
    subscription_id = "123456789"
    data_id = "subscription_123456789"
    request_id = "test_request_id"
    timestamp = "1704908010"
    
    # Generar una firma válida
    signature = generate_signature(data_id, request_id, timestamp, "test_secret")
    
    # Crear el payload
    payload = {
        "type": "subscription",
        "data": {
            "id": subscription_id
        }
    }
    
    # Act
    with patch("src.api.v1.endpoints.billing.get_db", return_value=mock_db):
        response = client.post(
            f"/billing/webhook/mercadopago?data.id={data_id}",
            json=payload,
            headers={
                "x-signature": signature,
                "x-request-id": request_id
            }
        )
    
    # Assert
    assert response.status_code == 200
    assert response.json() == {"status": "success"}
    
    # Verificar que se llamó a la verificación de firma
    mock_verify_signature.assert_called_once()
    
    # Verificar que se obtuvo la suscripción
    mock_mercadopago_service.sdk.preapproval().get.assert_called_once_with(subscription_id)
    
    # Verificar que se procesó la suscripción creada
    mock_subscription_service.handle_mercadopago_subscription_created.assert_called_once()


@pytest.mark.asyncio
async def test_mercadopago_webhook_invalid_signature(
    client, mock_db, mock_subscription_service, mock_verify_signature
):
    """Test para el webhook de Mercado Pago con una firma inválida."""
    # Arrange
    mock_verify_signature.side_effect = Exception("Invalid signature")
    
    # Crear el payload
    payload = {
        "type": "payment",
        "data": {
            "id": "123456789"
        }
    }
    
    # Act
    with patch("src.api.v1.endpoints.billing.get_db", return_value=mock_db):
        response = client.post(
            "/billing/webhook/mercadopago?data.id=test_data_id",
            json=payload,
            headers={
                "x-signature": "invalid_signature",
                "x-request-id": "test_request_id"
            }
        )
    
    # Assert
    assert response.status_code == 500
    assert "Error handling webhook" in response.json()["detail"]
    
    # Verificar que se llamó a la verificación de firma
    mock_verify_signature.assert_called_once()
    
    # Verificar que no se procesó ningún evento
    mock_subscription_service.handle_mercadopago_payment_approved.assert_not_called()
    mock_subscription_service.handle_mercadopago_payment_failed.assert_not_called()
    mock_subscription_service.handle_mercadopago_subscription_created.assert_not_called()
    mock_subscription_service.handle_mercadopago_subscription_paused.assert_not_called()
    mock_subscription_service.handle_mercadopago_subscription_cancelled.assert_not_called()
