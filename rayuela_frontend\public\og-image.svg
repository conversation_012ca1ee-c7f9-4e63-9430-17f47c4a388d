<svg width="1200" height="630" viewBox="0 0 1200 630" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg-gradient)"/>
  
  <!-- Grid pattern -->
  <defs>
    <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
      <path d="M 40 0 L 0 0 0 40" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
    </pattern>
  </defs>
  <rect width="1200" height="630" fill="url(#grid)"/>
  
  <!-- Logo/Brand -->
  <circle cx="200" cy="200" r="40" fill="rgba(255,255,255,0.2)"/>
  <circle cx="200" cy="200" r="25" fill="white"/>
  
  <!-- Main Title -->
  <text x="200" y="280" font-family="Arial, sans-serif" font-size="48" font-weight="bold" fill="white">
    Rayuela.ai
  </text>
  
  <!-- Subtitle -->
  <text x="200" y="320" font-family="Arial, sans-serif" font-size="24" fill="rgba(255,255,255,0.9)">
    Recommendation System as a Service
  </text>
  
  <!-- Description -->
  <text x="200" y="380" font-family="Arial, sans-serif" font-size="18" fill="rgba(255,255,255,0.8)">
    Sistemas de recomendación avanzados para tu negocio,
  </text>
  <text x="200" y="410" font-family="Arial, sans-serif" font-size="18" fill="rgba(255,255,255,0.8)">
    sin la complejidad de construirlos desde cero.
  </text>
  
  <!-- Features -->
  <g transform="translate(200, 460)">
    <circle cx="0" cy="0" r="4" fill="white"/>
    <text x="15" y="5" font-family="Arial, sans-serif" font-size="16" fill="white">API-First</text>
    
    <circle cx="120" cy="0" r="4" fill="white"/>
    <text x="135" y="5" font-family="Arial, sans-serif" font-size="16" fill="white">Machine Learning</text>
    
    <circle cx="300" cy="0" r="4" fill="white"/>
    <text x="315" y="5" font-family="Arial, sans-serif" font-size="16" fill="white">Escalable</text>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="950" cy="150" r="60" fill="rgba(255,255,255,0.1)"/>
  <circle cx="1050" cy="250" r="40" fill="rgba(255,255,255,0.08)"/>
  <circle cx="900" cy="400" r="80" fill="rgba(255,255,255,0.06)"/>
  
  <!-- API visualization -->
  <g transform="translate(800, 300)">
    <rect x="0" y="0" width="300" height="200" rx="10" fill="rgba(255,255,255,0.1)" stroke="rgba(255,255,255,0.3)" stroke-width="2"/>
    <text x="150" y="30" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="white" text-anchor="middle">
      API Response
    </text>
    <text x="20" y="60" font-family="monospace" font-size="12" fill="rgba(255,255,255,0.9)">
      {
    </text>
    <text x="40" y="80" font-family="monospace" font-size="12" fill="rgba(255,255,255,0.9)">
      "recommendations": [
    </text>
    <text x="60" y="100" font-family="monospace" font-size="12" fill="rgba(255,255,255,0.9)">
      {"product_id": "123", "score": 0.95},
    </text>
    <text x="60" y="120" font-family="monospace" font-size="12" fill="rgba(255,255,255,0.9)">
      {"product_id": "456", "score": 0.87}
    </text>
    <text x="40" y="140" font-family="monospace" font-size="12" fill="rgba(255,255,255,0.9)">
      ]
    </text>
    <text x="20" y="160" font-family="monospace" font-size="12" fill="rgba(255,255,255,0.9)">
      }
    </text>
  </g>
</svg>
