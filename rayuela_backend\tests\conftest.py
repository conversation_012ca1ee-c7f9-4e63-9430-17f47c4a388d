"""
Configuración de pytest para los tests de integración.
"""
import asyncio
import os
import sys
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient
from typing import AsyncGenerator, Generator, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import create_engine

from src.core.config import settings
from src.db.base import Base
from src.db.session import get_db
from main import app  # Importar desde el directorio raíz

# Configurar base de datos de prueba
TEST_DATABASE_URL = os.getenv(
    "TEST_DATABASE_URL",
    "postgresql+asyncpg://postgres:postgres@localhost:5432/rayuela_test"
)

# Sobreescribir configuración para tests
settings.POSTGRES_USER = "postgres"  # Usar el usuario real de la base de datos
settings.POSTGRES_PASSWORD = "postgres"  # Usar la contraseña real de la base de datos
settings.POSTGRES_SERVER = "localhost"
settings.POSTGRES_PORT = 5432
settings.POSTGRES_DB = "rayuela_test"
settings.REDIS_URL = "redis://localhost:6379/1"  # Usar base de datos 1 para tests
settings.ENV = "test"


@pytest.fixture(scope="session")
def event_loop():
    """Crear un event loop para tests asíncronos"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest_asyncio.fixture(scope="function")
async def async_db_engine():
    """Crear engine para tests"""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)

    yield engine

    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)

    await engine.dispose()


@pytest_asyncio.fixture(scope="function")
async def async_db_session(async_db_engine) -> AsyncGenerator[AsyncSession, None]:
    """Crear sesión de base de datos para tests"""
    async_session = sessionmaker(
        async_db_engine, class_=AsyncSession, expire_on_commit=False
    )

    async with async_session() as session:
        yield session


@pytest_asyncio.fixture(scope="function")
async def client(async_db_session: AsyncSession) -> AsyncGenerator[TestClient, None]:
    """Fixture para el cliente de prueba."""
    async def override_get_db():
        try:
            yield async_db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as test_client:
        yield test_client
    app.dependency_overrides.clear()


@pytest_asyncio.fixture(scope="function")
async def test_accounts(async_db_session: AsyncSession) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear cuentas de prueba."""
    from src.db.models import Account

    accounts = {}
    for i in range(2):
        account = Account(
            name=f"Test Account {i}",
            email=f"test{i}@example.com",
            status="active"
        )
        async_db_session.add(account)
        await async_db_session.commit()
        await async_db_session.refresh(account)
        accounts[account.id] = account

    yield accounts

    # Limpiar después de las pruebas
    for account in accounts.values():
        await async_db_session.delete(account)
    await async_db_session.commit()


@pytest_asyncio.fixture(scope="function")
async def test_users(async_db_session: AsyncSession, test_accounts: Dict[int, Any]) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear usuarios de prueba."""
    from src.db.models import User

    users = {}
    for account in test_accounts.values():
        user = User(
            account_id=account.id,
            email=f"user@{account.email}",
            status="active"
        )
        async_db_session.add(user)
        await async_db_session.commit()
        await async_db_session.refresh(user)
        users[user.id] = user

    yield users

    # Limpiar después de las pruebas
    for user in users.values():
        await async_db_session.delete(user)
    await async_db_session.commit()


@pytest_asyncio.fixture(scope="function")
async def test_products(async_db_session: AsyncSession, test_accounts: Dict[int, Any]) -> AsyncGenerator[Dict[int, Any], None]:
    """Fixture para crear productos de prueba."""
    from src.db.models import Product

    products = {}
    for account in test_accounts.values():
        product = Product(
            account_id=account.id,
            name=f"Test Product {account.id}",
            status="active"
        )
        async_db_session.add(product)
        await async_db_session.commit()
        await async_db_session.refresh(product)
        products[product.id] = product

    yield products

    # Limpiar después de las pruebas
    for product in products.values():
        await async_db_session.delete(product)
    await async_db_session.commit()


@pytest.fixture(scope="function")
def test_account() -> Dict[str, Any]:
    """Fixture para crear una cuenta de prueba"""
    from src.core.security.api_key import hash_api_key

    test_api_key = "test_api_key_123"
    return {
        "id": 1,
        "name": "Test Account",
        "subscription_plan": "BASIC",
        "api_key_hash": hash_api_key(test_api_key),
        "api_key_prefix": "test",
        "api_key_last_chars": "ey_123",
        "api_key_created_at": "2024-01-01T00:00:00",
        "api_key_revealed": True,
        "is_active": True,
    }


@pytest.fixture(scope="function")
def test_user() -> Dict[str, Any]:
    """Fixture para crear un usuario de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "email": "<EMAIL>",
        "status": "active",
    }


@pytest.fixture(scope="function")
def test_product() -> Dict[str, Any]:
    """Fixture para crear un producto de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "name": "Test Product",
        "status": "active",
    }


@pytest.fixture(scope="function")
def test_interaction() -> Dict[str, Any]:
    """Fixture para crear una interacción de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "user_id": 1,
        "product_id": 1,
        "type": "view",
        "timestamp": "2024-01-01T00:00:00",
    }


@pytest.fixture(scope="function")
def test_training_job() -> Dict[str, Any]:
    """Fixture para crear un job de entrenamiento de prueba"""
    return {
        "id": 1,
        "account_id": 1,
        "status": "pending",
        "created_at": "2024-01-01T00:00:00",
    }


@pytest.fixture(scope="function")
def mock_redis():
    """Fixture para mockear Redis"""
    class MockRedis:
        def __init__(self):
            self._store = {}

        async def get(self, key):
            return self._store.get(key)

        async def set(self, key, value, expire=None):
            self._store[key] = value

        async def delete(self, key):
            if key in self._store:
                del self._store[key]

        async def close(self):
            self._store.clear()

    return MockRedis()


@pytest.fixture(scope="function")
def mock_recommender():
    """Fixture para mockear el recomendador"""
    class MockRecommender:
        def __init__(self):
            self.initialized = False
            self.trained = False

        def is_initialized(self):
            return self.initialized

        def is_trained(self):
            return self.trained

        async def initialize(self):
            self.initialized = True

        async def train_models(self, db, account_id):
            self.trained = True

        async def get_recommendations(self, *args, **kwargs):
            return [{"id": 1, "score": 0.9}]

        def get_metrics(self):
            return {"precision": 0.8, "recall": 0.7}

        def get_parameters(self):
            return {"k": 10, "alpha": 0.5}

    return MockRecommender()

# Import utilities
try:
    from tests.integration.utils.mock_celery import mock_celery, mock_celery_task
    from tests.integration.utils.mock_external_services import mock_gcs, mock_redis, mock_mercadopago
except ImportError:
    pass  # Utilities might not be available in all test contexts
