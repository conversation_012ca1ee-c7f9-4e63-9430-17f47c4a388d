# CI/CD Testing Setup for Rayuela

This document describes the automated testing setup in the CI/CD pipeline for the Rayuela backend.

## Overview

The CI/CD pipeline includes automated testing to ensure code quality and prevent regressions. The tests are run as part of the Cloud Build pipeline before deployment.

## Test Stages

The pipeline includes the following test stages:

1. **Code Quality Checks**
   - Linting with flake8
   - Code formatting with black
   - Import sorting with isort
   - Security scanning with bandit

2. **Unit Tests**
   - Tests for individual components
   - No database or external service dependencies

3. **Integration Tests**
   - Tests for multi-tenancy
   - Tests for authentication flows
   - Requires database and Redis

## Test Environment

For integration tests, the pipeline sets up a test environment using Docker Compose:

- PostgreSQL database
- Redis instance
- Test runner container

The test environment is isolated from the production environment and uses test-specific credentials.

## Configuration

The test environment is configured using the following files:

- `cloudbuild.yaml`: Defines the CI/CD pipeline steps
- `docker-compose.test.yml`: Defines the test environment
- `Dockerfile.test`: Defines the test runner container

## Test Variables

The following variables are used to configure the test environment:

- `_TEST_DB_HOST`: Test database host
- `_TEST_DB_PORT`: Test database port
- `_TEST_DB_USER`: Test database user
- `_TEST_DB_PASSWORD`: Test database password
- `_TEST_DB_NAME`: Test database name
- `_TEST_REDIS_HOST`: Test Redis host
- `_TEST_REDIS_PORT`: Test Redis port
- `_TEST_REDIS_PASSWORD`: Test Redis password
- `_TEST_SECRET_KEY`: Secret key for test environment

These variables are defined in the `substitutions` section of the `cloudbuild.yaml` file.

## Running Tests Locally

To run the same tests locally, you can use the following commands:

```bash
# Run unit tests
python -m pytest tests/unit/ -v

# Run integration tests (requires database and Redis)
docker-compose -f docker-compose.test.yml up --build --abort-on-container-exit
```

## Test Focus

The integration tests focus on:

1. **Multi-tenancy**: Ensuring proper tenant isolation
   - Tests in `tests/integration/test_multi_tenancy.py`
   - Verifies that tenants cannot access each other's data
   - Verifies that RLS policies are correctly applied

2. **Authentication**: Ensuring secure authentication flows
   - Tests in `tests/integration/test_auth_flow.py`
   - Verifies login, token generation, and validation
   - Verifies API key authentication

3. **Security**: Ensuring secure token and password handling
   - Tests in `tests/unit/core/security/`
   - Verifies token generation, validation, and expiration
   - Verifies password hashing and verification
   - Verifies API key generation and validation

## Troubleshooting

If tests fail in the CI/CD pipeline, you can:

1. Check the Cloud Build logs for error messages
2. Run the tests locally to reproduce the issue
3. Check if the test environment is correctly configured
4. Verify that the test database migrations are applied correctly

## Adding New Tests

When adding new features, make sure to:

1. Add unit tests for individual components
2. Add integration tests for multi-component interactions
3. Update the CI/CD pipeline if necessary to include the new tests
