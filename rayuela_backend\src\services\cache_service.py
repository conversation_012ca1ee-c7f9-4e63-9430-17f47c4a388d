"""
Servicio para la gestión de caché.
"""
from typing import Optional, Any, Dict, List
from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession

from src.ml_pipeline.serving_engine import ServingEngine
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.core.auth_utils import get_user_and_account_ids
from src.utils.base_logger import log_info, log_error


class CacheService:
    """
    Servicio para la gestión de caché, incluyendo invalidación explícita.
    
    Este servicio proporciona métodos para invalidar la caché de recomendaciones
    de forma explícita y segura, sin depender de la extracción de IDs de la solicitud.
    """
    
    def __init__(self):
        """Inicializa el servicio de caché."""
        self.artifact_manager = ModelArtifactManager()
        self.serving_engine = ServingEngine(self.artifact_manager)
    
    async def invalidate_user_cache(self, account_id: int, user_id: int) -> bool:
        """
        Invalida la caché de recomendaciones para un usuario específico.
        
        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            # Usar el gestor de artefactos para invalidar la caché
            success = await self.artifact_manager.invalidate_user_cache(
                account_id=account_id,
                user_id=user_id
            )
            
            if success:
                log_info(f"Caché invalidada explícitamente para user_id={user_id}, account_id={account_id}")
            else:
                log_error(f"Error al invalidar caché para user_id={user_id}, account_id={account_id}")
                
            return success
        except Exception as e:
            log_error(f"Error en invalidate_user_cache: {str(e)}")
            return False
    
    async def invalidate_account_cache(self, account_id: int) -> bool:
        """
        Invalida toda la caché de recomendaciones para una cuenta.
        
        Args:
            account_id: ID de la cuenta
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            # Usar el gestor de artefactos para invalidar la caché
            await self.artifact_manager.invalidate_account_cache(account_id=account_id)
            log_info(f"Caché invalidada explícitamente para account_id={account_id}")
            return True
        except Exception as e:
            log_error(f"Error en invalidate_account_cache: {str(e)}")
            return False
    
    async def invalidate_from_request(self, request: Request, db: AsyncSession) -> bool:
        """
        Intenta invalidar la caché basándose en la información de la solicitud.
        
        Este método es un último recurso cuando no se puede invalidar la caché
        de forma explícita en el servicio de negocio.
        
        Args:
            request: Solicitud HTTP
            db: Sesión de base de datos
            
        Returns:
            True si la invalidación fue exitosa, False en caso contrario
        """
        try:
            # Obtener user_id y account_id de la autenticación
            user_id, account_id = await get_user_and_account_ids(request, db)
            
            # Verificar que tenemos account_id
            if not account_id:
                log_error("No se pudo determinar el account_id para invalidar caché")
                return False
            
            # Si no tenemos user_id, intentar extraerlo del cuerpo o la URL
            if not user_id:
                user_id = await self._extract_user_id_from_request(request)
            
            # Si aún no tenemos user_id, invalidar toda la caché de la cuenta
            if not user_id:
                return await self.invalidate_account_cache(account_id)
            
            # Invalidar caché para el usuario específico
            return await self.invalidate_user_cache(account_id, user_id)
        except Exception as e:
            log_error(f"Error en invalidate_from_request: {str(e)}")
            return False
    
    async def _extract_user_id_from_request(self, request: Request) -> Optional[int]:
        """
        Extrae el ID de usuario de la solicitud (cuerpo o URL).
        
        Este método es un último recurso y solo debe usarse cuando no se puede
        obtener el user_id de forma explícita.
        
        Args:
            request: Solicitud HTTP
            
        Returns:
            ID de usuario o None si no se pudo extraer
        """
        # Intentar extraer del cuerpo
        try:
            body = await request.json()
            
            # Buscar user_id en diferentes formatos posibles
            user_id = (
                body.get("user_id") or 
                body.get("end_user_id") or 
                body.get("userId") or
                body.get("endUserId")
            )
            
            if isinstance(user_id, int) and user_id > 0:
                return user_id
            elif isinstance(user_id, str) and user_id.isdigit():
                return int(user_id)
        except Exception:
            pass
        
        # Intentar extraer de la URL
        try:
            path = request.url.path
            segments = path.split("/")
            
            # Buscar patrones como /users/123/ o /end_users/123/
            for i, segment in enumerate(segments):
                if segment in ["users", "end_users", "customers"] and i + 1 < len(segments):
                    try:
                        user_id = int(segments[i + 1])
                        if user_id > 0:
                            return user_id
                    except (ValueError, IndexError):
                        pass
            
            # Buscar en los parámetros de consulta
            for param_name in ["user_id", "end_user_id", "userId", "endUserId"]:
                user_id = request.query_params.get(param_name)
                if user_id and user_id.isdigit():
                    return int(user_id)
        except Exception:
            pass
        
        return None
