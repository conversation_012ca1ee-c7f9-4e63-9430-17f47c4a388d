"""Integration tests for the Google Cloud Storage integration."""
import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient
import json
import joblib
import os
from datetime import datetime, timedelta

from main import app
from src.core.deps import get_db, get_current_account, get_limit_service
from src.services import LimitService
from src.services.batch_data_storage_service import BatchDataStorageService
from src.db.models import Account, ModelMetadata

from tests.integration.utils.mock_external_services import mock_gcs
from tests.integration.utils.test_db import (
    db_session,
    override_get_db,
    test_account,
    test_admin_user
)


@pytest.fixture(scope="function")
def client(override_get_db):
    """Create a test client with overridden dependencies."""
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as client:
        yield client
    app.dependency_overrides = {}


@pytest_asyncio.fixture(scope="function")
async def async_client(override_get_db, test_account, test_admin_user):
    """Create an async test client with overridden dependencies."""
    # Override dependencies
    async def override_get_current_account():
        return {"account_id": test_account.id, "name": test_account.name}
    
    async def override_get_limit_service(db=None):
        if db is None:
            db = override_get_db()
        return LimitService(db=db, account_id=test_account.id)
    
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_current_account] = override_get_current_account
    app.dependency_overrides[get_limit_service] = override_get_limit_service
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
    
    app.dependency_overrides = {}


@pytest.mark.asyncio
async def test_upload_model_to_gcs(
    async_client,
    db_session,
    test_account,
    mock_gcs
):
    """Test uploading a model to GCS."""
    # Create a model metadata record
    model_metadata = ModelMetadata(
        account_id=test_account.id,
        model_type="collaborative",
        version="1.0.0",
        parameters={
            "factors": 10,
            "iterations": 20,
            "regularization": 0.1
        },
        metrics={
            "rmse": 0.85,
            "mae": 0.65,
            "precision": 0.75,
            "recall": 0.80
        }
    )
    db_session.add(model_metadata)
    await db_session.commit()
    await db_session.refresh(model_metadata)
    
    # Create a mock model
    mock_model = {
        "model_type": "collaborative",
        "weights": [0.1, 0.2, 0.3, 0.4, 0.5],
        "biases": [0.01, 0.02, 0.03],
        "user_factors": {"user_1": [0.1, 0.2, 0.3], "user_2": [0.4, 0.5, 0.6]},
        "item_factors": {"item_1": [0.7, 0.8, 0.9], "item_2": [0.1, 0.2, 0.3]}
    }
    
    # Create a temporary file with the model (using secure joblib serialization)
    temp_file_path = "temp_model.joblib"
    joblib.dump(mock_model, temp_file_path)
    
    try:
        # Mock the storage service
        with patch("src.api.v1.endpoints.models.get_storage_service") as mock_get_service:
            mock_service = AsyncMock()
            mock_service.upload_model.return_value = {
                "model_path": f"models/{test_account.id}/collaborative/model.pkl",
                "metadata_path": f"models/{test_account.id}/collaborative/metadata.json",
                "model_url": f"https://storage.googleapis.com/default-bucket/models/{test_account.id}/collaborative/model.pkl",
                "metadata_url": f"https://storage.googleapis.com/default-bucket/models/{test_account.id}/collaborative/metadata.json"
            }
            mock_get_service.return_value = mock_service
            
            # Call the upload model endpoint
            with open(temp_file_path, "rb") as f:
                response = await async_client.post(
                    f"/api/v1/models/{model_metadata.id}/upload",
                    files={"model_file": ("model.pkl", f, "application/octet-stream")}
                )
            
            # Check response
            assert response.status_code == 200
            data = response.json()
            assert "model_path" in data
            assert "metadata_path" in data
            assert "model_url" in data
            assert "metadata_url" in data
            
            # Verify the service was called with correct parameters
            mock_service.upload_model.assert_called_once()
            
            # Verify the model metadata was updated in the database
            await db_session.refresh(model_metadata)
            assert model_metadata.storage_path == data["model_path"]
            assert model_metadata.storage_url == data["model_url"]
    
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)


@pytest.mark.asyncio
async def test_download_model_from_gcs(
    async_client,
    db_session,
    test_account,
    mock_gcs
):
    """Test downloading a model from GCS."""
    # Create a model metadata record
    model_metadata = ModelMetadata(
        account_id=test_account.id,
        model_type="collaborative",
        version="1.0.0",
        storage_path=f"models/{test_account.id}/collaborative/model.pkl",
        storage_url=f"https://storage.googleapis.com/default-bucket/models/{test_account.id}/collaborative/model.pkl",
        parameters={
            "factors": 10,
            "iterations": 20,
            "regularization": 0.1
        },
        metrics={
            "rmse": 0.85,
            "mae": 0.65,
            "precision": 0.75,
            "recall": 0.80
        }
    )
    db_session.add(model_metadata)
    await db_session.commit()
    await db_session.refresh(model_metadata)
    
    # Create a mock model in GCS
    bucket = mock_gcs.bucket("default-bucket")
    model_blob = bucket.blob(f"models/{test_account.id}/collaborative/model.pkl")
    
    # Create a mock model
    mock_model = {
        "model_type": "collaborative",
        "weights": [0.1, 0.2, 0.3, 0.4, 0.5],
        "biases": [0.01, 0.02, 0.03],
        "user_factors": {"user_1": [0.1, 0.2, 0.3], "user_2": [0.4, 0.5, 0.6]},
        "item_factors": {"item_1": [0.7, 0.8, 0.9], "item_2": [0.1, 0.2, 0.3]}
    }
    
    # Serialize the model and upload to GCS
    temp_file_path = "temp_model.pkl"
    with open(temp_file_path, "wb") as f:
        pickle.dump(mock_model, f)
    
    model_blob.upload_from_filename(temp_file_path)
    
    try:
        # Mock the storage service
        with patch("src.api.v1.endpoints.models.get_storage_service") as mock_get_service:
            mock_service = AsyncMock()
            mock_service.download_model.return_value = temp_file_path
            mock_get_service.return_value = mock_service
            
            # Call the download model endpoint
            response = await async_client.get(
                f"/api/v1/models/{model_metadata.id}/download"
            )
            
            # Check response
            assert response.status_code == 200
            assert response.headers["Content-Type"] == "application/octet-stream"
            assert response.headers["Content-Disposition"] == f"attachment; filename=model_{model_metadata.id}.pkl"
            
            # Verify the service was called with correct parameters
            mock_service.download_model.assert_called_once_with(
                model_metadata.storage_path
            )
    
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)


@pytest.mark.asyncio
async def test_list_models_in_gcs(
    async_client,
    db_session,
    test_account,
    mock_gcs
):
    """Test listing models in GCS."""
    # Create multiple model metadata records
    model_metadata_1 = ModelMetadata(
        account_id=test_account.id,
        model_type="collaborative",
        version="1.0.0",
        storage_path=f"models/{test_account.id}/collaborative/model_1.pkl",
        storage_url=f"https://storage.googleapis.com/default-bucket/models/{test_account.id}/collaborative/model_1.pkl"
    )
    
    model_metadata_2 = ModelMetadata(
        account_id=test_account.id,
        model_type="content_based",
        version="1.0.0",
        storage_path=f"models/{test_account.id}/content_based/model_2.pkl",
        storage_url=f"https://storage.googleapis.com/default-bucket/models/{test_account.id}/content_based/model_2.pkl"
    )
    
    db_session.add_all([model_metadata_1, model_metadata_2])
    await db_session.commit()
    
    # Create mock models in GCS
    bucket = mock_gcs.bucket("default-bucket")
    
    # Create model 1
    model_blob_1 = bucket.blob(f"models/{test_account.id}/collaborative/model_1.pkl")
    model_blob_1.upload_from_string("mock model 1 data", content_type="application/octet-stream")
    
    # Create model 2
    model_blob_2 = bucket.blob(f"models/{test_account.id}/content_based/model_2.pkl")
    model_blob_2.upload_from_string("mock model 2 data", content_type="application/octet-stream")
    
    # Call the list models endpoint
    response = await async_client.get(
        "/api/v1/models"
    )
    
    # Check response
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    
    # Verify the models are in the response
    model_ids = [model["id"] for model in data]
    assert model_metadata_1.id in model_ids
    assert model_metadata_2.id in model_ids
    
    # Verify the model types are correct
    model_types = {model["id"]: model["model_type"] for model in data}
    assert model_types[model_metadata_1.id] == "collaborative"
    assert model_types[model_metadata_2.id] == "content_based"


@pytest.mark.asyncio
async def test_delete_model_from_gcs(
    async_client,
    db_session,
    test_account,
    mock_gcs
):
    """Test deleting a model from GCS."""
    # Create a model metadata record
    model_metadata = ModelMetadata(
        account_id=test_account.id,
        model_type="collaborative",
        version="1.0.0",
        storage_path=f"models/{test_account.id}/collaborative/model.pkl",
        storage_url=f"https://storage.googleapis.com/default-bucket/models/{test_account.id}/collaborative/model.pkl"
    )
    db_session.add(model_metadata)
    await db_session.commit()
    await db_session.refresh(model_metadata)
    
    # Create a mock model in GCS
    bucket = mock_gcs.bucket("default-bucket")
    model_blob = bucket.blob(f"models/{test_account.id}/collaborative/model.pkl")
    model_blob.upload_from_string("mock model data", content_type="application/octet-stream")
    
    # Mock the storage service
    with patch("src.api.v1.endpoints.models.get_storage_service") as mock_get_service:
        mock_service = AsyncMock()
        mock_service.delete_model.return_value = True
        mock_get_service.return_value = mock_service
        
        # Call the delete model endpoint
        response = await async_client.delete(
            f"/api/v1/models/{model_metadata.id}"
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Model deleted successfully"
        
        # Verify the service was called with correct parameters
        mock_service.delete_model.assert_called_once_with(
            model_metadata.storage_path
        )
        
        # Verify the model metadata was deleted from the database
        model = await db_session.get(ModelMetadata, model_metadata.id)
        assert model is None


@pytest.mark.asyncio
async def test_export_model_to_gcs(
    async_client,
    db_session,
    test_account,
    mock_gcs
):
    """Test exporting a model to GCS."""
    # Create a model metadata record
    model_metadata = ModelMetadata(
        account_id=test_account.id,
        model_type="collaborative",
        version="1.0.0",
        storage_path=f"models/{test_account.id}/collaborative/model.pkl",
        storage_url=f"https://storage.googleapis.com/default-bucket/models/{test_account.id}/collaborative/model.pkl",
        parameters={
            "factors": 10,
            "iterations": 20,
            "regularization": 0.1
        },
        metrics={
            "rmse": 0.85,
            "mae": 0.65,
            "precision": 0.75,
            "recall": 0.80
        }
    )
    db_session.add(model_metadata)
    await db_session.commit()
    await db_session.refresh(model_metadata)
    
    # Create a mock model in GCS
    bucket = mock_gcs.bucket("default-bucket")
    model_blob = bucket.blob(f"models/{test_account.id}/collaborative/model.pkl")
    
    # Create a mock model
    mock_model = {
        "model_type": "collaborative",
        "weights": [0.1, 0.2, 0.3, 0.4, 0.5],
        "biases": [0.01, 0.02, 0.03],
        "user_factors": {"user_1": [0.1, 0.2, 0.3], "user_2": [0.4, 0.5, 0.6]},
        "item_factors": {"item_1": [0.7, 0.8, 0.9], "item_2": [0.1, 0.2, 0.3]}
    }
    
    # Serialize the model and upload to GCS
    temp_file_path = "temp_model.pkl"
    with open(temp_file_path, "wb") as f:
        pickle.dump(mock_model, f)
    
    model_blob.upload_from_filename(temp_file_path)
    
    try:
        # Mock the storage service
        with patch("src.api.v1.endpoints.models.get_storage_service") as mock_get_service:
            mock_service = AsyncMock()
            mock_service.export_model.return_value = {
                "export_path": f"exports/{test_account.id}/collaborative/model_{model_metadata.id}.zip",
                "export_url": f"https://storage.googleapis.com/default-bucket/exports/{test_account.id}/collaborative/model_{model_metadata.id}.zip"
            }
            mock_get_service.return_value = mock_service
            
            # Call the export model endpoint
            response = await async_client.post(
                f"/api/v1/models/{model_metadata.id}/export",
                json={"format": "zip", "include_metadata": True}
            )
            
            # Check response
            assert response.status_code == 200
            data = response.json()
            assert "export_path" in data
            assert "export_url" in data
            
            # Verify the service was called with correct parameters
            mock_service.export_model.assert_called_once_with(
                model_metadata=model_metadata,
                format="zip",
                include_metadata=True
            )
    
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_file_path):
            os.remove(temp_file_path)
