#!/bin/bash

# 🔍 Script de Verificación del Despliegue de Rayuela
# Este script verifica el estado completo del despliegue en GCP

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo "🔍 === VERIFICACIÓN DEL DESPLIEGUE RAYUELA ==="
echo ""

# 1. Verificar configuración del proyecto
PROJECT_ID=$(gcloud config get-value project)
REGION="us-central1"

print_status "Proyecto: $PROJECT_ID"
print_status "Región: $REGION"
echo ""

# 2. Estado de los servicios
print_status "📋 ESTADO DE LOS SERVICIOS:"
gcloud run services list --region=$REGION --format="table(
    metadata.name:label=SERVICIO,
    status.url:label=URL,
    status.conditions[0].status:label=ESTADO,
    status.traffic[0].percent:label=TRÁFICO
)"
echo ""

# 3. URLs de los servicios
print_status "🌐 URLs DE LA APLICACIÓN:"
BACKEND_URL=$(gcloud run services describe rayuela-backend --region=$REGION --format="value(status.url)" 2>/dev/null || echo "No disponible")
FRONTEND_URL=$(gcloud run services describe rayuela-frontend --region=$REGION --format="value(status.url)" 2>/dev/null || echo "No disponible")
API_URL=$(gcloud run services describe rayuela-api --region=$REGION --format="value(status.url)" 2>/dev/null || echo "No disponible")

echo "🔧 Backend:  $BACKEND_URL"
echo "🖥️  Frontend: $FRONTEND_URL"
echo "📡 API:      $API_URL"
echo ""

# 4. Health checks
print_status "🏥 VERIFICACIONES DE SALUD:"

# Frontend
if [ "$FRONTEND_URL" != "No disponible" ]; then
    echo -n "Frontend: "
    if curl -f -s "$FRONTEND_URL" > /dev/null; then
        print_success "✅ Funcionando"
    else
        print_error "❌ No responde"
    fi
else
    print_warning "⚠️  Frontend no desplegado"
fi

# Backend
if [ "$BACKEND_URL" != "No disponible" ]; then
    echo -n "Backend /health: "
    HEALTH_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" "$BACKEND_URL/health" || echo "000")
    if [ "$HEALTH_RESPONSE" = "200" ]; then
        print_success "✅ Funcionando"
    else
        print_error "❌ Error $HEALTH_RESPONSE"
        
        # Mostrar logs recientes del backend
        print_warning "📋 Últimos logs del backend:"
        gcloud run services logs read rayuela-backend --region=$REGION --limit=5 --format="value(textPayload)" | tail -5
    fi
else
    print_warning "⚠️  Backend no desplegado"
fi

echo ""

# 5. Recursos de infraestructura
print_status "🏗️  RECURSOS DE INFRAESTRUCTURA:"

# Base de datos
echo -n "Cloud SQL: "
if gcloud sql instances describe rayuela-postgres --format="value(state)" 2>/dev/null | grep -q "RUNNABLE"; then
    print_success "✅ Funcionando"
    DB_IP=$(gcloud sql instances describe rayuela-postgres --format="value(ipAddresses[0].ipAddress)")
    echo "   IP: $DB_IP"
else
    print_warning "⚠️  No encontrada o no funcionando"
fi

# Redis
echo -n "Redis: "
if gcloud redis instances describe rayuela-redis --region=$REGION --format="value(state)" 2>/dev/null | grep -q "READY"; then
    print_success "✅ Funcionando"
    REDIS_IP=$(gcloud redis instances describe rayuela-redis --region=$REGION --format="value(host)")
    echo "   IP: $REDIS_IP"
else
    print_warning "⚠️  No encontrada o no funcionando"
fi

# Storage
echo -n "Cloud Storage: "
if gsutil ls gs://$PROJECT_ID-rayuela-storage > /dev/null 2>&1; then
    print_success "✅ Funcionando"
    echo "   Bucket: gs://$PROJECT_ID-rayuela-storage"
else
    print_warning "⚠️  Bucket no encontrado"
fi

echo ""

# 6. Secretos
print_status "🔐 SECRETOS CONFIGURADOS:"
SECRETS=$(gcloud secrets list --format="value(name)" | grep -E "(DB_PASSWORD|REDIS_PASSWORD|SECRET_KEY)" | wc -l)
if [ "$SECRETS" -eq 3 ]; then
    print_success "✅ Todos los secretos configurados ($SECRETS/3)"
else
    print_warning "⚠️  Faltan secretos ($SECRETS/3)"
fi

echo ""

# 7. Resumen y recomendaciones
print_status "📋 RESUMEN Y RECOMENDACIONES:"

if [ "$HEALTH_RESPONSE" = "200" ] && curl -f -s "$FRONTEND_URL" > /dev/null; then
    print_success "🎉 ¡Aplicación completamente funcional!"
    echo ""
    echo "📧 URLs para usar:"
    echo "   Frontend: $FRONTEND_URL"
    echo "   Backend API: $BACKEND_URL"
    echo ""
    echo "🔧 Comandos útiles:"
    echo "   Logs backend: gcloud run services logs read rayuela-backend --region=$REGION"
    echo "   Logs frontend: gcloud run services logs read rayuela-frontend --region=$REGION"
    echo "   Redesplegar: ./scripts/deploy-production.sh --direct"
    
elif [ "$FRONTEND_URL" != "No disponible" ] && curl -f -s "$FRONTEND_URL" > /dev/null; then
    print_warning "⚠️  Frontend funcionando, backend con problemas"
    echo ""
    echo "🔧 Para diagnosticar el backend:"
    echo "   1. Revisar logs: gcloud run services logs read rayuela-backend --region=$REGION"
    echo "   2. Verificar variables de entorno y secretos"
    echo "   3. Revisar conectividad a base de datos"
    echo ""
    print_warning "⚠️  Problema identificado: SQLAlchemy warning en system_user_roles tabla"
    echo "   Revisar definición de la tabla en src/db/models/system_user_role.py"
    
else
    print_error "❌ Aplicación con problemas significativos"
    echo ""
    echo "🔧 Acciones recomendadas:"
    echo "   1. Verificar logs de ambos servicios"
    echo "   2. Revisar configuración de red y permisos"
    echo "   3. Redesplegar si es necesario"
fi

echo ""
print_status "🔗 Consola de GCP: https://console.cloud.google.com/run?project=$PROJECT_ID" 