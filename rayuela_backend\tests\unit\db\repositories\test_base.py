import pytest
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import AsyncSession
from src.db.repositories.base import BaseRepository
from src.db.models import Product, EndUser, Interaction
from src.core.exceptions import ResourceNotFoundError

class TestBaseRepository(BaseRepository):
    """Clase de prueba que extiende BaseRepository."""
    def __init__(self, db: AsyncSession):
        super().__init__(db, Product)  # Usamos Product como modelo de prueba

@pytest.fixture
@pytest.mark.asyncio
async def test_repo(db_session: AsyncSession):
    """Fixture para crear una instancia de TestBaseRepository."""
    return TestBaseRepository(db_session)

@pytest.fixture
async def sample_products(db_session: AsyncSession):
    """Fixture para crear productos de prueba."""
    products = [
        Product(
            name="Product 1",
            description="Description 1",
            price=10.0,
            account_id=1
        ),
        Product(
            name="Product 2",
            description="Description 2",
            price=20.0,
            account_id=1
        ),
        Product(
            name="Product 3",
            description="Description 3",
            price=30.0,
            account_id=2
        )
    ]
    for product in products:
        db_session.add(product)
    await db_session.commit()
    return products

async def test_add_tenant_filter(test_repo: TestBaseRepository):
    """Test para verificar el filtro de tenant."""
    # Crear query base
    query = select(Product)
    
    # Aplicar filtro de tenant
    filtered_query = test_repo._add_tenant_filter(query, 1)
    
    # Verificar que el filtro se aplicó correctamente
    assert str(filtered_query).count("account_id = :account_id_1") == 1

async def test_get_by_id_with_tenant(test_repo: TestBaseRepository, sample_products):
    """Test para verificar get_by_id con filtro de tenant."""
    # Obtener producto existente
    product = await test_repo.get_by_id(sample_products[0].id, account_id=1)
    assert product is not None
    assert product.account_id == 1
    
    # Intentar obtener producto de otro tenant
    with pytest.raises(ResourceNotFoundError):
        await test_repo.get_by_id(sample_products[2].id, account_id=1)

async def test_list_with_tenant(test_repo: TestBaseRepository, sample_products):
    """Test para verificar list con filtro de tenant."""
    # Listar productos de tenant 1
    products = await test_repo.list(account_id=1)
    assert len(products) == 2
    assert all(p.account_id == 1 for p in products)
    
    # Listar productos de tenant 2
    products = await test_repo.list(account_id=2)
    assert len(products) == 1
    assert all(p.account_id == 2 for p in products)

async def test_create_with_tenant(test_repo: TestBaseRepository):
    """Test para verificar create con tenant."""
    # Crear nuevo producto
    product_data = {
        "name": "New Product",
        "description": "New Description",
        "price": 40.0
    }
    product = await test_repo.create(product_data, account_id=1)
    
    # Verificar que se creó correctamente
    assert product.id is not None
    assert product.account_id == 1
    assert product.name == product_data["name"]

async def test_update_with_tenant(test_repo: TestBaseRepository, sample_products):
    """Test para verificar update con filtro de tenant."""
    # Actualizar producto existente
    update_data = {"name": "Updated Name"}
    updated = await test_repo.update(
        sample_products[0].id,
        update_data,
        account_id=1
    )
    assert updated.name == "Updated Name"
    
    # Intentar actualizar producto de otro tenant
    with pytest.raises(ResourceNotFoundError):
        await test_repo.update(
            sample_products[2].id,
            update_data,
            account_id=1
        )

async def test_delete_with_tenant(test_repo: TestBaseRepository, sample_products):
    """Test para verificar delete con filtro de tenant."""
    # Eliminar producto existente
    await test_repo.delete(sample_products[0].id, account_id=1)
    
    # Verificar que se eliminó
    with pytest.raises(ResourceNotFoundError):
        await test_repo.get_by_id(sample_products[0].id, account_id=1)
    
    # Intentar eliminar producto de otro tenant
    with pytest.raises(ResourceNotFoundError):
        await test_repo.delete(sample_products[2].id, account_id=1)

async def test_count_with_tenant(test_repo: TestBaseRepository, sample_products):
    """Test para verificar count con filtro de tenant."""
    # Contar productos de tenant 1
    count = await test_repo.count(account_id=1)
    assert count == 2
    
    # Contar productos de tenant 2
    count = await test_repo.count(account_id=2)
    assert count == 1

async def test_exists_with_tenant(test_repo: TestBaseRepository, sample_products):
    """Test para verificar exists con filtro de tenant."""
    # Verificar existencia de producto en tenant 1
    exists = await test_repo.exists(sample_products[0].id, account_id=1)
    assert exists
    
    # Verificar existencia de producto de otro tenant
    exists = await test_repo.exists(sample_products[2].id, account_id=1)
    assert not exists

async def test_filter_with_tenant(test_repo: TestBaseRepository, sample_products):
    """Test para verificar filter con filtro de tenant."""
    # Filtrar productos por precio y tenant
    products = await test_repo.filter(
        and_(Product.price > 15.0),
        account_id=1
    )
    assert len(products) == 1
    assert products[0].price > 15.0
    assert products[0].account_id == 1 