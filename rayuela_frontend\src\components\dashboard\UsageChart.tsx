"use client";

import { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions,
} from 'chart.js';
import annotationPlugin from 'chartjs-plugin-annotation';
import { Line, Bar } from 'react-chartjs-2';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import {
  baseChartOptions,
  chartColors,
  formatBytes,
  formatNumber,
  formatPercentage,
  UsageDataPoint,
  transformUsageData
} from '@/lib/chart-utils';
import { Badge } from '@/components/ui/badge';
import { InfoIcon } from 'lucide-react';
import {
  Tooltip as UITooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger
} from "@/components/ui/tooltip";

// Registrar los componentes necesarios de Chart.js
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  annotationPlugin
);

interface UsageChartProps {
  data?: UsageDataPoint[];
  isLoading?: boolean;
  error?: Error | null;
  title?: string;
  description?: string;
  apiCallsLimit?: number;
  storageLimit?: number;
}

export default function UsageChart({
  data = [],
  isLoading = false,
  error = null,
  title = "Uso de API",
  description = "Estadísticas de uso en los últimos 30 días",
  apiCallsLimit,
  storageLimit
}: UsageChartProps) {
  const [activeTab, setActiveTab] = useState('apiCalls');
  const [chartData, setChartData] = useState<UsageDataPoint[]>([]);

  useEffect(() => {
    // Use the utility function to transform or generate data
    setChartData(data && data.length > 0 ? data : transformUsageData([]));
  }, [data]);

  // Extend the base chart options with specific settings for usage charts
  const commonOptions: ChartOptions<'line' | 'bar'> = {
    ...baseChartOptions,
    plugins: {
      ...baseChartOptions.plugins,
      legend: {
        display: false,
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        callbacks: {
          title: (context) => {
            return `Fecha: ${context[0].label}`;
          },
          label: (context) => {
            let label = context.dataset.label || '';
            if (label) {
              label += ': ';
            }
            if (context.parsed.y !== null) {
              if (label.includes('Almacenamiento')) {
                label += formatBytes(context.parsed.y * 1024 * 1024); // Convert MB back to bytes for formatting
              } else {
                label += formatNumber(context.parsed.y);
              }
            }
            return label;
          },
          footer: (context) => {
            const datasetLabel = context[0].dataset.label || '';
            if (datasetLabel.includes('Llamadas') && apiCallsLimit) {
              const percentage = Math.min(((context[0].parsed.y / apiCallsLimit) * 100), 100);
              return `${formatPercentage(percentage)} del límite (${formatNumber(apiCallsLimit)})`;
            } else if (datasetLabel.includes('Almacenamiento') && storageLimit) {
              const limitInMB = storageLimit / (1024 * 1024);
              const percentage = Math.min(((context[0].parsed.y / limitInMB) * 100), 100);
              return `${formatPercentage(percentage)} del límite (${formatBytes(storageLimit)})`;
            }
            return '';
          }
        }
      },
    },
    scales: {
      ...baseChartOptions.scales,
      y: {
        ...baseChartOptions.scales?.y,
        ticks: {
          font: {
            size: 11
          },
          callback: function(value) {
            if (this.chart.config._config.type === 'bar' && this.chart.config._config.data.datasets[0].label?.includes('Almacenamiento')) {
              return `${value} MB`;
            }
            return formatNumber(value as number);
          }
        }
      },
    },
  };

  // Datos para el gráfico de llamadas a la API
  const apiCallsData = {
    labels: chartData.map(d => d.date.slice(5)), // Formato MM-DD
    datasets: [
      {
        label: 'Llamadas a la API',
        data: chartData.map(d => d.apiCalls),
        borderColor: chartColors.blue.primary,
        backgroundColor: chartColors.blue.background,
        tension: 0.3,
        fill: true,
      },
    ],
  };

  // Datos para el gráfico de almacenamiento
  const storageData = {
    labels: chartData.map(d => d.date.slice(5)), // Formato MM-DD
    datasets: [
      {
        label: 'Almacenamiento (MB)',
        data: chartData.map(d => d.storage / (1024 * 1024)), // Convertir bytes a MB
        borderColor: chartColors.green.primary,
        backgroundColor: chartColors.green.background,
        tension: 0.3,
      },
    ],
  };

  // Opciones específicas para cada gráfico
  const apiCallsOptions = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'Llamadas a la API por día',
        font: {
          size: 13,
          weight: 'normal'
        },
        padding: {
          top: 10,
          bottom: 10
        }
      },
      annotation: apiCallsLimit ? {
        annotations: {
          limitLine: {
            type: 'line',
            yMin: apiCallsLimit,
            yMax: apiCallsLimit,
            borderColor: chartColors.red.primary,
            borderWidth: 2,
            borderDash: [6, 6],
            label: {
              display: true,
              content: `Límite: ${formatNumber(apiCallsLimit)}`,
              position: 'end',
              backgroundColor: 'rgba(239, 68, 68, 0.7)',
              font: {
                size: 11
              }
            }
          }
        }
      } : undefined,
    },
  };

  const storageOptions = {
    ...commonOptions,
    plugins: {
      ...commonOptions.plugins,
      title: {
        display: true,
        text: 'Almacenamiento utilizado (MB)',
        font: {
          size: 13,
          weight: 'normal'
        },
        padding: {
          top: 10,
          bottom: 10
        }
      },
      annotation: storageLimit ? {
        annotations: {
          limitLine: {
            type: 'line',
            yMin: storageLimit / (1024 * 1024),
            yMax: storageLimit / (1024 * 1024),
            borderColor: chartColors.red.primary,
            borderWidth: 2,
            borderDash: [6, 6],
            label: {
              display: true,
              content: `Límite: ${formatBytes(storageLimit)}`,
              position: 'end',
              backgroundColor: 'rgba(239, 68, 68, 0.7)',
              font: {
                size: 11
              }
            }
          }
        }
      } : undefined,
    },
  };

  // Renderizar un skeleton durante la carga
  if (isLoading) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md">
        <CardHeader>
          <CardTitle><Skeleton className="h-6 w-1/3" /></CardTitle>
          <CardDescription><Skeleton className="h-4 w-1/2" /></CardDescription>
        </CardHeader>
        <CardContent className="h-80">
          <Skeleton className="h-full w-full rounded-md" />
        </CardContent>
      </Card>
    );
  }

  // Renderizar un mensaje de error
  if (error) {
    return (
      <Card className="transition-all duration-300 hover:shadow-md border-red-200 dark:border-red-800">
        <CardHeader>
          <CardTitle className="text-red-500 flex items-center gap-2">
            <InfoIcon className="h-5 w-5" />
            {title} - Error
          </CardTitle>
          <CardDescription className="text-red-400">
            No se pudieron cargar los datos: {error.message}
          </CardDescription>
        </CardHeader>
        <CardContent className="h-80 flex items-center justify-center">
          <div className="text-center">
            <p className="text-red-500 mb-2">Ocurrió un error al cargar los datos de uso.</p>
            <p className="text-sm text-gray-500">Mostrando datos de ejemplo para visualización.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="transition-all duration-300 hover:shadow-md">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl font-semibold">{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>

          {/* Límites como badges */}
          <div className="flex flex-col gap-1">
            {apiCallsLimit && (
              <TooltipProvider>
                <UITooltip>
                  <TooltipTrigger asChild>
                    <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-800">
                      Límite API: {formatNumber(apiCallsLimit)}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Límite máximo de llamadas a la API</p>
                  </TooltipContent>
                </UITooltip>
              </TooltipProvider>
            )}

            {storageLimit && (
              <TooltipProvider>
                <UITooltip>
                  <TooltipTrigger asChild>
                    <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800">
                      Límite Almacenamiento: {formatBytes(storageLimit)}
                    </Badge>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="text-xs">Límite máximo de almacenamiento</p>
                  </TooltipContent>
                </UITooltip>
              </TooltipProvider>
            )}
          </div>
        </div>

        <Tabs defaultValue="apiCalls" className="mt-2" onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger
              value="apiCalls"
              className="transition-all duration-200 data-[state=active]:bg-blue-100 data-[state=active]:text-blue-700 dark:data-[state=active]:bg-blue-900/30 dark:data-[state=active]:text-blue-300"
            >
              Llamadas API
            </TabsTrigger>
            <TabsTrigger
              value="storage"
              className="transition-all duration-200 data-[state=active]:bg-green-100 data-[state=active]:text-green-700 dark:data-[state=active]:bg-green-900/30 dark:data-[state=active]:text-green-300"
            >
              Almacenamiento
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="h-80 pt-4">
        <TabsContent value="apiCalls" className="h-full mt-0">
          <Line
            options={apiCallsOptions as ChartOptions<'line'>}
            data={apiCallsData}
            className="h-full"
          />
        </TabsContent>
        <TabsContent value="storage" className="h-full mt-0">
          <Bar
            options={storageOptions as ChartOptions<'bar'>}
            data={storageData}
            className="h-full"
          />
        </TabsContent>
      </CardContent>
    </Card>
  );
}
