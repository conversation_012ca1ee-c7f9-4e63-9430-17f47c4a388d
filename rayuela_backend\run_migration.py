#!/usr/bin/env python3
"""
Script para ejecutar la migración de Alembic directamente desde Python
"""

import os
import sys
from alembic.config import Config
from alembic import command


def run_migration():
    """Ejecuta la migración de Alembic"""
    try:
        # Configurar Alembic
        alembic_cfg = Config("alembic.ini")

        print("🚀 Ejecutando migración de Alembic...")

        # Ejecutar upgrade
        command.upgrade(alembic_cfg, "head")

        print("✅ Migración completada exitosamente!")

    except Exception as e:
        print(f"❌ Error durante la migración: {e}")
        return False

    return True


if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1)
