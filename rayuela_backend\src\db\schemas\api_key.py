"""
Esquemas Pydantic para API Keys.
"""
from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, Field, ConfigDict

class ApiKeyBase(BaseModel):
    """Base schema for API Keys"""
    name: Optional[str] = Field(None, description="Nombre descriptivo para esta API Key")

class ApiKeyCreate(ApiKeyBase):
    """Schema for creating a new API Key"""
    pass

class ApiKeyUpdate(BaseModel):
    """Schema for updating an API Key"""
    name: Optional[str] = Field(None, description="Nuevo nombre descriptivo para esta API Key")

class ApiKeyResponse(ApiKeyBase):
    """Schema for API Key information (no sensitive data)"""
    id: int = Field(..., description="ID único de la API Key")
    prefix: Optional[str] = Field(None, description="Prefijo de la API Key")
    last_chars: Optional[str] = Field(None, description="Últimos caracteres de la API Key")
    created_at: Optional[datetime] = Field(None, description="Fecha de creación")
    is_active: bool = Field(True, description="Estado de la API Key")
    last_used: Optional[datetime] = Field(None, description="Último uso de la API Key")

    model_config = ConfigDict(from_attributes=True)

class ApiKeyListResponse(BaseModel):
    """Schema for listing API Keys"""
    api_keys: List[ApiKeyResponse] = Field(..., description="Lista de API Keys")
    total: int = Field(..., description="Número total de API Keys")

class NewApiKeyResponse(BaseModel):
    """Schema for a newly created API Key (includes the full key)"""
    id: int = Field(..., description="ID único de la API Key")
    api_key: str = Field(..., description="API Key completa (solo se muestra una vez)")
    name: Optional[str] = Field(None, description="Nombre descriptivo de la API Key")
    prefix: str = Field(..., description="Prefijo de la API Key")
    created_at: datetime = Field(..., description="Fecha de creación")
    message: str = Field(..., description="Mensaje informativo")