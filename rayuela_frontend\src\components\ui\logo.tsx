"use client";

import React from 'react';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface LogoProps {
  variant?: 'default' | 'marketing';
  className?: string;
  linkClassName?: string;
  href?: string;
}

export function Logo({
  variant = 'default',
  className,
  linkClassName,
  href = '/',
}: LogoProps) {
  const isMarketing = variant === 'marketing';
  
  const logoContent = (
    <div className={cn(
      "font-bold transition-colors",
      isMarketing ? "text-3xl" : "text-xl",
      className
    )}>
      {isMarketing ? 'Rayuela.ai' : '<PERSON>uel<PERSON>'}
    </div>
  );

  if (href) {
    return (
      <Link href={href} className={cn("hover:opacity-90 transition-opacity", linkClassName)}>
        {logoContent}
      </Link>
    );
  }

  return logoContent;
}
