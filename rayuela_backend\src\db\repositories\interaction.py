"""
Repositorios para la gestión de interacciones y búsquedas.
"""

from sqlalchemy import select, func, and_, or_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
from src.db import models, schemas
from src.db.enums import InteractionType
from .base import BaseRepository


class InteractionRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.Interaction)

    async def create(
        self, interaction_create: schemas.InteractionCreate
    ) -> models.Interaction:
        try:
            interaction = models.Interaction(
                **interaction_create.model_dump(), account_id=self.account_id
            )
            self.db.add(interaction)
            await self.db.refresh(interaction)
            return interaction
        except SQLAlchemyError as e:
            await self._handle_error("creating interaction", e)

    async def get_by_user_and_product(
        self,
        user_id: int,
        product_id: int,
        interaction_type: Optional[InteractionType] = None,
    ) -> List[models.Interaction]:
        """Obtener interacciones por usuario y producto."""
        try:
            query = select(models.Interaction).filter(
                models.Interaction.end_user_id == user_id,
                models.Interaction.product_id == product_id,
                models.Interaction.account_id == self.account_id,
            )

            if interaction_type:
                query = query.filter(
                    models.Interaction.interaction_type == interaction_type
                )

            query = query.order_by(models.Interaction.created_at.desc())

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching interactions by user and product", e)
            return []

    async def get_user_interactions(
        self,
        user_id: int,
        limit: int = 100,
        interaction_type: Optional[InteractionType] = None,
    ) -> List[models.Interaction]:
        """Obtener interacciones de un usuario."""
        try:
            query = select(models.Interaction).filter(
                models.Interaction.end_user_id == user_id,
                models.Interaction.account_id == self.account_id,
            )

            if interaction_type:
                query = query.filter(
                    models.Interaction.interaction_type == interaction_type
                )

            query = query.order_by(models.Interaction.created_at.desc()).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching user interactions", e)
            return []

    async def get_product_interactions(
        self,
        product_id: int,
        limit: int = 100,
        interaction_type: Optional[InteractionType] = None,
    ) -> List[models.Interaction]:
        """Obtener interacciones de un producto."""
        try:
            query = select(models.Interaction).filter(
                models.Interaction.product_id == product_id,
                models.Interaction.account_id == self.account_id,
            )

            if interaction_type:
                query = query.filter(
                    models.Interaction.interaction_type == interaction_type
                )

            query = query.order_by(models.Interaction.created_at.desc()).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching product interactions", e)
            return []

    async def get_recent_interactions(
        self, limit: int = 100, interaction_type: Optional[InteractionType] = None
    ) -> List[models.Interaction]:
        """Obtener interacciones recientes."""
        try:
            query = select(models.Interaction).filter(
                models.Interaction.account_id == self.account_id,
            )

            if interaction_type:
                query = query.filter(
                    models.Interaction.interaction_type == interaction_type
                )

            query = query.order_by(models.Interaction.created_at.desc()).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching recent interactions", e)
            return []

    async def get_interaction_counts_by_type(
        self, start_date: Optional[datetime] = None, end_date: Optional[datetime] = None
    ) -> Dict[str, int]:
        """Obtener conteo de interacciones por tipo."""
        try:
            # Establecer fechas por defecto si no se proporcionan
            if not end_date:
                end_date = datetime.utcnow()
            if not start_date:
                start_date = end_date - timedelta(days=30)

            # Construir consulta
            query = (
                select(
                    models.Interaction.interaction_type,
                    func.count(models.Interaction.interaction_id).label("count"),
                )
                .filter(
                    models.Interaction.account_id == self.account_id,
                    models.Interaction.created_at >= start_date,
                    models.Interaction.created_at <= end_date,
                )
                .group_by(models.Interaction.interaction_type)
            )

            result = await self.db.execute(query)
            counts = {str(row[0]): row[1] for row in result}

            # Asegurar que todos los tipos de interacción estén representados
            for interaction_type in InteractionType:
                if str(interaction_type) not in counts:
                    counts[str(interaction_type)] = 0

            return counts
        except SQLAlchemyError as e:
            await self._handle_error("fetching interaction counts by type", e)
            return {}

    async def get_popular_products(
        self, limit: int = 10, days: int = 30
    ) -> List[Dict[str, Any]]:
        """Obtener productos populares basados en interacciones recientes."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)

            # Consulta para contar interacciones por producto
            query = (
                select(
                    models.Interaction.product_id,
                    func.count(models.Interaction.interaction_id).label(
                        "interaction_count"
                    ),
                )
                .filter(
                    models.Interaction.account_id == self.account_id,
                    models.Interaction.created_at >= start_date,
                )
                .group_by(models.Interaction.product_id)
                .order_by(func.count(models.Interaction.interaction_id).desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            popular_products = []

            for row in result:
                product_id = row[0]
                interaction_count = row[1]

                # Obtener detalles del producto
                product_query = select(models.Product).filter(
                    models.Product.id == product_id,
                    models.Product.account_id == self.account_id,
                )
                product_result = await self.db.execute(product_query)
                product = product_result.scalars().first()

                if product:
                    popular_products.append(
                        {"product": product, "interaction_count": interaction_count}
                    )

            return popular_products
        except SQLAlchemyError as e:
            await self._handle_error("fetching popular products", e)
            return []

    async def get_popular_categories(
        self, limit: int = 5, days: int = 30
    ) -> List[Dict[str, Any]]:
        """Obtener categorías populares basadas en interacciones recientes."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)

            # Consulta para obtener productos con interacciones
            products_query = (
                select(
                    models.Interaction.product_id,
                    func.count(models.Interaction.interaction_id).label("interaction_count"),
                )
                .filter(
                    models.Interaction.account_id == self.account_id,
                    models.Interaction.created_at >= start_date,
                )
                .group_by(models.Interaction.product_id)
            )

            products_result = await self.db.execute(products_query)
            product_interactions = {row[0]: row[1] for row in products_result}

            if not product_interactions:
                return []

            # Obtener categorías de los productos con interacciones
            product_ids = list(product_interactions.keys())
            categories_query = (
                select(
                    models.Product.category,
                    func.count(models.Product.id).label("product_count"),
                )
                .filter(
                    models.Product.id.in_(product_ids),
                    models.Product.account_id == self.account_id,
                )
                .group_by(models.Product.category)
            )

            categories_result = await self.db.execute(categories_query)

            # Calcular popularidad de categorías sumando interacciones de sus productos
            category_popularity = {}

            # Obtener todos los productos por categoría
            for category, _ in categories_result:
                if not category:
                    continue

                products_in_category_query = select(models.Product.id).filter(
                    models.Product.category == category,
                    models.Product.account_id == self.account_id,
                )
                products_in_category_result = await self.db.execute(products_in_category_query)
                products_in_category = [row[0] for row in products_in_category_result]

                # Sumar interacciones de productos en esta categoría
                total_interactions = sum(
                    product_interactions.get(pid, 0) for pid in products_in_category
                )

                category_popularity[category] = {
                    "name": category,
                    "interaction_count": total_interactions,
                    "product_count": len(products_in_category)
                }

            # Ordenar categorías por popularidad
            popular_categories = sorted(
                category_popularity.values(),
                key=lambda x: x["interaction_count"],
                reverse=True
            )

            return popular_categories[:limit]

        except SQLAlchemyError as e:
            await self._handle_error("fetching popular categories", e)
            return []


class SearchRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.Search)

    async def create(self, search_create: schemas.SearchCreate) -> models.Search:
        try:
            search = models.Search(
                **search_create.model_dump(), account_id=self.account_id
            )
            self.db.add(search)
            await self.db.refresh(search)
            return search
        except SQLAlchemyError as e:
            await self._handle_error("creating search", e)

    async def get_recent_searches(
        self, limit: int = 10, user_id: Optional[int] = None
    ) -> List[models.Search]:
        try:
            query = select(models.Search)
            query = self._add_tenant_filter(query)

            if user_id:
                query = query.filter(models.Search.end_user_id == user_id)

            query = query.order_by(models.Search.created_at.desc()).limit(limit)
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching recent searches", e)

    async def get_popular_searches(
        self, limit: int = 10, days: int = 30
    ) -> List[Dict[str, Any]]:
        """Obtener términos de búsqueda populares."""
        try:
            start_date = datetime.utcnow() - timedelta(days=days)

            # Consulta para contar búsquedas por término
            query = (
                select(
                    models.Search.query_text,
                    func.count(models.Search.search_id).label("search_count"),
                )
                .filter(
                    models.Search.account_id == self.account_id,
                    models.Search.created_at >= start_date,
                )
                .group_by(models.Search.query_text)
                .order_by(func.count(models.Search.search_id).desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            popular_searches = [
                {"query_text": row[0], "count": row[1]} for row in result
            ]

            return popular_searches
        except SQLAlchemyError as e:
            await self._handle_error("fetching popular searches", e)
            return []

    async def get_search_with_results(self, search_id: int) -> Optional[Dict[str, Any]]:
        """Obtener una búsqueda con sus resultados."""
        try:
            search = await self.get_by_id(search_id)
            if not search:
                return None

            # Obtener productos relacionados con la búsqueda
            products = []
            if search.results_metadata and "product_ids" in search.results_metadata:
                product_ids = search.results_metadata["product_ids"]

                if product_ids:
                    query = select(models.Product).filter(
                        models.Product.product_id.in_(product_ids),
                        models.Product.account_id == self.account_id,
                    )
                    result = await self.db.execute(query)
                    products = result.scalars().all()

            return {
                "search": search,
                "products": products,
            }
        except SQLAlchemyError as e:
            await self._handle_error("fetching search with results", e)
            return None

    async def get_searches_by_date_range(
        self, start_date: datetime, end_date: datetime, limit: int = 100
    ) -> List[models.Search]:
        """Obtener búsquedas en un rango de fechas."""
        try:
            query = (
                select(models.Search)
                .filter(
                    models.Search.account_id == self.account_id,
                    models.Search.created_at >= start_date,
                    models.Search.created_at <= end_date,
                )
                .order_by(models.Search.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching searches by date range", e)
            return []
