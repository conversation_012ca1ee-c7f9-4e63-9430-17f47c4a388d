# Architecture Diagram

```mermaid
graph TD
    %% Styles
    classDef db fill:#c9ddf5,stroke:#333,stroke-width:2px;
    classDef cache fill:#f5d4c9,stroke:#333,stroke-width:2px;
    classDef service fill:#d4f5c9,stroke:#333,stroke-width:2px;
    classDef external fill:#f5f5c9,stroke:#333,stroke-width:2px;
    classDef middleware fill:#e0e0e0,stroke:#666,stroke-width:1px,stroke-dasharray: 5 5;
    classDef worker fill:#f9e79f,stroke:#333,stroke-width:2px;
    classDef ml fill:#d2b4de,stroke:#333,stroke-width:2px;

    %% Components
    Client["Client (Browser/App/API Consumer)"] --> LB[Load Balancer / Cloud Run Entrypoint];

    subgraph "FastAPI Application (main.py)"
        LB --> MW[Middleware Chain];

        subgraph "Middleware Processing"
            direction TB
            MW --> ErrorHandling(Error Handling):::middleware;
            ErrorHandling --> CORS(CORS):::middleware;
            CORS --> TrustedHost(Trusted Hosts):::middleware;
            TrustedHost --> GZip(GZip):::middleware;
            GZip --> Timing(Timing Logger):::middleware;
            Timing --> RateLimitMW(Rate Limiter):::middleware;
            RateLimitMW --> AuditMW(Audit Logger):::middleware;
            AuditMW --> CacheInvalidation(Cache Invalidation):::middleware;
            CacheInvalidation --> SecurityHeaders(Security Headers):::middleware;
            SecurityHeaders --> Routers{API Routers};
        end

        Routers --> PublicRouter["/api/v1 (Public)"];
        Routers --> PrivateRouter["/api/v1 (Private)<br/>(Requires Auth)"];

        %% Public Endpoints
        PublicRouter --> AuthEP["/auth (Login, etc)"];
        PublicRouter --> AccountsEP_Public["/accounts (Create)"];

        %% Private Endpoints (Require get_current_account / get_current_system_user)
        PrivateRouter --> AccountsEP_Private["/accounts (Read, Update, Keys)"];
        PrivateRouter --> SystemUsersEP["/system-users"];
        PrivateRouter --> EndUsersEP["/end-users"];
        PrivateRouter --> ProductsEP["/products"];
        PrivateRouter --> InteractionsEP["/interactions"];
        PrivateRouter --> RecommendationsEP["/recommendations"];
        PrivateRouter --> PipelineEP["/pipeline (Train, Status)"];
        PrivateRouter --> AnalyticsEP["/analytics"];
        PrivateRouter --> SubscriptionEP["/subscription"];
        PrivateRouter --> DataIngestionEP["/batch"];
        %% Add other private endpoints as needed

        %% Endpoint Interactions
        AuthEP --> SecurityUtils(core/security);
        AuthEP --> SystemUserRepo(System User Repository);
        AuthEP --> AccountRepo(Account Repository);
        AuthEP --> RedisCache[(Redis Cache)]:::cache;
            %% For login attempts

        AccountsEP_Public --> AccountRepo;
        AccountsEP_Private --> AccountRepo;
        AccountsEP_Private --> AuditRepo(Audit Log Repository); 
			%% For getting logs
        AccountsEP_Private --> RedisCache; 
			%% For usage stats (if stored there)

        SystemUsersEP --> SystemUserRepo;
        SystemUsersEP --> RoleRepo(Role Repository);
        SystemUsersEP --> SystemUserRoleRepo(System User Role Repository);
        SystemUsersEP --> SecurityUtils;

        EndUsersEP --> EndUserRepo(End User Repository);
        EndUsersEP --> LimitSvc(Limit Service):::service;

        ProductsEP --> ProductRepo(Product Repository);
        ProductsEP --> LimitSvc;

        InteractionsEP --> InteractionRepo(Interaction Repository);
        InteractionsEP --> LimitSvc;
        InteractionsEP --> RedisCache; 
			%% For interaction rate limiting

        RecommendationsEP --> RecSvc(Recommendation Service):::service;
        RecommendationsEP --> LimitSvc;

        PipelineEP --> TrainingJobRepo(Training Job Repository);
        PipelineEP --> CeleryBroker[(Redis Broker)]:::cache;
        PipelineEP --> RecSvc; 
			%% For status/metrics
        PipelineEP --> LimitSvc;

        AnalyticsEP --> APIAnalyticsSvc(API Analytics Service):::service;

        SubscriptionEP --> SubscriptionSvc(Subscription Service):::service;
        SubscriptionEP --> RateLimitConfig(Rate Limit Config);

        DataIngestionEP --> ProductRepo;
        DataIngestionEP --> EndUserRepo;
        DataIngestionEP --> InteractionRepo;
        DataIngestionEP --> LimitSvc;
        DataIngestionEP --> BackgroundTasks(FastAPI Background Tasks);

        BackgroundTasks --> ProcessBatchData(process_batch_data);
        ProcessBatchData --> ProductRepo;
        ProcessBatchData --> EndUserRepo;
        ProcessBatchData --> InteractionRepo;

    end

    %% Services Layer
    subgraph "Services Layer"
        direction LR
        LimitSvc --> SubscriptionSvc;
        LimitSvc --> AccountRepo;
        LimitSvc --> ProductRepo; 
			%% For counting items
        LimitSvc --> EndUserRepo; 
			%% For counting users

        SubscriptionSvc --> SubscriptionRepo(Subscription Repository);
        SubscriptionSvc --> AccountRepo; 
			%% For usage metrics

        PermissionSvc(Permission Service):::service --> SystemUserRoleRepo;
        PermissionSvc --> RolePermissionRepo(Role Permission Repository);

        RecSvc --> ArtifactMgr(Model Artifact Manager):::ml;
        RecSvc --> MetricsTracker(Metrics Tracker):::ml;
        RecSvc --> Evaluator(Recommendation Evaluator):::ml;
        RecSvc --> RecCache(Recommendation Cache):::cache;
        RecSvc --> CollabTrainer(Collaborative Trainer):::ml;
        RecSvc --> ContentTrainer(Content Trainer):::ml;
        RecSvc --> InteractionRepo; 
			%% To get data for training/recs
        RecSvc --> ProductRepo; 
			%% To get data for training/recs
        RecSvc --> SystemEventLogger(System Event Logger);

        APIAnalyticsSvc --> AccountUsageMetricsRepo(Account Usage Metrics Repository);
        APIAnalyticsSvc --> EndpointMetricsRepo(Endpoint Metrics Repository);
    end

    %% Data Access Layer (Repositories)
    subgraph "Data Access Layer (Repositories)"
        direction TB
        AccountRepo --> PostgresDB[(PostgreSQL DB<br/>Partitioned by account_id)]:::db;
        SubscriptionRepo --> PostgresDB;
        SystemUserRepo --> PostgresDB;
        EndUserRepo --> PostgresDB;
        ProductRepo --> PostgresDB;
        InteractionRepo --> PostgresDB;
        SearchRepo(Search Repository) --> PostgresDB;
        AuditRepo --> PostgresDB;
        NotificationRepo(Notification Repository) --> PostgresDB;
        RoleRepo --> PostgresDB;
        PermissionRepo(Permission Repository) --> PostgresDB;
        RolePermissionRepo --> PostgresDB;
        SystemUserRoleRepo --> PostgresDB;
        TrainingJobRepo --> PostgresDB;
        ModelMetadataRepo(Model Metadata Repository) --> PostgresDB;
        AccountUsageMetricsRepo --> PostgresDB;
        EndpointMetricsRepo --> PostgresDB;
    end

    %% Caching Layer
    subgraph "Caching & Broker Layer"
        RateLimitMW --> RedisCache;
        RateLimitConfig --> RedisCache;
        RecCache --> RedisCache;
        MetricsTracker --> RedisCache;
        CeleryBroker --> RedisCache; 
			%% Celery uses Redis
        CeleryBackend[(Redis Backend)]:::cache --> RedisCache; 
			%% Celery uses Redis
    end

    %% Background Worker (Celery)
    subgraph "Background Worker (Celery)"
        direction TB
        CeleryBroker --> CeleryWorker[Celery Worker Process]:::worker;
        CeleryWorker --> CeleryTasks(celery_tasks.py):::worker;
        CeleryTasks --> RecSvc; 
			%% Calls train_models
        CeleryTasks --> TrainingJobRepo; 
			%% Updates job status
        CeleryTasks --> CeleryBackend; 
			%% Reports results
    end

    %% ML Pipeline Components (Used by RecSvc & Celery Tasks)
    subgraph "ML Pipeline Components"
        direction TB
        ArtifactMgr --> GCS[(Google Cloud Storage<br/>or Local FS)]:::external;
        ArtifactMgr --> ModelMetadataRepo;
        MetricsTracker --> ModelMetadataRepo;
        CollabTrainer --> ModelMetadataRepo; 
			%% Potentially to save params
        ContentTrainer --> ModelMetadataRepo; 
			%% Potentially to save params
    end

    %% Logging & Auditing
    subgraph "Logging & Auditing"
        AuditMW --> AuditTask(write_audit_log_to_db_task);
        AuditTask --> AuditRepo;
        SystemEventLogger --> AuditTask;
        APIAnalyticsSvc --> PostgresDB; 
			%% Writes metrics
        TimingMiddleware --> BaseLogger(utils/base_logger.py);
        ErrorHandling --> BaseLogger;
        BaseLogger --> CloudLogging[(Google Cloud Logging)]:::external;
        BaseLogger --> ConsoleOutput["Console (JSON)"];
    end

    %% Dependencies & Utils
    subgraph "Core Dependencies & Utils"
        Deps(core/deps.py) --> AccountRepo;
        Deps --> SystemUserRepo;
        Deps --> SubscriptionSvc;
        Deps --> PermissionSvc;
        Deps --> LimitSvc;
        Deps --> RedisCache;
        Deps --> SecurityUtils;

        SecurityUtils --> RedisCache; 
			%% For token revocation
    end

    %% External Services
    subgraph "External Services"
        GCS;
        CloudLogging;
    end

    %% Link Services to Repositories
    RecSvc --> ModelMetadataRepo;
    RecSvc --> TrainingJobRepo;
    APIAnalyticsSvc --> PostgresDB;

    %% Link Endpoints to Services/Repos
    RecommendationsEP --> RecSvc;
    PipelineEP --> RecSvc;
    PipelineEP --> TrainingJobRepo;
    AnalyticsEP --> APIAnalyticsSvc;
    SubscriptionEP --> SubscriptionSvc;
    DataIngestionEP --> LimitSvc;
    DataIngestionEP --> ProductRepo;
    DataIngestionEP --> EndUserRepo;
    DataIngestionEP --> InteractionRepo;
``` 