from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, func, Index, PrimaryKeyConstraint, ForeignKeyConstraint, JSON, Identity
from sqlalchemy.orm import relationship
from datetime import datetime
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class ModelMetric(Base, TenantMixin):
    """Modelo para almacenar métricas específicas de modelos de recomendación"""
    __tablename__ = "model_metrics"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, Identity(), primary_key=True)
    model_metadata_id = Column(Integer, nullable=False)
    metric_name = Column(String(100), nullable=False, index=True)
    metric_value = Column(Float, nullable=False)
    metric_type = Column(String(50), nullable=False)  # offline, online, system
    timestamp = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())

    # Metadatos adicionales (opcional)
    details = Column(String, nullable=True)

    # Campos adicionales de ModelMetrics
    ndcg = Column(Float, nullable=True)
    diversity = Column(Float, nullable=True)
    novelty = Column(Float, nullable=True)
    coverage = Column(Float, nullable=True)

    # Métricas de rendimiento
    latency_ms = Column(Float, nullable=True)
    throughput = Column(Float, nullable=True)

    # Datos adicionales
    additional_metrics = Column(JSON, nullable=True)

    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        ForeignKeyConstraint(
            ["account_id", "model_metadata_id"],
            ["artifact_metadata.account_id", "artifact_metadata.id"],
            ondelete="CASCADE",
        ),
        Index("idx_metric_model", "account_id", "model_metadata_id"),
        Index("idx_metric_name_type", "account_id", "metric_name", "metric_type"),
        Index("idx_metric_timestamp", "timestamp"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    )

    # Relationships
    account = relationship("Account", back_populates="model_metrics")
    model_metadata = relationship(
        "ModelMetadata",
        foreign_keys=[account_id, model_metadata_id],
        primaryjoin="and_(ModelMetric.account_id==ModelMetadata.account_id, ModelMetric.model_metadata_id==ModelMetadata.id)",
        back_populates="metrics"
    )
    model = relationship(
        "ModelMetadata",
        foreign_keys=[account_id, model_metadata_id],
        primaryjoin="and_(ModelMetric.account_id==ModelMetadata.account_id, ModelMetric.model_metadata_id==ModelMetadata.id)",
        back_populates="model_metrics",
        overlaps="model_metadata,metrics"
    )

    def __repr__(self):
        return f"<ModelMetric(id={self.id}, model_metadata_id={self.model_metadata_id}, metric_name={self.metric_name})>"
