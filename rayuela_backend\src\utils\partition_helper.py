import asyncpg
import asyncio
import time
from typing import Set, <PERSON>tional, <PERSON><PERSON>
from sqlalchemy.ext.asyncio import AsyncConnection, AsyncSession
from sqlalchemy.sql import text
from src.core.config import settings
from src.core.exceptions import (
    handle_partition_errors,
    PartitionError,
    PartitionCreationError,
)
from src.utils.base_logger import log_info, log_error, log_warning


# Nombres de tablas permitidos (puede seguir siendo útil para el script de creación)
ALLOWED_TABLES: Set[str] = {
    "accounts",
    "subscriptions",
    "system_users",
    "roles",
    "permissions",
    "role_permissions",
    "system_user_roles",
    "audit_logs",
    "end_users",
    "products",
    "interactions",
    "searches",
    "artifact_metadata",
    "training_jobs",
    "account_usage_metrics",  # Añadir tablas faltantes si son particionadas
}


def validate_table_name(table_name: str):
    """Valida que el nombre de la tabla esté en la lista de tablas permitidas."""
    if table_name not in ALLOWED_TABLES:
        # Podría lanzar un error o simplemente loggear y retornar False
        log_warning(
            f"Table name '{table_name}' is not in the allowed list for partitioning."
        )
        raise ValueError(f"Invalid or non-partitioned table name: {table_name}")


def get_partition_range(account_id: int) -> Tuple[int, int]:
    """Calcula el rango de partición para un account_id dado."""
    partition_size = settings.PARTITION_SIZE
    if partition_size <= 0:
        raise ValueError("PARTITION_SIZE must be positive.")
    start_range = (account_id // partition_size) * partition_size
    end_range = start_range + partition_size
    return start_range, end_range


def get_partition_name(table_name: str, account_id: int) -> str:
    """Calcula el nombre esperado de la partición para una tabla y account_id."""
    validate_table_name(table_name)  # Validar primero
    start_range, end_range = get_partition_range(account_id)
    return f"{table_name}_p_{start_range}_{end_range}"
