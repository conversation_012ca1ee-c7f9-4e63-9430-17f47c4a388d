import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import <PERSON>TT<PERSON><PERSON>xception
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.schemas.recommendation import RecommendationStrategy, STRATEGY_CONFIGS
from src.ml_pipeline.serving_engine import ServingEngine
from src.ml_pipeline.post_processing_service import PostProcessingService
from src.ml_pipeline.fallback_handler import FallbackHandler
from main import app


@pytest.fixture
def mock_db():
    return AsyncMock(spec=AsyncSession)


@pytest.fixture
def mock_serving_engine():
    engine = ServingEngine(MagicMock())
    engine.model_loader = AsyncMock()
    engine.collab_recommender = AsyncMock()
    engine.content_recommender = AsyncMock()
    engine.hybrid_recommender = MagicMock()
    return engine


@pytest.fixture
def mock_post_processing():
    service = PostProcessingService()
    service.contextual_filter = MagicMock()
    service.contextual_reranker = MagicMock()
    service.diversification_engine = MagicMock()
    service.recency_booster = MagicMock()
    service.ltr_applier = MagicMock()
    return service


@pytest.mark.asyncio
async def test_get_strategy_params():
    """Test that calculate_weights returns correct parameters for each strategy"""
    serving_engine = ServingEngine(MagicMock())
    
    # Test each strategy
    for strategy in RecommendationStrategy:
        params = serving_engine.hybrid_recommender._calculate_weights(
            [], [], strategy=strategy.value
        )
        expected_config = STRATEGY_CONFIGS[strategy]
        
        collab_weight, content_weight = params
        assert collab_weight == expected_config.collab_weight
        assert content_weight == expected_config.content_weight


@pytest.mark.asyncio
async def test_get_strategy_params_fallback():
    """Test that calculate_weights falls back to balanced when invalid strategy is provided"""
    serving_engine = ServingEngine(MagicMock())
    
    # Test with invalid strategy
    params = serving_engine.hybrid_recommender._calculate_weights(
        [], [], strategy="invalid_strategy"
    )
    expected_config = STRATEGY_CONFIGS[RecommendationStrategy.BALANCED]
    
    collab_weight, content_weight = params
    assert collab_weight == expected_config.collab_weight
    assert content_weight == expected_config.content_weight


@pytest.mark.asyncio
async def test_apply_recency_boost():
    """Test that apply_recency_boost correctly boosts recent items"""
    service = PostProcessingService()
    
    # Create test candidates with different timestamps
    from datetime import datetime, timedelta
    now = datetime.now()
    
    candidates = [
        {"item_id": 1, "score": 0.8, "created_at": now.isoformat()},  # New item
        {"item_id": 2, "score": 0.9, "created_at": (now - timedelta(days=15)).isoformat()},  # Recent item
        {"item_id": 3, "score": 1.0, "created_at": (now - timedelta(days=60)).isoformat()},  # Older item
    ]
    
    # Apply boost with factor 0.5
    boosted = service.recency_booster.apply_recency_boost(candidates, recency_boost=0.5)
    
    # New item should have highest boost
    assert boosted[0]["score"] > 0.8
    assert boosted[0].get("recency_boosted") is True
    
    # Recent item should have good boost
    assert boosted[1]["score"] > 0.9
    assert boosted[1].get("recency_boosted") is True
    
    # Older item should have smaller boost
    assert boosted[2]["score"] > 1.0
    assert boosted[2].get("recency_boosted") is True
    
    # Test with zero boost (should return unchanged)
    no_boost = service.recency_booster.apply_recency_boost(candidates, recency_boost=0)
    assert no_boost[0]["score"] == 0.8
    assert no_boost[1]["score"] == 0.9
    assert no_boost[2]["score"] == 1.0


@pytest.mark.asyncio
async def test_recommendation_with_strategy(mock_db, mock_serving_engine):
    """Test that get_candidate_recommendations passes strategy parameter to combine_recommendations"""
    # Setup mocks
    mock_serving_engine.collab_recommender.get_recommendations.return_value = [
        {"item_id": 1, "score": 0.8}
    ]
    mock_serving_engine.content_recommender.get_recommendations.return_value = [
        {"item_id": 2, "score": 0.9}
    ]
    mock_serving_engine.hybrid_recommender.combine_recommendations.return_value = [
        {"item_id": 1, "score": 0.85}, 
        {"item_id": 2, "score": 0.82}
    ]
    
    # Test with different strategies
    for strategy in RecommendationStrategy:
        result = await mock_serving_engine.get_candidate_recommendations(
            db=mock_db,
            account_id=1,
            user_id=1,
            n_recommendations=5,
            recommendation_type="hybrid",
            strategy=strategy.value
        )
        
        # Verify strategy was passed to combine_recommendations
        mock_serving_engine.hybrid_recommender.combine_recommendations.assert_called_with(
            mock_serving_engine.collab_recommender.get_recommendations.return_value,
            mock_serving_engine.content_recommender.get_recommendations.return_value,
            5,
            user_id=1,
            user_history=None,  # Este valor puede ser diferente según la implementación
            is_new_user=False,
            strategy=strategy.value
        )
        
        assert len(result) == 2
        assert result[0]["item_id"] == 1
        assert result[1]["item_id"] == 2


@pytest.mark.asyncio
async def test_api_endpoint_with_strategy():
    """Test that the API endpoint correctly handles the strategy parameter"""
    from fastapi.testclient import TestClient
    from src.api.v1.endpoints.recommendations import router
    
    # Mock dependencies
    with patch("src.api.v1.endpoints.recommendations.get_current_account") as mock_get_account:
        mock_get_account.return_value = MagicMock(account_id=1)
        
        with patch("src.api.v1.endpoints.recommendations.get_db") as mock_get_db:
            mock_get_db.return_value = AsyncMock()
            
            with patch("src.api.v1.endpoints.recommendations.get_pagination_params") as mock_pagination:
                mock_pagination.return_value = (0, 10)
                
                with patch("src.api.v1.endpoints.recommendations.get_limit_service") as mock_limit_service:
                    mock_limit_service.return_value = MagicMock()
                    
                    with patch("src.api.v1.endpoints.recommendations.serving_engine") as mock_serving:
                        mock_serving.get_candidate_recommendations.return_value = [
                            {"item_id": 1, "name": "Test Product", "score": 0.9}
                        ]
                        
                        with patch("src.api.v1.endpoints.recommendations.post_processing") as mock_post_processing:
                            mock_post_processing.apply_post_processing.return_value = [
                                {"item_id": 1, "name": "Test Product", "score": 0.95}
                            ]
                            
                            # Create test client
                            client = TestClient(app)
                            
                            # Test each strategy
                            for strategy in RecommendationStrategy:
                                response = client.get(f"/api/v1/recommendations/personalized/1?strategy={strategy.value}")
                                
                                assert response.status_code == 200
                                assert "items" in response.json()
                                
                                # Verify strategy was passed to service
                                mock_serving.get_candidate_recommendations.assert_called_with(
                                    db=mock_get_db.return_value,
                                    account_id=1,
                                    user_id=1,
                                    n_recommendations=20,  # Este valor puede ser diferente según la implementación
                                    recommendation_type="hybrid",
                                    strategy=strategy.value
                                )
