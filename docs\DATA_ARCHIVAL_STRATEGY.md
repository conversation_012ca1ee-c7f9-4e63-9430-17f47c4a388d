# Estrategia de Archivado de Datos Históricos

## 📋 Resumen

Este documento describe la implementación de la estrategia de archivado de datos históricos para las tablas de alto volumen (`interactions` y `audit_logs`) en Rayuela. La estrategia reemplaza la eliminación directa con un proceso de **archivado a GCS seguido de eliminación**, proporcionando una solución costo-efectiva para la retención de datos a largo plazo.

## 🎯 Objetivos

- **Reducir costos de Cloud SQL**: Mover datos históricos a almacenamiento más barato (GCS)
- **Mejorar rendimiento**: Mantener tablas activas más pequeñas y rápidas
- **Preservar datos**: Mantener acceso a datos históricos para análisis futuros
- **Cumplimiento**: Satisfacer requisitos de auditoría y retención de datos

## 🏗️ Arquitectura

### Componentes Principales

1. **DataArchivalService** (`src/services/data_archival_service.py`)
   - Servicio central para archivado de datos
   - Soporte para formatos Parquet, CSV y JSON
   - Verificación de integridad de exportación
   - Manejo de errores y reintentos

2. **Tareas Celery de Archivado**
   - `archive_and_cleanup_old_audit_logs`: Archiva y limpia audit logs
   - `archive_and_cleanup_old_interactions`: Archiva y limpia interactions
   - Ejecución programada y manual

3. **Endpoints API**
   - `/maintenance/archive-and-cleanup-audit-logs`
   - `/maintenance/archive-and-cleanup-interactions`
   - `/maintenance/archived-files/{table_name}`

## 📊 Flujo de Archivado

```mermaid
graph TD
    A[Tarea Celery Programada] --> B[Identificar Datos Antiguos]
    B --> C[Exportar a GCS en Lotes]
    C --> D{Verificar Exportación}
    D -->|Exitosa| E[Eliminar de Cloud SQL]
    D -->|Fallida| F[Registrar Error y Reintentar]
    E --> G[Confirmar Operación]
    F --> C
```

### Proceso Detallado

1. **Identificación**: Consultar datos más antiguos que la fecha de corte
2. **Exportación**: Procesar en lotes y exportar a GCS en formato Parquet
3. **Verificación**: Confirmar integridad y completitud de la exportación
4. **Eliminación**: Solo eliminar de Cloud SQL si la exportación fue exitosa
5. **Logging**: Registrar todas las operaciones para auditoría

## 🗂️ Estructura de Archivos en GCS

```
gs://bucket-name/archived_data/
├── audit_logs/
│   ├── 2024/01/15/
│   │   ├── audit_logs_20240115_120000_account_123_batch_1.parquet
│   │   ├── audit_logs_20240115_120000_account_123_batch_2.parquet
│   │   └── audit_logs_20240115_120000_all_accounts_batch_1.parquet
│   └── 2024/01/16/
└── interactions/
    ├── 2024/01/15/
    │   ├── interactions_20240115_120000_account_456_batch_1.parquet
    │   └── interactions_20240115_120000_account_456_batch_2.parquet
    └── 2024/01/16/
```

### Convenciones de Nomenclatura

- **Formato**: `{table}_{timestamp}_{account}_{batch}.{format}`
- **Timestamp**: `YYYYMMDD_HHMMSS` en UTC
- **Account**: `account_{id}` o `all_accounts`
- **Batch**: `batch_{number}` para procesamiento en lotes

## ⚙️ Configuración

### Variables de Entorno

```bash
# Configuración de archivado
GCS_BUCKET_NAME=rayuela-production-bucket
ARCHIVAL_FORMAT=parquet                    # parquet, csv, json
ARCHIVAL_VERIFY_EXPORT=true               # Verificar exportación
ARCHIVAL_COMPRESSION=gzip                 # gzip, snappy, none
```

### Configuración en `config.py`

```python
# Data Archival Configuration (GCS only)
GCS_ARCHIVAL_PATH: str = "archived_data"
ARCHIVAL_FORMAT: str = os.getenv("ARCHIVAL_FORMAT", "parquet")
ARCHIVAL_VERIFY_EXPORT: bool = os.getenv("ARCHIVAL_VERIFY_EXPORT", "true").lower() == "true"
ARCHIVAL_COMPRESSION: str = os.getenv("ARCHIVAL_COMPRESSION", "gzip")
```

## 📅 Programación de Tareas

### Beat Schedule (Celery)

```python
# Nuevas tareas de archivado (recomendadas)
"archive-and-cleanup-old-audit-logs": {
    "task": "archive_and_cleanup_old_audit_logs",
    "schedule": 86400.0,  # Una vez al día
    "kwargs": {"days_to_keep": 90, "account_id": None, "batch_size": 10000},
    "options": {"queue": "maintenance"},
},
"archive-and-cleanup-old-interactions": {
    "task": "archive_and_cleanup_old_interactions", 
    "schedule": 86400.0 * 7,  # Una vez a la semana
    "kwargs": {"days_to_keep": 180, "account_id": None, "batch_size": 10000},
    "options": {"queue": "maintenance"},
},
```

### Políticas de Retención

- **Audit Logs**: 90 días en Cloud SQL, archivado indefinido en GCS
- **Interactions**: 180 días en Cloud SQL, archivado indefinido en GCS
- **Batch Size**: 10,000 registros por lote (configurable)

## 🔧 Uso Manual

### API Endpoints

#### Archivar y Limpiar Audit Logs

```bash
curl -X POST "http://localhost:8000/api/v1/maintenance/archive-and-cleanup-audit-logs" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "days_to_keep": 90,
    "account_id": null,
    "batch_size": 10000,
    "run_async": true
  }'
```

#### Archivar y Limpiar Interactions

```bash
curl -X POST "http://localhost:8000/api/v1/maintenance/archive-and-cleanup-interactions" \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "days_to_keep": 180,
    "account_id": 123,
    "batch_size": 5000,
    "run_async": true
  }'
```

#### Listar Archivos Archivados

```bash
curl -X GET "http://localhost:8000/api/v1/maintenance/archived-files/audit_logs?account_id=123&start_date=2024-01-01T00:00:00Z" \
  -H "Authorization: Bearer $TOKEN"
```

### Ejecución Directa con Celery

```bash
# Archivar audit logs para cuenta específica
celery -A src.workers.celery_app call archive_and_cleanup_old_audit_logs \
  --kwargs='{"days_to_keep": 90, "account_id": 123, "batch_size": 5000}'

# Archivar interactions para todas las cuentas
celery -A src.workers.celery_app call archive_and_cleanup_old_interactions \
  --kwargs='{"days_to_keep": 180, "account_id": null, "batch_size": 10000}'
```

## 💰 Impacto en Costos

### Estimaciones de Ahorro

**Antes (Solo Cloud SQL)**:
- Audit logs: ~1GB/mes → $0.17/mes (Cloud SQL)
- Interactions: ~5GB/mes → $0.85/mes (Cloud SQL)
- **Total mensual**: ~$1.02

**Después (Cloud SQL + GCS)**:
- Datos activos (90-180 días): ~0.5GB → $0.085/mes (Cloud SQL)
- Datos archivados: ~5.5GB → $0.11/mes (GCS Standard)
- **Total mensual**: ~$0.195
- **Ahorro**: ~80% ($0.825/mes)

### Factores de Costo

1. **Cloud SQL**: $0.17/GB/mes (almacenamiento SSD)
2. **GCS Standard**: $0.02/GB/mes (almacenamiento estándar)
3. **Transferencia**: Mínima (misma región)
4. **Operaciones**: Despreciables para el volumen esperado

## 🔍 Monitoreo y Alertas

### Métricas Clave

- **Tasa de éxito de archivado**: >95%
- **Tiempo de procesamiento**: <30 minutos por tabla
- **Tamaño de archivos**: Consistente con datos esperados
- **Errores de verificación**: <1%

### Logs de Auditoría

Todas las operaciones de archivado se registran con:
- Timestamp de inicio y fin
- Número de registros procesados
- Archivos creados en GCS
- Errores y reintentos
- Duración total de la operación

## 🚨 Manejo de Errores

### Estrategias de Recuperación

1. **Reintentos Automáticos**: Hasta 3 intentos con backoff exponencial
2. **Reducción de Batch Size**: Si falla un lote, reducir tamaño y reintentar
3. **Verificación de Integridad**: Confirmar exportación antes de eliminar
4. **Rollback Seguro**: No eliminar si la exportación falla

### Escenarios de Error Comunes

- **Falta de espacio en GCS**: Alertar y pausar archivado
- **Problemas de conectividad**: Reintentar con delay
- **Datos corruptos**: Registrar y continuar con siguiente lote
- **Permisos insuficientes**: Fallar rápido y alertar

## 📈 Migración y Rollout

### Fase 1: Implementación (Completada)
- ✅ Crear `DataArchivalService`
- ✅ Implementar tareas Celery de archivado
- ✅ Agregar endpoints API
- ✅ Configurar beat schedule

### Fase 2: Pruebas (En Progreso)
- 🔄 Pruebas unitarias del servicio de archivado
- 🔄 Pruebas de integración con GCS
- 🔄 Validación de formatos de archivo
- 🔄 Pruebas de rendimiento con datos reales

### Fase 3: Despliegue (Pendiente)
- ⏳ Despliegue en staging
- ⏳ Monitoreo de métricas
- ⏳ Despliegue en producción
- ⏳ Migración gradual de tareas legacy

## 🔗 Referencias

- [HIGH_VOLUME_TABLES.md](./HIGH_VOLUME_TABLES.md) - Documentación de tablas de alto volumen
- [Google Cloud Storage Pricing](https://cloud.google.com/storage/pricing)
- [Parquet Format Documentation](https://parquet.apache.org/docs/)
- [Celery Beat Documentation](https://docs.celeryproject.org/en/stable/userguide/periodic-tasks.html)
