"""
Celery tasks for the usage meter service.
"""
import asyncio
from celery import shared_task
from datetime import datetime, timezone
from typing import Dict, Any

from src.db.session import get_db
from src.core.redis_utils import get_redis
from src.services.usage_meter_service import UsageMeterService
from src.utils.base_logger import log_info, log_error, log_warning


@shared_task(name="sync_api_call_counters", queue="maintenance")
def sync_api_call_counters() -> Dict[str, Any]:
    """
    Sync API call counters from Redis to the database.
    This task should be scheduled to run periodically (e.g., every 15 minutes).
    
    Returns:
        Dictionary with status and results
    """
    try:
        # Run the async function in the event loop
        return asyncio.run(_sync_api_call_counters_async())
    except Exception as e:
        log_error(f"Error syncing API call counters: {str(e)}")
        return {"status": "error", "message": str(e)}


async def _sync_api_call_counters_async() -> Dict[str, Any]:
    """
    Async implementation of sync_api_call_counters.
    
    Returns:
        Dictionary with status and results
    """
    try:
        # Get database session
        db_generator = get_db()
        db = await anext(db_generator)
        
        # Get Redis connection
        redis = await get_redis()
        
        # Create usage meter service
        usage_meter = UsageMeterService(db, redis)
        
        # Sync counters to database
        results = await usage_meter.sync_counters_to_db()
        
        # Close database session
        await db.close()
        
        return {
            "status": "success",
            "message": f"Synced API call counters for {len(results)} accounts",
            "results": results
        }
    except Exception as e:
        log_error(f"Error in _sync_api_call_counters_async: {str(e)}")
        raise


@shared_task(name="reset_monthly_counters", queue="maintenance")
def reset_monthly_counters() -> Dict[str, Any]:
    """
    Reset monthly API call counters for all accounts.
    This task should be scheduled to run at the beginning of each month.
    
    Returns:
        Dictionary with status and results
    """
    try:
        # Run the async function in the event loop
        return asyncio.run(_reset_monthly_counters_async())
    except Exception as e:
        log_error(f"Error resetting monthly counters: {str(e)}")
        return {"status": "error", "message": str(e)}


async def _reset_monthly_counters_async() -> Dict[str, Any]:
    """
    Async implementation of reset_monthly_counters.
    
    Returns:
        Dictionary with status and results
    """
    try:
        # Get database session
        db_generator = get_db()
        db = await anext(db_generator)
        
        # Get Redis connection
        redis = await get_redis()
        
        # Create usage meter service
        usage_meter = UsageMeterService(db, redis)
        
        # Reset monthly counters
        result = await usage_meter.reset_monthly_counters()
        
        # Close database session
        await db.close()
        
        return {
            "status": "success",
            "message": "Reset monthly API call counters for all accounts",
            "result": result
        }
    except Exception as e:
        log_error(f"Error in _reset_monthly_counters_async: {str(e)}")
        raise
