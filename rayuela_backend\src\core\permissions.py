"""
Permission constants and helper functions for RBAC.
"""

from typing import Dict, Set, List, Optional
from src.db.enums import PermissionType, RoleType

# Define role-permission mappings
ROLE_PERMISSIONS: Dict[RoleType, Set[PermissionType]] = {
    RoleType.ADMIN: {
        # Admin has all permissions
        PermissionType.READ,
        PermissionType.WRITE,
        PermissionType.DELETE,
        PermissionType.ADMIN,
        # Resource-specific permissions
        PermissionType.PRODUCT_READ,
        PermissionType.PRODUCT_CREATE,
        PermissionType.PRODUCT_UPDATE,
        PermissionType.PRODUCT_DELETE,
        PermissionType.USER_READ,
        PermissionType.USER_CREATE,
        PermissionType.USER_UPDATE,
        PermissionType.USER_DELETE,
        PermissionType.SYSTEM_USER_READ,
        PermissionType.SYSTEM_USER_CREATE,
        PermissionType.SYSTEM_USER_UPDATE,
        PermissionType.SYSTEM_USER_DELETE,
        PermissionType.ROLE_READ,
        PermissionType.ROLE_CREATE,
        PermissionType.ROLE_UPDATE,
        PermissionType.ROLE_DELETE,
        PermissionType.PERMISSION_ASSIGN,
        PermissionType.ANALYTICS_READ,
        PermissionType.MODEL_READ,
        PermissionType.MODEL_CREATE,
        PermissionType.MODEL_UPDATE,
        PermissionType.MODEL_DELETE,
        PermissionType.TRAINING_JOB_READ,
        PermissionType.TRAINING_JOB_CREATE,
        PermissionType.TRAINING_JOB_UPDATE,
        PermissionType.TRAINING_JOB_CANCEL,
    },
    RoleType.EDITOR: {
        # Editor has read and write permissions
        PermissionType.READ,
        PermissionType.WRITE,
        # Resource-specific permissions
        PermissionType.PRODUCT_READ,
        PermissionType.PRODUCT_CREATE,
        PermissionType.PRODUCT_UPDATE,
        PermissionType.USER_READ,
        PermissionType.USER_CREATE,
        PermissionType.USER_UPDATE,
        PermissionType.MODEL_READ,
        PermissionType.MODEL_CREATE,
        PermissionType.MODEL_UPDATE,
        PermissionType.TRAINING_JOB_READ,
        PermissionType.TRAINING_JOB_CREATE,
        PermissionType.TRAINING_JOB_UPDATE,
    },
    RoleType.VIEWER: {
        # Viewer has only read permissions
        PermissionType.READ,
        # Resource-specific permissions
        PermissionType.PRODUCT_READ,
        PermissionType.USER_READ,
        PermissionType.MODEL_READ,
        PermissionType.TRAINING_JOB_READ,
    },
}

# Resource-specific permission groups
PRODUCT_PERMISSIONS = {
    "read": {PermissionType.PRODUCT_READ, PermissionType.READ},
    "create": {PermissionType.PRODUCT_CREATE, PermissionType.WRITE},
    "update": {PermissionType.PRODUCT_UPDATE, PermissionType.WRITE},
    "delete": {PermissionType.PRODUCT_DELETE, PermissionType.DELETE},
}

USER_PERMISSIONS = {
    "read": {PermissionType.USER_READ, PermissionType.READ},
    "create": {PermissionType.USER_CREATE, PermissionType.WRITE},
    "update": {PermissionType.USER_UPDATE, PermissionType.WRITE},
    "delete": {PermissionType.USER_DELETE, PermissionType.DELETE},
}

SYSTEM_USER_PERMISSIONS = {
    "read": {PermissionType.SYSTEM_USER_READ, PermissionType.ADMIN},
    "create": {PermissionType.SYSTEM_USER_CREATE, PermissionType.ADMIN},
    "update": {PermissionType.SYSTEM_USER_UPDATE, PermissionType.ADMIN},
    "delete": {PermissionType.SYSTEM_USER_DELETE, PermissionType.ADMIN},
}

ROLE_MANAGEMENT_PERMISSIONS = {
    "read": {PermissionType.ROLE_READ, PermissionType.ADMIN},
    "create": {PermissionType.ROLE_CREATE, PermissionType.ADMIN},
    "update": {PermissionType.ROLE_UPDATE, PermissionType.ADMIN},
    "delete": {PermissionType.ROLE_DELETE, PermissionType.ADMIN},
    "assign": {PermissionType.PERMISSION_ASSIGN, PermissionType.ADMIN},
}

MODEL_PERMISSIONS = {
    "read": {PermissionType.MODEL_READ, PermissionType.READ},
    "create": {PermissionType.MODEL_CREATE, PermissionType.WRITE},
    "update": {PermissionType.MODEL_UPDATE, PermissionType.WRITE},
    "delete": {PermissionType.MODEL_DELETE, PermissionType.DELETE},
}

TRAINING_JOB_PERMISSIONS = {
    "read": {PermissionType.TRAINING_JOB_READ, PermissionType.READ},
    "create": {PermissionType.TRAINING_JOB_CREATE, PermissionType.WRITE},
    "update": {PermissionType.TRAINING_JOB_UPDATE, PermissionType.WRITE},
    "cancel": {PermissionType.TRAINING_JOB_CANCEL, PermissionType.DELETE},
}

ANALYTICS_PERMISSIONS = {
    "read": {PermissionType.ANALYTICS_READ, PermissionType.ADMIN},
}


# Helper functions
def get_required_permissions_for_resource(
    resource_type: str, action: str
) -> Optional[Set[PermissionType]]:
    """
    Get the required permissions for a specific resource and action.

    Args:
        resource_type: The type of resource (product, user, etc.)
        action: The action to perform (read, create, update, delete)

    Returns:
        A set of required permissions or None if the resource/action is not found
    """
    resource_map = {
        "product": PRODUCT_PERMISSIONS,
        "user": USER_PERMISSIONS,
        "system_user": SYSTEM_USER_PERMISSIONS,
        "role": ROLE_MANAGEMENT_PERMISSIONS,
        "model": MODEL_PERMISSIONS,
        "training_job": TRAINING_JOB_PERMISSIONS,
        "analytics": ANALYTICS_PERMISSIONS,
    }

    if resource_type not in resource_map or action not in resource_map[resource_type]:
        return None

    return resource_map[resource_type][action]


def get_permissions_for_role(role_type: RoleType) -> Set[PermissionType]:
    """
    Get all permissions for a specific role type.

    Args:
        role_type: The role type

    Returns:
        A set of permissions for the role
    """
    return ROLE_PERMISSIONS.get(role_type, set())
