"""
Tests para las funciones de API key.
"""
import pytest
from src.core.security.api_key import (
    generate_api_key,
    generate_api_key_simple,
    hash_api_key,
    verify_api_key
)

def test_generate_api_key():
    """Test para verificar la generación de API keys."""
    # Generar una API key
    api_key, api_key_hash = generate_api_key()

    # Verificar formato
    assert isinstance(api_key, str)
    assert isinstance(api_key_hash, str)
    assert api_key.startswith("sk_")  # Prefijo por defecto
    assert len(api_key) > 32  # Longitud suficiente

    # Verificar que el hash funciona
    assert verify_api_key(api_key, api_key_hash)

def test_generate_api_key_simple():
    """Test para verificar la generación de API keys simples."""
    # Generar una API key simple
    api_key = generate_api_key_simple()

    # Verificar formato
    assert isinstance(api_key, str)
    assert len(api_key) > 32  # Longitud suficiente

def test_hash_api_key():
    """Test para verificar el hashing de API keys."""
    # Generar una API key
    api_key = "sk_test_key"

    # Hashear la API key
    hashed_key = hash_api_key(api_key)

    # Verificar formato
    assert isinstance(hashed_key, str)
    assert len(hashed_key) == 64  # SHA-256 produce 64 caracteres hexadecimales

def test_verify_api_key():
    """Test para verificar la validación de API keys."""
    # Generar una API key
    api_key = "sk_test_key"

    # Hashear la API key
    hashed_key = hash_api_key(api_key)

    # Verificar que la API key original es válida
    assert verify_api_key(api_key, hashed_key) is True

    # Verificar que una API key incorrecta no es válida
    wrong_key = "sk_wrong_key"
    assert verify_api_key(wrong_key, hashed_key) is False

def test_api_key_uniqueness():
    """Test para verificar que las API keys generadas son únicas."""
    # Generar múltiples API keys
    keys = [generate_api_key()[0] for _ in range(10)]

    # Verificar que todas son únicas
    assert len(set(keys)) == len(keys)

def test_api_key_case_sensitivity():
    """Test para verificar que las API keys son case-sensitive."""
    # Generar una API key
    api_key = "sk_test_key"

    # Hashear la API key
    hashed_key = hash_api_key(api_key)

    # Verificar que una versión en mayúsculas no es válida
    assert verify_api_key(api_key.upper(), hashed_key) is False