"""Add user preferences for cold start recommendations

Revision ID: add_user_preferences_cold_start
Revises: 4fdd0755dc85
Create Date: 2024-12-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_user_preferences_cold_start'
down_revision = '4fdd0755dc85'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Add new preference columns to end_users table
    op.add_column('end_users', sa.Column('external_id', sa.String(255), nullable=False, server_default=''))
    op.add_column('end_users', sa.Column('preferred_categories', postgresql.JSON(), nullable=True))
    op.add_column('end_users', sa.Column('disliked_categories', postgresql.JSON(), nullable=True))
    op.add_column('end_users', sa.Column('preferred_brands', postgresql.JSON(), nullable=True))
    op.add_column('end_users', sa.Column('disliked_brands', postgresql.JSON(), nullable=True))
    op.add_column('end_users', sa.Column('price_range_min', sa.Integer(), nullable=True))
    op.add_column('end_users', sa.Column('price_range_max', sa.Integer(), nullable=True))
    op.add_column('end_users', sa.Column('demographic_info', postgresql.JSON(), nullable=True))
    op.add_column('end_users', sa.Column('onboarding_preferences', postgresql.JSON(), nullable=True))
    op.add_column('end_users', sa.Column('is_active', sa.Boolean(), nullable=True, server_default='true'))
    
    # Add indexes for better query performance
    op.create_index('idx_end_user_preferred_categories', 'end_users', ['account_id', 'preferred_categories'], postgresql_using='gin')
    op.create_index('idx_end_user_price_range', 'end_users', ['account_id', 'price_range_min', 'price_range_max'])
    
    # Add unique constraint for external_id per account
    op.create_unique_constraint('uq_end_user_external_id', 'end_users', ['account_id', 'external_id'])


def downgrade() -> None:
    # Remove indexes
    op.drop_index('idx_end_user_preferred_categories', table_name='end_users')
    op.drop_index('idx_end_user_price_range', table_name='end_users')
    
    # Remove unique constraint
    op.drop_constraint('uq_end_user_external_id', 'end_users', type_='unique')
    
    # Remove columns
    op.drop_column('end_users', 'is_active')
    op.drop_column('end_users', 'onboarding_preferences')
    op.drop_column('end_users', 'demographic_info')
    op.drop_column('end_users', 'price_range_max')
    op.drop_column('end_users', 'price_range_min')
    op.drop_column('end_users', 'disliked_brands')
    op.drop_column('end_users', 'preferred_brands')
    op.drop_column('end_users', 'disliked_categories')
    op.drop_column('end_users', 'preferred_categories')
    op.drop_column('end_users', 'external_id')
