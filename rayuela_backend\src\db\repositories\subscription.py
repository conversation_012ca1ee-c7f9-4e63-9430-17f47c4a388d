"""
Repositorio para la gestión de suscripciones.
"""

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from src.db import models, schemas
from .base import BaseRepository
from src.utils.base_logger import log_error


class SubscriptionRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.Subscription)

    async def create_or_update(
        self, subscription_create: schemas.SubscriptionCreate
    ) -> Optional[models.Subscription]:
        """Crea o actualiza una suscripción para una cuenta."""
        try:
            if self.account_id is None:
                raise ValueError("account_id must be provided")

            # Verificar si ya existe una suscripción para esta cuenta
            existing_subscription = await self.get_by_account(self.account_id)

            if existing_subscription:
                # Si ya existe, actualizar en lugar de crear
                for key, value in subscription_create.model_dump().items():
                    setattr(existing_subscription, key, value)
                return existing_subscription
            else:
                # Si no existe, crear una nueva
                # Crear un diccionario con los datos y añadir account_id
                subscription_data = subscription_create.model_dump()
                subscription_data["account_id"] = self.account_id

                # Crear la suscripción
                subscription = models.Subscription(**subscription_data)
                self.db.add(subscription)
                await self.db.flush()
                return subscription
        except SQLAlchemyError as e:
            await self._handle_error("creating or updating subscription", e)
            return None

    async def get_by_account(self, account_id: int) -> Optional[models.Subscription]:
        try:
            query = select(models.Subscription).filter(
                models.Subscription.account_id == account_id,
                models.Subscription.is_active.is_(True),
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching subscription", e)
            return None

    async def get_by_stripe_subscription_id(self, stripe_subscription_id: str) -> Optional[models.Subscription]:
        """Obtiene una suscripción por su ID de suscripción de Stripe."""
        try:
            query = select(models.Subscription).filter(
                models.Subscription.stripe_subscription_id == stripe_subscription_id,
                models.Subscription.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching subscription by Stripe ID", e)
            return None

    # Sobrescribimos el método delete para mantener compatibilidad
    async def delete(self, id: int) -> bool:
        """Elimina una suscripción por ID."""
        try:
            subscription = await self.get_by_id(id)
            if subscription:
                await self.db.delete(subscription)
                return True
            return False
        except SQLAlchemyError as e:
            await self._handle_error("deleting subscription", e)
            return False

    async def get_all(self, skip: int = 0, limit: int = 100) -> List[models.Subscription]:
        """Obtiene todas las suscripciones."""
        try:
            query = select(models.Subscription).filter(
                models.Subscription.account_id == self.account_id
            )
            query = query.offset(skip).limit(limit)
            result = await self.db.execute(query)
            return list(result.scalars().all())
        except SQLAlchemyError as e:
            await self._handle_error("fetching all subscriptions", e)
            return []

    # Sobrescribimos el método update para mantener compatibilidad
    async def update(
        self, id: int, obj_in: Dict[str, Any]
    ) -> Optional[models.Subscription]:
        """Actualiza una suscripción existente."""
        try:
            subscription = await self.get_by_id(id)
            if not subscription:
                return None

            for key, value in obj_in.items():
                setattr(subscription, key, value)

            await self.db.flush()
            return subscription
        except SQLAlchemyError as e:
            await self._handle_error("updating subscription", e)
            return None
