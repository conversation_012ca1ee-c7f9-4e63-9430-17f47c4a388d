# Integración OpenAPI en Rayuela Frontend

Este documento describe la integración automática del frontend con la especificación OpenAPI del backend, lo que permite generar automáticamente tipos y funciones cliente para interactuar con la API.

## Objetivo

Automatizar la generación de código TypeScript basado en la especificación OpenAPI del backend para:

1. Mantener sincronizados los tipos y endpoints del frontend con el backend
2. Reducir errores derivados de inconsistencias entre ambas capas
3. Acelerar el desarrollo al eliminar la necesidad de mantener manualmente las interfaces
4. Facilitar la refactorización y evolución de la API

## Configuración Implementada

### 1. Herramientas y Dependencias

Se han agregado las siguientes dependencias:

- **orval**: Generador de código TypeScript desde OpenAPI
- **axios**: Cliente HTTP usado para las peticiones generadas
- **ts-node**: Para ejecutar scripts TypeScript
- **node-fetch**: Para descargar la especificación OpenAPI

### 2. Scripts

Se han añadido los siguientes scripts al `package.json`:

- **fetch-openapi**: Descarga la especificación OpenAPI del backend
- **generate-api**: Ejecuta la generación de código desde la especificación
- **prebuild**: Configura la generación automática antes de cada build

### 3. Estructura de archivos

```
src/
└── lib/
    ├── api.ts                  # API existente (a migrar gradualmente)
    ├── api-wrapper.ts          # Ejemplo de migración gradual
    ├── openapi/                # Especificación OpenAPI descargada
    │   └── openapi.json        # Archivo JSON de la especificación
    └── generated/              # Código generado automáticamente
        ├── api-client.ts       # Cliente Axios personalizado
        ├── migration-helper.ts # Utilidades para la migración 
        ├── schemas/            # Tipos/interfaces generados
        └── */                  # Funciones por recurso/endpoint
```

## Funcionamiento

### Proceso de Generación

1. **Descarga de Especificación**: El script `fetch-openapi.ts` descarga la especificación OpenAPI del backend y la guarda localmente.

2. **Generación de Código**: Orval lee la especificación y genera:
   - Interfaces TypeScript para todos los esquemas/modelos
   - Funciones cliente para cada endpoint
   - Tipos para parámetros y respuestas

3. **Cliente Personalizado**: Se integra con un cliente Axios personalizado que:
   - Maneja autenticación (token JWT y API Key)
   - Procesa errores de forma consistente
   - Mantiene la misma lógica del cliente anterior

### Estrategia de Migración

Se propone una migración gradual para no interrumpir el desarrollo:

1. **Fase 1: Generación y coexistencia**
   - Generar el código desde la especificación OpenAPI
   - Mantener el sistema existente funcionando sin cambios
   - Crear wrappers que usen internamente el código generado

2. **Fase 2: Migración de componentes**
   - Actualizar progresivamente las importaciones en los componentes
   - Migrar de `import { func } from '@/lib/api'` a `import { func } from '@/lib/api-wrapper'`

3. **Fase 3: Consolidación**
   - Una vez migrados todos los componentes, renombrar api-wrapper.ts a api.ts
   - Opcionalmente, considerar usar directamente las funciones generadas

## Uso

### Generación manual

```bash
# Descargar especificación y generar código
npm run generate-api

# Solo descargar especificación
npm run fetch-openapi
```

### Uso desde componentes (modo actual)

```typescript
import { getMe } from '@/lib/api';
// o en la nueva versión:
import { getMe } from '@/lib/api-wrapper';

// El uso sigue siendo el mismo
const { data } = useSWR('user', () => getMe(token, apiKey));
```

### Uso directo del código generado

```typescript
import { getUsersMe } from '@/lib/generated/users';
import { convertAuthOptions } from '@/lib/generated/migration-helper';

const fetchUserData = async () => {
  const response = await getUsersMe({}, convertAuthOptions(token, apiKey));
  return response.data;
};
```

## Ventajas

1. **Consistencia garantizada**: Los tipos se generan directamente desde el backend
2. **Detección temprana de cambios**: Si el backend cambia, el código generado reflejará esos cambios
3. **Autocompletado y validación**: El editor mostrará sugerencias precisas de parámetros y respuestas
4. **Documentación integrada**: Los comentarios de la API se integran en las definiciones de tipos

## Consideraciones Futuras

1. **Integración CI/CD**: Automatizar la generación como parte del pipeline de CI/CD
2. **Versionado de API**: Manejar múltiples versiones de la API si es necesario
3. **Pruebas**: Añadir tests para verificar la correcta generación y funcionamiento
4. **Desarrollo local**: Mejorar el flujo de desarrollo local con regeneración automática 