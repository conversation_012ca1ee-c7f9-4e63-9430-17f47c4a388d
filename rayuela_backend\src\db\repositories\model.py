"""
Repositorios para la gestión de modelos de machine learning y métricas.
"""

from sqlalchemy import select, func, and_, or_, text
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any, Literal
from datetime import datetime, timed<PERSON>ta
from fastapi import HTTPException
from src.db import models, schemas
from src.db.enums import TrainingJobStatus
from .base import BaseRepository


class ModelMetadataRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.ModelMetadata)

    async def create(
        self, metadata_create: schemas.ModelMetadataCreate
    ) -> models.ModelMetadata:
        try:
            metadata = models.ModelMetadata(
                **metadata_create.model_dump(), account_id=self.account_id
            )
            self.db.add(metadata)
            await self.db.refresh(metadata)
            return metadata
        except SQLAlchemyError as e:
            await self._handle_error("creating model metadata", e)

    async def get_latest_model(self, model_type: str) -> Optional[models.ModelMetadata]:
        """Obtener el modelo más reciente de un tipo específico."""
        try:
            query = (
                select(models.ModelMetadata)
                .filter(
                    models.ModelMetadata.model_type == model_type,
                    models.ModelMetadata.account_id == self.account_id,
                    models.ModelMetadata.is_active == True,
                )
                .order_by(models.ModelMetadata.created_at.desc())
            )

            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching latest model", e)
            return None

    async def get_model_versions(
        self, model_type: str, limit: int = 10
    ) -> List[models.ModelMetadata]:
        """Obtener versiones de un tipo de modelo específico."""
        try:
            query = (
                select(models.ModelMetadata)
                .filter(
                    models.ModelMetadata.model_type == model_type,
                    models.ModelMetadata.account_id == self.account_id,
                )
                .order_by(models.ModelMetadata.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching model versions", e)
            return []

    async def update_model_metrics(
        self, model_id: int, metrics: Dict[str, Any]
    ) -> Optional[models.ModelMetadata]:
        """Actualizar métricas de un modelo."""
        try:
            model = await self.get_by_id(model_id)
            if not model:
                return None

            # Actualizar métricas
            if model.performance_metrics is None:
                model.performance_metrics = {}

            model.performance_metrics.update(metrics)
            model.updated_at = datetime.utcnow()

            await self.db.refresh(model)
            return model
        except SQLAlchemyError as e:
            await self._handle_error("updating model metrics", e)
            return None

    async def deactivate_model(self, model_id: int) -> bool:
        """Desactivar un modelo."""
        try:
            model = await self.get_by_id(model_id)
            if not model:
                return False

            model.is_active = False
            model.updated_at = datetime.utcnow()

            return True
        except SQLAlchemyError as e:
            await self._handle_error("deactivating model", e)
            return False

    async def get_model_with_training_job(
        self, model_id: int
    ) -> Optional[Dict[str, Any]]:
        """Obtener un modelo con su trabajo de entrenamiento asociado."""
        try:
            model = await self.get_by_id(model_id)
            if not model:
                return None

            # Obtener trabajo de entrenamiento asociado
            query = select(models.TrainingJob).filter(
                models.TrainingJob.model_id == model_id,
                models.TrainingJob.account_id == self.account_id,
            )

            result = await self.db.execute(query)
            training_job = result.scalars().first()

            return {
                "model": model,
                "training_job": training_job,
            }
        except SQLAlchemyError as e:
            await self._handle_error("fetching model with training job", e)
            return None

    async def compare_models(
        self, model_id1: int, model_id2: int
    ) -> Optional[Dict[str, Any]]:
        """Comparar dos modelos."""
        try:
            model1 = await self.get_by_id(model_id1)
            model2 = await self.get_by_id(model_id2)

            if not model1 or not model2:
                return None

            # Comparar métricas
            metrics_comparison = {}
            if model1.performance_metrics and model2.performance_metrics:
                all_metrics = set(model1.performance_metrics.keys()).union(
                    set(model2.performance_metrics.keys())
                )

                for metric in all_metrics:
                    value1 = model1.performance_metrics.get(metric)
                    value2 = model2.performance_metrics.get(metric)

                    if value1 is not None and value2 is not None:
                        diff = value1 - value2
                        metrics_comparison[metric] = {
                            "model1": value1,
                            "model2": value2,
                            "diff": diff,
                            "percent_change": (
                                (diff / value2) * 100 if value2 != 0 else None
                            ),
                        }
                    else:
                        metrics_comparison[metric] = {
                            "model1": value1,
                            "model2": value2,
                            "diff": None,
                            "percent_change": None,
                        }

            return {
                "model1": model1,
                "model2": model2,
                "metrics_comparison": metrics_comparison,
            }
        except SQLAlchemyError as e:
            await self._handle_error("comparing models", e)
            return None


class TrainingJobRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.TrainingJob)

    async def create(self, job_create: schemas.TrainingJobCreate) -> models.TrainingJob:
        try:
            job = models.TrainingJob(
                **job_create.model_dump(), account_id=self.account_id
            )
            self.db.add(job)
            await self.db.refresh(job)
            return job
        except SQLAlchemyError as e:
            await self._handle_error("creating training job", e)

    async def update_status(
        self, job_id: int, status: TrainingJobStatus, message: Optional[str] = None
    ) -> Optional[models.TrainingJob]:
        """Actualizar estado de un trabajo de entrenamiento."""
        try:
            job = await self.get_by_id(job_id)
            if not job:
                return None

            job.status = status
            if message:
                job.status_message = message

            job.updated_at = datetime.utcnow()

            # Si el trabajo ha terminado, actualizar fecha de finalización
            if status in [
                TrainingJobStatus.COMPLETED,
                TrainingJobStatus.FAILED,
                TrainingJobStatus.CANCELLED,
            ]:
                job.completed_at = datetime.utcnow()

            await self.db.refresh(job)
            return job
        except SQLAlchemyError as e:
            await self._handle_error("updating training job status", e)
            return None

    async def get_active_jobs(self) -> List[models.TrainingJob]:
        """Obtener trabajos de entrenamiento activos."""
        try:
            active_statuses = [
                TrainingJobStatus.QUEUED,
                TrainingJobStatus.RUNNING,
                TrainingJobStatus.VALIDATING,
            ]

            query = (
                select(models.TrainingJob)
                .filter(
                    models.TrainingJob.status.in_(active_statuses),
                    models.TrainingJob.account_id == self.account_id,
                )
                .order_by(models.TrainingJob.created_at.asc())
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching active training jobs", e)
            return []

    async def get_jobs_by_status(
        self, status: TrainingJobStatus, limit: int = 10
    ) -> List[models.TrainingJob]:
        """Obtener trabajos de entrenamiento por estado."""
        try:
            query = (
                select(models.TrainingJob)
                .filter(
                    models.TrainingJob.status == status,
                    models.TrainingJob.account_id == self.account_id,
                )
                .order_by(models.TrainingJob.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching training jobs by status", e)
            return []

    async def cancel_job(self, job_id: int, reason: str) -> bool:
        """Cancelar un trabajo de entrenamiento."""
        try:
            job = await self.get_by_id(job_id)
            if not job:
                return False

            # Solo se pueden cancelar trabajos que no hayan terminado
            if job.status in [
                TrainingJobStatus.COMPLETED,
                TrainingJobStatus.FAILED,
                TrainingJobStatus.CANCELLED,
            ]:
                return False

            job.status = TrainingJobStatus.CANCELLED
            job.status_message = f"Cancelled: {reason}"
            job.updated_at = datetime.utcnow()
            job.completed_at = datetime.utcnow()

            return True
        except SQLAlchemyError as e:
            await self._handle_error("cancelling training job", e)
            return False

    async def get_job_with_model(self, job_id: int) -> Optional[Dict[str, Any]]:
        """Obtener un trabajo de entrenamiento con su modelo asociado."""
        try:
            job = await self.get_by_id(job_id)
            if not job:
                return None

            # Obtener modelo asociado
            model = None
            if job.model_id:
                query = select(models.ModelMetadata).filter(
                    models.ModelMetadata.model_id == job.model_id,
                    models.ModelMetadata.account_id == self.account_id,
                )

                result = await self.db.execute(query)
                model = result.scalars().first()

            return {
                "job": job,
                "model": model,
            }
        except SQLAlchemyError as e:
            await self._handle_error("fetching job with model", e)
            return None

    async def get_last_successful(self) -> Optional[models.TrainingJob]:
        """Obtener el último trabajo de entrenamiento exitoso."""
        try:
            query = (
                select(models.TrainingJob)
                .filter(
                    models.TrainingJob.status == TrainingJobStatus.COMPLETED,
                    models.TrainingJob.account_id == self.account_id,
                )
                .order_by(models.TrainingJob.completed_at.desc())
                .limit(1)
            )

            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching last successful training job", e)
            return None


# NOTA: Esta clase está comentada porque el modelo models.Metrics no existe.
# Las métricas se manejan a través de AccountUsageMetrics, EndpointMetrics y ModelMetadata.
# class MetricsRepository(BaseRepository):
#     def __init__(self, db: AsyncSession, account_id: int):
#         # Pasamos el modelo y el account_id para filtrar automáticamente
#         super().__init__(db, account_id=account_id, model=models.Metrics)

#     async def _get_interaction_metrics(
#         self,
#         start_date: datetime,
#         end_date: datetime,
#         granularity: Literal["day", "week", "month"] = "day",
#     ) -> List[Dict[str, Any]]:
#         """Obtener métricas de interacciones con agregación por tiempo."""
#         try:
#             # Definir la expresión de agrupación según la granularidad
#             if granularity == "day":
#                 date_trunc = func.date_trunc("day", models.Interaction.created_at)
#             elif granularity == "week":
#                 date_trunc = func.date_trunc("week", models.Interaction.created_at)
#             else:  # month
#                 date_trunc = func.date_trunc("month", models.Interaction.created_at)
#
#             # Consulta para contar interacciones por tipo y tiempo
#             query = select(
#                 date_trunc.label("date"),
#                 models.Interaction.interaction_type,
#                 func.count().label("count")
#             ).filter(
#                 models.Interaction.account_id == self.account_id,
#                 models.Interaction.created_at >= start_date,
#                 models.Interaction.created_at <= end_date,
#             ).group_by(
#                 date_trunc,
#                 models.Interaction.interaction_type
#             ).order_by(date_trunc)
#
#             result = await self.db.execute(query)
#
#             # Procesar resultados
#             metrics = []
#             for row in result:
#                 metrics.append({
#                     "date": row.date,
#                     "interaction_type": str(row.interaction_type),
#                     "count": row.count,
#                 })
#
#             return metrics
#         except SQLAlchemyError as e:
#             await self._handle_error("fetching interaction metrics", e)
#             return []

#     async def _get_search_metrics(
#         self,
#         start_date: datetime,
#         end_date: datetime,
#         granularity: Literal["day", "week", "month"] = "day",
#     ) -> List[Dict[str, Any]]:
#         """Obtener métricas de búsquedas con agregación por tiempo."""
#         try:
#             # Definir la expresión de agrupación según la granularidad
#             if granularity == "day":
#                 date_trunc = func.date_trunc("day", models.Search.created_at)
#             elif granularity == "week":
#                 date_trunc = func.date_trunc("week", models.Search.created_at)
#             else:  # month
#                 date_trunc = func.date_trunc("month", models.Search.created_at)
#
#             # Consulta para contar búsquedas por tiempo
#             query = select(
#                 date_trunc.label("date"),
#                 func.count().label("count")
#             ).filter(
#                 models.Search.account_id == self.account_id,
#                 models.Search.created_at >= start_date,
#                 models.Search.created_at <= end_date,
#             ).group_by(date_trunc).order_by(date_trunc)
#
#             result = await self.db.execute(query)
#
#             # Procesar resultados
#             metrics = []
#             for row in result:
#                 metrics.append({
#                     "date": row.date,
#                     "count": row.count,
#                 })
#
#             return metrics
#         except SQLAlchemyError as e:
#             await self._handle_error("fetching search metrics", e)
#             return []

#     async def _get_product_performance_metrics(
#         self, start_date: datetime, end_date: datetime
#     ) -> List[Dict[str, Any]]:
#         """Obtener métricas de rendimiento de productos."""
#         try:
#             # Consulta para contar interacciones por producto
#             query = select(
#                 models.Product.product_id,
#                 models.Product.name,
#                 models.Product.category,
#                 func.count(models.Interaction.interaction_id).label("interaction_count"),
#                 func.count(
#                     models.Interaction.interaction_id
#                 ).filter(
#                     models.Interaction.interaction_type == "purchase"
#                 ).label("purchase_count"),
#                 func.count(
#                     models.Interaction.interaction_id
#                 ).filter(
#                     models.Interaction.interaction_type == "view"
#                 ).label("view_count"),
#             ).join(
#                 models.Interaction,
#                 models.Product.product_id == models.Interaction.product_id
#             ).filter(
#                 models.Product.account_id == self.account_id,
#                 models.Interaction.created_at >= start_date,
#                 models.Interaction.created_at <= end_date,
#             ).group_by(
#                 models.Product.product_id,
#                 models.Product.name,
#                 models.Product.category,
#             ).order_by(func.count(models.Interaction.interaction_id).desc())
#
#             result = await self.db.execute(query)
#
#             # Procesar resultados
#             metrics = []
#             for row in result:
#                 conversion_rate = (
#                     row.purchase_count / row.view_count * 100
#                     if row.view_count > 0 else 0
#                 )
#
#                 metrics.append({
#                     "product_id": row.product_id,
#                     "name": row.name,
#                     "category": row.category,
#                     "interaction_count": row.interaction_count,
#                     "purchase_count": row.purchase_count,
#                     "view_count": row.view_count,
#                     "conversion_rate": round(conversion_rate, 2),
#                 })
#
#             return metrics
#         except SQLAlchemyError as e:
#             await self._handle_error("fetching product performance metrics", e)
#             return []

#     async def get_comprehensive_metrics(
#         self,
#         start_date: datetime,
#         end_date: datetime,
#         granularity: Literal["day", "week", "month"] = "day",
#     ) -> Dict[str, Any]:
#         """
#         Obtener métricas comprehensivas con agregación por granularidad
#         """
#         try:
#             # Consultas con agregación
#             metrics = {
#                 "interactions": await self._get_interaction_metrics(
#                     start_date, end_date, granularity
#                 ),
#                 "searches": await self._get_search_metrics(
#                     start_date, end_date, granularity
#                 ),
#                 "product_performance": await self._get_product_performance_metrics(
#                     start_date, end_date
#                 ),
#             }
#
#             return metrics
#
#         except SQLAlchemyError as e:
#             await self._handle_error("fetching comprehensive metrics", e)
#             return {
#                 "interactions": [],
#                 "searches": [],
#                 "product_performance": [],
#             }
