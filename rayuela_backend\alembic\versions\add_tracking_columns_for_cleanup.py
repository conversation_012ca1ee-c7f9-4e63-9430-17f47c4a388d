"""add_tracking_columns_for_cleanup

Revision ID: add_tracking_columns_for_cleanup
Revises: secure_rls_functions
Create Date: 2025-01-23 15:30:00.000000

"""

from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
from sqlalchemy import DateTime

# revision identifiers, used by Alembic.
revision = "add_tracking_columns_for_cleanup"
down_revision = "secure_rls_functions"
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add tracking columns needed for cleanup_old_data function."""

    # Add last_interaction_at to products table
    op.add_column(
        "products",
        sa.Column(
            "last_interaction_at",
            DateTime(timezone=True),
            nullable=True,
            comment="Timestamp of last interaction with this product",
        ),
    )

    # Add last_login_at to system_users table
    op.add_column(
        "system_users",
        sa.Column(
            "last_login_at",
            DateTime(timezone=True),
            nullable=True,
            comment="Timestamp of last login by this user",
        ),
    )

    # Add last_activity_at to end_users table
    op.add_column(
        "end_users",
        sa.Column(
            "last_activity_at",
            DateTime(timezone=True),
            nullable=True,
            comment="Timestamp of last activity by this end user",
        ),
    )

    # Create indexes for better performance during cleanup operations
    op.create_index(
        "idx_products_account_last_interaction",
        "products",
        ["account_id", "last_interaction_at"],
    )

    op.create_index(
        "idx_system_users_account_last_login",
        "system_users",
        ["account_id", "last_login_at"],
    )

    op.create_index(
        "idx_end_users_account_last_activity",
        "end_users",
        ["account_id", "last_activity_at"],
    )


def downgrade() -> None:
    """Remove tracking columns and indexes."""

    # Drop indexes first
    op.drop_index("idx_end_users_account_last_activity", "end_users")
    op.drop_index("idx_system_users_account_last_login", "system_users")
    op.drop_index("idx_products_account_last_interaction", "products")

    # Remove columns
    op.drop_column("end_users", "last_activity_at")
    op.drop_column("system_users", "last_login_at")
    op.drop_column("products", "last_interaction_at")
