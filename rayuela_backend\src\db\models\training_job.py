from sqlalchemy import (
    Column,
    Integer,
    DateTime,
    ForeignKey,
    Text,
    String,
    JSON,
    UniqueConstraint,
    Index,
    func,
    Enum as SQLAEnum,
    ForeignKeyConstraint,
    PrimaryKeyConstraint,
    Identity,
)
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.enums import TrainingJobStatus
from .mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class TrainingJob(Base, TenantMixin):
    __tablename__ = "training_jobs"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    id = Column(Integer, Identity(), primary_key=True)
    artifact_metadata_id = Column(Integer, nullable=True)
    status = Column(SQLAEnum(TrainingJobStatus))
    started_at = Column(DateTime, default=func.now(), server_default=func.now())
    completed_at = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    # New fields for Celery integration
    task_id = Column(String(255), nullable=True, index=True)
    parameters = Column(JSON, nullable=True)
    metrics = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=func.now(), server_default=func.now())

    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        # Composite FK for proper tenant isolation
        ForeignKeyConstraint(
            ["account_id", "artifact_metadata_id"],
            ["artifact_metadata.account_id", "artifact_metadata.id"],
            ondelete="CASCADE",
            name="fk_training_job_artifact_metadata"
        ),
        Index("idx_job_account_status", "account_id", "status"),
        Index("idx_job_account_metadata", "account_id", "artifact_metadata_id"),
        Index("idx_training_jobs_task_id", "task_id"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    )

    account = relationship("Account", back_populates="training_jobs")
    model_metadata = relationship(
        "ModelMetadata",
        foreign_keys=[account_id, artifact_metadata_id],
        primaryjoin="and_(TrainingJob.account_id==ModelMetadata.account_id, TrainingJob.artifact_metadata_id==ModelMetadata.id)",
        back_populates="training_jobs"
    )
    # Alias for backward compatibility
    artifact_metadata = relationship(
        "ModelMetadata",
        foreign_keys=[account_id, artifact_metadata_id],
        primaryjoin="and_(TrainingJob.account_id==ModelMetadata.account_id, TrainingJob.artifact_metadata_id==ModelMetadata.id)",
        viewonly=True,
        overlaps="model_metadata"
    )
