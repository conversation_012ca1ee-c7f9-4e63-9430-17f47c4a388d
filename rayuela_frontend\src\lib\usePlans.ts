// src/lib/usePlans.ts
import useSWR from 'swr';
import { getAvailablePlans, PlanInfo } from '@/lib/api';

/**
 * Hook para obtener y gestionar la información de los planes disponibles.
 * Utiliza SWR para cachear los datos y proporcionar revalidación automática.
 */
export function usePlans() {
  const { data, error, isLoading, mutate } = useSWR<Record<string, PlanInfo>>(
    'available-plans',
    async () => {
      return await getAvailablePlans();
    },
    {
      revalidateOnFocus: false, // No revalidar al volver a la pestaña
      revalidateOnReconnect: true, // Revalidar al reconectar
      dedupingInterval: 60000, // 1 minuto entre revalidaciones
    }
  );

  // Función para obtener los límites de un plan específico
  const getPlanLimits = (planId: string) => {
    if (!data) return null;
    return data[planId]?.limits || null;
  };

  // Función para obtener un plan específico por su ID
  const getPlanById = (planId: string) => {
    if (!data) return null;
    return data[planId] || null;
  };

  // Función para obtener todos los planes como un array
  const getAllPlans = () => {
    if (!data) return [];
    return Object.values(data);
  };

  // Función para obtener el nombre de un plan
  const getPlanName = (planId: string) => {
    if (!data) return planId;
    return data[planId]?.name || planId;
  };

  return {
    plans: data,
    error,
    isLoading,
    refresh: mutate,
    getPlanLimits,
    getPlanById,
    getAllPlans,
    getPlanName,
  };
}
