"""
Tests para verificar la unicidad global de los hashes de API keys.
"""

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.security.api_key import generate_api_key, hash_api_key
from src.db.models.account import Account
from src.db.repositories.account import AccountRepository
from src.db import schemas


class TestAPIKeyGlobalUniqueness:
    """Test suite para verificar la unicidad global de API key hashes."""

    @pytest.mark.asyncio
    async def test_global_api_key_hash_uniqueness_constraint(self, db_session: AsyncSession):
        """Verifica que la base de datos impida API key hashes duplicados globalmente."""
        
        # Generate a test API key and hash
        api_key, api_key_hash = generate_api_key()
        
        # Create first account with the API key hash
        account1_data = schemas.AccountCreate(
            name="Test Account 1",
            api_key_hash=api_key_hash,
            api_key_prefix=api_key[:10],
            api_key_last_chars=api_key[-6:],
            api_key_revealed=True
        )
        
        account_repo = AccountRepository(db_session)
        account1 = await account_repo.create(account1_data)
        await db_session.commit()
        
        assert account1.api_key_hash == api_key_hash
        
        # Try to create second account with the same API key hash
        account2_data = schemas.AccountCreate(
            name="Test Account 2",
            api_key_hash=api_key_hash,  # Same hash as account1
            api_key_prefix=api_key[:10],
            api_key_last_chars=api_key[-6:],
            api_key_revealed=True
        )
        
        # This should raise an IntegrityError due to the unique constraint
        with pytest.raises(IntegrityError) as exc_info:
            account2 = await account_repo.create(account2_data)
            await db_session.commit()
        
        # Verify the error is related to the unique constraint
        error_message = str(exc_info.value)
        assert "uq_api_key_hash_global" in error_message or "api_key_hash" in error_message

    @pytest.mark.asyncio
    async def test_different_api_keys_allowed(self, db_session: AsyncSession):
        """Verifica que diferentes API keys puedan coexistir sin problemas."""
        
        # Generate two different API keys
        api_key1, api_key_hash1 = generate_api_key()
        api_key2, api_key_hash2 = generate_api_key()
        
        assert api_key_hash1 != api_key_hash2  # Ensure they're different
        
        # Create first account
        account1_data = schemas.AccountCreate(
            name="Test Account 1",
            api_key_hash=api_key_hash1,
            api_key_prefix=api_key1[:10],
            api_key_last_chars=api_key1[-6:],
            api_key_revealed=True
        )
        
        # Create second account
        account2_data = schemas.AccountCreate(
            name="Test Account 2",
            api_key_hash=api_key_hash2,
            api_key_prefix=api_key2[:10],
            api_key_last_chars=api_key2[-6:],
            api_key_revealed=True
        )
        
        account_repo = AccountRepository(db_session)
        
        # Both accounts should be created successfully
        account1 = await account_repo.create(account1_data)
        account2 = await account_repo.create(account2_data)
        await db_session.commit()
        
        assert account1.api_key_hash == api_key_hash1
        assert account2.api_key_hash == api_key_hash2
        assert account1.account_id != account2.account_id

    @pytest.mark.asyncio
    async def test_null_api_key_hashes_allowed(self, db_session: AsyncSession):
        """Verifica que múltiples cuentas puedan tener api_key_hash NULL."""
        
        # Create accounts without API key hashes
        account1_data = schemas.AccountCreate(
            name="Test Account 1",
            api_key_hash=None
        )
        
        account2_data = schemas.AccountCreate(
            name="Test Account 2", 
            api_key_hash=None
        )
        
        account_repo = AccountRepository(db_session)
        
        # Both accounts should be created successfully with NULL hashes
        account1 = await account_repo.create(account1_data)
        account2 = await account_repo.create(account2_data)
        await db_session.commit()
        
        assert account1.api_key_hash is None
        assert account2.api_key_hash is None
        assert account1.account_id != account2.account_id

    @pytest.mark.asyncio
    async def test_api_key_lookup_returns_correct_account(self, db_session: AsyncSession):
        """Verifica que la búsqueda por API key devuelva la cuenta correcta."""
        
        # Generate unique API keys for two accounts
        api_key1, api_key_hash1 = generate_api_key()
        api_key2, api_key_hash2 = generate_api_key()
        
        # Create two accounts with different API keys
        account1_data = schemas.AccountCreate(
            name="Test Account 1",
            api_key_hash=api_key_hash1,
            api_key_prefix=api_key1[:10],
            api_key_last_chars=api_key1[-6:],
            api_key_revealed=True
        )
        
        account2_data = schemas.AccountCreate(
            name="Test Account 2",
            api_key_hash=api_key_hash2,
            api_key_prefix=api_key2[:10],
            api_key_last_chars=api_key2[-6:],
            api_key_revealed=True
        )
        
        account_repo = AccountRepository(db_session)
        
        account1 = await account_repo.create(account1_data)
        account2 = await account_repo.create(account2_data)
        await db_session.commit()
        
        # Test API key lookup returns correct accounts
        found_account1 = await account_repo.get_by_api_key(api_key1)
        found_account2 = await account_repo.get_by_api_key(api_key2)
        
        assert found_account1 is not None
        assert found_account2 is not None
        assert found_account1.account_id == account1.account_id
        assert found_account2.account_id == account2.account_id
        assert found_account1.name == "Test Account 1"
        assert found_account2.name == "Test Account 2"

    @pytest.mark.asyncio
    async def test_api_key_lookup_with_invalid_key(self, db_session: AsyncSession):
        """Verifica que la búsqueda con API key inválida devuelva None."""
        
        # Create an account with a valid API key
        api_key, api_key_hash = generate_api_key()
        
        account_data = schemas.AccountCreate(
            name="Test Account",
            api_key_hash=api_key_hash,
            api_key_prefix=api_key[:10],
            api_key_last_chars=api_key[-6:],
            api_key_revealed=True
        )
        
        account_repo = AccountRepository(db_session)
        account = await account_repo.create(account_data)
        await db_session.commit()
        
        # Try to find account with invalid API key
        invalid_key = "ray_invalid_key_12345678901234567890"
        found_account = await account_repo.get_by_api_key(invalid_key)
        
        assert found_account is None

    def test_hash_collision_probability(self):
        """Verifica que la probabilidad de colisión de hashes sea extremadamente baja."""
        
        # Generate multiple API keys and verify uniqueness
        hashes = set()
        num_keys = 1000
        
        for _ in range(num_keys):
            api_key, api_key_hash = generate_api_key()
            assert api_key_hash not in hashes, f"Hash collision detected: {api_key_hash}"
            hashes.add(api_key_hash)
        
        # All hashes should be unique
        assert len(hashes) == num_keys

    def test_manual_hash_collision_prevention(self):
        """Verifica que incluso con hashes idénticos manuales, la unicidad se mantenga."""
        
        # Create a specific hash manually
        test_api_key = "ray_test_key_for_collision_test"
        hash1 = hash_api_key(test_api_key)
        hash2 = hash_api_key(test_api_key)
        
        # Same input should produce same hash
        assert hash1 == hash2
        
        # But different inputs should produce different hashes
        different_key = "ray_different_test_key"
        hash3 = hash_api_key(different_key)
        assert hash1 != hash3
