# Version control
.git
.gitignore

# Environment files
.env*
!.env.example

# Python cache files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
.pytest_cache/
.coverage
htmlcov/

# Virtual environments
venv/
env/
ENV/

# IDE files
.idea/
.vscode/
*.swp
*.swo

# Local development files
*.db
*.sqlite3
test.db

# Documentation
docs/

# Test files
tests/
test_*.py
pytest.ini

# Temporary files
tmp/
temp/

# Log files
logs/
*.log

# Build artifacts
dist/
build/
*.egg-info/

# Docker files (except the ones we need)
docker-compose*.yml
docker/

# Other unnecessary files
*.md
!README.md
LICENSE
IMPLEMENTATION_SUMMARY.md
QUICKSTART.md
