"""
Service for handling account-related business logic.
"""

from typing import Dict, Any, Optional
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from src.db import models
from src.db.repositories import AccountRepository
from src.utils.base_logger import log_info, log_error


class AccountService:
    """Service for handling account-related business logic."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self._account_repo = AccountRepository(db)

    async def count_users(self, account_id: int) -> int:
        """
        Count the total number of active users for an account.

        Args:
            account_id: The ID of the account

        Returns:
            The number of active users
        """
        try:
            # Construir la consulta
            query = (
                select(func.count())
                .select_from(models.SystemUser)
                .where(
                    models.SystemUser.account_id == account_id,
                    models.SystemUser.is_active == True,
                )
            )

            # Ejecutar la consulta
            result = await self.db.execute(query)
            return result.scalar_one() or 0
        except SQLAlchemyError as e:
            log_error(f"Error counting users: {e}")
            return 0

    async def count_products(self, account_id: int) -> int:
        """
        Count the total number of active products for an account.

        Args:
            account_id: The ID of the account

        Returns:
            The number of active products
        """
        try:
            # Construir la consulta
            query = (
                select(func.count())
                .select_from(models.Product)
                .where(
                    models.Product.account_id == account_id,
                    models.Product.is_active == True,
                )
            )

            # Ejecutar la consulta
            result = await self.db.execute(query)
            return result.scalar_one() or 0
        except SQLAlchemyError as e:
            log_error(f"Error counting products: {e}")
            return 0

    async def count_items(self, account_id: int) -> int:
        """
        Count the total number of items (products) for an account.
        This method is an alias of count_products for backward compatibility.

        Args:
            account_id: The ID of the account

        Returns:
            The number of active products
        """
        return await self.count_products(account_id)

    async def get_usage_metrics(self, account_id: int) -> Optional[models.AccountUsageMetrics]:
        """
        Get usage metrics for an account.

        Args:
            account_id: The ID of the account

        Returns:
            The account usage metrics or None if not found
        """
        try:
            # Construir la consulta
            query = select(models.AccountUsageMetrics).where(
                models.AccountUsageMetrics.account_id == account_id
            )

            # Ejecutar la consulta
            result = await self.db.execute(query)
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            log_error(f"Error getting usage metrics: {e}")
            return None

    async def get_storage_usage(self, account_id: int) -> int:
        """
        Get the current storage usage for an account.

        Args:
            account_id: The ID of the account

        Returns:
            The storage usage in bytes
        """
        try:
            metrics = await self.get_usage_metrics(account_id)
            return metrics.storage_used if metrics else 0
        except SQLAlchemyError as e:
            log_error(f"Error getting storage usage: {e}")
            return 0

    async def get_account_summary(self, account_id: int) -> Dict[str, Any]:
        """
        Get a summary of account information including users, products, and usage metrics.

        Args:
            account_id: The ID of the account

        Returns:
            A dictionary with account summary information
        """
        try:
            # Usar una transacción para garantizar lecturas consistentes
            async with self.db.begin():
                # Get the account
                account = await self._account_repo.get_by_id(account_id)
                if not account:
                    return {"error": "Account not found"}

                # Get counts and metrics
                user_count = await self.count_users(account_id)
                product_count = await self.count_products(account_id)
                metrics = await self.get_usage_metrics(account_id)

                # Get subscription information
                subscription_plan = None
                if account.subscription and account.subscription.is_active:
                    subscription_plan = account.subscription.plan_type

                # Build the summary
                summary = {
                    "account_id": account_id,
                    "name": account.name,
                    "is_active": account.is_active,
                    "created_at": account.created_at,
                    "subscription_plan": subscription_plan,  # Ahora obtenemos el plan de la suscripción
                    "user_count": user_count,
                    "product_count": product_count,
                    "usage": {
                        "api_calls": metrics.api_calls_count if metrics else 0,
                        "storage_used": metrics.storage_used if metrics else 0,
                        "last_reset": metrics.last_reset if metrics else None,
                        "last_billing_cycle": metrics.last_billing_cycle if metrics else None,
                    }
                }

            return summary
        except Exception as e:
            log_error(f"Error getting account summary: {e}")
            return {"error": str(e)}
