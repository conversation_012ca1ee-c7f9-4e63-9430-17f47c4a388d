# Timing Attack Resistance: API Key Hash Comparison Fixes

## Overview

This document outlines the critical security fixes implemented to address **US-SEC-002: Proteger API Key Hash contra Ataques de Tiempo** - timing attack vulnerabilities in API key hash comparisons.

## Problem Addressed

**Critical Security Vulnerability:** API key hash comparisons were vulnerable to timing attacks, where attackers could measure response time differences to infer information about valid API key hashes.

### Vulnerable Code Patterns (Before)

1. **Direct SQL comparison:**
   ```python
   # VULNERABLE - timing attack possible
   query = select(Account).where(Account.api_key_hash == api_key)
   ```

2. **Direct hash comparison:**
   ```python
   # VULNERABLE - timing attack possible  
   return hash_api_key(api_key) == account.api_key_hash
   ```

### Attack Vector

An attacker could:
1. Send requests with different API keys
2. Measure response times precisely
3. Use timing differences to infer correct hash characters
4. Eventually reconstruct valid API key hashes
5. Gain unauthorized access to tenant data

## Security Fixes Implemented

### ✅ **1. Middleware Layer Protection**

**Files:** `src/middleware/usage_meter_middleware.py` (consolidated middleware)

**Before (Vulnerable):**
```python
query = select(Account).where(
    Account.api_key_hash == api_key,  # Direct comparison in SQL
    Account.is_active == True,
    Account.deleted_at.is_(None),
)
```

**After (Secure):**
```python
# Get all active accounts (no API key filtering in SQL)
query = select(Account).where(
    Account.is_active == True,
    Account.deleted_at.is_(None),
    Account.api_key_hash.is_not(None),
)
accounts = result.scalars().all()

# Use timing-safe comparison in application layer
for account in accounts:
    if verify_api_key(api_key, account.api_key_hash):
        return account
```

### ✅ **2. Service Layer Protection**

**File:** `src/services/api_key_service.py`

**Before (Vulnerable):**
```python
return hash_api_key(api_key) == account.api_key_hash
```

**After (Secure):**
```python
from src.core.security.api_key import verify_api_key
return verify_api_key(api_key, account.api_key_hash)
```

### ✅ **3. Repository Layer Protection**

**File:** `src/db/repositories/account.py`

**Before (Vulnerable):**
```python
query = select(models.Account).filter(
    models.Account.api_key_hash == api_key_hash  # Direct comparison
)
```

**After (Secure):**
```python
# Get all accounts, verify in application layer
query = select(models.Account).filter(
    models.Account.is_active == True,
    models.Account.deleted_at.is_(None),
    models.Account.api_key_hash.is_not(None)
)
accounts = result.scalars().all()

for account in accounts:
    if verify_api_key(api_key, account.api_key_hash):
        return account
```

### ✅ **4. Core Security Function**

**File:** `src/core/security/api_key.py`

The secure implementation was already present:
```python
def verify_api_key(api_key: str, stored_hash: str) -> bool:
    calculated_hash = hash_api_key(api_key)
    # Timing-safe comparison using secrets.compare_digest()
    return secrets.compare_digest(calculated_hash, stored_hash)
```

## Security Benefits

### 🛡️ **Timing Attack Resistance**
- **Constant-time comparison** using `secrets.compare_digest()`
- **No timing information leakage** about hash correctness
- **Protection against hash inference** attacks

### 🔒 **Defense in Depth**
- **Multiple layers protected** (middleware, service, repository)
- **Consistent security approach** across all API key validation
- **Centralized secure comparison** through `verify_api_key()`

### 📊 **Performance Considerations**
- **Minimal performance impact** - only affects API key validation
- **Caching still effective** - timing-safe comparison doesn't affect cache performance
- **Scalable solution** - works efficiently with large numbers of accounts

## Implementation Details

### **Timing-Safe Comparison Process**

1. **Hash Calculation:**
   ```python
   calculated_hash = hash_api_key(api_key)  # SHA-256 of provided key
   ```

2. **Constant-Time Comparison:**
   ```python
   secrets.compare_digest(calculated_hash, stored_hash)
   ```

3. **Why This Works:**
   - `secrets.compare_digest()` compares every byte regardless of early mismatches
   - Execution time is constant regardless of where differences occur
   - Prevents timing-based inference of hash content

### **Database Query Strategy**

Instead of filtering by API key hash in SQL (which could leak timing information), we:

1. **Fetch all eligible accounts** (active, non-deleted, with API keys)
2. **Iterate through accounts** in application layer
3. **Use timing-safe comparison** for each account
4. **Return first match** or None if no match found

This approach ensures consistent timing regardless of:
- Whether the API key exists
- Which account matches (if any)
- How many accounts are checked before finding a match

## Testing and Verification

### **Automated Tests**

**File:** `tests/security/test_timing_attack_resistance.py`

- **Timing consistency verification**
- **Correct/incorrect key comparison timing**
- **Edge case handling**
- **All layers tested** (middleware, service, repository)

### **Manual Verification Steps**

1. **Check middleware behavior:**
   ```bash
   # Test with valid API key
   curl -H "X-API-Key: valid_key" https://api.example.com/api/v1/endpoint
   
   # Test with invalid API key
   curl -H "X-API-Key: invalid_key" https://api.example.com/api/v1/endpoint
   ```

2. **Verify timing consistency:**
   - Response times should be similar for valid/invalid keys
   - No significant timing differences based on key correctness

3. **Check logs:**
   - No hash values should be logged
   - Error messages should not leak timing information

## Compliance and Standards

### ✅ **Security Standards Met**

- **OWASP Top 10** - Addresses A02:2021 Cryptographic Failures
- **CWE-208** - Observable Timing Discrepancy mitigation
- **NIST SP 800-63B** - Authentication security guidelines

### ✅ **Best Practices Implemented**

- **Constant-time comparison** for all cryptographic operations
- **Defense in depth** across multiple application layers
- **Centralized security functions** for consistent implementation
- **Comprehensive testing** for timing attack resistance

## Monitoring and Maintenance

### **Security Monitoring**

1. **Response time monitoring** - watch for unusual timing patterns
2. **Failed authentication tracking** - monitor for brute force attempts
3. **API key usage patterns** - detect suspicious access patterns

### **Code Review Guidelines**

When reviewing API key related code, ensure:
- ✅ No direct hash comparisons (`==` operator)
- ✅ All comparisons use `verify_api_key()` or `secrets.compare_digest()`
- ✅ No API key hashes in SQL WHERE clauses for authentication
- ✅ Consistent error handling that doesn't leak timing information

### **Future Considerations**

1. **Rate limiting** - implement additional protection against brute force
2. **API key rotation** - regular rotation policies
3. **Monitoring enhancements** - advanced timing attack detection
4. **Performance optimization** - if needed, optimize while maintaining security

---

**Security Impact:** These fixes eliminate timing attack vulnerabilities in API key validation, preventing attackers from inferring valid API key hashes through response time analysis. This significantly improves the authentication security of the Rayuela platform.
