"""
Servicio para operaciones relacionadas con usuarios finales.
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.db.models import EndUser
from src.db.repositories.base import BaseRepository
from src.core.exceptions import NotFoundError, ValidationError


class UserService:
    """
    Servicio para operaciones relacionadas con usuarios finales (EndUser).

    Este servicio proporciona métodos para crear, actualizar, eliminar y consultar
    usuarios finales, gestionando la lógica de negocio relacionada con ellos.
    """

    def __init__(self, db: AsyncSession):
        """
        Inicializa el servicio de usuarios.

        Args:
            db: Sesión de base de datos
        """
        self.db = db
        self.repository = BaseRepository(db, model=EndUser)

    async def get(self, account_id: int, user_id: int) -> EndUser:
        """
        Obtiene un usuario por su ID.

        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario

        Returns:
            Usuario encontrado

        Raises:
            NotFoundError: Si el usuario no existe
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        user = await self.repository.get_by_id(user_id)
        if not user:
            raise NotFoundError(f"User with ID {user_id} not found")
        return user

    async def get_by_email(self, account_id: int, email: str) -> Optional[EndUser]:
        """
        Obtiene un usuario por su email.

        Args:
            account_id: ID de la cuenta
            email: Email del usuario

        Returns:
            Usuario encontrado o None si no existe
        """
        stmt = select(EndUser).where(
            EndUser.account_id == account_id,
            EndUser.email == email
        )
        result = await self.db.execute(stmt)
        return result.scalars().first()

    async def list(
        self,
        account_id: int,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        include_deleted: bool = False
    ) -> List[EndUser]:
        """
        Lista los usuarios que cumplan con los criterios especificados.

        Args:
            account_id: ID de la cuenta
            skip: Número de registros a saltar
            limit: Número máximo de registros a devolver
            search: Texto para búsqueda
            include_deleted: Si se deben incluir usuarios eliminados

        Returns:
            Lista de usuarios
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Usar get_by_filters en lugar de list
        if search:
            # Para búsquedas de texto, necesitamos usar get_all y filtrar manualmente
            # ya que get_by_filters no soporta operadores como ilike
            users = await self.repository.get_all(skip=skip, limit=limit)
            filtered_users = []
            for u in users:
                # Verificar si el texto de búsqueda está en el email
                email_match = False
                if hasattr(u, 'email') and isinstance(u.email, str):
                    email_match = search.lower() in u.email.lower()

                # Verificar si el texto de búsqueda está en el nombre
                name_match = False
                if hasattr(u, 'name') and isinstance(u.name, str):
                    name_match = search.lower() in u.name.lower()

                # Agregar el usuario si cumple con los criterios
                if email_match or name_match:
                    filtered_users.append(u)

            return filtered_users

        # Si no hay búsqueda de texto, podemos usar filtros directos
        return await self.repository.get_by_filters(
            filters={},
            skip=skip,
            limit=limit,
            include_deleted=include_deleted
        )

    async def create(self, account_id: int, data: Dict[str, Any]) -> EndUser:
        """
        Crea un nuevo usuario final.

        Args:
            account_id: ID de la cuenta
            data: Datos del usuario

        Returns:
            Usuario creado

        Raises:
            ValidationError: Si los datos son inválidos
        """
        # Validación básica
        if not data.get("email"):
            raise ValidationError("Email is required")

        # Verificar que no exista otro usuario con el mismo email
        existing_user = await self.get_by_email(account_id, data["email"])
        if existing_user:
            raise ValidationError(f"User with email {data['email']} already exists")

        # Asignar account_id
        data["account_id"] = account_id

        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Crear usuario dentro de una transacción
        async with self.db.begin():
            return await self.repository.create(obj_in=data)

    async def update(self, account_id: int, user_id: int, data: Dict[str, Any]) -> EndUser:
        """
        Actualiza un usuario existente.

        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario
            data: Datos a actualizar

        Returns:
            Usuario actualizado

        Raises:
            NotFoundError: Si el usuario no existe
            ValidationError: Si los datos son inválidos
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Verificar que el usuario existe
        user = await self.get(account_id, user_id)

        # Si se está actualizando el email, verificar que no exista otro usuario con ese email
        if "email" in data and data["email"] != user.email:
            existing_user = await self.get_by_email(account_id, data["email"])
            if existing_user:
                raise ValidationError(f"User with email {data['email']} already exists")

        # Actualizar usuario dentro de una transacción
        async with self.db.begin():
            updated_user = await self.repository.update(id=user_id, obj_in=data)
            if not updated_user:
                raise NotFoundError(f"User with ID {user_id} not found")
            return updated_user

    async def delete(self, account_id: int, user_id: int, soft: bool = True) -> None:
        """
        Elimina un usuario.

        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario
            soft: Si se debe hacer una eliminación suave (soft delete)

        Raises:
            NotFoundError: Si el usuario no existe
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Verificar que el usuario existe
        await self.get(account_id, user_id)  # Esto lanzará NotFoundError si no existe

        # Eliminar usuario dentro de una transacción
        async with self.db.begin():
            success = False
            if soft:
                # Soft delete
                success = await self.repository.soft_delete(id=user_id)
            else:
                # Hard delete
                success = await self.repository.delete(id=user_id)

            if not success:
                raise NotFoundError(f"User with ID {user_id} not found or could not be deleted")