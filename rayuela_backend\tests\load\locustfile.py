import json
import random
from locust import HttpUser, task, between, tag
from typing import Dict, Any, List
from datetime import datetime


class RecSysUser(HttpUser):
    """
    Usuario de prueba para tests de carga.
    Simula el comportamiento de un cliente de la API.
    """

    # Tiempo de espera entre tareas
    wait_time = between(1, 3)

    # API key para autenticación
    api_key = "test_api_key"

    # Datos de prueba
    account_id = 1
    user_ids = list(range(1, 101))  # 100 usuarios
    product_ids = list(range(1, 1001))  # 1000 productos

    def on_start(self):
        """Método que se ejecuta al iniciar cada usuario"""
        # Configurar headers para todas las peticiones
        self.client.headers = {
            "X-API-Key": self.api_key,
            "Content-Type": "application/json",
        }

        # Crear cuenta si no existe
        self.create_test_account()

    def create_test_account(self):
        """Crear cuenta de prueba si no existe"""
        response = self.client.post(
            "/api/v1/accounts/",
            json={"name": "Load Test Account", "subscription_plan": "BASIC"},
        )

        if response.status_code == 200:
            data = response.json()
            self.account_id = data.get("id", 1)
            self.api_key = data.get("api_key", self.api_key)
            self.client.headers["X-API-Key"] = self.api_key

    @tag("health")
    @task(1)
    def check_health(self):
        """Verificar el estado de la API"""
        self.client.get("/api/v1/health")

    @tag("recommendations")
    @task(10)
    def get_recommendations_for_user(self):
        """Obtener recomendaciones para un usuario aleatorio"""
        user_id = random.choice(self.user_ids)
        self.client.get(f"/api/v1/recommendations/user/{user_id}?limit=10")

    @tag("recommendations")
    @task(5)
    def get_similar_products(self):
        """Obtener productos similares a un producto aleatorio"""
        product_id = random.choice(self.product_ids)
        self.client.get(f"/api/v1/recommendations/similar/{product_id}?limit=10")

    @tag("recommendations")
    @task(3)
    def get_personalized_recommendations(self):
        """Obtener recomendaciones personalizadas"""
        user_id = random.choice(self.user_ids)
        product_id = random.choice(self.product_ids)
        self.client.get(
            f"/api/v1/recommendations/personalized/{user_id}/{product_id}?limit=10"
        )

    @tag("interactions")
    @task(20)
    def record_interaction(self):
        """Registrar una interacción aleatoria"""
        interaction_types = ["VIEW", "LIKE", "PURCHASE", "CART", "CLICK"]

        self.client.post(
            "/api/v1/interactions/",
            json={
                "end_user_id": random.choice(self.user_ids),
                "product_id": random.choice(self.product_ids),
                "interaction_type": random.choice(interaction_types),
                "value": random.uniform(0.5, 5.0),
            },
        )

    @tag("batch")
    @task(1)
    def batch_interactions(self):
        """Enviar un lote de interacciones"""
        interaction_types = ["VIEW", "LIKE", "PURCHASE", "CART", "CLICK"]
        batch_size = random.randint(10, 50)

        interactions = [
            {
                "end_user_id": random.choice(self.user_ids),
                "product_id": random.choice(self.product_ids),
                "interaction_type": random.choice(interaction_types),
                "value": random.uniform(0.5, 5.0),
            }
            for _ in range(batch_size)
        ]

        self.client.post(
            "/api/v1/interactions/batch", json={"interactions": interactions}
        )

    @tag("training")
    @task(1)
    def request_training(self):
        """Solicitar entrenamiento del modelo"""
        self.client.post("/api/v1/pipeline/train")

    @tag("training")
    @task(2)
    def check_training_status(self):
        """Verificar el estado de un trabajo de entrenamiento"""
        job_id = random.randint(1, 10)  # Asumir que hay 10 jobs
        self.client.get(f"/api/v1/pipeline/status/{job_id}")


class HighVolumeUser(RecSysUser):
    """
    Usuario de alto volumen para pruebas de estrés.
    Realiza más interacciones y solicitudes de recomendaciones.
    """

    # Tiempo de espera más corto entre tareas
    wait_time = between(0.1, 1)

    # Más usuarios y productos
    user_ids = list(range(1, 1001))  # 1000 usuarios
    product_ids = list(range(1, 10001))  # 10000 productos

    @tag("interactions")
    @task(50)
    def record_interaction(self):
        """Registrar muchas interacciones"""
        super().record_interaction()

    @tag("batch")
    @task(10)
    def batch_interactions(self):
        """Enviar lotes grandes de interacciones"""
        interaction_types = ["VIEW", "LIKE", "PURCHASE", "CART", "CLICK"]
        batch_size = random.randint(100, 500)

        interactions = [
            {
                "end_user_id": random.choice(self.user_ids),
                "product_id": random.choice(self.product_ids),
                "interaction_type": random.choice(interaction_types),
                "value": random.uniform(0.5, 5.0),
            }
            for _ in range(batch_size)
        ]

        self.client.post(
            "/api/v1/interactions/batch", json={"interactions": interactions}
        )

    @tag("recommendations")
    @task(30)
    def get_recommendations_for_user(self):
        """Obtener muchas recomendaciones"""
        super().get_recommendations_for_user()


class RecommendationUser(HttpUser):
    """Usuario simulado para pruebas de carga de recomendaciones."""
    wait_time = between(1, 5)  # Tiempo entre requests
    
    def on_start(self):
        """Inicializar usuario con autenticación."""
        # Login para obtener token
        response = self.client.post(
            "/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "test123"
            }
        )
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            self.api_key = response.json()["api_key"]
        else:
            self.token = None
            self.api_key = None
    
    @task(3)
    def get_personalized_recommendations(self):
        """Obtener recomendaciones personalizadas."""
        if not self.token:
            return
        
        # Simular diferentes estrategias y filtros
        strategies = ["collaborative", "content", "hybrid"]
        strategy = random.choice(strategies)
        
        filters = {
            "strategy": strategy,
            "limit": random.randint(5, 20),
            "min_price": random.randint(0, 50),
            "max_price": random.randint(51, 200),
            "categories": random.sample(
                ["electronics", "books", "clothing", "food"],
                random.randint(1, 3)
            )
        }
        
        self.client.get(
            "/recommendations/personalized/1",
            params=filters,
            headers={"X-API-Key": self.api_key}
        )
    
    @task(2)
    def get_similar_products(self):
        """Obtener productos similares."""
        if not self.token:
            return
        
        # Simular diferentes productos y filtros
        product_id = random.randint(1, 100)
        filters = {
            "limit": random.randint(5, 20),
            "min_similarity": random.uniform(0.3, 0.8)
        }
        
        self.client.get(
            f"/recommendations/products/{product_id}/similar",
            params=filters,
            headers={"X-API-Key": self.api_key}
        )
    
    @task(1)
    def get_recommendations_with_context(self):
        """Obtener recomendaciones con contexto."""
        if not self.token:
            return
        
        # Simular diferentes contextos
        contexts = [
            {
                "time_of_day": "morning",
                "device": "mobile",
                "location": "home"
            },
            {
                "time_of_day": "afternoon",
                "device": "desktop",
                "location": "work"
            },
            {
                "time_of_day": "evening",
                "device": "tablet",
                "location": "cafe"
            }
        ]
        
        context = random.choice(contexts)
        
        self.client.get(
            "/recommendations/personalized/1",
            params={"context": context},
            headers={"X-API-Key": self.api_key}
        )


class ProductUser(HttpUser):
    """Usuario simulado para pruebas de carga de productos."""
    wait_time = between(2, 10)  # Tiempo entre requests
    
    def on_start(self):
        """Inicializar usuario con autenticación."""
        # Login para obtener token
        response = self.client.post(
            "/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "test123"
            }
        )
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            self.api_key = response.json()["api_key"]
        else:
            self.token = None
            self.api_key = None
    
    @task(3)
    def list_products(self):
        """Listar productos."""
        if not self.token:
            return
        
        # Simular diferentes filtros y paginación
        filters = {
            "page": random.randint(1, 10),
            "per_page": random.randint(10, 50),
            "sort_by": random.choice(["price", "name", "created_at"]),
            "order": random.choice(["asc", "desc"])
        }
        
        self.client.get(
            "/products",
            params=filters,
            headers={"X-API-Key": self.api_key}
        )
    
    @task(2)
    def get_product(self):
        """Obtener producto específico."""
        if not self.token:
            return
        
        product_id = random.randint(1, 100)
        
        self.client.get(
            f"/products/{product_id}",
            headers={"X-API-Key": self.api_key}
        )
    
    @task(1)
    def create_product(self):
        """Crear producto."""
        if not self.token:
            return
        
        # Simular datos de producto
        product_data = {
            "name": f"Test Product {datetime.now().timestamp()}",
            "price": random.uniform(10.0, 1000.0),
            "description": "Test description",
            "category": random.choice(
                ["electronics", "books", "clothing", "food"]
            )
        }
        
        self.client.post(
            "/products",
            json=product_data,
            headers={"X-API-Key": self.api_key}
        )


class UserBehavior(HttpUser):
    """Comportamiento de usuario que combina diferentes tipos de requests."""
    wait_time = between(1, 3)
    
    def on_start(self):
        """Inicializar usuario con autenticación."""
        # Login para obtener token
        response = self.client.post(
            "/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "test123"
            }
        )
        if response.status_code == 200:
            self.token = response.json()["access_token"]
            self.api_key = response.json()["api_key"]
        else:
            self.token = None
            self.api_key = None
    
    @task(3)
    def browse_products(self):
        """Simular navegación de productos."""
        if not self.token:
            return
        
        # Listar productos
        self.client.get(
            "/products",
            params={"page": 1, "per_page": 20},
            headers={"X-API-Key": self.api_key}
        )
        
        # Ver producto específico
        product_id = random.randint(1, 100)
        self.client.get(
            f"/products/{product_id}",
            headers={"X-API-Key": self.api_key}
        )
        
        # Obtener productos similares
        self.client.get(
            f"/recommendations/products/{product_id}/similar",
            headers={"X-API-Key": self.api_key}
        )
    
    @task(2)
    def get_recommendations(self):
        """Simular obtención de recomendaciones."""
        if not self.token:
            return
        
        # Obtener recomendaciones personalizadas
        self.client.get(
            "/recommendations/personalized/1",
            params={"strategy": "hybrid"},
            headers={"X-API-Key": self.api_key}
        )
        
        # Obtener recomendaciones con contexto
        context = {
            "time_of_day": "morning",
            "device": "mobile",
            "location": "home"
        }
        self.client.get(
            "/recommendations/personalized/1",
            params={"context": context},
            headers={"X-API-Key": self.api_key}
        )
    
    @task(1)
    def manage_products(self):
        """Simular gestión de productos."""
        if not self.token:
            return
        
        # Crear producto
        product_data = {
            "name": f"Test Product {datetime.now().timestamp()}",
            "price": random.uniform(10.0, 1000.0),
            "description": "Test description",
            "category": "electronics"
        }
        response = self.client.post(
            "/products",
            json=product_data,
            headers={"X-API-Key": self.api_key}
        )
        
        if response.status_code == 201:
            product_id = response.json()["id"]
            
            # Actualizar producto
            update_data = {
                "price": random.uniform(10.0, 1000.0),
                "description": "Updated description"
            }
            self.client.put(
                f"/products/{product_id}",
                json=update_data,
                headers={"X-API-Key": self.api_key}
            )
            
            # Eliminar producto
            self.client.delete(
                f"/products/{product_id}",
                headers={"X-API-Key": self.api_key}
            )
