# Simplificación del Modelo de Suscripción

Este documento explica la simplificación realizada en el modelo `Subscription` para reforzar la relación uno-a-uno con `Account`.

## Problema Identificado

La tabla `subscriptions` tenía una clave primaria compuesta `(account_id, id)`, pero la relación `subscription` en el modelo `Account` estaba definida con `uselist=False`, lo que indicaba una relación uno-a-uno (o uno-a-cero). Esto creaba una ambigüedad:

- ¿Puede una cuenta tener múltiples registros de suscripción (historial)?
- ¿O solo puede tener uno activo?

## Solución Implementada

Se optó por la **Opción A**: Simplificar el modelo para reforzar la relación 1:1 entre `Account` y `Subscription`.

### Cambios Realizados

1. **Modificación del Modelo `Subscription`**:
   - Se eliminó la columna `id` del modelo.
   - Se cambió la clave primaria a solo `account_id`.
   - Se eliminó la herencia de `TenantMixin` ya que no era necesaria (la clase ya tenía `account_id` como PK).

2. **Actualización del Esquema Pydantic**:
   - Se eliminó el campo `id` del esquema `Subscription`.

3. **Modificación del Repositorio `SubscriptionRepository`**:
   - Se actualizó el método `create` para verificar si ya existe una suscripción para la cuenta y actualizarla en lugar de crear una nueva.
   - Se renombró a `create_or_update` para reflejar mejor su comportamiento.
   - Se actualizaron los métodos `update` y `delete` para mantener compatibilidad con la clase base.

4. **Actualización del Servicio `SubscriptionService`**:
   - Se modificó el método `validate_and_update_subscription` para usar `account_id` como identificador en lugar de `subscription.id`.

5. **Migración de Base de Datos**:
   - Se creó una migración Alembic para:
     - Eliminar la clave primaria compuesta existente.
     - Eliminar la columna `id`.
     - Crear una nueva clave primaria solo en `account_id`.

## Beneficios del Cambio

1. **Claridad en el Modelo de Datos**:
   - Ahora es explícito que una cuenta solo puede tener una suscripción activa.
   - Se elimina la ambigüedad en la relación entre `Account` y `Subscription`.

2. **Simplificación del Código**:
   - Ya no es necesario gestionar múltiples suscripciones por cuenta.
   - La lógica de negocio es más clara y directa.

3. **Mejora en la Integridad de Datos**:
   - La restricción de clave primaria en `account_id` garantiza que no puede haber duplicados.
   - Se refuerza la relación 1:1 a nivel de base de datos.

## Consideraciones para Webhooks de Stripe

La lógica en `src/api/v1/endpoints/billing.py` (webhooks) ya estaba diseñada para buscar una suscripción existente por `stripe_subscription_id` y actualizarla si existe, o crear una nueva si no existe. Este comportamiento es compatible con el nuevo modelo, ya que:

1. Si no existe una suscripción para la cuenta, se crea una nueva.
2. Si ya existe, se actualiza con los nuevos datos de Stripe.

No se requieren cambios adicionales en la lógica de webhooks.

## Impacto en el Historial de Suscripciones

Con este cambio, no se mantiene un historial de suscripciones anteriores en la tabla `subscriptions`. Si se necesita un historial de cambios en las suscripciones, se recomienda:

1. Utilizar la tabla `audit_log` para registrar cambios en las suscripciones.
2. Considerar la creación de una tabla separada `subscription_history` si se requiere un historial detallado.

## Conclusión

Esta simplificación del modelo de suscripción mejora la claridad y la integridad del modelo de datos, reforzando la relación uno-a-uno entre `Account` y `Subscription` tanto a nivel de código como de base de datos.
