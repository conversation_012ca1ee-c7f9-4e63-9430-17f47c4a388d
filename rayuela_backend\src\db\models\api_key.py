"""
API Key model for multi-API key management.
"""

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>te<PERSON>, String, Boolean, DateTime, ForeignKey, UniqueConstraint
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from src.db.base import Base
from src.db.models.mixins import TenantMixin


class ApiKey(Base, TenantMixin):
    """
    Model for storing multiple API keys per account.
    Replaces the single API key fields in the Account model.
    """
    __tablename__ = "api_keys"

    id = Column(Integer, primary_key=True, autoincrement=True)
    account_id = Column(Integer, ForeignKey("accounts.account_id"), nullable=False)
    
    # API Key metadata
    name = Column(String(255), nullable=True, comment="Descriptive name for this API key")
    api_key_hash = Column(String(64), nullable=False, comment="SHA-256 hash of the API key")
    api_key_prefix = Column(String(10), nullable=False, comment="Prefix of the API key for display")
    api_key_last_chars = Column(String(6), nullable=False, comment="Last 6 characters for display")
    
    # Status and tracking
    is_active = Column(Boolean, default=True, nullable=False, comment="Whether this API key is active")
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    last_used = Column(DateTime(timezone=True), nullable=True, comment="When this API key was last used")
    
    # Relationship to Account
    account = relationship("Account", back_populates="api_keys")

    __table_args__ = (
        UniqueConstraint("api_key_hash", name="uq_api_key_hash_global"),
        # Global uniqueness for API key hashes to prevent cross-tenant access vulnerabilities
        # Index on account_id for efficient queries
        {"schema": None}  # Use default schema
    )

    def __repr__(self):
        return f"<ApiKey(id={self.id}, account_id={self.account_id}, name='{self.name}', is_active={self.is_active})>"
