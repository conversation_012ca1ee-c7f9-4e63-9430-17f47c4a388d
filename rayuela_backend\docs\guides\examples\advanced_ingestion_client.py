#!/usr/bin/env python3
"""
Cliente avanzado de ingesta masiva para Rayuela.

Este cliente incluye funcionalidades avanzadas como:
- Validación de datos antes del envío
- Monitoreo automático del progreso
- Reintentos con backoff exponencial
- Manejo robusto de errores
- Logging detallado
- Soporte para múltiples formatos de entrada
"""

import json
import csv
import time
import logging
import argparse
import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from pathlib import Path
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class RayuelaAdvancedClient:
    """Cliente avanzado para ingesta masiva en Rayuela."""

    def __init__(
        self,
        api_key: str,
        base_url: str = "https://api.rayuela.com/api/v1",
        timeout: int = 30,
        max_retries: int = 3,
    ):
        """
        Inicializa el cliente.

        Args:
            api_key: API key de Rayuela
            base_url: URL base de la API
            timeout: Timeout para requests en segundos
            max_retries: Número máximo de reintentos
        """
        self.api_key = api_key
        self.base_url = base_url.rstrip("/")
        self.timeout = timeout
        self.max_retries = max_retries

        # Configurar sesión HTTP con reintentos
        self.session = requests.Session()
        retry_strategy = Retry(
            total=max_retries,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "OPTIONS", "POST"],
            backoff_factor=1,
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

        # Headers por defecto
        self.session.headers.update(
            {
                "X-API-Key": api_key,
                "Content-Type": "application/json",
                "User-Agent": "RayuelaAdvancedClient/1.0",
            }
        )

        # Configurar logging
        self.logger = logging.getLogger(__name__)

    def validate_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Valida los datos antes del envío.

        Args:
            data: Datos a validar

        Returns:
            Resultado de la validación
        """
        from validate_data import DataValidator

        validator = DataValidator()
        is_valid = validator.validate_json_data(data)
        validator.check_data_consistency(data)

        return validator.get_summary()

    def start_batch_ingestion(
        self, data: Dict[str, Any], validate_first: bool = True
    ) -> Dict[str, Any]:
        """
        Inicia un trabajo de ingesta masiva.

        Args:
            data: Datos para la ingesta
            validate_first: Si validar los datos antes del envío

        Returns:
            Respuesta de la API

        Raises:
            ValueError: Si los datos no son válidos
            requests.RequestException: Si hay error en la request
        """
        # Validar datos si se solicita
        if validate_first:
            validation_result = self.validate_data(data)
            if not validation_result["valid"]:
                error_msg = (
                    f"Datos inválidos: {validation_result['error_count']} errores"
                )
                self.logger.error(error_msg)
                for error in validation_result["errors"][:5]:
                    self.logger.error(f"  - {error}")
                raise ValueError(error_msg)

            if validation_result["warning_count"] > 0:
                self.logger.warning(
                    f"Datos tienen {validation_result['warning_count']} advertencias"
                )
                for warning in validation_result["warnings"][:3]:
                    self.logger.warning(f"  - {warning}")

        # Calcular estadísticas
        stats = {
            "users": len(data.get("users", [])),
            "products": len(data.get("products", [])),
            "interactions": len(data.get("interactions", [])),
        }

        self.logger.info(f"Iniciando ingesta: {stats}")

        # Enviar solicitud
        url = f"{self.base_url}/ingestion/batch"

        try:
            response = self.session.post(url, json=data, timeout=self.timeout)
            response.raise_for_status()

            result = response.json()
            self.logger.info(
                f"Ingesta iniciada exitosamente. Job ID: {result['job_id']}"
            )

            return result

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 422:
                # Error de validación
                error_detail = e.response.json().get("detail", "Error de validación")
                self.logger.error(f"Error de validación: {error_detail}")
                raise ValueError(f"Error de validación: {error_detail}")
            elif e.response.status_code == 429:
                # Límites excedidos
                error_detail = e.response.json().get("detail", "Límites excedidos")
                self.logger.error(f"Límites excedidos: {error_detail}")
                raise ValueError(f"Límites excedidos: {error_detail}")
            else:
                self.logger.error(
                    f"Error HTTP {e.response.status_code}: {e.response.text}"
                )
                raise
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error de conexión: {e}")
            raise

    def get_job_status(self, job_id: int) -> Dict[str, Any]:
        """
        Obtiene el estado de un trabajo de ingesta.

        Args:
            job_id: ID del trabajo

        Returns:
            Estado del trabajo
        """
        url = f"{self.base_url}/ingestion/batch/{job_id}"

        try:
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            return response.json()

        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                raise ValueError(f"Trabajo {job_id} no encontrado")
            else:
                self.logger.error(f"Error obteniendo estado: {e.response.status_code}")
                raise
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error de conexión: {e}")
            raise

    def wait_for_completion(
        self,
        job_id: int,
        max_wait_time: int = 600,
        poll_interval: int = 5,
        progress_callback: Optional[callable] = None,
    ) -> Dict[str, Any]:
        """
        Espera a que se complete un trabajo de ingesta.

        Args:
            job_id: ID del trabajo
            max_wait_time: Tiempo máximo de espera en segundos
            poll_interval: Intervalo entre consultas en segundos
            progress_callback: Función a llamar con el progreso

        Returns:
            Estado final del trabajo

        Raises:
            TimeoutError: Si el trabajo no se completa en el tiempo especificado
        """
        start_time = time.time()
        last_progress = 0

        self.logger.info(f"Monitoreando trabajo {job_id}...")

        while time.time() - start_time < max_wait_time:
            try:
                status = self.get_job_status(job_id)

                # Llamar callback de progreso si se proporciona
                if progress_callback:
                    progress_callback(status)

                # Log del progreso
                current_status = status["status"]
                progress = status.get("progress_percentage", 0)

                if progress > last_progress:
                    self.logger.info(
                        f"Progreso: {progress:.1f}% - Estado: {current_status}"
                    )
                    last_progress = progress

                # Verificar si terminó
                if current_status in ["COMPLETED", "FAILED"]:
                    if current_status == "COMPLETED":
                        self.logger.info("✅ Ingesta completada exitosamente")

                        # Log de estadísticas finales
                        if "processed_count" in status:
                            counts = status["processed_count"]
                            self.logger.info(f"Procesados: {counts}")

                            if "success_rate" in status:
                                self.logger.info(
                                    f"Tasa de éxito: {status['success_rate']:.1f}%"
                                )
                    else:
                        self.logger.error(
                            f"❌ Ingesta falló: {status.get('error_message', 'Error desconocido')}"
                        )

                        # Log de errores detallados
                        if "error_details" in status:
                            self.logger.error("Detalles de errores:")
                            for entity_type, errors in status["error_details"].items():
                                for error in errors[:3]:  # Solo los primeros 3
                                    self.logger.error(f"  {entity_type}: {error}")

                    return status

                # Esperar antes de la siguiente consulta
                time.sleep(poll_interval)

            except Exception as e:
                self.logger.warning(f"Error consultando estado: {e}")
                time.sleep(poll_interval)

        raise TimeoutError(
            f"El trabajo {job_id} no se completó en {max_wait_time} segundos"
        )

    def ingest_and_wait(self, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """
        Inicia una ingesta y espera a que se complete.

        Args:
            data: Datos para la ingesta
            **kwargs: Argumentos adicionales para wait_for_completion

        Returns:
            Estado final del trabajo
        """
        # Iniciar ingesta
        result = self.start_batch_ingestion(data)
        job_id = result["job_id"]

        # Esperar a que se complete
        return self.wait_for_completion(job_id, **kwargs)

    def load_json_file(self, file_path: str) -> Dict[str, Any]:
        """Carga datos desde un archivo JSON."""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                data = json.load(f)
            self.logger.info(f"Cargado archivo JSON: {file_path}")
            return data
        except Exception as e:
            self.logger.error(f"Error cargando archivo JSON {file_path}: {e}")
            raise

    def load_csv_files(
        self,
        users_file: Optional[str] = None,
        products_file: Optional[str] = None,
        interactions_file: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Carga datos desde archivos CSV."""
        data = {}

        if users_file:
            try:
                with open(users_file, "r", encoding="utf-8") as f:
                    reader = csv.DictReader(f)
                    data["users"] = list(reader)
                self.logger.info(
                    f"Cargados {len(data['users'])} usuarios desde {users_file}"
                )
            except Exception as e:
                self.logger.error(f"Error cargando usuarios desde {users_file}: {e}")
                raise

        if products_file:
            try:
                with open(products_file, "r", encoding="utf-8") as f:
                    reader = csv.DictReader(f)
                    products = []
                    for row in reader:
                        # Convertir tipos de datos
                        if "price" in row and row["price"]:
                            row["price"] = float(row["price"])
                        if "average_rating" in row and row["average_rating"]:
                            row["average_rating"] = float(row["average_rating"])
                        if "num_ratings" in row and row["num_ratings"]:
                            row["num_ratings"] = int(row["num_ratings"])
                        if "inventory_count" in row and row["inventory_count"]:
                            row["inventory_count"] = int(row["inventory_count"])
                        products.append(row)
                    data["products"] = products
                self.logger.info(
                    f"Cargados {len(data['products'])} productos desde {products_file}"
                )
            except Exception as e:
                self.logger.error(
                    f"Error cargando productos desde {products_file}: {e}"
                )
                raise

        if interactions_file:
            try:
                with open(interactions_file, "r", encoding="utf-8") as f:
                    reader = csv.DictReader(f)
                    interactions = []
                    for row in reader:
                        # Convertir tipos de datos
                        if "user_id" in row and row["user_id"]:
                            row["user_id"] = int(row["user_id"])
                        if "product_id" in row and row["product_id"]:
                            row["product_id"] = int(row["product_id"])
                        if "value" in row and row["value"]:
                            row["value"] = float(row["value"])
                        interactions.append(row)
                    data["interactions"] = interactions
                self.logger.info(
                    f"Cargadas {len(data['interactions'])} interacciones desde {interactions_file}"
                )
            except Exception as e:
                self.logger.error(
                    f"Error cargando interacciones desde {interactions_file}: {e}"
                )
                raise

        return data

    def save_report(self, job_status: Dict[str, Any], output_file: str) -> None:
        """Guarda un reporte del trabajo de ingesta."""
        report = {
            "timestamp": datetime.now().isoformat(),
            "job_status": job_status,
            "summary": {
                "job_id": job_status["job_id"],
                "status": job_status["status"],
                "duration_seconds": None,
                "success_rate": job_status.get("success_rate"),
                "error_count": job_status.get("error_count", 0),
            },
        }

        # Calcular duración si está disponible
        if job_status.get("started_at") and job_status.get("completed_at"):
            from datetime import datetime

            start = datetime.fromisoformat(
                job_status["started_at"].replace("Z", "+00:00")
            )
            end = datetime.fromisoformat(
                job_status["completed_at"].replace("Z", "+00:00")
            )
            report["summary"]["duration_seconds"] = (end - start).total_seconds()

        try:
            with open(output_file, "w", encoding="utf-8") as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            self.logger.info(f"Reporte guardado en: {output_file}")
        except Exception as e:
            self.logger.error(f"Error guardando reporte: {e}")


def setup_logging(level: str = "INFO", log_file: Optional[str] = None) -> None:
    """Configura el logging."""
    log_level = getattr(logging, level.upper())

    # Formato de log
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )

    # Handler para consola
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # Configurar logger principal
    logger = logging.getLogger()
    logger.setLevel(log_level)
    logger.addHandler(console_handler)

    # Handler para archivo si se especifica
    if log_file:
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)


def progress_callback(status: Dict[str, Any]) -> None:
    """Callback de ejemplo para mostrar progreso."""
    progress = status.get("progress_percentage", 0)
    current_status = status["status"]

    # Crear barra de progreso simple
    bar_length = 30
    filled_length = int(bar_length * progress / 100)
    bar = "█" * filled_length + "-" * (bar_length - filled_length)

    print(f"\r[{bar}] {progress:.1f}% - {current_status}", end="", flush=True)

    if current_status in ["COMPLETED", "FAILED"]:
        print()  # Nueva línea al final


def main():
    parser = argparse.ArgumentParser(
        description="Cliente avanzado de ingesta masiva para Rayuela",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:

  # Ingesta desde archivo JSON
  python advanced_ingestion_client.py --api-key YOUR_KEY --json batch_payload.json

  # Ingesta desde archivos CSV
  python advanced_ingestion_client.py --api-key YOUR_KEY --users users.csv --products products.csv

  # Solo monitorear un trabajo existente
  python advanced_ingestion_client.py --api-key YOUR_KEY --monitor-job 123

  # Con logging detallado
  python advanced_ingestion_client.py --api-key YOUR_KEY --json data.json --log-level DEBUG --log-file ingestion.log

Variables de entorno:
  RAYUELA_API_KEY: API key (alternativa a --api-key)
  RAYUELA_API_URL: URL base de la API (alternativa a --api-url)
        """,
    )

    # Configuración de API
    parser.add_argument(
        "--api-key",
        default=os.getenv("RAYUELA_API_KEY"),
        help="API key de Rayuela (o usar RAYUELA_API_KEY)",
    )
    parser.add_argument(
        "--api-url",
        default=os.getenv("RAYUELA_API_URL", "https://api.rayuela.com/api/v1"),
        help="URL base de la API",
    )

    # Opciones de entrada
    input_group = parser.add_mutually_exclusive_group()
    input_group.add_argument("--json", help="Archivo JSON con datos de ingesta")
    input_group.add_argument(
        "--monitor-job", type=int, help="Solo monitorear trabajo existente"
    )

    # Archivos CSV
    csv_group = parser.add_argument_group("Archivos CSV")
    csv_group.add_argument("--users", help="Archivo CSV de usuarios")
    csv_group.add_argument("--products", help="Archivo CSV de productos")
    csv_group.add_argument("--interactions", help="Archivo CSV de interacciones")

    # Opciones de procesamiento
    parser.add_argument(
        "--no-validate",
        action="store_true",
        help="Omitir validación de datos antes del envío",
    )
    parser.add_argument(
        "--max-wait",
        type=int,
        default=600,
        help="Tiempo máximo de espera en segundos (default: 600)",
    )
    parser.add_argument(
        "--poll-interval",
        type=int,
        default=5,
        help="Intervalo entre consultas en segundos (default: 5)",
    )

    # Opciones de salida
    parser.add_argument("--output", help="Archivo para guardar reporte final")
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Nivel de logging",
    )
    parser.add_argument("--log-file", help="Archivo de log")
    parser.add_argument(
        "--progress-bar", action="store_true", help="Mostrar barra de progreso"
    )

    args = parser.parse_args()

    # Validar argumentos
    if not args.api_key:
        parser.error("API key requerida. Use --api-key o configure RAYUELA_API_KEY")

    if (
        not args.monitor_job
        and not args.json
        and not any([args.users, args.products, args.interactions])
    ):
        parser.error(
            "Debe especificar --json, --monitor-job, o al menos uno de --users, --products, --interactions"
        )

    # Configurar logging
    setup_logging(args.log_level, args.log_file)
    logger = logging.getLogger(__name__)

    try:
        # Crear cliente
        client = RayuelaAdvancedClient(
            api_key=args.api_key, base_url=args.api_url, max_retries=3
        )

        # Caso 1: Solo monitorear trabajo existente
        if args.monitor_job:
            logger.info(f"Monitoreando trabajo {args.monitor_job}")

            callback = progress_callback if args.progress_bar else None
            final_status = client.wait_for_completion(
                args.monitor_job,
                max_wait_time=args.max_wait,
                poll_interval=args.poll_interval,
                progress_callback=callback,
            )

            if args.output:
                client.save_report(final_status, args.output)

            sys.exit(0 if final_status["status"] == "COMPLETED" else 1)

        # Caso 2: Cargar datos e iniciar ingesta
        if args.json:
            data = client.load_json_file(args.json)
        else:
            data = client.load_csv_files(args.users, args.products, args.interactions)

        if not data:
            logger.error("No se cargaron datos")
            sys.exit(1)

        # Iniciar ingesta
        validate_first = not args.no_validate
        result = client.start_batch_ingestion(data, validate_first=validate_first)
        job_id = result["job_id"]

        logger.info(f"Trabajo iniciado con ID: {job_id}")

        # Monitorear progreso
        callback = progress_callback if args.progress_bar else None
        final_status = client.wait_for_completion(
            job_id,
            max_wait_time=args.max_wait,
            poll_interval=args.poll_interval,
            progress_callback=callback,
        )

        # Guardar reporte si se solicita
        if args.output:
            client.save_report(final_status, args.output)

        # Código de salida basado en el resultado
        if final_status["status"] == "COMPLETED":
            logger.info("🎉 Ingesta completada exitosamente!")
            sys.exit(0)
        else:
            logger.error("❌ Ingesta falló")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Operación cancelada por el usuario")
        sys.exit(130)
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
