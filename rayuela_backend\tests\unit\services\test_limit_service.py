import pytest
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from src.services.limit_service import LimitService
from src.core.exceptions import LimitExceededError
from src.db.models import (
    Product,
    EndUser,
    Interaction,
    ModelMetadata,
    Subscription
)

@pytest.fixture
async def limit_service(db_session: AsyncSession):
    """Fixture para crear una instancia de LimitService."""
    return LimitService(db_session, account_id=1)

@pytest.fixture
async def subscription(db_session: AsyncSession):
    """Fixture para crear una suscripción de prueba."""
    subscription = Subscription(
        account_id=1,
        plan="basic",
        is_active=True,
        expires_at=datetime.utcnow() + timedelta(days=30),
        max_users=100,
        max_products=1000,
        max_storage_mb=1000,
        max_api_calls=10000,
        training_frequency="daily"
    )
    db_session.add(subscription)
    await db_session.commit()
    return subscription

@pytest.mark.asyncio
async def test_validate_user_limit(limit_service: LimitService, db_session: AsyncSession, subscription):
    """Test para validar el límite de usuarios."""
    # Crear usuarios hasta el límite
    for i in range(50):
        user = EndUser(
            account_id=1,
            email=f"user{i}@example.com",
            name=f"User {i}"
        )
        db_session.add(user)
    await db_session.commit()
    
    # Verificar que no excede el límite
    await limit_service.validate_user_limit()
    
    # Crear más usuarios para exceder el límite
    for i in range(51):
        user = EndUser(
            account_id=1,
            email=f"extra_user{i}@example.com",
            name=f"Extra User {i}"
        )
        db_session.add(user)
    await db_session.commit()
    
    # Verificar que lanza excepción al exceder el límite
    with pytest.raises(LimitExceededError):
        await limit_service.validate_user_limit()

async def test_validate_product_limit(limit_service: LimitService, db_session: AsyncSession, subscription):
    """Test para validar el límite de productos."""
    # Crear productos hasta el límite
    for i in range(500):
        product = Product(
            account_id=1,
            name=f"Product {i}",
            description=f"Description {i}",
            price=10.0
        )
        db_session.add(product)
    await db_session.commit()
    
    # Verificar que no excede el límite
    await limit_service.validate_product_limit()
    
    # Crear más productos para exceder el límite
    for i in range(501):
        product = Product(
            account_id=1,
            name=f"Extra Product {i}",
            description=f"Extra Description {i}",
            price=10.0
        )
        db_session.add(product)
    await db_session.commit()
    
    # Verificar que lanza excepción al exceder el límite
    with pytest.raises(LimitExceededError):
        await limit_service.validate_product_limit()

async def test_validate_storage_limit(limit_service: LimitService, db_session: AsyncSession, subscription):
    """Test para validar el límite de almacenamiento."""
    # Crear productos con datos hasta el límite
    for i in range(100):
        product = Product(
            account_id=1,
            name=f"Product {i}",
            description=f"Description {i}",
            price=10.0,
            metadata={"size": 5}  # 5MB por producto
        )
        db_session.add(product)
    await db_session.commit()
    
    # Verificar que no excede el límite
    await limit_service.validate_storage_limit()
    
    # Crear más productos para exceder el límite
    for i in range(101):
        product = Product(
            account_id=1,
            name=f"Extra Product {i}",
            description=f"Extra Description {i}",
            price=10.0,
            metadata={"size": 5}  # 5MB por producto
        )
        db_session.add(product)
    await db_session.commit()
    
    # Verificar que lanza excepción al exceder el límite
    with pytest.raises(LimitExceededError):
        await limit_service.validate_storage_limit()

async def test_validate_api_calls_limit(limit_service: LimitService, db_session: AsyncSession, subscription):
    """Test para validar el límite de llamadas a la API."""
    # Simular llamadas a la API hasta el límite
    for i in range(5000):
        interaction = Interaction(
            account_id=1,
            end_user_id=1,
            product_id=1,
            interaction_type="api_call",
            timestamp=datetime.utcnow()
        )
        db_session.add(interaction)
    await db_session.commit()
    
    # Verificar que no excede el límite
    await limit_service.validate_api_calls_limit()
    
    # Simular más llamadas para exceder el límite
    for i in range(5001):
        interaction = Interaction(
            account_id=1,
            end_user_id=1,
            product_id=1,
            interaction_type="api_call",
            timestamp=datetime.utcnow()
        )
        db_session.add(interaction)
    await db_session.commit()
    
    # Verificar que lanza excepción al exceder el límite
    with pytest.raises(LimitExceededError):
        await limit_service.validate_api_calls_limit()

async def test_validate_training_frequency(limit_service: LimitService, db_session: AsyncSession, subscription):
    """Test para validar la frecuencia de entrenamiento."""
    # Crear un entrenamiento reciente
    model = ModelMetadata(
        account_id=1,
        model_type="collaborative",
        training_date=datetime.utcnow(),
        status="completed"
    )
    db_session.add(model)
    await db_session.commit()
    
    # Verificar que no se puede entrenar inmediatamente
    with pytest.raises(LimitExceededError):
        await limit_service.validate_training_frequency()
    
    # Modificar la fecha del último entrenamiento para permitir uno nuevo
    model.training_date = datetime.utcnow() - timedelta(days=2)
    await db_session.commit()
    
    # Verificar que ahora se puede entrenar
    await limit_service.validate_training_frequency()

async def test_validate_training_data_limit(limit_service: LimitService, db_session: AsyncSession, subscription):
    """Test para validar el límite de datos de entrenamiento."""
    # Crear interacciones hasta el límite
    for i in range(5000):
        interaction = Interaction(
            account_id=1,
            end_user_id=1,
            product_id=1,
            interaction_type="view",
            timestamp=datetime.utcnow()
        )
        db_session.add(interaction)
    await db_session.commit()
    
    # Verificar que no excede el límite
    await limit_service.validate_training_data_limit(5000)
    
    # Verificar que lanza excepción al exceder el límite
    with pytest.raises(LimitExceededError):
        await limit_service.validate_training_data_limit(6000)

async def test_get_usage_metrics(limit_service: LimitService, db_session: AsyncSession, subscription):
    """Test para obtener métricas de uso."""
    # Crear datos de uso
    for i in range(50):
        user = EndUser(account_id=1, email=f"user{i}@example.com", name=f"User {i}")
        db_session.add(user)
    
    for i in range(500):
        product = Product(
            account_id=1,
            name=f"Product {i}",
            description=f"Description {i}",
            price=10.0,
            metadata={"size": 1}  # 1MB por producto
        )
        db_session.add(product)
    
    for i in range(5000):
        interaction = Interaction(
            account_id=1,
            end_user_id=1,
            product_id=1,
            interaction_type="api_call",
            timestamp=datetime.utcnow()
        )
        db_session.add(interaction)
    
    await db_session.commit()
    
    # Obtener métricas
    metrics = await limit_service.get_usage_metrics()
    
    # Verificar métricas
    assert metrics["users"]["used"] == 50
    assert metrics["users"]["limit"] == 100
    assert metrics["products"]["used"] == 500
    assert metrics["products"]["limit"] == 1000
    assert metrics["storage"]["used_mb"] == 500
    assert metrics["storage"]["limit_mb"] == 1000
    assert metrics["api_calls"]["used"] == 5000
    assert metrics["api_calls"]["limit"] == 10000 