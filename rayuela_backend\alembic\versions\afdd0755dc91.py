"""remove_unique_interaction_constraint

Revision ID: remove_unique_interaction_constraint
Revises: 9fdd0755dc90
Create Date: 2023-06-17 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "afdd0755dc91"  # ID corto en lugar del nombre descriptivo largo
down_revision: Union[str, None] = "9fdd0755dc90"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Remove the unique constraint from interactions table."""
    # Drop the unique constraint
    op.drop_constraint("uq_interaction", "interactions", type_="unique")


def downgrade() -> None:
    """Add back the unique constraint to interactions table."""
    # Add back the unique constraint
    op.create_unique_constraint(
        "uq_interaction",
        "interactions",
        ["account_id", "end_user_id", "product_id", "interaction_type"]
    )
