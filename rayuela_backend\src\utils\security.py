"""
Utilidades de seguridad para la aplicación.
"""
from typing import Type, TypeVar, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from src.core.exceptions import ResourceNotFoundError
from src.db.base import Base
from src.utils.base_logger import log_info

ModelType = TypeVar("ModelType", bound=Base)


async def verify_resource_ownership(
    db: AsyncSession,
    model: Type[ModelType],
    resource_id: int,
    account_id: int,
    raise_error: bool = True,
    error_class: Any = ResourceNotFoundError,
) -> Optional[ModelType]:
    """
    Verifica que un recurso pertenezca a la cuenta especificada.

    Args:
        db: Sesión de base de datos
        model: Clase del modelo a verificar
        resource_id: ID del recurso
        account_id: ID de la cuenta que debería ser propietaria
        raise_error: Si es True, lanza una excepción si el recurso no existe o no pertenece a la cuenta
        error_class: Clase de excepción a lanzar si raise_error es True

    Returns:
        El recurso si existe y pertenece a la cuenta, None en caso contrario

    Raises:
        ResourceNotFoundError: Si el recurso no existe o no pertenece a la cuenta y raise_error es True
    """
    # Verificar que el modelo tenga el atributo account_id
    if not hasattr(model, "account_id"):
        raise ValueError(f"El modelo {model.__name__} no tiene el atributo account_id")

    # Intentar obtener el recurso
    try:
        # Primero intentamos con clave compuesta si el modelo usa TenantMixin
        try:
            resource = await db.get(model, (account_id, resource_id))
            if resource:
                return resource
        except Exception:
            # Si falla, intentamos con consulta filtrada
            pass

        # Consulta filtrada por ID y account_id
        query = select(model).where(
            model.id == resource_id,
            model.account_id == account_id
        )
        result = await db.execute(query)
        resource = result.scalars().first()

        if not resource and raise_error:
            log_info(f"Intento de acceso no autorizado: {model.__name__} con ID {resource_id} para account_id {account_id}")
            raise error_class(model.__name__, resource_id)

        return resource
    except Exception as e:
        if isinstance(e, error_class):
            raise
        if raise_error:
            log_info(f"Error verificando propiedad de recurso: {str(e)}")
            raise error_class(model.__name__, resource_id)
        return None
