// src/app/(public)/home/<USER>
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';

export const metadata = generateSEOMetadata({
  title: 'Rayuela.ai - Recommendation System as a Service',
  description: 'Sistemas de recomendación avanzados para tu negocio, sin la complejidad de construirlos desde cero. API-first, escalable y fácil de integrar.',
  path: '/',
  keywords: ['sistema de recomendación', 'API', 'machine learning', 'recomendaciones', 'SaaS', 'inteligencia artificial'],
});

export default function HomePage() {
  const organizationSchema = generateJsonLd('Organization', {});
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela Recommendation API',
    description: 'API de recomendaciones personalizada para empresas',
  });

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema),
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareSchema),
        }}
      />

      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-20 text-center">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
            Bienvenido a Rayuela
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">
            Sistema de recomendaciones inteligentes para tu negocio
          </p>
          <div className="flex justify-center gap-4">
            <Button asChild>
              <Link href="/register">Comenzar Gratis</Link>
            </Button>
            <Button variant="outline" asChild>
              <Link href="/login">Iniciar Sesión</Link>
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
