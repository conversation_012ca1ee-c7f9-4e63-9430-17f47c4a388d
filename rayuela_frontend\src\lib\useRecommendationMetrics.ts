// src/lib/useRecommendationMetrics.ts
import useSWR from 'swr';
import { 
  getRecommendationPerformance, 
  getConfidenceMetrics,
  RecommendationPerformanceMetrics,
  ConfidenceMetrics
} from '@/lib/api/recommendation-metrics';
import { useAuth } from '@/lib/auth';

/**
 * Hook para obtener y gestionar las métricas de rendimiento de recomendaciones.
 * 
 * @param modelId ID del modelo específico (opcional)
 * @param metricType Tipo de métrica a filtrar (opcional)
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de métricas de rendimiento, estado de carga, errores y funciones de utilidad
 */
export function useRecommendationPerformance(
  modelId?: number,
  metricType?: string,
  options: {
    revalidateOnFocus?: boolean;
    refreshInterval?: number;
    dedupingInterval?: number;
    errorRetryCount?: number;
  } = {}
) {
  const { token, apiKey } = useAuth();
  
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt
  } = useSWR<RecommendationPerformanceMetrics>(
    token && apiKey ? ['recommendation-performance', token, apiKey, modelId, metricType] : null,
    async ([_, tokenValue, apiKeyValue, model, metric]) => 
      await getRecommendationPerformance(tokenValue, apiKeyValue, model, metric),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 300000, // 5 minutos por defecto
      errorRetryCount: options.errorRetryCount ?? 3
    }
  );

  // Función para obtener la precisión general
  const getPrecision = () => {
    if (!data) return 0;
    return data.summary.precision;
  };

  // Función para obtener la diversidad general
  const getDiversity = () => {
    if (!data) return 0;
    return data.summary.diversity;
  };

  // Función para obtener la novedad general
  const getNovelty = () => {
    if (!data) return 0;
    return data.summary.novelty;
  };

  // Función para obtener el NDCG general
  const getNDCG = () => {
    if (!data) return 0;
    return data.summary.ndcg;
  };

  // Función para obtener la serendipia general
  const getSerendipity = () => {
    if (!data) return 0;
    return data.summary.serendipity;
  };

  // Función para obtener la cobertura del catálogo
  const getCatalogCoverage = () => {
    if (!data) return 0;
    return data.summary.catalog_coverage;
  };

  // Función para obtener datos formateados para gráficos
  const getChartData = () => {
    if (!data) return [];
    
    return [
      { name: 'Precisión', value: data.summary.precision * 100 },
      { name: 'Diversidad', value: data.summary.diversity * 100 },
      { name: 'Novedad', value: data.summary.novelty * 100 },
      { name: 'NDCG', value: data.summary.ndcg * 100 },
      { name: 'Serendipia', value: data.summary.serendipity * 100 },
      { name: 'Cobertura', value: data.summary.catalog_coverage * 100 }
    ];
  };

  return {
    performanceData: data,
    error,
    isLoading,
    isValidating,
    refresh: mutate,
    lastUpdated: dataUpdatedAt ? new Date(dataUpdatedAt) : null,
    getPrecision,
    getDiversity,
    getNovelty,
    getNDCG,
    getSerendipity,
    getCatalogCoverage,
    getChartData
  };
}

/**
 * Hook para obtener y gestionar las métricas de confianza de recomendaciones.
 * 
 * @param options Opciones de configuración para el hook
 * @returns Objeto con datos de métricas de confianza, estado de carga, errores y funciones de utilidad
 */
export function useConfidenceMetrics(options: {
  revalidateOnFocus?: boolean;
  refreshInterval?: number;
  dedupingInterval?: number;
  errorRetryCount?: number;
} = {}) {
  const { token, apiKey } = useAuth();
  
  const {
    data,
    error,
    isLoading,
    isValidating,
    mutate,
    dataUpdatedAt
  } = useSWR<ConfidenceMetrics>(
    token && apiKey ? ['confidence-metrics', token, apiKey] : null,
    async ([_, tokenValue, apiKeyValue]) => 
      await getConfidenceMetrics(tokenValue, apiKeyValue),
    {
      revalidateOnFocus: options.revalidateOnFocus ?? false,
      refreshInterval: options.refreshInterval,
      dedupingInterval: options.dedupingInterval ?? 300000, // 5 minutos por defecto
      errorRetryCount: options.errorRetryCount ?? 3
    }
  );

  // Función para obtener la confianza global
  const getGlobalConfidence = () => {
    if (!data) return 0;
    return data.global_confidence;
  };

  // Función para obtener la confianza promedio por tipo de modelo
  const getAverageConfidenceByModelType = () => {
    if (!data) return {};
    
    return {
      collaborative: data.confidence_distribution.collaborative.avg,
      content: data.confidence_distribution.content.avg,
      hybrid: data.confidence_distribution.hybrid.avg
    };
  };

  // Función para obtener la confianza por categoría
  const getConfidenceByCategory = () => {
    if (!data) return {};
    return data.category_confidence;
  };

  // Función para obtener los factores de confianza
  const getConfidenceFactors = () => {
    if (!data) return {};
    return data.confidence_factors;
  };

  // Función para obtener las tendencias de confianza de los últimos 7 días
  const getConfidenceTrends = () => {
    if (!data) return [];
    return data.confidence_trends.last_7_days;
  };

  // Función para obtener datos formateados para gráficos de distribución
  const getDistributionChartData = () => {
    if (!data) return [];
    
    return [
      {
        name: 'Colaborativo',
        low: data.confidence_distribution.collaborative.low_confidence_count,
        medium: data.confidence_distribution.collaborative.medium_confidence_count,
        high: data.confidence_distribution.collaborative.high_confidence_count,
        avg: data.confidence_distribution.collaborative.avg * 100
      },
      {
        name: 'Basado en Contenido',
        low: data.confidence_distribution.content.low_confidence_count,
        medium: data.confidence_distribution.content.medium_confidence_count,
        high: data.confidence_distribution.content.high_confidence_count,
        avg: data.confidence_distribution.content.avg * 100
      },
      {
        name: 'Híbrido',
        low: data.confidence_distribution.hybrid.low_confidence_count,
        medium: data.confidence_distribution.hybrid.medium_confidence_count,
        high: data.confidence_distribution.hybrid.high_confidence_count,
        avg: data.confidence_distribution.hybrid.avg * 100
      }
    ];
  };

  // Función para obtener datos formateados para gráficos de categorías
  const getCategoryChartData = () => {
    if (!data) return [];
    
    return Object.entries(data.category_confidence).map(([category, value]) => ({
      category,
      confidence: value * 100
    }));
  };

  // Función para obtener datos formateados para gráficos de factores
  const getFactorsChartData = () => {
    if (!data) return [];
    
    return Object.entries(data.confidence_factors).map(([factor, value]) => ({
      factor,
      value: value * 100
    }));
  };

  // Función para obtener datos formateados para gráficos de tendencias
  const getTrendsChartData = () => {
    if (!data) return [];
    
    return data.confidence_trends.last_7_days.map(day => ({
      date: new Date(day.date),
      confidence: day.avg_confidence * 100,
      interactions: day.interactions_count
    }));
  };

  return {
    confidenceData: data,
    error,
    isLoading,
    isValidating,
    refresh: mutate,
    lastUpdated: dataUpdatedAt ? new Date(dataUpdatedAt) : null,
    getGlobalConfidence,
    getAverageConfidenceByModelType,
    getConfidenceByCategory,
    getConfidenceFactors,
    getConfidenceTrends,
    getDistributionChartData,
    getCategoryChartData,
    getFactorsChartData,
    getTrendsChartData
  };
}
