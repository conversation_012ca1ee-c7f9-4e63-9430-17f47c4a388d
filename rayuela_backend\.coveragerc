[run]
source = src
omit = 
    */migrations/*
    */alembic/*
    */tests/*
    */venv/*
    */env/*
    */site-packages/*
    */__pycache__/*
    */__init__.py
    */settings.py
    */config.py
    */conftest.py
    */main.py

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    raise NotImplementedError
    if __name__ == .__main__.:
    pass
    raise ImportError
    except ImportError
    def __str__

[html]
directory = coverage_html_report
