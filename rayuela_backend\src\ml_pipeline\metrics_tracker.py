from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from src.utils.base_logger import log_info, log_error
from src.core.redis_utils import get_redis
from redis.asyncio import Redis
from src.db.models import ModelMetadata


class MetricsTracker:
    """Gestor de métricas de modelos"""

    def __init__(self):
        self._redis: Optional[Redis] = None
        self._cache_ttl = 3600  # 1 hora

    async def _get_redis(self) -> Redis:
        """Obtiene la conexión a Redis"""
        if not self._redis:
            self._redis = await get_redis()
        return self._redis

    def _get_cache_key(self, account_id: int, metric_type: str) -> str:
        """Genera la clave de caché para una métrica"""
        return f"metric:{account_id}:{metric_type}"

    async def save_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        metrics: Dict[str, Any],
        model_metadata_id: Optional[int] = None,
    ) -> None:
        """
        Guarda las métricas en la base de datos y caché.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            metrics: Diccionario con las métricas
            model_metadata_id: ID opcional del modelo al que pertenecen las métricas
        """
        try:
            # Guardar en base de datos
            if model_metadata_id:
                # Actualizar métricas en un registro existente
                stmt = (
                    update(ModelMetadata)
                    .where(
                        ModelMetadata.account_id == account_id,
                        ModelMetadata.id == model_metadata_id,
                    )
                    .values(performance_metrics=metrics)
                )
                await db.execute(stmt)
            else:
                # Buscar el último registro de metadatos para esta cuenta
                stmt = (
                    select(ModelMetadata)
                    .where(ModelMetadata.account_id == account_id)
                    .order_by(ModelMetadata.training_date.desc())
                    .limit(1)
                )
                result = await db.execute(stmt)
                model_metadata = result.scalar_one_or_none()

                if model_metadata:
                    # Actualizar el último registro
                    model_metadata.performance_metrics = metrics
                    await db.commit()
                else:
                    # No hay registros, crear uno nuevo con valores mínimos
                    model_metadata = ModelMetadata(
                        account_id=account_id,
                        artifact_name=f"metrics_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                        artifact_version="1.0.0",
                        description="Automatically generated metrics record",
                        performance_metrics=metrics,
                        parameters={},
                        artifacts_path="/metrics",
                    )
                    db.add(model_metadata)
                    await db.commit()

            # Guardar en caché
            redis = await self._get_redis()
            for metric_type, value in metrics.items():
                cache_key = self._get_cache_key(account_id, metric_type)
                await redis.setex(cache_key, self._cache_ttl, str(value))

            log_info(f"Métricas guardadas para account_id={account_id}")

        except Exception as e:
            log_error(f"Error guardando métricas: {str(e)}")
            raise

    async def get_metrics(
        self,
        db: AsyncSession,
        account_id: int,
        metric_type: Optional[str] = None,
        model_metadata_id: Optional[int] = None,
    ) -> Dict[str, Any]:
        """
        Obtiene las métricas de un modelo.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            metric_type: Tipo de métrica (opcional)
            model_metadata_id: ID opcional del modelo del que obtener las métricas

        Returns:
            Diccionario con las métricas
        """
        try:
            metrics = {}

            if metric_type:
                # Intentar obtener de caché
                redis = await self._get_redis()
                cache_key = self._get_cache_key(account_id, metric_type)
                cached_value = await redis.get(cache_key)

                if cached_value:
                    try:
                        metrics[metric_type] = float(cached_value)
                    except ValueError:
                        # Si no es un número, guardarlo como string
                        metrics[metric_type] = cached_value.decode("utf-8")
                    return metrics

            # Si no está en caché o se requieren todas las métricas,
            # obtener de base de datos
            if model_metadata_id:
                # Buscar un modelo específico
                stmt = select(ModelMetadata.performance_metrics).where(
                    ModelMetadata.account_id == account_id,
                    ModelMetadata.id == model_metadata_id,
                )
            else:
                # Buscar el último modelo
                stmt = (
                    select(ModelMetadata.performance_metrics)
                    .where(ModelMetadata.account_id == account_id)
                    .order_by(ModelMetadata.training_date.desc())
                    .limit(1)
                )

            result = await db.execute(stmt)
            row = result.scalar_one_or_none()

            if not row:
                return {}

            db_metrics = row

            if metric_type:
                # Actualizar caché
                if metric_type in db_metrics:
                    redis = await self._get_redis()
                    cache_key = self._get_cache_key(account_id, metric_type)
                    await redis.setex(
                        cache_key, self._cache_ttl, str(db_metrics[metric_type])
                    )
                    metrics[metric_type] = db_metrics[metric_type]
            else:
                metrics = db_metrics
                # Actualizar caché para todas las métricas
                redis = await self._get_redis()
                for m_type, value in db_metrics.items():
                    cache_key = self._get_cache_key(account_id, m_type)
                    await redis.setex(cache_key, self._cache_ttl, str(value))

            return metrics

        except Exception as e:
            log_error(f"Error obteniendo métricas: {str(e)}")
            raise

    async def invalidate_cache(
        self, account_id: Optional[int] = None, metric_type: Optional[str] = None
    ) -> None:
        """
        Invalida la caché de métricas.

        Args:
            account_id: ID de la cuenta (opcional)
            metric_type: Tipo de métrica (opcional)
        """
        try:
            redis = await self._get_redis()

            if account_id is None:
                # Invalidar toda la caché
                pattern = "metric:*"
                keys = await redis.keys(pattern)
                if keys:
                    await redis.delete(*keys)
            else:
                # Invalidar métricas de una cuenta
                if metric_type:
                    # Invalidar un tipo específico
                    cache_key = self._get_cache_key(account_id, metric_type)
                    await redis.delete(cache_key)
                else:
                    # Invalidar todos los tipos
                    pattern = f"metric:{account_id}:*"
                    keys = await redis.keys(pattern)
                    if keys:
                        await redis.delete(*keys)

            log_info(
                f"Caché de métricas invalidada para account_id={account_id}, "
                f"metric_type={metric_type}"
            )

        except Exception as e:
            log_error(f"Error invalidando caché de métricas: {str(e)}")
            raise

    async def compare_model_versions(
        self,
        db: AsyncSession,
        account_id: int,
        model_ids: List[int] = None,
        limit: int = 5,
    ) -> Dict[str, Any]:
        """
        Compara métricas entre diferentes versiones de modelos.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_ids: Lista de IDs de modelos a comparar (opcional)
            limit: Número máximo de modelos a comparar si no se especifican model_ids
            
        Returns:
            Diccionario con comparación de métricas entre versiones
        """
        try:
            model_metrics = {}
            
            # Si no se proporcionan model_ids, obtener los últimos N modelos
            if not model_ids:
                stmt = (
                    select(ModelMetadata.id, ModelMetadata.performance_metrics, 
                           ModelMetadata.artifact_version, ModelMetadata.training_date)
                    .where(ModelMetadata.account_id == account_id)
                    .order_by(ModelMetadata.training_date.desc())
                    .limit(limit)
                )
                
                result = await db.execute(stmt)
                models = result.fetchall()
                
                if not models:
                    return {"error": "No se encontraron modelos para comparar"}
                
                for model in models:
                    model_metrics[model.id] = {
                        "version": model.artifact_version,
                        "training_date": model.training_date.isoformat() if model.training_date else None,
                        "metrics": model.performance_metrics or {}
                    }
            else:
                # Obtener métricas para los model_ids especificados
                for model_id in model_ids:
                    stmt = select(
                        ModelMetadata.id, 
                        ModelMetadata.performance_metrics,
                        ModelMetadata.artifact_version,
                        ModelMetadata.training_date
                    ).where(
                        ModelMetadata.account_id == account_id,
                        ModelMetadata.id == model_id
                    )
                    
                    result = await db.execute(stmt)
                    model = result.scalar_one_or_none()
                    
                    if model:
                        model_metrics[model_id] = {
                            "version": model.artifact_version,
                            "training_date": model.training_date.isoformat() if model.training_date else None,
                            "metrics": model.performance_metrics or {}
                        }
            
            # Preparar comparación de métricas
            comparison = {
                "models": model_metrics,
                "diff": {},
                "business_impact": {},
                "summary": {}
            }
            
            # Si hay al menos dos modelos para comparar
            if len(model_metrics) >= 2:
                # Obtener todas las métricas únicas
                all_metrics = set()
                for model_id, model_data in model_metrics.items():
                    all_metrics.update(model_data["metrics"].keys())
                
                # Para cada métrica, calcular diferencias entre modelos
                for metric in all_metrics:
                    comparison["diff"][metric] = {}
                    
                    # Obtener valores para cada modelo
                    metric_values = {}
                    for model_id, model_data in model_metrics.items():
                        if metric in model_data["metrics"]:
                            metric_values[model_id] = model_data["metrics"][metric]
                    
                    # Calcular diferencias porcentuales entre modelos
                    model_ids_list = list(model_metrics.keys())
                    for i in range(len(model_ids_list)):
                        for j in range(i + 1, len(model_ids_list)):
                            model_a = model_ids_list[i]
                            model_b = model_ids_list[j]
                            
                            # Si ambos modelos tienen esta métrica
                            if model_a in metric_values and model_b in metric_values:
                                value_a = metric_values[model_a]
                                value_b = metric_values[model_b]
                                
                                # Calcular diferencia absoluta y porcentual
                                diff_abs = value_a - value_b
                                diff_pct = (diff_abs / abs(value_b) * 100) if value_b != 0 else float('inf')
                                
                                comparison["diff"][metric][f"{model_a}_vs_{model_b}"] = {
                                    "abs": round(diff_abs, 4),
                                    "pct": round(diff_pct, 2)
                                }
                
                # Calcular impacto estimado en métricas de negocio
                business_metrics = [
                    "test_conversion_rate", 
                    "estimated_roi", 
                    "estimated_engagement_rate",
                    "estimated_customer_value"
                ]
                
                for metric in business_metrics:
                    if metric in all_metrics:
                        comparison["business_impact"][metric] = {}
                        
                        # Comparar entre el modelo más reciente y los anteriores
                        most_recent_model = model_ids_list[0]  # Asumiendo que están ordenados por fecha desc
                        
                        if metric in model_metrics[most_recent_model]["metrics"]:
                            recent_value = model_metrics[most_recent_model]["metrics"][metric]
                            
                            for other_model in model_ids_list[1:]:
                                if metric in model_metrics[other_model]["metrics"]:
                                    old_value = model_metrics[other_model]["metrics"][metric]
                                    
                                    # Calcular mejora
                                    improvement_abs = recent_value - old_value
                                    improvement_pct = (improvement_abs / abs(old_value) * 100) if old_value != 0 else float('inf')
                                    
                                    comparison["business_impact"][metric][f"vs_{other_model}"] = {
                                        "improvement_abs": round(improvement_abs, 4),
                                        "improvement_pct": round(improvement_pct, 2)
                                    }
                
                # Añadir resumen general
                best_model_by_metric = {}
                for metric in all_metrics:
                    metric_values_by_model = {
                        model_id: model_data["metrics"].get(metric)
                        for model_id, model_data in model_metrics.items()
                        if metric in model_data["metrics"]
                    }
                    
                    if metric_values_by_model:
                        # Determinar si mayor es mejor para esta métrica
                        higher_is_better = not metric.startswith("error_") and "time" not in metric.lower()
                        
                        if higher_is_better:
                            best_model = max(metric_values_by_model.items(), key=lambda x: x[1])[0]
                        else:
                            best_model = min(metric_values_by_model.items(), key=lambda x: x[1])[0]
                            
                        best_model_by_metric[metric] = best_model
                
                comparison["summary"]["best_model_by_metric"] = best_model_by_metric
                
                # Determinar el mejor modelo general basado en métricas de negocio
                if business_metrics:
                    business_scores = {}
                    
                    for model_id in model_metrics:
                        score = 0
                        count = 0
                        
                        for metric in business_metrics:
                            if metric in model_metrics[model_id]["metrics"]:
                                score += model_metrics[model_id]["metrics"][metric]
                                count += 1
                        
                        if count > 0:
                            business_scores[model_id] = score / count
                    
                    if business_scores:
                        best_overall_model = max(business_scores.items(), key=lambda x: x[1])[0]
                        comparison["summary"]["best_overall_model"] = best_overall_model
                        comparison["summary"]["best_overall_score"] = business_scores[best_overall_model]
            
            return comparison
            
        except Exception as e:
            log_error(f"Error comparando versiones de modelos: {str(e)}")
            return {"error": str(e)}
            
    async def get_metrics_history(
        self,
        db: AsyncSession,
        account_id: int,
        metric_name: str,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Obtiene el historial de valores para una métrica específica.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            metric_name: Nombre de la métrica
            limit: Número máximo de puntos de datos a devolver
            
        Returns:
            Lista con valores históricos de la métrica
        """
        try:
            stmt = (
                select(
                    ModelMetadata.id, 
                    ModelMetadata.performance_metrics,
                    ModelMetadata.training_date,
                    ModelMetadata.artifact_version
                )
                .where(ModelMetadata.account_id == account_id)
                .order_by(ModelMetadata.training_date.desc())
                .limit(limit)
            )
            
            result = await db.execute(stmt)
            models = result.fetchall()
            
            history = []
            
            for model in models:
                if model.performance_metrics and metric_name in model.performance_metrics:
                    history.append({
                        "model_id": model.id,
                        "value": model.performance_metrics[metric_name],
                        "date": model.training_date.isoformat() if model.training_date else None,
                        "version": model.artifact_version
                    })
            
            return history
            
        except Exception as e:
            log_error(f"Error obteniendo historial de métricas: {str(e)}")
            return []
