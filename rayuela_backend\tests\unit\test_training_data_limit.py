import pytest
import pytest_asyncio
from unittest.mock import Async<PERSON><PERSON>, MagicMock, patch
from sqlalchemy.ext.asyncio import AsyncSession
from src.services.limit_service import LimitService
from src.core.exceptions import LimitExceededError
from src.db.enums import SubscriptionPlan
from src.ml_pipeline.training_pipeline import DataF<PERSON>cher


@pytest_asyncio.fixture
async def mock_db():
    """Mock database session."""
    return AsyncMock(spec=AsyncSession)


@pytest_asyncio.fixture
async def mock_subscription_service():
    """Mock subscription service."""
    mock = AsyncMock()
    return mock


@pytest_asyncio.fixture
async def mock_account_service():
    """Mock account service."""
    mock = AsyncMock()
    return mock


@pytest.mark.asyncio
async def test_validate_training_data_limit_under_limit(mock_db, mock_subscription_service, mock_account_service):
    """Test that validate_training_data_limit passes when under the limit."""
    # Setup
    account_id = 1
    interaction_count = 5000  # Under the FREE plan limit of 10000
    
    # Create a mock subscription with FREE plan
    mock_subscription = MagicMock()
    mock_subscription.plan_type = SubscriptionPlan.FREE
    
    # Setup the subscription service to return our mock subscription
    mock_subscription_service.get_subscription.return_value = mock_subscription
    
    # Create the limit service with our mocks
    limit_service = LimitService(mock_db, account_id)
    limit_service._subscription_service = mock_subscription_service
    limit_service._account_service = mock_account_service
    
    # Execute
    await limit_service.validate_training_data_limit(interaction_count)
    
    # Verify
    mock_subscription_service.get_subscription.assert_called_once_with(account_id)


@pytest.mark.asyncio
async def test_validate_training_data_limit_over_limit(mock_db, mock_subscription_service, mock_account_service):
    """Test that validate_training_data_limit raises an exception when over the limit."""
    # Setup
    account_id = 1
    interaction_count = 15000  # Over the FREE plan limit of 10000
    
    # Create a mock subscription with FREE plan
    mock_subscription = MagicMock()
    mock_subscription.plan_type = SubscriptionPlan.FREE
    
    # Setup the subscription service to return our mock subscription
    mock_subscription_service.get_subscription.return_value = mock_subscription
    
    # Create the limit service with our mocks
    limit_service = LimitService(mock_db, account_id)
    limit_service._subscription_service = mock_subscription_service
    limit_service._account_service = mock_account_service
    
    # Execute and verify
    with pytest.raises(LimitExceededError) as excinfo:
        await limit_service.validate_training_data_limit(interaction_count)
    
    # Verify the exception message contains useful information
    assert "Training data limit exceeded" in str(excinfo.value)
    assert "10000" in str(excinfo.value)  # The limit
    assert "15000" in str(excinfo.value)  # The attempted count


@pytest.mark.asyncio
async def test_validate_training_data_limit_unlimited(mock_db, mock_subscription_service, mock_account_service):
    """Test that validate_training_data_limit passes for unlimited plans."""
    # Setup
    account_id = 1
    interaction_count = 2000000  # A large number
    
    # Create a mock subscription with ENTERPRISE plan (unlimited)
    mock_subscription = MagicMock()
    mock_subscription.plan_type = SubscriptionPlan.ENTERPRISE
    
    # Setup the subscription service to return our mock subscription
    mock_subscription_service.get_subscription.return_value = mock_subscription
    
    # Create the limit service with our mocks
    limit_service = LimitService(mock_db, account_id)
    limit_service._subscription_service = mock_subscription_service
    limit_service._account_service = mock_account_service
    
    # Execute
    await limit_service.validate_training_data_limit(interaction_count)
    
    # Verify
    mock_subscription_service.get_subscription.assert_called_once_with(account_id)


@pytest.mark.asyncio
async def test_fetch_training_data_validates_limits(mock_db):
    """Test that fetch_training_data validates training data limits."""
    # Setup
    account_id = 1
    
    # Mock the database execute method to return a count of interactions
    mock_result = AsyncMock()
    mock_result.scalar_one.return_value = 15000  # Over the FREE plan limit
    mock_db.execute.return_value = mock_result
    
    # Mock the LimitService
    mock_limit_service = AsyncMock()
    mock_limit_service.validate_training_data_limit.side_effect = LimitExceededError(
        "Training data limit exceeded. Your plan (FREE) allows up to 10000 interactions for training. "
        "Attempted to use 15000 interactions."
    )
    
    # Patch the LimitService class to return our mock
    with patch('src.services.limit_service.LimitService', return_value=mock_limit_service):
        # Create the data fetcher
        data_fetcher = DataFetcher()
        
        # Execute and verify
        with pytest.raises(LimitExceededError) as excinfo:
            await data_fetcher.fetch_training_data(
                db=mock_db,
                account_id=account_id,
                validate_limits=True
            )
        
        # Verify the exception message
        assert "Training data limit exceeded" in str(excinfo.value)
        
        # Verify the limit service was called with the correct count
        mock_limit_service.validate_training_data_limit.assert_called_once_with(15000)
