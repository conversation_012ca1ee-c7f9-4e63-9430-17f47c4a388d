from fastapi import APIRouter, Depends, status, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List
from src.db.session import get_db
from src.core.deps import (
    get_current_active_user,
    get_current_system_user,
    get_current_admin_user,
    get_current_account,
    get_permission_service,
)
from src.core.security import get_password_hash
from src.db import models, schemas
from src.core.exceptions import (
    InvalidCredentialsError,
    ResourceNotFoundError,
    DuplicateEntryError,
)
from src.utils.base_logger import logger, log_info, log_error
from src.utils.security import verify_resource_ownership
from src.db.repositories.auth import SystemUserRoleRepository
from src.services.permission_service import PermissionService

router = APIRouter()


@router.get("/me", response_model=schemas.SystemUserResponse)
async def get_current_user_info(
    current_user: models.SystemUser = Depends(get_current_system_user),
):
    """Get information about the current authenticated user.

    This endpoint only requires a valid JWT token, no API Key is needed.
    It's useful for the initial login flow when the user hasn't confirmed
    the API Key yet.
    """
    return current_user


@router.post("/", response_model=schemas.SystemUserResponse)
async def create_system_user(
    user: schemas.SystemUserCreate,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
):
    """Create a new system user."""
    try:
        async with db.begin():
            # Verificar si el email ya existe
            existing_user = await db.execute(
                select(models.SystemUser).where(
                    models.SystemUser.email == user.email,
                    models.SystemUser.account_id == current_account.account_id,
                )
            )
            if existing_user.scalar_one_or_none():
                raise DuplicateEntryError("Email already registered")

            db_user = models.SystemUser(
                **user.model_dump(exclude={"password"}),
                account_id=current_account.account_id,
                hashed_password=get_password_hash(user.password),
            )
            db.add(db_user)
            await db.refresh(db_user)
            return db_user
    except DuplicateEntryError:
        raise
    except Exception as e:
        logger.error(f"Error creating system user: {str(e)}")
        raise HTTPException(status_code=500, detail="Error creating system user")


@router.put("/me", response_model=schemas.SystemUserResponse)
async def update_user_me(
    user: schemas.SystemUserUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: models.SystemUser = Depends(get_current_active_user),
):
    """Update current user's information."""
    try:
        async with db.begin():
            if user.password:
                current_user.hashed_password = get_password_hash(user.password)
            if user.email:
                current_user.email = user.email

            db.add(current_user)
            await db.refresh(current_user)
            return current_user
    except Exception as e:
        logger.error(f"Error updating user: {str(e)}")
        raise


@router.delete("/me", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_me(
    db: AsyncSession = Depends(get_db),
    current_user: models.SystemUser = Depends(get_current_active_user),
):
    """Delete current user."""
    try:
        async with db.begin():
            await db.delete(current_user)
        return {"detail": "User deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting user: {str(e)}")
        raise


@router.get("/{user_id}", response_model=schemas.SystemUserResponse)
async def get_system_user(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
):
    """Get a system user by ID."""
    # Usar la función de verificación de propiedad
    system_user = await verify_resource_ownership(
        db=db,
        model=models.SystemUser,
        resource_id=user_id,
        account_id=current_account.account_id
    )
    return system_user


@router.post("/roles/", response_model=schemas.Role)
async def create_role(
    role: schemas.RoleCreate,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
):
    """Create a new role."""
    try:
        async with db.begin():
            db_role = models.Role(
                **role.model_dump(), account_id=current_account.account_id
            )
            db.add(db_role)
            await db.refresh(db_role)
            return db_role
    except Exception as e:
        logger.error(f"Error creating role: {str(e)}")
        raise HTTPException(status_code=500, detail="Error creating role")


@router.post("/{user_id}/roles/{role_id}", status_code=status.HTTP_200_OK)
async def assign_role(
    user_id: int,
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """Assign a role to a system user."""
    try:
        async with db.begin():
            # Verificar usuario usando la función de verificación de propiedad
            system_user = await verify_resource_ownership(
                db=db,
                model=models.SystemUser,
                resource_id=user_id,
                account_id=current_account.account_id
            )

            # Verificar rol usando la función de verificación de propiedad
            role = await verify_resource_ownership(
                db=db,
                model=models.Role,
                resource_id=role_id,
                account_id=current_account.account_id
            )

            # Usar el repositorio para asignar el rol
            user_role_repo = SystemUserRoleRepository(db, account_id=current_account.account_id)
            await user_role_repo.assign_role(user_id, role_id)

            # Log the action
            log_info(f"Role {role_id} assigned to user {user_id} by admin {current_user.id}")

            return {"message": "Role assigned successfully"}
    except ResourceNotFoundError:
        raise
    except Exception as e:
        log_error(f"Error assigning role: {str(e)}")
        raise HTTPException(status_code=500, detail="Error assigning role")


@router.delete("/{user_id}/roles/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_role(
    user_id: int,
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """Remove a role from a system user."""
    try:
        async with db.begin():
            # Verificar usuario usando la función de verificación de propiedad
            await verify_resource_ownership(
                db=db,
                model=models.SystemUser,
                resource_id=user_id,
                account_id=current_account.account_id
            )

            # Verificar rol usando la función de verificación de propiedad
            await verify_resource_ownership(
                db=db,
                model=models.Role,
                resource_id=role_id,
                account_id=current_account.account_id
            )

            # Usar el repositorio para eliminar el rol
            user_role_repo = SystemUserRoleRepository(db, account_id=current_account.account_id)
            await user_role_repo.remove_role(user_id, role_id)

            # Log the action
            log_info(f"Role {role_id} removed from user {user_id} by admin {current_user.id}")

            return None
    except ResourceNotFoundError:
        raise
    except Exception as e:
        log_error(f"Error removing role: {str(e)}")
        raise HTTPException(status_code=500, detail="Error removing role")


@router.get("/{user_id}/roles", response_model=List[schemas.Role])
async def get_user_roles(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """Get all roles assigned to a system user."""
    try:
        # Verificar usuario usando la función de verificación de propiedad
        await verify_resource_ownership(
            db=db,
            model=models.SystemUser,
            resource_id=user_id,
            account_id=current_account.account_id
        )

        # Usar el repositorio para obtener los roles
        user_role_repo = SystemUserRoleRepository(db, account_id=current_account.account_id)
        roles = await user_role_repo.get_user_roles(user_id)

        return roles
    except ResourceNotFoundError:
        raise
    except Exception as e:
        log_error(f"Error getting user roles: {str(e)}")
        raise HTTPException(status_code=500, detail="Error getting user roles")


@router.get("/{user_id}/permissions", response_model=List[str])
async def get_user_permissions(
    user_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
    permission_service: PermissionService = Depends(get_permission_service),
):
    """Get all permissions assigned to a system user through their roles."""
    try:
        # Verificar usuario usando la función de verificación de propiedad
        await verify_resource_ownership(
            db=db,
            model=models.SystemUser,
            resource_id=user_id,
            account_id=current_account.account_id
        )

        # Usar el servicio para obtener los permisos
        permissions = await permission_service.get_user_permissions(user_id)

        return list(permissions)
    except ResourceNotFoundError:
        raise
    except Exception as e:
        log_error(f"Error getting user permissions: {str(e)}")
        raise HTTPException(status_code=500, detail="Error getting user permissions")
