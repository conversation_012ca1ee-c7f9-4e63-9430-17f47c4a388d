"""
Servicio para operaciones relacionadas con productos.
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.models import Product
from src.db.repositories.base import BaseRepository
from src.core.exceptions import NotFoundError, ValidationError


class ProductService:
    """
    Servicio para operaciones relacionadas con productos.

    Este servicio proporciona métodos para crear, actualizar, eliminar y consultar
    productos, gestionando la lógica de negocio relacionada con los productos.
    """

    def __init__(self, db: AsyncSession):
        """
        Inicializa el servicio de productos.

        Args:
            db: Sesión de base de datos
        """
        self.db = db
        self.repository = BaseRepository(db, model=Product)

    async def get(self, account_id: int, product_id: int) -> Product:
        """
        Obtiene un producto por su ID.

        Args:
            account_id: ID de la cuenta
            product_id: ID del producto

        Returns:
            Producto encontrado

        Raises:
            NotFoundError: Si el producto no existe
        """
        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        product = await self.repository.get_by_id(product_id)
        if not product:
            raise NotFoundError(f"Product with ID {product_id} not found")
        return product

    async def list(
        self,
        account_id: int,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        category: Optional[str] = None,
        include_deleted: bool = False
    ) -> List[Product]:
        """
        Lista los productos que cumplan con los criterios especificados.

        Args:
            account_id: ID de la cuenta
            skip: Número de registros a saltar
            limit: Número máximo de registros a devolver
            search: Texto para búsqueda
            category: Categoría para filtrar
            include_deleted: Si se deben incluir productos eliminados

        Returns:
            Lista de productos
        """
        filters = {}

        # Asignar account_id para filtrar por tenant
        self.repository.account_id = account_id

        # Usar get_by_filters en lugar de list
        if search:
            # Para búsquedas de texto, necesitamos usar get_all y filtrar manualmente
            # ya que get_by_filters no soporta operadores como ilike
            products = await self.repository.get_all(skip=skip, limit=limit)
            filtered_products = []
            for p in products:
                # Verificar si el texto de búsqueda está en el nombre
                name_match = False
                if hasattr(p, 'name') and isinstance(p.name, str):
                    name_match = search.lower() in p.name.lower()

                # Verificar si el texto de búsqueda está en la descripción
                desc_match = False
                if hasattr(p, 'description') and isinstance(p.description, str):
                    desc_match = search.lower() in p.description.lower()

                # Verificar si coincide con la categoría (si se especificó)
                category_match = True
                if category and hasattr(p, 'category'):
                    category_match = str(p.category) == str(category)

                # Agregar el producto si cumple con todos los criterios
                if (name_match or desc_match) and category_match:
                    filtered_products.append(p)

            return filtered_products

        # Si no hay búsqueda de texto, podemos usar filtros directos
        if category:
            filters["category"] = category

        return await self.repository.get_by_filters(
            filters=filters,
            skip=skip,
            limit=limit,
            include_deleted=include_deleted
        )

    async def create(self, account_id: int, data: Dict[str, Any]) -> Product:
        """
        Crea un nuevo producto.

        Args:
            account_id: ID de la cuenta
            data: Datos del producto

        Returns:
            Producto creado

        Raises:
            ValidationError: Si los datos son inválidos
        """
        # Validación básica
        if not data.get("name"):
            raise ValidationError("Product name is required")

        if "price" in data and (not isinstance(data["price"], (int, float)) or data["price"] < 0):
            raise ValidationError("Product price must be a non-negative number")

        # Asignar account_id
        data["account_id"] = account_id

        # Crear producto dentro de una transacción
        async with self.db.begin():
            # Convertir a formato esperado por el repositorio
            return await self.repository.create(obj_in=data)

    async def update(self, account_id: int, product_id: int, data: Dict[str, Any]) -> Product:
        """
        Actualiza un producto existente.

        Args:
            account_id: ID de la cuenta
            product_id: ID del producto
            data: Datos a actualizar

        Returns:
            Producto actualizado

        Raises:
            NotFoundError: Si el producto no existe
            ValidationError: Si los datos son inválidos
        """
        # Validación básica
        if "price" in data and (not isinstance(data["price"], (int, float)) or data["price"] < 0):
            raise ValidationError("Product price must be a non-negative number")

        # Verificar que el producto existe y asignar account_id para filtrar por tenant
        self.repository.account_id = account_id
        await self.get(account_id, product_id)  # Esto lanzará NotFoundError si no existe

        # Actualizar producto dentro de una transacción
        async with self.db.begin():
            # Convertir a formato esperado por el repositorio
            updated_product = await self.repository.update(id=product_id, obj_in=data)
            if not updated_product:
                raise NotFoundError(f"Product with ID {product_id} not found")
            return updated_product

    async def delete(self, account_id: int, product_id: int, soft: bool = True) -> None:
        """
        Elimina un producto.

        Args:
            account_id: ID de la cuenta
            product_id: ID del producto
            soft: Si se debe hacer una eliminación suave (soft delete)

        Raises:
            NotFoundError: Si el producto no existe
        """
        # Verificar que el producto existe y asignar account_id para filtrar por tenant
        self.repository.account_id = account_id
        await self.get(account_id, product_id)  # Esto lanzará NotFoundError si no existe

        # Eliminar producto dentro de una transacción
        async with self.db.begin():
            success = False
            if soft:
                # Soft delete
                success = await self.repository.soft_delete(id=product_id)
            else:
                # Hard delete
                success = await self.repository.delete(id=product_id)

            if not success:
                raise NotFoundError(f"Product with ID {product_id} not found or could not be deleted")