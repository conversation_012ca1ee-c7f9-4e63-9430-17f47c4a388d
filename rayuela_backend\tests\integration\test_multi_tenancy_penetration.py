"""
Tests de penetración específicos para multi-tenancy.
Diseñados para detectar vulnerabilidades críticas de aislamiento de datos.

Estos tests intentan explotar vulnerabilidades comunes en sistemas multi-tenant:
- Inyección de account_id en URLs, headers y payloads
- Bypass de middleware de tenant
- Escalación de privilegios entre tenants
- Filtraciones en cache y sesiones
"""

import pytest
import json
import uuid
from typing import Dict, Any, List, Optional
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import text, func
import asyncio
import time

from src.db.session import DatabaseConnectionManager, get_db
from src.db.models import (
    Account,
    Product,
    EndUser,
    Interaction,
    ModelMetadata,
    AuditLog,
    TrainingJob,
    SystemUser,
    Search,
    Recommendation,
    Notification,
)
from src.core.config import settings
from src.utils.base_logger import log_info, log_error
from src.core.deps import get_current_account
from src.db.repositories.base import BaseRepository


class TestMultiTenancyPenetration:
    """Tests de penetración para detectar vulnerabilidades de multi-tenancy."""

    @pytest.fixture
    async def db_session(self) -> AsyncSession:
        """Fixture para sesión de base de datos."""
        connection_manager = await DatabaseConnectionManager.get_instance()
        session = await connection_manager.get_session()
        try:
            yield session
        finally:
            await session.close()

    @pytest.fixture
    async def isolated_test_accounts(self, db_session: AsyncSession) -> List[Account]:
        """Crear cuentas aisladas para tests de penetración."""
        accounts = []
        for i in range(3):
            account = Account(
                name=f"Penetration Test Account {i}",
                email=f"pentest_{i}_{uuid.uuid4().hex[:8]}@example.com",
                status="active",
                api_key_hash=f"pentest_hash_{i}_{uuid.uuid4().hex[:16]}",
                api_key_prefix=f"pen_{i}",
                api_key_last_chars=f"test_{i}",
            )
            db_session.add(account)
            await db_session.commit()
            await db_session.refresh(account)
            accounts.append(account)
        return accounts

    @pytest.fixture
    async def isolated_test_data(
        self, db_session: AsyncSession, isolated_test_accounts: List[Account]
    ) -> Dict[str, Any]:
        """Crear datos aislados para tests de penetración."""
        test_data = {"accounts": isolated_test_accounts}

        for i, account in enumerate(isolated_test_accounts):
            # Crear productos únicos para cada cuenta
            products = []
            for j in range(2):
                product = Product(
                    account_id=account.account_id,
                    name=f"CONFIDENTIAL Product {i}-{j}",
                    description=f"SECRET: Data for account {account.account_id} only",
                    price=999.99 + i + j,  # Precios únicos para detectar filtraciones
                    category=f"RESTRICTED_category_{i}_{j}",
                    inventory_count=i * 1000 + j,  # Inventarios únicos
                )
                db_session.add(product)
                await db_session.flush()
                products.append(product)

            # Crear usuarios únicos con IDs específicos
            users = []
            for j in range(2):
                user = EndUser(
                    account_id=account.account_id,
                    external_id=f"PRIVATE_user_{i}_{j}_{uuid.uuid4().hex[:8]}",
                    is_active=True,
                )
                db_session.add(user)
                await db_session.flush()
                users.append(user)

            # Crear interacciones con datos específicos
            interactions = []
            for product in products:
                for user in users:
                    interaction = Interaction(
                        account_id=account.account_id,
                        end_user_id=user.id,
                        product_id=product.id,
                        interaction_type="CONFIDENTIAL_VIEW",
                        value=float(f"{i}.{j}{account.account_id}"),  # Valores únicos
                    )
                    db_session.add(interaction)
                    await db_session.flush()
                    interactions.append(interaction)

            # Crear búsquedas con queries específicas
            searches = []
            for j in range(2):
                search = Search(
                    account_id=account.account_id,
                    search_query=f"SECRET_query_account_{account.account_id}_{j}",
                    results_count=i + j + 1,
                    response_time_ms=i * 100 + j * 10,
                )
                db_session.add(search)
                await db_session.flush()
                searches.append(search)

            await db_session.commit()

            test_data[f"account_{account.account_id}"] = {
                "products": products,
                "users": users,
                "interactions": interactions,
                "searches": searches,
            }

        return test_data

    @pytest.mark.asyncio
    async def test_account_id_injection_in_url_paths(
        self, client: TestClient, isolated_test_data: Dict[str, Any]
    ):
        """Test 1: Intentar inyectar account_id en rutas URL."""
        accounts = isolated_test_data["accounts"]
        victim_account = accounts[0]
        attacker_account = accounts[1]

        # Autenticarse como atacante
        # (Simulamos autenticación exitosa)
        attacker_token = f"mock_token_for_account_{attacker_account.account_id}"

        # Intentar acceder a datos de la víctima mediante manipulación de URL
        malicious_attempts = [
            f"/api/v1/accounts/{victim_account.account_id}/products",
            f"/api/v1/products?account_id={victim_account.account_id}",
            f"/api/v1/interactions/{victim_account.account_id}",
            f"/api/v1/users?force_account_id={victim_account.account_id}",
            # Intentos con encoding
            f"/api/v1/products?account_id=%{victim_account.account_id:02x}",
            # Intentos con SQL injection style
            f"/api/v1/products?account_id={victim_account.account_id}%20OR%201=1",
        ]

        for malicious_url in malicious_attempts:
            response = client.get(
                malicious_url,
                headers={"Authorization": f"Bearer {attacker_token}"},
            )

            # Verificar que el acceso fue denegado o que solo retorna datos del atacante
            assert response.status_code in [
                401,
                403,
                404,
            ], f"VULNERABILIDAD: URL {malicious_url} permitió acceso no autorizado"

            if response.status_code == 200:
                # Si retorna 200, verificar que NO contiene datos de la víctima
                response_data = response.json()
                victim_data = isolated_test_data[f"account_{victim_account.account_id}"]

                # Verificar que no hay productos de la víctima
                if "products" in response_data or isinstance(response_data, list):
                    products_in_response = (
                        response_data.get("products", [])
                        if isinstance(response_data, dict)
                        else response_data
                    )
                    for product in products_in_response:
                        for victim_product in victim_data["products"]:
                            assert product.get("id") != victim_product.id, (
                                f"FILTRACIÓN CRÍTICA: Producto {victim_product.id} "
                                f"de cuenta {victim_account.account_id} expuesto a "
                                f"cuenta {attacker_account.account_id}"
                            )

    @pytest.mark.asyncio
    async def test_account_id_injection_in_headers(
        self, client: TestClient, isolated_test_data: Dict[str, Any]
    ):
        """Test 2: Intentar inyectar account_id via headers HTTP."""
        accounts = isolated_test_data["accounts"]
        victim_account = accounts[0]
        attacker_account = accounts[1]

        attacker_token = f"mock_token_for_account_{attacker_account.account_id}"

        # Headers maliciosos para intentar bypassear el tenant middleware
        malicious_headers = [
            {"X-Account-ID": str(victim_account.account_id)},
            {"X-Tenant-ID": str(victim_account.account_id)},
            {"X-Account-Override": str(victim_account.account_id)},
            {"X-Force-Account": str(victim_account.account_id)},
            {"Account-ID": str(victim_account.account_id)},
            {"Tenant-Context": str(victim_account.account_id)},
            # Headers con encoding
            {"X-Account-ID": f"%{victim_account.account_id:02x}"},
            # Headers múltiples
            {
                "X-Account-ID": str(attacker_account.account_id),
                "X-Real-Account-ID": str(victim_account.account_id),
            },
        ]

        for malicious_header in malicious_headers:
            headers = {"Authorization": f"Bearer {attacker_token}"}
            headers.update(malicious_header)

            response = client.get("/api/v1/products", headers=headers)

            # Verificar que el header malicioso no afecta el aislamiento
            if response.status_code == 200:
                products = response.json()
                victim_data = isolated_test_data[f"account_{victim_account.account_id}"]

                for product in products:
                    for victim_product in victim_data["products"]:
                        assert product.get("id") != victim_product.id, (
                            f"FILTRACIÓN CRÍTICA via HEADER: Producto {victim_product.id} "
                            f"de cuenta {victim_account.account_id} expuesto mediante header "
                            f"{malicious_header}"
                        )

    @pytest.mark.asyncio
    async def test_account_id_injection_in_request_body(
        self, client: TestClient, isolated_test_data: Dict[str, Any]
    ):
        """Test 3: Intentar inyectar account_id en el body de requests."""
        accounts = isolated_test_data["accounts"]
        victim_account = accounts[0]
        attacker_account = accounts[1]

        attacker_token = f"mock_token_for_account_{attacker_account.account_id}"

        # Payloads maliciosos para CREATE/UPDATE operations
        malicious_payloads = [
            # Producto con account_id de víctima
            {
                "name": "Malicious Product",
                "account_id": victim_account.account_id,
                "price": 10.99,
                "category": "attack",
            },
            # Usuario con account_id de víctima
            {
                "external_id": "malicious_user",
                "account_id": victim_account.account_id,
                "is_active": True,
            },
            # Interacción con IDs de víctima
            {
                "product_id": 1,  # Producto de víctima
                "end_user_id": 1,  # Usuario de víctima
                "account_id": victim_account.account_id,
                "interaction_type": "malicious_view",
                "value": 1.0,
            },
            # Búsqueda forzando account_id
            {
                "search_query": "attack query",
                "account_id": victim_account.account_id,
                "results_count": 10,
            },
        ]

        endpoints_to_test = [
            "/api/v1/products",
            "/api/v1/users",
            "/api/v1/interactions",
            "/api/v1/searches",
        ]

        for endpoint in endpoints_to_test:
            for payload in malicious_payloads:
                response = client.post(
                    endpoint,
                    json=payload,
                    headers={"Authorization": f"Bearer {attacker_token}"},
                )

                # Verificar que la creación fue rechazada o asignada al account correcto
                if response.status_code in [200, 201]:
                    created_item = response.json()
                    assert (
                        created_item.get("account_id") == attacker_account.account_id
                    ), (
                        f"VULNERABILIDAD CRÍTICA: Item creado en endpoint {endpoint} "
                        f"fue asignado incorrectamente a account_id {created_item.get('account_id')} "
                        f"en lugar del atacante {attacker_account.account_id}"
                    )

    @pytest.mark.asyncio
    async def test_concurrent_session_isolation(
        self, isolated_test_data: Dict[str, Any]
    ):
        """Test 4: Verificar aislamiento en sesiones concurrentes."""
        accounts = isolated_test_data["accounts"]

        async def simulate_user_session(account: Account, session_id: str):
            """Simular una sesión de usuario específica."""
            # Simular operaciones concurrentes en diferentes accounts
            async for db in get_db():
                # Verificar que cada sesión solo ve sus propios datos
                result = await db.execute(
                    text("SELECT * FROM products WHERE account_id = :account_id"),
                    {"account_id": account.account_id},
                )
                products = result.fetchall()

                # Verificar que solo vemos productos de nuestra cuenta
                for product in products:
                    assert product.account_id == account.account_id, (
                        f"FILTRACIÓN en sesión {session_id}: "
                        f"Producto {product.id} de cuenta {product.account_id} "
                        f"visible en sesión de cuenta {account.account_id}"
                    )

                return len(products)

        # Ejecutar sesiones concurrentes
        tasks = []
        for i, account in enumerate(accounts):
            task = simulate_user_session(account, f"session_{i}")
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        # Verificar que cada sesión retornó datos únicos
        for i, count in enumerate(results):
            assert count > 0, f"Cuenta {accounts[i].account_id} no tiene datos visibles"

    @pytest.mark.asyncio
    async def test_cache_isolation_bypass(self, isolated_test_data: Dict[str, Any]):
        """Test 5: Intentar bypassear aislamiento mediante cache poisoning."""
        accounts = isolated_test_data["accounts"]
        victim_account = accounts[0]
        attacker_account = accounts[1]

        # Intentar contaminar cache con claves que incluyan account_id de víctima
        cache_poisoning_attempts = [
            f"products:account:{victim_account.account_id}",
            f"user_session:{victim_account.account_id}",
            f"recommendations:{victim_account.account_id}",
        ]

        # Este test verificaría que el sistema no permite acceso a cache
        # de otros tenants mediante manipulación de claves
        for cache_key in cache_poisoning_attempts:
            # Intentar acceder a datos cacheados de otro tenant
            # (Implementación dependería del sistema de cache específico)
            pass

    @pytest.mark.asyncio
    async def test_rls_bypass_attempts(
        self, db_session: AsyncSession, isolated_test_data: Dict[str, Any]
    ):
        """Test 6: Intentar bypassear políticas RLS con queries directos."""
        accounts = isolated_test_data["accounts"]
        victim_account = accounts[0]

        # Intentos de bypass RLS mediante queries SQL directos
        bypass_attempts = [
            # Intentar deshabilitar RLS
            "SET row_security = off;",
            # Intentar cambiar usuario
            f"SET session_replication_role = replica;",
            # Intentar queries sin filtros de account_id
            "SELECT * FROM products;",
            # Intentar queries con UNION para combinar datos
            f"SELECT * FROM products WHERE account_id = 999999 UNION SELECT * FROM products WHERE account_id = {victim_account.account_id};",
            # Intentar con comentarios SQL
            f"SELECT * FROM products WHERE account_id = 1 /*OR account_id = {victim_account.account_id}*/;",
        ]

        for malicious_query in bypass_attempts:
            try:
                result = await db_session.execute(text(malicious_query))
                rows = result.fetchall()

                # Verificar que no se obtuvieron datos no autorizados
                for row in rows:
                    if hasattr(row, "account_id"):
                        # Si retorna datos, verificar que son solo del tenant actual
                        # (en tests, no hay tenant context set, así que no debería retornar nada)
                        assert False, (
                            f"VULNERABILIDAD RLS: Query '{malicious_query}' "
                            f"retornó datos no autorizados: {row}"
                        )

            except Exception as e:
                # Se espera que estos queries fallen por restricciones de seguridad
                pass

    @pytest.mark.asyncio
    async def test_privilege_escalation_attempts(
        self, isolated_test_data: Dict[str, Any]
    ):
        """Test 7: Intentar escalar privilegios entre tenants."""
        accounts = isolated_test_data["accounts"]
        low_privilege_account = accounts[2]  # Cuenta con privilegios limitados

        # Intentar acceder a funciones administrativas de otros tenants
        privilege_escalation_attempts = [
            # Intentar ejecutar funciones de administración
            "cleanup_old_data",
            "reset_account_data",
            "export_account_data",
            # Intentar acceder a métricas de otros accounts
            "get_account_usage_metrics",
            "get_billing_information",
        ]

        for function_name in privilege_escalation_attempts:
            # Verificar que las funciones administrativas están debidamente protegidas
            # (La implementación específica dependería de cómo están definidas estas funciones)
            pass

    @pytest.mark.asyncio
    async def test_timing_attack_resistance(self, isolated_test_data: Dict[str, Any]):
        """Test 8: Verificar resistencia a ataques de timing."""
        accounts = isolated_test_data["accounts"]
        valid_account = accounts[0]
        invalid_account_id = 999999

        # Medir tiempo de respuesta para account_id válido vs inválido
        start_time = time.time()
        async for db in get_db():
            try:
                result = await db.execute(
                    text(
                        "SELECT COUNT(*) FROM products WHERE account_id = :account_id"
                    ),
                    {"account_id": valid_account.account_id},
                )
                valid_count = result.scalar()
            except:
                pass
        valid_time = time.time() - start_time

        start_time = time.time()
        async for db in get_db():
            try:
                result = await db.execute(
                    text(
                        "SELECT COUNT(*) FROM products WHERE account_id = :account_id"
                    ),
                    {"account_id": invalid_account_id},
                )
                invalid_count = result.scalar()
            except:
                pass
        invalid_time = time.time() - start_time

        # Verificar que no hay diferencias significativas de timing
        time_difference = abs(valid_time - invalid_time)
        assert time_difference < 0.1, (
            f"VULNERABILIDAD DE TIMING: Diferencia significativa en tiempo de respuesta "
            f"({time_difference:.3f}s) podría revelar información sobre existencia de datos"
        )

    @pytest.mark.asyncio
    async def test_metadata_leakage_prevention(
        self, isolated_test_data: Dict[str, Any]
    ):
        """Test 9: Verificar que no se filtra metadata entre tenants."""
        accounts = isolated_test_data["accounts"]

        # Verificar que errores y metadata no revelan información de otros tenants
        async for db in get_db():
            try:
                # Intentar queries que podrían revelar estructura de datos de otros tenants
                result = await db.execute(
                    text(
                        """
                        SELECT table_name, column_name 
                        FROM information_schema.columns 
                        WHERE table_schema = 'public' 
                        AND table_name IN ('products', 'end_users', 'interactions')
                    """
                    )
                )
                schema_info = result.fetchall()

                # Verificar que la información del esquema no revela datos específicos de tenants
                for row in schema_info:
                    # No debería haber columnas que contengan account_ids específicos en sus nombres
                    assert not any(
                        str(account.account_id) in str(row).lower()
                        for account in accounts
                    ), f"FILTRACIÓN DE METADATA: Esquema revela información específica de tenant"

            except Exception as e:
                # Se espera que algunas queries fallen por restricciones de seguridad
                pass


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
