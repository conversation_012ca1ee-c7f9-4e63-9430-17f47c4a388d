#!/usr/bin/env python
import os
import sys
import click
import subprocess
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)


@click.group()
def cli():
    """Comandos para ejecutar tests del sistema de recomendación"""
    pass


@cli.command()
@click.option("--coverage", is_flag=True, help="Ejecutar con cobertura de código")
def unit(coverage):
    """Ejecutar tests unitarios"""
    cmd = ["pytest", "tests/unit", "-v"]

    if coverage:
        cmd = [
            "pytest",
            "tests/unit",
            "-v",
            "--cov=src",
            "--cov-report=term",
            "--cov-report=html",
        ]

    click.echo("Ejecutando tests unitarios...")
    result = subprocess.run(cmd)
    sys.exit(result.returncode)


@cli.command()
@click.option("--coverage", is_flag=True, help="Ejecutar con cobertura de código")
def integration(coverage):
    """Ejecutar tests de integración"""
    cmd = ["pytest", "tests/integration", "-v"]

    if coverage:
        cmd = [
            "pytest",
            "tests/integration",
            "-v",
            "--cov=src",
            "--cov-report=term",
            "--cov-report=html",
        ]

    click.echo("Ejecutando tests de integración...")
    result = subprocess.run(cmd)
    sys.exit(result.returncode)


@cli.command()
@click.option("--host", default="localhost", help="Host para el servidor")
@click.option("--port", default=8000, help="Puerto para el servidor")
@click.option("--users", default=10, help="Número de usuarios concurrentes")
@click.option(
    "--spawn-rate", default=1, help="Tasa de creación de usuarios por segundo"
)
@click.option("--time", default=60, help="Tiempo de ejecución en segundos")
@click.option(
    "--tags", default=None, help="Tags para filtrar tests (separados por comas)"
)
def load(host, port, users, spawn_rate, time, tags):
    """Ejecutar tests de carga con Locust"""
    cmd = [
        "locust",
        "-f",
        "tests/load/locustfile.py",
        "--host",
        f"http://{host}:{port}",
        "--headless",
        "-u",
        str(users),
        "-r",
        str(spawn_rate),
        "-t",
        f"{time}s",
        "--html",
        "load_test_report.html",
    ]

    if tags:
        cmd.extend(["--tags", tags])

    click.echo(f"Ejecutando tests de carga contra {host}:{port}...")
    result = subprocess.run(cmd)

    click.echo(f"Reporte generado en load_test_report.html")
    sys.exit(result.returncode)


@cli.command()
@click.option("--coverage", is_flag=True, help="Ejecutar con cobertura de código")
def all(coverage):
    """Ejecutar todos los tests (unitarios e integración)"""
    cmd = ["pytest", "tests/unit", "tests/integration", "-v"]

    if coverage:
        cmd = [
            "pytest",
            "tests/unit",
            "tests/integration",
            "-v",
            "--cov=src",
            "--cov-report=term",
            "--cov-report=html",
        ]

    click.echo("Ejecutando todos los tests...")
    result = subprocess.run(cmd)
    sys.exit(result.returncode)


if __name__ == "__main__":
    cli()
