"""
Servicio centralizado para la gestión de caché usando Redis.
"""
from typing import Any, Optional, Dict, List
from redis.asyncio import Redis
import json
from datetime import datetime, timedelta
from src.core.redis_utils import get_redis
from src.utils.base_logger import log_info, log_error

class CacheManager:
    """Gestor centralizado de caché usando Redis."""
    
    def __init__(self, redis: Optional[Redis] = None):
        """Inicializa el gestor de caché."""
        self._redis = redis
        self._default_ttl = 3600  # 1 hora por defecto
    
    async def get_redis(self) -> Redis:
        """Obtiene la instancia de Redis."""
        if not self._redis:
            self._redis = await get_redis()
        return self._redis
    
    async def get(self, key: str, default: Any = None) -> Any:
        """
        Obtiene un valor de la caché.
        
        Args:
            key: Clave del valor a obtener
            default: Valor por defecto si no se encuentra
            
        Returns:
            El valor almacenado o el valor por defecto
        """
        try:
            redis = await self.get_redis()
            value = await redis.get(key)
            if value is None:
                return default
            return json.loads(value)
        except Exception as e:
            log_error(f"Error getting cache value for key {key}: {str(e)}")
            return default
    
    async def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """
        Almacena un valor en la caché.
        
        Args:
            key: Clave para almacenar el valor
            value: Valor a almacenar
            ttl: Tiempo de vida en segundos (opcional)
            nx: Solo establecer si la clave no existe
            xx: Solo establecer si la clave existe
            
        Returns:
            bool: True si se almacenó correctamente
        """
        try:
            redis = await self.get_redis()
            value_json = json.dumps(value)
            ttl = ttl if ttl is not None else self._default_ttl
            
            if nx:
                return await redis.set(key, value_json, ex=ttl, nx=True)
            elif xx:
                return await redis.set(key, value_json, ex=ttl, xx=True)
            else:
                return await redis.set(key, value_json, ex=ttl)
        except Exception as e:
            log_error(f"Error setting cache value for key {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Elimina un valor de la caché.
        
        Args:
            key: Clave a eliminar
            
        Returns:
            bool: True si se eliminó correctamente
        """
        try:
            redis = await self.get_redis()
            return await redis.delete(key) > 0
        except Exception as e:
            log_error(f"Error deleting cache value for key {key}: {str(e)}")
            return False
    
    async def delete_pattern(self, pattern: str) -> bool:
        """
        Elimina valores que coinciden con un patrón.
        
        Args:
            pattern: Patrón de claves a eliminar
            
        Returns:
            bool: True si se eliminaron correctamente
        """
        try:
            redis = await self.get_redis()
            keys = await redis.keys(pattern)
            if keys:
                await redis.delete(*keys)
            return True
        except Exception as e:
            log_error(f"Error deleting cache pattern {pattern}: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Verifica si una clave existe en la caché.
        
        Args:
            key: Clave a verificar
            
        Returns:
            bool: True si la clave existe
        """
        try:
            redis = await self.get_redis()
            return await redis.exists(key) > 0
        except Exception as e:
            log_error(f"Error checking cache existence for key {key}: {str(e)}")
            return False
    
    async def increment(self, key: str, amount: int = 1) -> int:
        """
        Incrementa un contador en la caché.
        
        Args:
            key: Clave del contador
            amount: Cantidad a incrementar
            
        Returns:
            int: Nuevo valor del contador
        """
        try:
            redis = await self.get_redis()
            return await redis.incrby(key, amount)
        except Exception as e:
            log_error(f"Error incrementing cache counter for key {key}: {str(e)}")
            return 0
    
    async def get_many(self, keys: List[str]) -> Dict[str, Any]:
        """
        Obtiene múltiples valores de la caché.
        
        Args:
            keys: Lista de claves a obtener
            
        Returns:
            Dict[str, Any]: Diccionario con los valores encontrados
        """
        try:
            redis = await self.get_redis()
            values = await redis.mget(keys)
            result = {}
            for key, value in zip(keys, values):
                if value is not None:
                    result[key] = json.loads(value)
            return result
        except Exception as e:
            log_error(f"Error getting multiple cache values: {str(e)}")
            return {}
    
    async def set_many(
        self,
        mapping: Dict[str, Any],
        ttl: Optional[int] = None
    ) -> bool:
        """
        Almacena múltiples valores en la caché.
        
        Args:
            mapping: Diccionario de claves y valores
            ttl: Tiempo de vida en segundos (opcional)
            
        Returns:
            bool: True si se almacenaron correctamente
        """
        try:
            redis = await self.get_redis()
            ttl = ttl if ttl is not None else self._default_ttl
            pipeline = redis.pipeline()
            
            for key, value in mapping.items():
                value_json = json.dumps(value)
                pipeline.set(key, value_json, ex=ttl)
            
            await pipeline.execute()
            return True
        except Exception as e:
            log_error(f"Error setting multiple cache values: {str(e)}")
            return False 