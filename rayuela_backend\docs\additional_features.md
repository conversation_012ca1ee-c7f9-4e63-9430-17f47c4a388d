# Adiciones al README

## Procedimiento para Testear la Aplicación

El testing es una parte crucial del desarrollo y mantenimiento de la aplicación. A continuación, se detallan los procedimientos para ejecutar pruebas automatizadas y realizar pruebas manuales.

### Pruebas Automatizadas

La aplicación utiliza pytest para las pruebas automatizadas. Estas pruebas verifican el funcionamiento correcto de los componentes individuales y la integración entre ellos.

#### Requisitos Previos

1. Entorno virtual activado con todas las dependencias instaladas (incluidas las de desarrollo)
2. Base de datos de prueba configurada (se puede usar SQLite en memoria para pruebas)
3. Variables de entorno configuradas para el entorno de prueba

#### Ejecutar Todas las Pruebas

```bash
# Desde la raíz del proyecto
pytest

# Para ver más detalles
pytest -v

# Para ver la cobertura de código
pytest --cov=src
```

#### Ejecutar Pruebas Específicas

```bash
# Ejecutar pruebas de un módulo específico
pytest tests/test_api/

# Ejecutar una prueba específica
pytest tests/test_api/test_recommendations.py::test_get_recommendations

# Ejecutar pruebas que coincidan con un patrón
pytest -k "recommendations"
```

#### Pruebas de Integración

Las pruebas de integración verifican que los diferentes componentes de la aplicación funcionen correctamente juntos.

```bash
# Ejecutar solo pruebas de integración
pytest tests/integration/

# Ejecutar pruebas de integración con la base de datos
pytest tests/integration/test_database.py
```

### Pruebas Manuales

Además de las pruebas automatizadas, es importante realizar pruebas manuales para verificar el comportamiento de la aplicación desde la perspectiva del usuario.

#### Pruebas con la Documentación Interactiva

1. Inicia la aplicación en modo de desarrollo:
   ```bash
   python main.py
   ```

2. Abre la documentación interactiva en tu navegador:
   ```
   http://localhost:8001/docs
   ```

3. Prueba los diferentes endpoints:
   - Crea una cuenta y obtén una API Key
   - Carga datos de productos y usuarios
   - Registra interacciones
   - Solicita recomendaciones
   - Verifica que las respuestas sean correctas

#### Pruebas con Herramientas de API

Puedes usar herramientas como Postman, Insomnia o curl para probar la API de forma más detallada.

Ejemplo de prueba con curl:

```bash
# Obtener el estado de la API
curl http://localhost:8001/health

# Autenticarse y obtener un token
curl -X POST http://localhost:8001/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username": "<EMAIL>", "password": "password123"}'

# Usar el token para acceder a un endpoint protegido
curl http://localhost:8001/api/v1/accounts/current \
  -H "Authorization: Bearer TU_TOKEN"

# Usar una API Key para obtener recomendaciones
curl http://localhost:8001/api/v1/recommendations/user/123 \
  -H "X-API-Key: TU_API_KEY"
```

#### Pruebas de Carga

Para verificar el rendimiento de la aplicación bajo carga, puedes usar herramientas como Apache Benchmark (ab) o locust.

Ejemplo con Apache Benchmark:

```bash
# Realizar 100 solicitudes con 10 concurrentes
ab -n 100 -c 10 -H "X-API-Key: TU_API_KEY" http://localhost:8001/api/v1/recommendations/popular
```

## Procedimiento Detallado para Desplegar en Producción

Esta sección proporciona un procedimiento paso a paso para desplegar la aplicación en producción, especialmente diseñado para un solopreneur.

### 1. Preparación del Entorno de Producción

#### 1.1. Crear Cuenta y Proyecto en Google Cloud Platform

1. Regístrate en [Google Cloud Platform](https://cloud.google.com/) si aún no tienes una cuenta.
2. Crea un nuevo proyecto:
   - Ve a la [Consola de Google Cloud](https://console.cloud.google.com/)
   - Haz clic en el selector de proyectos en la parte superior
   - Haz clic en "Nuevo proyecto"
   - Asigna un nombre descriptivo (ej: "rayuela-rayuela-prod")
   - Haz clic en "Crear"

#### 1.2. Configurar Facturación

1. Ve a [Facturación](https://console.cloud.google.com/billing) en la consola de Google Cloud
2. Vincula tu proyecto con una cuenta de facturación
3. Considera establecer alertas de presupuesto para evitar sorpresas

#### 1.3. Habilitar APIs Necesarias

1. Ve a [APIs y Servicios > Biblioteca](https://console.cloud.google.com/apis/library)
2. Busca y habilita las siguientes APIs:
   - Cloud Run API
   - Cloud Build API
   - Artifact Registry API
   - Cloud SQL Admin API
   - Secret Manager API
   - Cloud Storage API
   - Redis API (Memorystore)
   - VPC Access API

#### 1.4. Instalar y Configurar Google Cloud SDK

1. Descarga e instala [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
2. Abre una terminal y ejecuta:
   ```bash
   # Iniciar sesión con tu cuenta de Google
   gcloud auth login

   # Configurar el proyecto por defecto
   gcloud config set project TU_ID_DE_PROYECTO

   # Configurar la región por defecto (elige una cercana a tus usuarios)
   gcloud config set compute/region us-central1
   ```

### 2. Crear Infraestructura en Google Cloud

#### 2.1. Crear Red VPC

1. Ve a [VPC Network > Redes de VPC](https://console.cloud.google.com/networking/networks/list)
2. Haz clic en "Crear red de VPC"
3. Asigna un nombre (ej: "rayuela-network")
4. En "Modo de creación de subred", selecciona "Personalizado"
5. Crea una subred:
   - Nombre: "rayuela-subnet"
   - Región: Tu región elegida (ej: us-central1)
   - Rango de IP: "10.0.0.0/20"
6. Haz clic en "Crear"

#### 2.2. Crear Instancia de Cloud SQL (PostgreSQL)

1. Ve a [SQL](https://console.cloud.google.com/sql) en la consola de Google Cloud
2. Haz clic en "Crear instancia"
3. Selecciona "PostgreSQL"
4. Configura la instancia:
   - ID de instancia: "rayuela-db-prod"
   - Contraseña: Genera una contraseña segura y guárdala
   - Región: La misma que elegiste antes
   - Versión: PostgreSQL 15
   - Configuración de la máquina: Elige según tus necesidades (para empezar, db-g1-small puede ser suficiente)
   - Almacenamiento: 10 GB (aumenta según necesites)
   - Conexiones: Selecciona "IP privada" y elige la red VPC que creaste
5. Haz clic en "Crear instancia" (esto puede tardar varios minutos)
6. Una vez creada, crea una base de datos:
   - Haz clic en la instancia
   - Ve a "Bases de datos"
   - Haz clic en "Crear base de datos"
   - Nombre: "rayuela_prod"
   - Haz clic en "Crear"

#### 2.3. Crear Instancia de Memorystore (Redis)

1. Ve a [Memorystore para Redis](https://console.cloud.google.com/memorystore/redis/instances)
2. Haz clic en "Crear instancia"
3. Configura la instancia:
   - ID de instancia: "rayuela-redis-prod"
   - Nivel: Básico
   - Capacidad: 1 GB (aumenta según necesites)
   - Región: La misma que elegiste antes
   - Zona: Cualquiera disponible
   - Red autorizada: Selecciona la red VPC que creaste
4. Haz clic en "Crear" (esto puede tardar varios minutos)
5. Una vez creada, anota la "Dirección IP" y el "Puerto" (normalmente 6379)

#### 2.4. Crear Bucket de Cloud Storage

1. Ve a [Cloud Storage > Buckets](https://console.cloud.google.com/storage/browser)
2. Haz clic en "Crear bucket"
3. Configura el bucket:
   - Nombre: Un nombre único globalmente (ej: "rayuela-models-prod-XXXX")
   - Ubicación: Región (selecciona la misma región que has usado)
   - Clase de almacenamiento: Standard
   - Control de acceso: Uniforme
   - Protección: Ninguna (para empezar)
4. Haz clic en "Crear"

#### 2.5. Crear Secretos en Secret Manager

1. Ve a [Secret Manager](https://console.cloud.google.com/security/secret-manager)
2. Crea los siguientes secretos (haz clic en "Crear secreto" para cada uno):
   - Nombre: "db-password"
     - Valor: La contraseña de la base de datos que creaste
   - Nombre: "fastapi-secret-key"
     - Valor: Una cadena aleatoria larga (puedes generarla con `openssl rand -hex 32`)
3. Para cada secreto, asegúrate de dar acceso a la cuenta de servicio que usará Cloud Run (se creará automáticamente más adelante)

#### 2.6. Crear Conector VPC Serverless

1. Ve a [VPC Network > Conectores de acceso a VPC sin servidor](https://console.cloud.google.com/networking/connectors/list)
2. Haz clic en "Crear conector"
3. Configura el conector:
   - Nombre: "rayuela-vpc-connector"
   - Región: La misma que has usado
   - Red: La red VPC que creaste
   - Rango de IP: Automático
   - Instancias: 2
   - Tipo de máquina: e2-micro (para empezar)
4. Haz clic en "Crear" (esto puede tardar varios minutos)

### 3. Preparar la Aplicación para Producción

#### 3.1. Verificar Dockerfile

Asegúrate de que el Dockerfile esté optimizado para producción. Debe incluir:
- Uso de una imagen base ligera
- Instalación de dependencias de producción
- Configuración para usar gunicorn con workers uvicorn
- Exposición del puerto configurado por variable de entorno

#### 3.2. Verificar Variables de Entorno

Asegúrate de que todas las variables de entorno necesarias estén documentadas y configuradas correctamente para producción.

### 4. Construir y Desplegar la Aplicación

#### 4.1. Construir y Subir la Imagen Docker

```bash
# Asegúrate de estar en la raíz del proyecto
cd rayuela-backend

# Construir y subir la imagen usando Cloud Build
gcloud builds submit --tag gcr.io/TU_ID_DE_PROYECTO/rayuela-recsys-api:v1.0.0
```

#### 4.2. Desplegar en Cloud Run

```bash
# Obtener la conexión de Cloud SQL
CLOUD_SQL_CONNECTION="TU_ID_DE_PROYECTO:TU_REGION:rayuela-db-prod"

# Obtener la IP de Redis
REDIS_IP=$(gcloud redis instances describe rayuela-redis-prod --region=TU_REGION --format='value(host)')

# Desplegar en Cloud Run
gcloud run deploy rayuela-api-prod \
  --image gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.0 \
  --platform managed \
  --region TU_REGION \
  --allow-unauthenticated \
  --vpc-connector rayuela-vpc-connector \
  --set-env-vars="ENV=production,API_HOST=0.0.0.0,API_PORT=8080,MODEL_STORAGE_TYPE=gcs,GCS_BUCKET_NAME=TU_BUCKET_GCS,POSTGRES_USER=postgres,POSTGRES_SERVER=localhost,POSTGRES_PORT=5432,POSTGRES_DB=rayuela_prod,REDIS_HOST=${REDIS_IP},REDIS_PORT=6379" \
  --set-secrets="POSTGRES_PASSWORD=projects/TU_ID_DE_PROYECTO/secrets/db-password:latest,SECRET_KEY=projects/TU_ID_DE_PROYECTO/secrets/fastapi-secret-key:latest" \
  --add-cloudsql-instances ${CLOUD_SQL_CONNECTION} \
  --memory 1Gi \
  --cpu 1 \
  --min-instances 1 \
  --max-instances 10
```

#### 4.3. Aplicar Migraciones en Producción

Para aplicar las migraciones de base de datos en producción, puedes crear un job de Cloud Run:

```bash
# Crear un job para ejecutar las migraciones
gcloud run jobs create rayuela-migrations \
  --image gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.0 \
  --command "alembic" \
  --args "upgrade,head" \
  --region TU_REGION \
  --vpc-connector rayuela-vpc-connector \
  --set-env-vars="ENV=production,MODEL_STORAGE_TYPE=gcs,GCS_BUCKET_NAME=TU_BUCKET_GCS,POSTGRES_USER=postgres,POSTGRES_SERVER=localhost,POSTGRES_PORT=5432,POSTGRES_DB=rayuela_prod" \
  --set-secrets="POSTGRES_PASSWORD=projects/TU_ID_DE_PROYECTO/secrets/db-password:latest,SECRET_KEY=projects/TU_ID_DE_PROYECTO/secrets/fastapi-secret-key:latest" \
  --add-cloudsql-instances ${CLOUD_SQL_CONNECTION} \
  --memory 1Gi \
  --cpu 1

# Ejecutar el job
gcloud run jobs execute rayuela-migrations --region TU_REGION
```

#### 4.4. Configurar el Worker Celery

Para el worker Celery, puedes usar Cloud Run con CPU siempre asignada:

```bash
# Desplegar el worker Celery en Cloud Run
gcloud run deploy rayuela-worker-prod \
  --image gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.0 \
  --platform managed \
  --region TU_REGION \
  --no-allow-unauthenticated \
  --vpc-connector rayuela-vpc-connector \
  --set-env-vars="ENV=production,MODEL_STORAGE_TYPE=gcs,GCS_BUCKET_NAME=TU_BUCKET_GCS,POSTGRES_USER=postgres,POSTGRES_SERVER=localhost,POSTGRES_PORT=5432,POSTGRES_DB=rayuela_prod,REDIS_HOST=${REDIS_IP},REDIS_PORT=6379" \
  --set-secrets="POSTGRES_PASSWORD=projects/TU_ID_DE_PROYECTO/secrets/db-password:latest,SECRET_KEY=projects/TU_ID_DE_PROYECTO/secrets/fastapi-secret-key:latest" \
  --add-cloudsql-instances ${CLOUD_SQL_CONNECTION} \
  --memory 2Gi \
  --cpu 2 \
  --min-instances 1 \
  --max-instances 5 \
  --command "celery" \
  --args "-A,src.celery_worker.celery_app,worker,--loglevel=info"
```

### 5. Verificar el Despliegue

#### 5.1. Probar la API

1. Obtén la URL de tu servicio Cloud Run:
   ```bash
   gcloud run services describe rayuela-api-prod --region TU_REGION --format='value(status.url)'
   ```

2. Prueba el endpoint de salud:
   ```bash
   curl TU_URL_DE_CLOUD_RUN/health
   ```

3. Accede a la documentación de la API:
   ```
   TU_URL_DE_CLOUD_RUN/docs
   ```

#### 5.2. Monitorizar la Aplicación

1. Ve a [Cloud Run](https://console.cloud.google.com/run) para ver el estado de tus servicios
2. Ve a [Cloud Logging](https://console.cloud.google.com/logs) para ver los logs
3. Ve a [Cloud Monitoring](https://console.cloud.google.com/monitoring) para configurar alertas y dashboards

## Configuración del Servidor

La aplicación ahora permite configurar el host y puerto del servidor a través de variables de entorno, lo que facilita el despliegue en diferentes entornos.

### Variables de Entorno para la Configuración del Servidor

| Variable   | Descripción                                | Valor por defecto | Rango válido        |
|------------|--------------------------------------------|--------------------|---------------------|
| `API_HOST` | Dirección IP en la que escucha el servidor | `0.0.0.0`          | Cualquier IP válida |
| `API_PORT` | Puerto en el que escucha el servidor       | `8001`             | 1024-65535          |

### Configuración en Desarrollo

Para configurar el servidor en un entorno de desarrollo, puedes:

1. Establecer las variables de entorno directamente:
   ```bash
   # En Linux/Mac
   export API_PORT=8002
   python main.py

   # En Windows (PowerShell)
   $env:API_PORT=8002; python main.py

   # En Windows (CMD)
   set API_PORT=8002
   python main.py
   ```

2. O añadir estas variables a tu archivo `.env`:
   ```
   API_HOST=0.0.0.0
   API_PORT=8002
   ```

### Configuración en Producción

En un entorno de producción (como Cloud Run), estas variables se configuran como parte del despliegue:

```bash
gcloud run deploy rayuela-api-prod \
  --set-env-vars="API_HOST=0.0.0.0,API_PORT=8080" \
  # ... otras opciones de despliegue
```

### Manejo de Puertos Ocupados

La aplicación ahora incluye un mecanismo para manejar puertos ocupados:

1. Si el puerto configurado está ocupado, la aplicación intentará encontrar un puerto disponible automáticamente.
2. La búsqueda comenzará desde el puerto configurado + 1 y continuará hasta encontrar un puerto disponible o alcanzar el número máximo de intentos (10 por defecto).
3. Si se encuentra un puerto disponible, la aplicación se iniciará en ese puerto y mostrará un mensaje informativo.
4. Si no se encuentra un puerto disponible, la aplicación mostrará un error y se detendrá.

## Manejo de Errores y Solución de Problemas

Esta sección proporciona información sobre cómo diagnosticar y resolver problemas comunes que puedes encontrar al ejecutar o desplegar la aplicación.

### Problemas Comunes en Desarrollo

#### La aplicación no inicia

**Síntoma**: Al ejecutar `python main.py`, la aplicación no inicia o muestra errores.

**Posibles soluciones**:
1. Verifica que todas las dependencias estén instaladas: `pip install -r requirements.txt`
2. Asegúrate de que las variables de entorno estén configuradas correctamente en tu archivo `.env`
3. Verifica que PostgreSQL y Redis estén en ejecución: `docker-compose ps`
4. Comprueba los logs para ver errores específicos: `python main.py 2>&1 | tee app.log`

#### Error "Puerto en uso"

**Síntoma**: La aplicación muestra un error indicando que el puerto ya está en uso.

**Posibles soluciones**:
1. Usa un puerto diferente: `API_PORT=8002 python main.py`
2. Encuentra y termina el proceso que está usando el puerto:
   ```bash
   # En Linux/Mac
   lsof -i :8001
   kill -9 PID_DEL_PROCESO

   # En Windows
   netstat -ano | findstr :8001
   taskkill /PID PID_DEL_PROCESO /F
   ```

#### Errores de conexión a la base de datos

**Síntoma**: La aplicación muestra errores al conectarse a PostgreSQL.

**Posibles soluciones**:
1. Verifica que PostgreSQL esté en ejecución: `docker-compose ps`
2. Comprueba la URL de conexión en tu archivo `.env`
3. Intenta conectarte manualmente a la base de datos:
   ```bash
   psql postgresql://rayuela_user:supersecretpassword@localhost:5432/rayuela_dev
   ```

#### Errores de conexión a Redis

**Síntoma**: La aplicación muestra errores al conectarse a Redis.

**Posibles soluciones**:
1. Verifica que Redis esté en ejecución: `docker-compose ps`
2. Comprueba la URL de conexión en tu archivo `.env`
3. Intenta conectarte manualmente a Redis:
   ```bash
   redis-cli -h localhost -p 6379 ping
   ```

### Problemas Comunes en Producción

#### Error al desplegar en Cloud Run

**Síntoma**: El despliegue en Cloud Run falla o el servicio no inicia correctamente.

**Posibles soluciones**:
1. Verifica los logs del servicio en Cloud Run
2. Asegúrate de que todas las variables de entorno y secretos estén configurados correctamente
3. Comprueba que el conector VPC esté configurado y funcionando
4. Verifica que la cuenta de servicio tenga los permisos necesarios

#### Error al conectarse a Cloud SQL

**Síntoma**: La aplicación no puede conectarse a la base de datos en producción.

**Posibles soluciones**:
1. Verifica que la instancia de Cloud SQL esté en ejecución
2. Comprueba que el conector VPC esté configurado correctamente
3. Asegúrate de que la instancia de Cloud SQL esté configurada para usar IP privada
4. Verifica que el servicio Cloud Run tenga la opción `--add-cloudsql-instances` configurada correctamente

#### Error al conectarse a Memorystore (Redis)

**Síntoma**: La aplicación no puede conectarse a Redis en producción.

**Posibles soluciones**:
1. Verifica que la instancia de Memorystore esté en ejecución
2. Comprueba que el conector VPC esté configurado correctamente
3. Asegúrate de que la instancia de Memorystore esté en la misma red VPC que el conector
4. Verifica que las variables de entorno `REDIS_HOST` y `REDIS_PORT` estén configuradas correctamente

#### Errores en el worker Celery

**Síntoma**: Las tareas de Celery no se ejecutan o fallan.

**Posibles soluciones**:
1. Verifica los logs del servicio worker en Cloud Run
2. Comprueba que Redis esté configurado correctamente y accesible
3. Asegúrate de que el worker tenga acceso a todos los recursos necesarios (base de datos, almacenamiento)
4. Verifica que el comando para iniciar el worker sea correcto

### Cómo Obtener Ayuda

Si encuentras problemas que no puedes resolver, puedes:

1. Consultar la documentación oficial de las tecnologías utilizadas:
   - [FastAPI](https://fastapi.tiangolo.com/)
   - [SQLAlchemy](https://docs.sqlalchemy.org/)
   - [Celery](https://docs.celeryq.dev/)
   - [Google Cloud Run](https://cloud.google.com/run/docs)

2. Buscar en foros y comunidades:
   - [Stack Overflow](https://stackoverflow.com/)
   - [Reddit r/FastAPI](https://www.reddit.com/r/FastAPI/)
   - [Reddit r/googlecloud](https://www.reddit.com/r/googlecloud/)

3. Contratar ayuda profesional:
   - Freelancers en plataformas como Upwork o Fiverr
   - Consultores especializados en Python y Google Cloud

## Actualización de la Aplicación

Esta sección explica cómo actualizar la aplicación una vez desplegada en producción.

### Actualización del Código

1. Realiza los cambios necesarios en el código
2. Prueba los cambios en un entorno de desarrollo
3. Actualiza la versión en el archivo `pyproject.toml` o similar
4. Haz commit de los cambios y súbelos a tu repositorio

### Construcción y Despliegue de la Nueva Versión

1. Construye una nueva imagen Docker con la nueva versión:
   ```bash
   # Incrementa la versión en el tag
   gcloud builds submit --tag gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.1
   ```

2. Despliega la nueva versión en Cloud Run:
   ```bash
   # Usa el mismo comando que usaste para el despliegue inicial, pero con la nueva imagen
   gcloud run deploy rayuela-api-prod \
     --image gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.1 \
     # ... otras opciones de despliegue
   ```

3. Si hay cambios en la base de datos, aplica las migraciones:
   ```bash
   # Ejecuta el job de migraciones
   gcloud run jobs execute rayuela-migrations --region TU_REGION
   ```

4. Actualiza también el worker Celery:
   ```bash
   gcloud run deploy rayuela-worker-prod \
     --image gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.1 \
     # ... otras opciones de despliegue
   ```

### Estrategias de Despliegue Seguro

Para minimizar el tiempo de inactividad y los riesgos, considera estas estrategias:

#### Despliegue Gradual (Traffic Splitting)

Cloud Run permite dividir el tráfico entre diferentes revisiones:

```bash
# Desplegar la nueva versión sin enviarle tráfico
gcloud run deploy rayuela-api-prod \
  --image gcr.io/TU_ID_DE_PROYECTO/rayuela:v1.0.1 \
  --no-traffic

# Obtener la revisión actual y la nueva
CURRENT_REVISION=$(gcloud run services describe rayuela-api-prod --region TU_REGION --format='value(status.traffic[0].revisionName)')
NEW_REVISION=$(gcloud run services describe rayuela-api-prod --region TU_REGION --format='value(status.latestReadyRevisionName)')

# Enviar un 10% del tráfico a la nueva versión
gcloud run services update-traffic rayuela-api-prod \
  --region TU_REGION \
  --to-revisions="${CURRENT_REVISION}=90,${NEW_REVISION}=10"

# Si todo va bien, migrar todo el tráfico a la nueva versión
gcloud run services update-traffic rayuela-api-prod \
  --region TU_REGION \
  --to-revisions="${NEW_REVISION}=100"
```

#### Rollback en Caso de Problemas

Si la nueva versión tiene problemas, puedes volver a la versión anterior:

```bash
# Volver a la revisión anterior
gcloud run services update-traffic rayuela-api-prod \
  --region TU_REGION \
  --to-revisions="${CURRENT_REVISION}=100"
```

### Monitorización Después de la Actualización

Después de actualizar la aplicación, es importante monitorizar su comportamiento:

1. Verifica los logs en Cloud Logging para detectar errores
2. Comprueba las métricas en Cloud Monitoring (latencia, errores, uso de CPU/memoria)
3. Realiza pruebas manuales para verificar que todo funciona correctamente
4. Configura alertas para ser notificado si hay problemas
