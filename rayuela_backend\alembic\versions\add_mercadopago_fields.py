"""Add Mercado Pago fields to Account and Subscription models

Revision ID: add_mercadopago_fields
Revises: 48196a49d36e
Create Date: 2025-05-12 20:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_mercadopago_fields'
down_revision = '48196a49d36e'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # Añadir campos de Mercado Pago a la tabla accounts
    op.add_column('accounts', sa.Column('mercadopago_customer_id', sa.String(length=255), nullable=True))
    op.create_index(op.f('ix_accounts_mercadopago_customer_id'), 'accounts', ['mercadopago_customer_id'], unique=False)

    # Añadir campos de Mercado Pago a la tabla subscriptions
    op.add_column('subscriptions', sa.Column('mercadopago_subscription_id', sa.String(length=255), nullable=True))
    op.add_column('subscriptions', sa.Column('mercadopago_price_id', sa.String(length=255), nullable=True))
    op.add_column('subscriptions', sa.Column('payment_gateway', sa.String(length=20), nullable=True, server_default='mercadopago'))

    op.create_index(op.f('ix_subscriptions_mercadopago_subscription_id'), 'subscriptions', ['mercadopago_subscription_id'], unique=False)


def downgrade() -> None:
    # Eliminar campos de Mercado Pago de la tabla subscriptions
    op.drop_index(op.f('ix_subscriptions_mercadopago_subscription_id'), table_name='subscriptions')
    op.drop_column('subscriptions', 'payment_gateway')
    op.drop_column('subscriptions', 'mercadopago_price_id')
    op.drop_column('subscriptions', 'mercadopago_subscription_id')

    # Eliminar campos de Mercado Pago de la tabla accounts
    op.drop_index(op.f('ix_accounts_mercadopago_customer_id'), table_name='accounts')
    op.drop_column('accounts', 'mercadopago_customer_id')
