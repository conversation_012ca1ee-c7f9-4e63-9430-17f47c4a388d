#!/bin/bash

# Script para configurar secretos de producción en Google Secret Manager
# Este script debe ejecutarse una vez antes del primer deployment

set -e

echo "🔐 CONFIGURACIÓN DE SECRETOS DE PRODUCCIÓN"
echo "=========================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar que gcloud esté configurado
if ! command -v gcloud &> /dev/null; then
    log_error "gcloud CLI no está instalado. Por favor instálalo primero."
    exit 1
fi

# Verificar que el usuario esté autenticado
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    log_error "No hay una cuenta activa en gcloud. Por favor ejecuta 'gcloud auth login'"
    exit 1
fi

# Obtener el PROJECT_ID actual
PROJECT_ID=$(gcloud config get-value project)
if [ -z "$PROJECT_ID" ]; then
    log_error "No hay un proyecto configurado. Por favor ejecuta 'gcloud config set project YOUR_PROJECT_ID'"
    exit 1
fi

log_info "Configurando secretos para el proyecto: $PROJECT_ID"

# Función para crear secreto de forma segura
create_secret() {
    local secret_name=$1
    local description=$2
    local example_value=$3
    
    if gcloud secrets describe "$secret_name" >/dev/null 2>&1; then
        log_warning "El secreto $secret_name ya existe. Saltando..."
        return 0
    fi
    
    echo ""
    log_info "Configurando secreto: $secret_name"
    log_info "Descripción: $description"
    if [ -n "$example_value" ]; then
        log_info "Ejemplo: $example_value"
    fi
    
    read -p "Ingresa el valor para $secret_name: " -s secret_value
    echo ""
    
    if [ -z "$secret_value" ]; then
        log_warning "Valor vacío para $secret_name. Saltando..."
        return 0
    fi
    
    echo "$secret_value" | gcloud secrets create "$secret_name" --data-file=-
    log_success "Secreto $secret_name creado exitosamente"
}

# Habilitar APIs necesarias
log_info "Habilitando APIs necesarias..."
gcloud services enable secretmanager.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
log_success "APIs habilitadas"

echo ""
log_info "A continuación se configurarán los secretos necesarios para producción."
log_warning "IMPORTANTE: Los valores ingresados serán almacenados de forma segura en Secret Manager."
echo ""

# Secretos de base de datos
log_info "=== CONFIGURACIÓN DE BASE DE DATOS ==="
create_secret "POSTGRES_USER" "Usuario de PostgreSQL" "rayuela_user"
create_secret "POSTGRES_PASSWORD" "Contraseña de PostgreSQL" ""
create_secret "POSTGRES_SERVER" "Servidor de PostgreSQL" "******** o your-db-instance.region.gcp.cloud.sql.proxy"
create_secret "POSTGRES_PORT" "Puerto de PostgreSQL" "5432"
create_secret "POSTGRES_DB" "Nombre de la base de datos" "rayuela_production"

echo ""
log_info "=== CONFIGURACIÓN DE REDIS ==="
create_secret "REDIS_HOST" "Host de Redis" "******** o your-redis-instance.region.cache.amazonaws.com"
create_secret "REDIS_PORT" "Puerto de Redis" "6379"
create_secret "REDIS_PASSWORD" "Contraseña de Redis (opcional)" ""
create_secret "REDIS_URL" "URL completa de Redis" "redis://user:password@host:port/0"

echo ""
log_info "=== CONFIGURACIÓN DE APLICACIÓN ==="
create_secret "SECRET_KEY" "Clave secreta de la aplicación (32+ caracteres)" ""
create_secret "MERCADOPAGO_ACCESS_TOKEN" "Token de acceso de MercadoPago" ""
create_secret "GCS_BUCKET_NAME" "Nombre del bucket de Google Cloud Storage" "rayuela-production-data"

echo ""
log_info "=== CONFIGURACIÓN DE PERMISOS ==="

# Otorgar permisos a Cloud Build para acceder a los secretos
CLOUDBUILD_SA="$(gcloud projects describe $PROJECT_ID --format='value(projectNumber)')@cloudbuild.gserviceaccount.com"

log_info "Otorgando permisos a Cloud Build Service Account: $CLOUDBUILD_SA"

# Otorgar rol de Secret Manager Secret Accessor
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$CLOUDBUILD_SA" \
    --role="roles/secretmanager.secretAccessor"

# Otorgar rol de Cloud SQL Client (si se usa Cloud SQL)
gcloud projects add-iam-policy-binding $PROJECT_ID \
    --member="serviceAccount:$CLOUDBUILD_SA" \
    --role="roles/cloudsql.client"

log_success "Permisos otorgados a Cloud Build"

echo ""
log_info "=== VERIFICACIÓN DE SECRETOS ==="

# Lista de secretos requeridos
REQUIRED_SECRETS=(
    "POSTGRES_USER" "POSTGRES_PASSWORD" "POSTGRES_SERVER" "POSTGRES_PORT" "POSTGRES_DB"
    "REDIS_HOST" "REDIS_PORT" "REDIS_URL" "REDIS_PASSWORD"
    "SECRET_KEY" "MERCADOPAGO_ACCESS_TOKEN" "GCS_BUCKET_NAME"
)

ALL_SECRETS_OK=true

for secret in "${REQUIRED_SECRETS[@]}"; do
    if gcloud secrets describe "$secret" >/dev/null 2>&1; then
        log_success "✅ $secret configurado"
    else
        log_error "❌ $secret faltante"
        ALL_SECRETS_OK=false
    fi
done

echo ""
if [ "$ALL_SECRETS_OK" = true ]; then
    log_success "🎉 TODOS LOS SECRETOS ESTÁN CONFIGURADOS CORRECTAMENTE"
    echo ""
    log_info "Próximos pasos:"
    log_info "1. Verificar que la base de datos y Redis estén accesibles"
    log_info "2. Ejecutar el pipeline de CI/CD: gcloud builds submit --config=cloudbuild.yaml"
    log_info "3. Monitorear el deployment en Cloud Console"
    echo ""
    log_info "Para ver los secretos configurados:"
    log_info "  gcloud secrets list"
    echo ""
    log_info "Para actualizar un secreto:"
    log_info "  echo 'nuevo_valor' | gcloud secrets versions add SECRET_NAME --data-file=-"
else
    log_error "❌ FALTAN SECRETOS POR CONFIGURAR"
    log_info "Por favor ejecuta este script nuevamente para configurar los secretos faltantes."
    exit 1
fi

echo ""
log_success "🔐 Configuración de secretos completada exitosamente"
