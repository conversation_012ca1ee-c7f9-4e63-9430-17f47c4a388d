"""Utility functions for database maintenance tasks."""

import asyncio
import json
from datetime import datetime, timedelta, timezone
from sqlalchemy import text
from src.db.session import get_db
from src.utils.base_logger import log_info, log_error, log_warning
from src.utils.rls_utils import execute_cleanup_old_data, RLSBypassContext
from typing import Optional, Dict, Any, List


async def _is_rls_enabled(table_name: str) -> bool:
    """
    Verifica si una tabla tiene RLS habilitado.

    Args:
        table_name: Nombre de la tabla a verificar

    Returns:
        True si la tabla tiene RLS habilitado, False en caso contrario
    """
    try:
        async with get_db() as db:
            query = text("""
                SELECT c.relrowsecurity
                FROM pg_catalog.pg_class c
                JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                WHERE c.relname = :table_name
                AND n.nspname = 'public'
            """)

            result = await db.execute(query, {"table_name": table_name})
            row = result.fetchone()

            if row:
                return bool(row[0])
            return False
    except Exception as e:
        log_error(f"Error verificando RLS para tabla {table_name}: {str(e)}")
        # Por defecto, asumimos que no tiene RLS
        return False


async def cleanup_old_data_secure(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
) -> Dict[str, Any]:
    """
    Limpia datos antiguos para una cuenta específica utilizando la función segura de PostgreSQL.

    Esta función utiliza la función SECURITY DEFINER de PostgreSQL para limpiar datos antiguos
    de forma segura, con validación de parámetros y protección contra inyección SQL.

    Args:
        days_to_keep: Número de días a mantener los datos (por defecto 180)
        account_id: ID de la cuenta (requerido)

    Returns:
        Diccionario con información sobre la operación
    """
    if account_id is None:
        raise ValueError("account_id is required for cleanup_old_data_secure")

    start_time = datetime.now(timezone.utc)
    log_info(f"Starting secure cleanup of old data for account_id={account_id} with days_to_keep={days_to_keep}")

    try:
        async with get_db() as db:
            # Usar la función utilitaria para ejecutar cleanup_old_data de forma segura
            result = await execute_cleanup_old_data(
                db=db,
                account_id=account_id,
                days_to_keep=days_to_keep
            )

            # Agregar información de duración
            duration_seconds = (datetime.now(timezone.utc) - start_time).total_seconds()
            result["duration_seconds"] = duration_seconds

            log_info(f"Completed secure cleanup of old data for account_id={account_id} in {duration_seconds:.2f}s")
            return result
    except Exception as e:
        duration_seconds = (datetime.now(timezone.utc) - start_time).total_seconds()
        error_msg = f"Error in secure cleanup of old data: {str(e)}"
        log_error(f"{error_msg} (after {duration_seconds:.2f}s)")

        return {
            "account_id": account_id,
            "days_kept": days_to_keep,
            "error": str(e),
            "duration_seconds": duration_seconds,
            "success": False,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


async def cleanup_old_audit_logs(
    days_to_keep: int = 90,
    batch_size: int = 10000,
    account_id: Optional[int] = None
) -> Dict[str, Any]:
    """Elimina logs de auditoría más antiguos que el período especificado.

    Implementa eliminación por lotes para evitar bloqueos prolongados y es idempotente
    (puede ejecutarse múltiples veces sin efectos secundarios).

    Args:
        days_to_keep: Número de días a mantener los logs (por defecto 90)
        batch_size: Tamaño del lote para eliminación (por defecto 10000)
        account_id: ID de la cuenta específica (None para todas las cuentas)

    Returns:
        Diccionario con información sobre la operación
    """
    start_time = datetime.now(timezone.utc)
    account_msg = f" for account {account_id}" if account_id else ""
    log_info(f"Starting cleanup of audit logs older than {days_to_keep} days{account_msg} with batch size {batch_size}")

    try:
        async with get_db() as db:
            # Establecer el tenant_id en la sesión de PostgreSQL si se proporciona account_id
            if account_id is not None:
                await db.execute("SET app.tenant_id = :account_id", {"account_id": account_id})
                log_info(f"Set tenant_id={account_id} for audit logs cleanup")

            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            log_info(f"Cutoff date for audit logs cleanup: {cutoff_date.isoformat()}")

            # Primero contar cuántos registros se eliminarán
            count_query = text(
                """
                SELECT COUNT(*)
                FROM audit_logs
                WHERE created_at < :cutoff_date
                """
            )
            count_result = await db.execute(count_query, {"cutoff_date": cutoff_date})
            total_to_delete = count_result.scalar() or 0

            if total_to_delete == 0:
                log_info("No audit logs found to delete")
                return {
                    "table": "audit_logs",
                    "deleted_count": 0,
                    "expected_count": 0,
                    "cutoff_date": cutoff_date.isoformat(),
                    "days_kept": days_to_keep,
                    "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds(),
                    "success": True
                }

            log_info(f"Found {total_to_delete} audit logs to delete")

            # Eliminar en lotes para evitar bloqueos prolongados
            total_deleted = 0
            batch_count = 0

            while True:
                batch_count += 1
                batch_start_time = datetime.now(timezone.utc)

                # Identificar IDs a eliminar en este lote
                batch_query = text(
                    """
                    WITH ids_to_delete AS (
                        SELECT id
                        FROM audit_logs
                        WHERE created_at < :cutoff_date
                        ORDER BY created_at ASC
                        LIMIT :batch_size
                    )
                    DELETE FROM audit_logs a
                    USING ids_to_delete d
                    WHERE a.id = d.id
                    RETURNING a.id
                    """
                )

                try:
                    batch_params = {"cutoff_date": cutoff_date, "batch_size": batch_size}
                    result = await db.execute(batch_query, batch_params)
                    deleted_rows = result.fetchall()
                    deleted_count = len(deleted_rows)

                    if deleted_count == 0:
                        log_info("No more audit logs to delete")
                        break

                    await db.commit()
                    total_deleted += deleted_count
                    batch_duration = (datetime.now(timezone.utc) - batch_start_time).total_seconds()

                    log_info(
                        f"Batch {batch_count}: Deleted {deleted_count} audit logs in {batch_duration:.2f}s, "
                        f"total: {total_deleted}/{total_to_delete} ({(total_deleted/total_to_delete*100):.1f}%)"
                    )

                    # Si eliminamos menos que el tamaño del lote, hemos terminado
                    if deleted_count < batch_size:
                        log_info("Completed deletion with a partial batch")
                        break

                except Exception as batch_error:
                    # Si hay un error en un lote, registrarlo pero continuar con el siguiente
                    log_error(f"Error in batch {batch_count}: {str(batch_error)}")
                    await db.rollback()  # Asegurar que la transacción se revierta

                    # Reducir el tamaño del lote si hay un error y reintentar
                    if batch_size > 1000:
                        old_batch_size = batch_size
                        batch_size = max(1000, batch_size // 2)
                        log_info(f"Reducing batch size from {old_batch_size} to {batch_size} and retrying")
                    else:
                        # Si el tamaño del lote ya es pequeño, registrar el error y continuar
                        log_error(f"Batch size already at minimum, skipping problematic batch")

            duration_seconds = (datetime.now(timezone.utc) - start_time).total_seconds()
            log_info(
                f"Completed cleanup of {total_deleted}/{total_to_delete} audit logs in {duration_seconds:.2f}s"
            )

            return {
                "table": "audit_logs",
                "deleted_count": total_deleted,
                "expected_count": total_to_delete,
                "cutoff_date": cutoff_date.isoformat(),
                "days_kept": days_to_keep,
                "duration_seconds": duration_seconds,
                "batches": batch_count,
                "success": True
            }
    except Exception as e:
        duration_seconds = (datetime.now(timezone.utc) - start_time).total_seconds()
        error_msg = f"Error cleaning up audit logs: {str(e)}"
        log_error(f"{error_msg} (after {duration_seconds:.2f}s)")

        # Crear un diccionario con la información del error
        error_info = {
            "table": "audit_logs",
            "error": error_msg,
            "days_kept": days_to_keep,
            "duration_seconds": duration_seconds,
            "success": False
        }

        # Agregar cutoff_date solo si está definido
        try:
            if 'cutoff_date' in locals() and cutoff_date is not None:
                error_info["cutoff_date"] = cutoff_date.isoformat()
        except:
            pass  # Ignorar errores al acceder a cutoff_date

        return error_info


async def cleanup_old_interactions(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    batch_size: int = 10000,
    max_retries: int = 3,
    retry_delay: int = 5
) -> Dict[str, Any]:
    """
    Elimina interacciones más antiguas que el período especificado.
    Puede limitarse a una cuenta específica o aplicarse a todas.
    Utiliza eliminación por lotes para evitar bloqueos prolongados.

    Es idempotente (puede ejecutarse múltiples veces sin efectos secundarios) y
    tiene capacidad de recuperación ante errores (reintentos, reducción de tamaño de lote).

    Args:
        days_to_keep: Número de días a mantener las interacciones (por defecto 180)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        batch_size: Tamaño del lote para eliminación (por defecto 10000)
        max_retries: Número máximo de reintentos por lote (por defecto 3)
        retry_delay: Segundos de espera entre reintentos (por defecto 5)

    Returns:
        Diccionario con información sobre la operación
    """
    start_time = datetime.now(timezone.utc)
    account_msg = f" for account {account_id}" if account_id else ""
    log_info(f"Starting cleanup of interactions older than {days_to_keep} days{account_msg} with batch size {batch_size}")

    # Variables para seguimiento de errores
    error_batches = 0
    retry_count = 0

    try:
        async with get_db() as db:
            # Establecer el tenant_id en la sesión de PostgreSQL si se proporciona account_id
            if account_id is not None:
                await db.execute("SET app.tenant_id = :account_id", {"account_id": account_id})
                log_info(f"Set tenant_id={account_id} for interactions cleanup")

            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)
            log_info(f"Cutoff date for interactions cleanup: {cutoff_date.isoformat()}{account_msg}")

            # Primero contar cuántos registros se eliminarán
            # Si RLS está habilitado, no necesitamos filtrar por account_id en la consulta
            # ya que PostgreSQL lo hará automáticamente

            # Verificar si la tabla tiene RLS habilitado
            has_rls = await _is_rls_enabled("interactions")

            if account_id is not None and not has_rls:
                # Si no hay RLS, necesitamos filtrar explícitamente por account_id
                count_query = text(
                    """
                    SELECT COUNT(*)
                    FROM interactions
                    WHERE timestamp < :cutoff_date AND account_id = :account_id
                    """
                )
                count_result = await db.execute(
                    count_query,
                    {"cutoff_date": cutoff_date, "account_id": account_id}
                )
            else:
                # Si hay RLS o no se especificó account_id, no necesitamos filtrar por account_id
                count_query = text(
                    """
                    SELECT COUNT(*)
                    FROM interactions
                    WHERE timestamp < :cutoff_date
                    """
                )
                count_result = await db.execute(
                    count_query,
                    {"cutoff_date": cutoff_date}
                )

            total_to_delete = count_result.scalar() or 0

            if total_to_delete == 0:
                log_info(f"No interactions found to delete older than {days_to_keep} days{account_msg}")
                return {
                    "table": "interactions",
                    "deleted_count": 0,
                    "expected_count": 0,
                    "cutoff_date": cutoff_date.isoformat(),
                    "days_kept": days_to_keep,
                    "account_id": account_id,
                    "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds(),
                    "success": True
                }

            log_info(f"Found {total_to_delete} interactions to delete{account_msg}")

            # Eliminar en lotes para evitar bloqueos prolongados
            total_deleted = 0
            batch_count = 0
            original_batch_size = batch_size

            while True:
                batch_count += 1
                batch_start_time = datetime.now(timezone.utc)

                # Identificar IDs a eliminar en este lote
                # Usar la misma lógica de filtrado que para el conteo
                if account_id is not None and not has_rls:
                    batch_query = text(
                        """
                        WITH ids_to_delete AS (
                            SELECT account_id, id
                            FROM interactions
                            WHERE timestamp < :cutoff_date AND account_id = :account_id
                            ORDER BY timestamp ASC
                            LIMIT :batch_size
                        )
                        DELETE FROM interactions i
                        USING ids_to_delete d
                        WHERE i.account_id = d.account_id AND i.id = d.id
                        RETURNING i.account_id, i.id
                        """
                    )
                    batch_params = {
                        "cutoff_date": cutoff_date,
                        "account_id": account_id,
                        "batch_size": batch_size
                    }
                else:
                    batch_query = text(
                        """
                        WITH ids_to_delete AS (
                            SELECT account_id, id
                            FROM interactions
                            WHERE timestamp < :cutoff_date
                            ORDER BY timestamp ASC
                            LIMIT :batch_size
                        )
                        DELETE FROM interactions i
                        USING ids_to_delete d
                        WHERE i.account_id = d.account_id AND i.id = d.id
                        RETURNING i.account_id, i.id
                        """
                    )
                    batch_params = {
                        "cutoff_date": cutoff_date,
                        "batch_size": batch_size
                    }

                # Intentar ejecutar el lote con reintentos
                retry_attempt = 0
                batch_success = False

                while retry_attempt <= max_retries and not batch_success:
                    try:
                        result = await db.execute(batch_query, batch_params)
                        deleted_rows = result.fetchall()
                        deleted_count = len(deleted_rows)

                        if deleted_count == 0:
                            log_info("No more interactions to delete")
                            batch_success = True
                            break

                        await db.commit()
                        total_deleted += deleted_count
                        batch_duration = (datetime.now(timezone.utc) - batch_start_time).total_seconds()
                        batch_success = True

                        # Restaurar el tamaño del lote si tuvimos éxito y estamos por debajo del original
                        if batch_size < original_batch_size and batch_count % 3 == 0:
                            old_batch_size = batch_size
                            batch_size = min(original_batch_size, batch_size * 2)
                            log_info(f"Increasing batch size from {old_batch_size} to {batch_size} after successful batches")

                        log_info(
                            f"Batch {batch_count}: Deleted {deleted_count} interactions in {batch_duration:.2f}s, "
                            f"total: {total_deleted}/{total_to_delete} ({(total_deleted/total_to_delete*100):.1f}%){account_msg}"
                        )

                        # Si eliminamos menos que el tamaño del lote, hemos terminado
                        if deleted_count < batch_size:
                            log_info("Completed deletion with a partial batch")
                            break

                    except Exception as batch_error:
                        retry_attempt += 1
                        retry_count += 1
                        await db.rollback()  # Asegurar que la transacción se revierta

                        if retry_attempt <= max_retries:
                            # Reducir el tamaño del lote si hay un error y reintentar
                            old_batch_size = batch_size
                            batch_size = max(1000, batch_size // 2)
                            log_error(
                                f"Error in batch {batch_count} (attempt {retry_attempt}/{max_retries}): {str(batch_error)}. "
                                f"Reducing batch size from {old_batch_size} to {batch_size} and retrying in {retry_delay}s"
                            )
                            # Esperar antes de reintentar
                            await asyncio.sleep(retry_delay)
                        else:
                            # Si agotamos los reintentos, registrar el error y continuar con el siguiente lote
                            error_batches += 1
                            log_error(
                                f"Failed to process batch {batch_count} after {max_retries} attempts: {str(batch_error)}. "
                                f"Moving to next batch."
                            )
                            batch_success = False  # Marcar como fallido pero continuar con el siguiente lote

                # Si no pudimos procesar ningún lote exitosamente y no hay más datos, salir
                if not batch_success:
                    break

                # Si hemos tenido demasiados errores consecutivos, pausar brevemente
                if error_batches > 5:
                    log_info(f"Too many consecutive errors ({error_batches}), pausing for 30s")
                    await asyncio.sleep(30)
                    error_batches = 0  # Reiniciar contador

            duration_seconds = (datetime.now(timezone.utc) - start_time).total_seconds()
            log_info(
                f"Completed cleanup of {total_deleted}/{total_to_delete} interactions in {duration_seconds:.2f}s{account_msg}"
            )

            return {
                "table": "interactions",
                "deleted_count": total_deleted,
                "expected_count": total_to_delete,
                "cutoff_date": cutoff_date.isoformat(),
                "days_kept": days_to_keep,
                "account_id": account_id,
                "duration_seconds": duration_seconds,
                "batches": batch_count,
                "retries": retry_count,
                "error_batches": error_batches,
                "success": True
            }
    except Exception as e:
        duration_seconds = (datetime.now(timezone.utc) - start_time).total_seconds()
        error_msg = f"Error cleaning up interactions: {str(e)}"
        log_error(f"{error_msg} (after {duration_seconds:.2f}s){account_msg}")

        # Crear un diccionario con la información del error
        error_info = {
            "table": "interactions",
            "error": error_msg,
            "days_kept": days_to_keep,
            "account_id": account_id,
            "duration_seconds": duration_seconds,
            "success": False
        }

        # Agregar cutoff_date solo si está definido
        try:
            if 'cutoff_date' in locals() and cutoff_date is not None:
                error_info["cutoff_date"] = cutoff_date.isoformat()
        except:
            pass  # Ignorar errores al acceder a cutoff_date

        return error_info


async def archive_and_cleanup_audit_logs_async(
    days_to_keep: int = 90,
    batch_size: int = 10000,
    account_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Archiva y luego elimina logs de auditoría más antiguos que el período especificado.

    Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
    proporcionando una estrategia de retención de datos costo-efectiva.

    Args:
        days_to_keep: Número de días a mantener en Cloud SQL (por defecto 90)
        batch_size: Tamaño del lote para procesamiento (por defecto 10000)
        account_id: ID de la cuenta específica (None para todas las cuentas)

    Returns:
        Diccionario con información sobre la operación de archivado y limpieza
    """
    start_time = datetime.now(timezone.utc)
    account_msg = f" for account {account_id}" if account_id else ""
    log_info(f"Starting archive and cleanup of audit logs older than {days_to_keep} days{account_msg}")

    try:
        # Importar aquí para evitar dependencias circulares
        from src.services.data_archival_service import DataArchivalService

        # Calcular fecha de corte
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)

        # Paso 1: Archivar datos a GCS
        archival_service = DataArchivalService()
        archive_result = await archival_service.archive_audit_logs(
            cutoff_date=cutoff_date,
            account_id=account_id,
            batch_size=batch_size
        )

        if not archive_result.get("success", False):
            log_error(f"Failed to archive audit logs: {archive_result.get('error', 'Unknown error')}")
            return {
                "table": "audit_logs",
                "archived_count": archive_result.get("archived_count", 0),
                "deleted_count": 0,
                "error": f"Archival failed: {archive_result.get('error', 'Unknown error')}",
                "success": False,
                "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
            }

        archived_count = archive_result.get("archived_count", 0)
        log_info(f"Successfully archived {archived_count} audit logs{account_msg}")

        # Paso 2: Solo eliminar si el archivado fue exitoso
        if archived_count > 0:
            cleanup_result = await cleanup_old_audit_logs(
                days_to_keep=days_to_keep,
                batch_size=batch_size,
                account_id=account_id
            )

            if cleanup_result.get("success", False):
                deleted_count = cleanup_result.get("deleted_count", 0)
                log_info(f"Successfully deleted {deleted_count} audit logs after archival{account_msg}")

                duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                return {
                    "table": "audit_logs",
                    "archived_count": archived_count,
                    "deleted_count": deleted_count,
                    "archive_files": archive_result.get("archive_files", []),
                    "cutoff_date": cutoff_date.isoformat(),
                    "days_kept": days_to_keep,
                    "success": True,
                    "duration_seconds": duration
                }
            else:
                log_error(f"Archival succeeded but cleanup failed: {cleanup_result.get('error', 'Unknown error')}")
                return {
                    "table": "audit_logs",
                    "archived_count": archived_count,
                    "deleted_count": 0,
                    "archive_files": archive_result.get("archive_files", []),
                    "error": f"Cleanup failed after successful archival: {cleanup_result.get('error', 'Unknown error')}",
                    "success": False,
                    "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
                }
        else:
            # No había datos para archivar
            log_info(f"No audit logs found to archive{account_msg}")
            return {
                "table": "audit_logs",
                "archived_count": 0,
                "deleted_count": 0,
                "success": True,
                "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
            }

    except Exception as e:
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        error_msg = f"Error in archive and cleanup of audit logs: {str(e)}"
        log_error(f"{error_msg} (after {duration:.2f}s){account_msg}")

        return {
            "table": "audit_logs",
            "archived_count": 0,
            "deleted_count": 0,
            "error": error_msg,
            "success": False,
            "duration_seconds": duration
        }


async def archive_and_cleanup_interactions_async(
    days_to_keep: int = 180,
    batch_size: int = 10000,
    account_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Archiva y luego elimina interacciones más antiguas que el período especificado.

    Esta función primero exporta los datos a GCS y luego los elimina de Cloud SQL,
    proporcionando una estrategia de retención de datos costo-efectiva.

    Args:
        days_to_keep: Número de días a mantener en Cloud SQL (por defecto 180)
        batch_size: Tamaño del lote para procesamiento (por defecto 10000)
        account_id: ID de la cuenta específica (None para todas las cuentas)

    Returns:
        Diccionario con información sobre la operación de archivado y limpieza
    """
    start_time = datetime.now(timezone.utc)
    account_msg = f" for account {account_id}" if account_id else ""
    log_info(f"Starting archive and cleanup of interactions older than {days_to_keep} days{account_msg}")

    try:
        # Importar aquí para evitar dependencias circulares
        from src.services.data_archival_service import DataArchivalService

        # Calcular fecha de corte
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_to_keep)

        # Paso 1: Archivar datos a GCS
        archival_service = DataArchivalService()
        archive_result = await archival_service.archive_interactions(
            cutoff_date=cutoff_date,
            account_id=account_id,
            batch_size=batch_size
        )

        if not archive_result.get("success", False):
            log_error(f"Failed to archive interactions: {archive_result.get('error', 'Unknown error')}")
            return {
                "table": "interactions",
                "archived_count": archive_result.get("archived_count", 0),
                "deleted_count": 0,
                "error": f"Archival failed: {archive_result.get('error', 'Unknown error')}",
                "success": False,
                "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
            }

        archived_count = archive_result.get("archived_count", 0)
        log_info(f"Successfully archived {archived_count} interactions{account_msg}")

        # Paso 2: Solo eliminar si el archivado fue exitoso
        if archived_count > 0:
            cleanup_result = await cleanup_old_interactions(
                days_to_keep=days_to_keep,
                account_id=account_id,
                batch_size=batch_size
            )

            if cleanup_result.get("success", False):
                deleted_count = cleanup_result.get("deleted_count", 0)
                log_info(f"Successfully deleted {deleted_count} interactions after archival{account_msg}")

                duration = (datetime.now(timezone.utc) - start_time).total_seconds()
                return {
                    "table": "interactions",
                    "archived_count": archived_count,
                    "deleted_count": deleted_count,
                    "archive_files": archive_result.get("archive_files", []),
                    "cutoff_date": cutoff_date.isoformat(),
                    "days_kept": days_to_keep,
                    "success": True,
                    "duration_seconds": duration
                }
            else:
                log_error(f"Archival succeeded but cleanup failed: {cleanup_result.get('error', 'Unknown error')}")
                return {
                    "table": "interactions",
                    "archived_count": archived_count,
                    "deleted_count": 0,
                    "archive_files": archive_result.get("archive_files", []),
                    "error": f"Cleanup failed after successful archival: {cleanup_result.get('error', 'Unknown error')}",
                    "success": False,
                    "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
                }
        else:
            # No había datos para archivar
            log_info(f"No interactions found to archive{account_msg}")
            return {
                "table": "interactions",
                "archived_count": 0,
                "deleted_count": 0,
                "success": True,
                "duration_seconds": (datetime.now(timezone.utc) - start_time).total_seconds()
            }

    except Exception as e:
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        error_msg = f"Error in archive and cleanup of interactions: {str(e)}"
        log_error(f"{error_msg} (after {duration:.2f}s){account_msg}")

        return {
            "table": "interactions",
            "archived_count": 0,
            "deleted_count": 0,
            "error": error_msg,
            "success": False,
            "duration_seconds": duration
        }


async def cleanup_soft_deleted_records_async(
    retention_days: int = 365,
    account_id: Optional[int] = None,
    dry_run: bool = False
) -> Dict[str, Any]:
    """
    Limpia registros con soft delete que han excedido el período de retención final.

    Esta función identifica y elimina permanentemente (o archiva y luego elimina)
    registros que tienen is_active = FALSE y deleted_at anterior al umbral definido.

    Args:
        retention_days: Número de días de retención después del soft delete (por defecto 365)
        account_id: ID de la cuenta específica (None para todas las cuentas)
        dry_run: Si es True, solo reporta qué se eliminaría sin hacer cambios

    Returns:
        Diccionario con información sobre la operación de limpieza
    """
    start_time = datetime.now(timezone.utc)
    account_msg = f" for account {account_id}" if account_id else ""
    action = "DRY RUN - Would cleanup" if dry_run else "Cleaning up"

    log_info(f"{action} soft deleted records older than {retention_days} days{account_msg}")

    try:
        # Importar aquí para evitar dependencias circulares
        from src.services.soft_delete_cleanup_service import SoftDeleteCleanupService

        # Crear servicio de limpieza
        cleanup_service = SoftDeleteCleanupService()

        # Ejecutar limpieza
        result = await cleanup_service.cleanup_all_soft_deleted_records(
            account_id=account_id,
            dry_run=dry_run
        )

        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        if result.get("success", False):
            log_info(f"Completed soft delete cleanup{account_msg}: "
                    f"archived {result.get('total_archived', 0)}, "
                    f"deleted {result.get('total_deleted', 0)} records in {duration:.2f}s")
        else:
            log_error(f"Soft delete cleanup failed{account_msg}: {result.get('errors', [])}")

        # Agregar información adicional al resultado
        result.update({
            "retention_days": retention_days,
            "account_id": account_id,
            "dry_run": dry_run,
            "duration_seconds": duration
        })

        return result

    except Exception as e:
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()
        error_msg = f"Error in soft delete cleanup: {str(e)}"
        log_error(f"{error_msg} (after {duration:.2f}s){account_msg}")

        return {
            "retention_days": retention_days,
            "account_id": account_id,
            "dry_run": dry_run,
            "total_archived": 0,
            "total_deleted": 0,
            "error": error_msg,
            "success": False,
            "duration_seconds": duration
        }


async def get_soft_delete_statistics_async(
    account_id: Optional[int] = None
) -> Dict[str, Any]:
    """
    Obtiene estadísticas sobre registros con soft delete.

    Args:
        account_id: ID de la cuenta específica (None para todas las cuentas)

    Returns:
        Diccionario con estadísticas por tabla
    """
    try:
        # Importar aquí para evitar dependencias circulares
        from src.services.soft_delete_cleanup_service import SoftDeleteCleanupService

        # Crear servicio de limpieza
        cleanup_service = SoftDeleteCleanupService()

        # Obtener estadísticas
        stats = await cleanup_service.get_soft_delete_statistics(account_id=account_id)

        log_info(f"Retrieved soft delete statistics for account {account_id if account_id else 'all'}")
        return stats

    except Exception as e:
        error_msg = f"Error getting soft delete statistics: {str(e)}"
        log_error(error_msg)
        return {
            "error": error_msg,
            "success": False
        }

