"""Add API key hash fields

Revision ID: 6fdd0755dc87
Revises: 4fdd0755dc85
Create Date: 2025-04-10 15:37:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "6fdd0755dc87"
down_revision: Union[str, None] = "4fdd0755dc85"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to add API key hash fields."""
    # Añadir nuevas columnas
    op.add_column(
        "accounts",
        sa.Column(
            "api_key_hash",
            sa.String(64),
            nullable=True,
            comment="SHA-256 hash of the API key",
        ),
    )
    op.add_column(
        "accounts",
        sa.Column(
            "api_key_prefix",
            sa.String(10),
            nullable=True,
            comment="Prefix of the API key for display purposes",
        ),
    )
    op.add_column(
        "accounts",
        sa.Column(
            "api_key_last_chars",
            sa.String(6),
            nullable=True,
            comment="Last 6 characters of the API key for display purposes",
        ),
    )
    op.add_column(
        "accounts",
        sa.Column(
            "api_key_created_at",
            sa.DateTime(),
            nullable=True,
            comment="When the API key was created",
        ),
    )

    # Crear nuevos índices y restricciones
    op.create_index(
        "idx_account_api_key_hash", "accounts", ["account_id", "api_key_hash"]
    )
    op.create_unique_constraint(
        "uq_account_api_key_hash", "accounts", ["account_id", "api_key_hash"]
    )

    # Nota: No eliminamos la columna api_key ni sus índices/restricciones inmediatamente
    # para permitir la migración de datos. Se eliminarán en una migración posterior.


def downgrade() -> None:
    """Downgrade schema to remove API key hash fields."""
    # Eliminar índices y restricciones
    op.drop_constraint("uq_account_api_key_hash", "accounts", type_="unique")
    op.drop_index("idx_account_api_key_hash", table_name="accounts")

    # Eliminar columnas
    op.drop_column("accounts", "api_key_created_at")
    op.drop_column("accounts", "api_key_last_chars")
    op.drop_column("accounts", "api_key_prefix")
    op.drop_column("accounts", "api_key_hash")
