"""merge_heads

Revision ID: 48196a49d36e
Revises: 20240520_add_deleted_at, add_task_id_to_training_jobs, cfdd0755dc93
Create Date: 2025-04-16 18:31:42.700479

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '48196a49d36e'
down_revision: Union[str, None] = ('20240520_add_deleted_at', 'add_task_id_to_training_jobs', 'cfdd0755dc93')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
