from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from src.db.session import get_db
from src.core.deps import get_current_account
from src.db import schemas

router = APIRouter()


@router.get("/health", response_model=dict)
async def health_check():
    """
    Endpoint para verificar el estado de la API.
    No requiere autenticación.
    """
    return {
        "status": "ok",
        "message": "API is running",
        "version": "1.0.0",
    }


@router.get("/health/db", response_model=dict)
async def db_health_check(db: AsyncSession = Depends(get_db)):
    """
    Endpoint para verificar la conexión a la base de datos.
    No requiere autenticación.
    """
    try:
        # Ejecutar una consulta simple para verificar la conexión
        result = await db.execute("SELECT 1")
        if result.scalar() == 1:
            return {
                "status": "ok",
                "message": "Database connection is working",
            }
        else:
            return {
                "status": "error",
                "message": "Database connection test failed",
            }
    except Exception as e:
        return {
            "status": "error",
            "message": f"Database connection error: {str(e)}",
        }


@router.get("/health/auth", response_model=dict)
async def auth_health_check(account: schemas.AccountResponse = Depends(get_current_account)):
    """
    Endpoint para verificar la autenticación.
    Requiere autenticación con API Key.
    """
    return {
        "status": "ok",
        "message": "Authentication is working",
        "account_id": account.account_id,
        "account_name": account.name,
    }
