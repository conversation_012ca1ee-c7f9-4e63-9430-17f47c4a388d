import pytest
import pytest_asyncio
import numpy as np
from unittest.mock import MagicMock, patch, AsyncMock
from src.ml_pipeline.serving_engine import HybridRecommender, ServingEngine
from src.ml_pipeline.evaluation import RecommendationEvaluator
from src.core.exceptions import ModelNotTrainedError
from src.core.redis_manager import RedisManager


class TestHybridRecommender:
    """Tests para el recomendador híbrido"""

    @pytest.fixture
    def recommender(self):
        """Fixture para crear una instancia del recomendador híbrido"""
        return HybridRecommender()

    @pytest.fixture
    def mock_redis_manager(self):
        """Fixture para mockear RedisManager"""
        with patch("src.core.redis_manager.RedisManager.get_instance") as mock:
            mock.return_value = AsyncMock(spec=RedisManager)
            return mock

    @pytest.fixture
    def mock_evaluator(self):
        """Fixture para mockear RecommendationEvaluator"""
        with patch(
            "src.ml_pipeline.evaluation.RecommendationEvaluator"
        ) as mock:
            mock.return_value = MagicMock(spec=RecommendationEvaluator)
            return mock

    def test_combine_recommendations(self, recommender):
        """Test que verifica la combinación de recomendaciones"""
        # Crear datos de prueba
        collab_recs = [
            {"item_id": 1, "score": 0.8},
            {"item_id": 2, "score": 0.6},
        ]
        content_recs = [
            {"item_id": 2, "score": 0.7},
            {"item_id": 3, "score": 0.5},
        ]
        
        # Combinar recomendaciones
        result = recommender.combine_recommendations(
            collab_recs, 
            content_recs, 
            n_recommendations=3
        )
        
        # Verificar que se combinaron los resultados
        assert len(result) <= 3
        assert all("item_id" in item for item in result)
        assert all("score" in item for item in result)
        
        # Verificar que los scores se combinaron correctamente
        item_scores = {item["item_id"]: item["score"] for item in result}
        assert len(item_scores) <= 3
        
        # Verificar que están ordenados por score descendente
        scores = [item["score"] for item in result]
        assert all(scores[i] >= scores[i+1] for i in range(len(scores)-1))

    def test_calculate_weights(self, recommender):
        """Test que verifica el cálculo de pesos para diferentes estrategias"""
        # Crear datos de prueba
        collab_recs = [
            {"item_id": 1, "score": 0.8},
            {"item_id": 2, "score": 0.6},
        ]
        content_recs = [
            {"item_id": 2, "score": 0.7},
            {"item_id": 3, "score": 0.5},
        ]
        
        # Probar diferentes estrategias
        strategies = [
            "balanced", 
            "maximize_engagement", 
            "promote_new_arrivals", 
            "discover_diverse"
        ]
        
        for strategy in strategies:
            # Calcular pesos
            collab_weight, content_weight = recommender._calculate_weights(
                collab_recs, 
                content_recs, 
                strategy=strategy
            )
            
            # Verificar que los pesos son positivos
            assert collab_weight > 0
            assert content_weight > 0

    def test_new_user_gets_more_content_weight(self, recommender):
        """Test que verifica que los usuarios nuevos reciben más peso de contenido"""
        # Crear datos de prueba
        collab_recs = [{"item_id": 1, "score": 0.8}]
        content_recs = [{"item_id": 2, "score": 0.7}]
        
        # Calcular pesos para usuario no nuevo
        collab_weight1, content_weight1 = recommender._calculate_weights(
            collab_recs, content_recs, is_new_user=False
        )
        
        # Calcular pesos para usuario nuevo
        collab_weight2, content_weight2 = recommender._calculate_weights(
            collab_recs, content_recs, is_new_user=True
        )
        
        # Verificar que el contenido tiene más peso para usuarios nuevos
        ratio1 = collab_weight1 / content_weight1
        ratio2 = collab_weight2 / content_weight2
        
        assert ratio2 < ratio1  # Menos peso colaborativo relativo para nuevos usuarios
