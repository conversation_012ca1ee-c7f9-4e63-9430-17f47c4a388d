# Configuración de pre-commit hooks para Rayuela
# Ejecuta verificaciones automáticamente antes de cada commit

repos:
  # Hooks de formato de código
  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black
        name: "Black: Formateo de código"
        language_version: python3.12
        args: [--line-length=88]

  - repo: https://github.com/pycqa/isort
    rev: 6.0.1
    hooks:
      - id: isort
        name: "isort: Orden de imports"
        args: [--profile, black, --line-length, "88"]

  # Hooks de linting
  - repo: https://github.com/pycqa/flake8
    rev: 7.2.0
    hooks:
      - id: flake8
        name: "flake8: Linting de código"
        additional_dependencies:
          - flake8-bugbear
          - flake8-bandit
          - flake8-naming
          - flake8-comprehensions
          - flake8-simplify

  # Hooks de type checking
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.15.0
    hooks:
      - id: mypy
        name: "MyPy: Verificación de tipos"
        args: [--ignore-missing-imports, --show-error-codes]
        additional_dependencies:
          - types-requests
          - types-redis
          - types-setuptools

  # Hooks de seguridad
  - repo: https://github.com/pycqa/bandit
    rev: 1.8.0
    hooks:
      - id: bandit
        name: "Bandit: Análisis de seguridad"
        args: [-ll, -r, src/]

  # Hooks generales de archivos
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        name: "Eliminar espacios en blanco al final"
      - id: end-of-file-fixer
        name: "Asegurar nueva línea al final del archivo"
      - id: check-yaml
        name: "Verificar sintaxis YAML"
      - id: check-toml
        name: "Verificar sintaxis TOML"
      - id: check-json
        name: "Verificar sintaxis JSON"
      - id: check-added-large-files
        name: "Verificar archivos grandes"
        args: [--maxkb=500]
      - id: check-merge-conflict
        name: "Verificar marcadores de merge conflict"
      - id: debug-statements
        name: "Verificar statements de debug"
      - id: check-docstring-first
        name: "Verificar docstrings al inicio"

  # Hooks específicos para Python
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        name: "pydocstyle: Verificar docstrings"
        args: [--convention=google]
        exclude: ^(tests/|alembic/)

  # Verificación de dependencias (solo en CI/CD por performance)
  - repo: local
    hooks:
      - id: safety-check
        name: "Safety: Verificar vulnerabilidades en dependencias"
        entry: safety
        args: [check, --short-report]
        language: python
        pass_filenames: false
        stages: [manual]  # Solo ejecutar manualmente o en CI/CD

      - id: pip-audit
        name: "pip-audit: Auditoría de dependencias"
        entry: pip-audit
        args: [--desc]
        language: python
        pass_filenames: false
        stages: [manual]  # Solo ejecutar manualmente o en CI/CD

# Configuración global
default_stages: [commit]
fail_fast: false
minimum_pre_commit_version: '3.0.0'

# Configuraciones específicas
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit hooks
    
    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: [safety-check, pip-audit]  # Skip expensive checks en CI 