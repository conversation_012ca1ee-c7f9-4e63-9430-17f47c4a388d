// src/lib/error-handler.ts
import { toast } from "sonner";
import { ApiError } from "@/lib/api";
import Link from "next/link";
import React from "react";

/**
 * Maneja errores de API y muestra mensajes de toast apropiados.
 * Para errores de limite excedido, muestra un mensaje con un enlace a la pagina de facturacion.
 *
 * @param error El error capturado
 * @param defaultMessage Mensaje por defecto si no se puede determinar el tipo de error
 */
export function handleApiError(error: unknown, defaultMessage: string = "Ha ocurrido un error"): void {
  // Registrar el error en la consola para depuracion
  console.group('API Error Handler');
  console.error("Error details:", error);

  // Si es un error de API
  if (error instanceof ApiError) {
    // Manejar errores de limite excedido
    if (error.errorCode === "RATE_LIMIT_EXCEEDED") {
      toast.error(React.createElement('div', {},
        "Limite de tasa excedido. Intenta de nuevo mas tarde o ",
        React.createElement(Link, { href: "/billing", className: "underline font-medium" }, "actualiza tu plan"),
        " para aumentar tus limites."
      ));
      return;
    }

    // Manejar errores de limite de recursos
    if (error.errorCode === "RESOURCE_LIMIT_EXCEEDED") {
      toast.error(React.createElement('div', {},
        "Limite de recursos excedido. Has alcanzado el limite de tu plan actual. ",
        React.createElement(Link, { href: "/billing", className: "underline font-medium" }, "Actualiza tu plan"),
        " para continuar."
      ));
      return;
    }

    // Manejar errores de limite de suscripcion
    if (error.errorCode === "SUBSCRIPTION_LIMIT") {
      toast.error(React.createElement('div', {},
        "Has alcanzado el limite de tu suscripcion. ",
        React.createElement(Link, { href: "/billing", className: "underline font-medium" }, "Actualiza tu plan"),
        " para obtener mas capacidad."
      ));
      return;
    }

    // Manejar errores de limite de frecuencia de entrenamiento
    if (error.errorCode === "TRAINING_FREQUENCY_LIMIT") {
      toast.error(React.createElement('div', {},
        "Has alcanzado el limite de frecuencia de entrenamiento. ",
        React.createElement(Link, { href: "/billing", className: "underline font-medium" }, "Actualiza tu plan"),
        " para entrenar con mayor frecuencia."
      ));
      return;
    }

    // Manejar errores de autenticacion
    if (error.errorCode === "UNAUTHORIZED" || error.errorCode === "INVALID_API_KEY") {
      toast.error(React.createElement('div', {},
        "Error de autenticacion. Tu API Key puede ser invalida o haber expirado. ",
        React.createElement(Link, { href: "/api-keys", className: "underline font-medium" }, "Regenerar API Key")
      ));
      return;
    }

    // Manejar errores de validacion
    if (error.errorCode === "VALIDATION_ERROR") {
      toast.error(React.createElement('div', {},
        "Error de validacion: " + error.message + ". ",
        React.createElement('a', {
          href: "https://docs.rayuela.ai/api-reference",
          target: "_blank",
          rel: "noopener noreferrer",
          className: "underline font-medium"
        }, "Consultar documentacion")
      ));
      return;
    }

    // Manejar errores de datos insuficientes
    if (error.errorCode === "INSUFFICIENT_DATA") {
      toast.error(React.createElement('div', {},
        "Datos insuficientes para generar recomendaciones. ",
        React.createElement('a', {
          href: "https://docs.rayuela.ai/quickstart#carga-de-datos-basicos",
          target: "_blank",
          rel: "noopener noreferrer",
          className: "underline font-medium"
        }, "Cargar mas datos")
      ));
      return;
    }

    // Manejar errores de servicio no disponible
    if (error.errorCode === "SERVICE_UNAVAILABLE") {
      toast.error("Servicio temporalmente no disponible. Por favor, intenta de nuevo mas tarde.");
      return;
    }

    // Para otros errores de API, mostrar el mensaje de error
    toast.error(error.message || defaultMessage);
    console.log("Unhandled API error code:", error.errorCode);
    return;
  }

  // Para errores que no son de API
  if (error instanceof Error) {
    toast.error(error.message || defaultMessage);
    return;
  }

  // Para errores desconocidos
  toast.error(defaultMessage);

  console.groupEnd();
}
