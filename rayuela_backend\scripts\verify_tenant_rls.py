#!/usr/bin/env python
"""
Script para verificar que todas las tablas que usan TenantMixin tengan políticas RLS activadas.

Este script debe ejecutarse con acceso de superusuario a la base de datos.
Ejemplo de uso:
    python -m scripts.verify_tenant_rls
"""
import asyncio
import sys
from typing import List, Dict, Any, Set
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.session import get_db
from src.utils.base_logger import logger, log_info, log_error, log_warning

# Lista de tablas que usan TenantMixin
TENANT_TABLES = [
    'products',
    'end_users',
    'interactions',
    'searches',
    'system_users',
    'roles',
    'permissions',
    'system_user_roles',
    'training_jobs',
    'batch_ingestion_jobs',
    'artifact_metadata',  # ModelMetadata.__tablename__
    'model_metrics',
    'audit_logs',
    'notifications',
    'account_usage_metrics',
    'subscriptions'
]

# Lista de operaciones que deben estar cubiertas por políticas RLS
REQUIRED_OPERATIONS = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']

async def get_all_tables() -> List[str]:
    """Obtener listado de todas las tablas en la base de datos."""
    async for db in get_db():
        result = await db.execute(
            text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_type = 'BASE TABLE'
            """)
        )
        return [row[0] for row in result.fetchall()]

async def get_tables_with_rls() -> List[str]:
    """Obtener listado de tablas que tienen RLS habilitado."""
    async for db in get_db():
        result = await db.execute(
            text("""
                SELECT table_name
                FROM information_schema.tables
                WHERE table_schema = 'public'
                AND table_name IN :tables
                AND EXISTS (
                    SELECT 1
                    FROM pg_catalog.pg_class c
                    JOIN pg_catalog.pg_namespace n ON c.relnamespace = n.oid
                    WHERE c.relname = table_name
                    AND n.nspname = 'public'
                    AND c.relrowsecurity = TRUE
                )
            """),
            {"tables": tuple(TENANT_TABLES)}
        )
        return [row[0] for row in result.fetchall()]

async def get_policies_for_tables() -> Dict[str, List[Dict[str, Any]]]:
    """Obtener todas las políticas RLS para las tablas configuradas."""
    policies = {}
    
    async for db in get_db():
        for table in TENANT_TABLES:
            result = await db.execute(
                text("""
                    SELECT polname, polcmd, polpermissive, polroles::text
                    FROM pg_policy
                    WHERE polrelid = (
                        SELECT oid FROM pg_class
                        WHERE relname = :table_name
                        AND relnamespace = (
                            SELECT oid FROM pg_namespace
                            WHERE nspname = 'public'
                        )
                    )
                """),
                {"table_name": table}
            )
            
            table_policies = []
            for row in result.fetchall():
                table_policies.append({
                    "name": row[0],
                    "operation": row[1],
                    "permissive": row[2],
                    "roles": row[3]
                })
            
            policies[table] = table_policies
    
    return policies

async def verify_rls_setup() -> bool:
    """Verificar que todas las tablas tengan RLS configurado correctamente."""
    # Obtener todas las tablas
    all_tables = await get_all_tables()
    log_info(f"Total de tablas en la base de datos: {len(all_tables)}")
    
    # Obtener tablas con RLS habilitado
    tables_with_rls = await get_tables_with_rls()
    log_info(f"Tablas con RLS habilitado: {len(tables_with_rls)}/{len(TENANT_TABLES)}")
    
    # Identificar tablas faltantes
    missing_tables = set(TENANT_TABLES) - set(tables_with_rls)
    if missing_tables:
        log_error(f"Tablas sin RLS habilitado: {', '.join(missing_tables)}")
    else:
        log_info("Todas las tablas requeridas tienen RLS habilitado.")
    
    # Verificar políticas
    all_policies = await get_policies_for_tables()
    all_valid = True
    
    for table in TENANT_TABLES:
        policies = all_policies.get(table, [])
        log_info(f"Tabla {table}: {len(policies)} políticas")
        
        # Verificar que todas las operaciones estén cubiertas
        covered_operations = set(policy["operation"] for policy in policies)
        missing_operations = set(REQUIRED_OPERATIONS) - covered_operations
        
        if missing_operations:
            log_error(f"Tabla {table}: Faltan políticas para operaciones {', '.join(missing_operations)}")
            all_valid = False
        
        # Verificar que las políticas usan account_id
        tenant_filter_found = False
        for policy in policies:
            async for db in get_db():
                # Obtener definición de la política
                result = await db.execute(
                    text("""
                        SELECT pg_catalog.pg_get_expr(polqual, polrelid) AS using_expr,
                               pg_catalog.pg_get_expr(polwithcheck, polrelid) AS check_expr
                        FROM pg_policy
                        WHERE polname = :policy_name
                        AND polrelid = (
                            SELECT oid FROM pg_class
                            WHERE relname = :table_name
                            AND relnamespace = (
                                SELECT oid FROM pg_namespace
                                WHERE nspname = 'public'
                            )
                        )
                    """),
                    {"policy_name": policy["name"], "table_name": table}
                )
                
                row = result.fetchone()
                if row:
                    using_expr = row[0] or ""
                    check_expr = row[1] or ""
                    
                    # Verificar si se usa account_id o tenant_id en la definición
                    tenant_filter_found = (
                        "account_id" in using_expr or
                        "tenant_id" in using_expr or
                        "app.tenant_id" in using_expr or
                        "account_id" in check_expr or
                        "tenant_id" in check_expr or
                        "app.tenant_id" in check_expr
                    )
                    
                    if not tenant_filter_found:
                        log_error(f"Política {policy['name']} para {table} no parece usar filtro de tenant.")
                        all_valid = False
                    break
    
    return all_valid

async def main():
    """Función principal."""
    logger.info("Verificando configuración de Row-Level Security para tablas con TenantMixin...")
    
    valid = await verify_rls_setup()
    
    if valid:
        logger.info("✅ La configuración de RLS parece correcta para todas las tablas con TenantMixin.")
    else:
        logger.error("❌ Se encontraron problemas en la configuración de RLS.")
        logger.info(
            "Para corregir los problemas, ejecute:"
            "\n  python -m alembic upgrade head"
        )

if __name__ == "__main__":
    asyncio.run(main())
