#!/bin/bash

set -e

# Configuración
DOMAIN="rayuela.ai"
API_DOMAIN="api.rayuela.ai"
PROJECT_ID="lateral-insight-461112-g9"
REGION="us-central1"

echo "🔧 === ACTUALIZANDO CONFIGURACIÓN DEL FRONTEND ==="
echo ""

echo "🔍 Estado actual del frontend:"
gcloud run services describe rayuela-frontend --region=$REGION --format="value(spec.template.spec.containers[0].env[?(@.name=='NEXT_PUBLIC_API_BASE_URL')].value)"

echo ""
echo "🔄 Actualizando variables de entorno del frontend..."

# Actualizar el frontend para usar el dominio personalizado del backend
gcloud run services update rayuela-frontend \
    --region=$REGION \
    --set-env-vars=NEXT_PUBLIC_API_BASE_URL=https://$API_DOMAIN,NODE_ENV=production

echo ""
echo "✅ Frontend actualizado para usar:"
echo "   🌐 Frontend: https://$DOMAIN"
echo "   🔗 Backend API: https://$API_DOMAIN"

echo ""
echo "🔍 Verificando nueva configuración:"
gcloud run services describe rayuela-frontend --region=$REGION --format="value(spec.template.spec.containers[0].env[?(@.name=='NEXT_PUBLIC_API_BASE_URL')].value)"

echo ""
echo "🎉 ¡Configuración del frontend actualizada!" 