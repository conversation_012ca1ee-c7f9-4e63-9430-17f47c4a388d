"""
Integration tests for multi-tenant security, focusing on SQL injection prevention
and comprehensive tenant isolation.
"""
import pytest
import json
from typing import Dict, Any, List, Optional
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import text, func
import re

from src.db.session import DatabaseConnectionManager, get_db
from src.db.models import (
    Account,
    Product,
    EndUser,
    Interaction,
    ModelMetadata,
    AuditLog,
    TrainingJob
)
from src.core.config import settings
from src.utils.base_logger import log_info, log_error
from src.core.deps import get_current_account
from src.db.repositories.base import BaseRepository


class TestMultiTenancySecurity:
    """Tests for multi-tenant security and isolation."""

    @pytest.fixture
    async def db_session(self) -> AsyncSession:
        """Fixture for database session."""
        connection_manager = await DatabaseConnectionManager.get_instance()
        session = await connection_manager.get_session()
        try:
            yield session
        finally:
            await session.close()

    @pytest.fixture
    async def test_accounts(self, db_session: AsyncSession) -> List[Account]:
        """Fixture to create test accounts."""
        accounts = []
        for i in range(2):
            account = Account(
                name=f"Security Test Account {i}",
                email=f"security_test{i}@example.com",
                status="active",
                api_key_hash=f"hash_{i}",
                api_key_prefix=f"prefix_{i}",
                api_key_last_chars=f"last_{i}"
            )
            db_session.add(account)
            await db_session.commit()
            await db_session.refresh(account)
            accounts.append(account)
        return accounts

    @pytest.fixture
    async def test_products(self, db_session: AsyncSession, test_accounts: List[Account]) -> List[Product]:
        """Fixture to create test products."""
        products = []
        for account in test_accounts:
            for i in range(2):
                product = Product(
                    account_id=account.account_id,
                    name=f"Security Test Product {account.account_id}-{i}",
                    description=f"Description for product {i}",
                    price=10.99 + i,
                    category=f"category_{i}",
                    inventory_count=100 + i
                )
                db_session.add(product)
            await db_session.commit()

            # Fetch the created products
            stmt = select(Product).where(Product.account_id == account.account_id)
            result = await db_session.execute(stmt)
            account_products = result.scalars().all()
            products.extend(account_products)

        return products

    @pytest.fixture
    async def test_users(self, db_session: AsyncSession, test_accounts: List[Account]) -> List[EndUser]:
        """Fixture to create test end users."""
        users = []
        for account in test_accounts:
            for i in range(2):
                user = EndUser(
                    account_id=account.account_id,
                    external_id=f"user_{account.account_id}_{i}",
                    is_active=True
                )
                db_session.add(user)
            await db_session.commit()

            # Fetch the created users
            stmt = select(EndUser).where(EndUser.account_id == account.account_id)
            result = await db_session.execute(stmt)
            account_users = result.scalars().all()
            users.extend(account_users)

        return users

    @pytest.fixture
    async def test_interactions(self, db_session: AsyncSession, test_accounts: List[Account],
                               test_users: List[EndUser], test_products: List[Product]) -> List[Interaction]:
        """Fixture to create test interactions."""
        interactions = []

        # Group users and products by account_id
        users_by_account = {}
        products_by_account = {}

        for user in test_users:
            if user.account_id not in users_by_account:
                users_by_account[user.account_id] = []
            users_by_account[user.account_id].append(user)

        for product in test_products:
            if product.account_id not in products_by_account:
                products_by_account[product.account_id] = []
            products_by_account[product.account_id].append(product)

        # Create interactions for each account
        for account in test_accounts:
            account_users = users_by_account.get(account.account_id, [])
            account_products = products_by_account.get(account.account_id, [])

            if not account_users or not account_products:
                continue

            for user in account_users:
                for product in account_products:
                    interaction = Interaction(
                        account_id=account.account_id,
                        end_user_id=user.id,
                        product_id=product.id,
                        interaction_type="view",
                        value=1.0
                    )
                    db_session.add(interaction)

            await db_session.commit()

            # Fetch the created interactions
            stmt = select(Interaction).where(Interaction.account_id == account.account_id)
            result = await db_session.execute(stmt)
            account_interactions = result.scalars().all()
            interactions.extend(account_interactions)

        return interactions

    @pytest.fixture
    async def test_audit_logs(self, db_session: AsyncSession, test_accounts: List[Account]) -> List[AuditLog]:
        """Fixture to create test audit logs."""
        audit_logs = []
        for account in test_accounts:
            for i in range(2):
                audit_log = AuditLog(
                    account_id=account.account_id,
                    action=f"test_action_{i}",
                    entity_type="product",
                    entity_id=f"{i}",
                    performed_by=f"test_user_{i}",
                    details=f"Test audit log {i} for account {account.account_id}"
                )
                db_session.add(audit_log)
            await db_session.commit()

            # Fetch the created audit logs
            stmt = select(AuditLog).where(AuditLog.account_id == account.account_id)
            result = await db_session.execute(stmt)
            account_audit_logs = result.scalars().all()
            audit_logs.extend(account_audit_logs)

        return audit_logs

    async def test_sql_injection_in_url_params(self, client: TestClient, test_accounts: List[Account],
                                              test_products: List[Product]):
        """Test SQL injection prevention in URL parameters."""
        # Authenticate as tenant A
        account_a = test_accounts[0]

        # Get token for tenant A
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": account_a.email,
                "password": "test123"
            }
        )
        token = response.json()["access_token"]

        # Attempt SQL injection in product ID
        sql_injection_attempts = [
            "1 OR 1=1",
            "1; SELECT * FROM products",
            "1 UNION SELECT * FROM accounts",
            f"{test_products[0].id} OR account_id = {test_accounts[1].account_id}",
            f"{test_products[0].id}; SET app.tenant_id = {test_accounts[1].account_id}"
        ]

        for injection in sql_injection_attempts:
            response = client.get(
                f"/api/v1/products/{injection}",
                headers={"Authorization": f"Bearer {token}"}
            )
            # Should either return 404 (not found) or 422 (validation error)
            assert response.status_code in [404, 422], f"SQL injection not prevented: {injection}"

            # If we get a 200, make sure it's only our own product
            if response.status_code == 200:
                product_data = response.json()
                assert product_data["account_id"] == account_a.account_id

    async def test_sql_injection_in_query_params(self, client: TestClient, test_accounts: List[Account]):
        """Test SQL injection prevention in query parameters."""
        # Authenticate as tenant A
        account_a = test_accounts[0]

        # Get token for tenant A
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": account_a.email,
                "password": "test123"
            }
        )
        token = response.json()["access_token"]

        # Attempt SQL injection in query parameters
        sql_injection_attempts = [
            "category=Electronics' OR '1'='1",
            "limit=10; DROP TABLE products",
            "sort=name; UPDATE accounts SET status='inactive'",
            f"account_id={test_accounts[1].account_id}"  # Try to access another tenant's data
        ]

        for injection in sql_injection_attempts:
            response = client.get(
                f"/api/v1/products?{injection}",
                headers={"Authorization": f"Bearer {token}"}
            )

            # Should not cause a server error
            assert response.status_code != 500, f"SQL injection caused server error: {injection}"

            # If successful, verify all returned products belong to tenant A
            if response.status_code == 200:
                products_data = response.json()
                if "items" in products_data:
                    for product in products_data["items"]:
                        assert product["account_id"] == account_a.account_id

    async def test_sql_injection_in_request_body(self, client: TestClient, test_accounts: List[Account]):
        """Test SQL injection prevention in request body."""
        # Authenticate as tenant A
        account_a = test_accounts[0]

        # Get token for tenant A
        # In a real test, we would use the actual email from the account
        # For this test, we'll use a mock login
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": f"user_{account_a.account_id}@example.com",
                "password": "test123"
            }
        )
        token = response.json()["access_token"]

        # Attempt SQL injection in request body
        sql_injection_bodies = [
            {"name": "Test Product', account_id = " + str(test_accounts[1].account_id) + " --", "price": 10.99, "category": "Electronics"},
            {"name": "Test Product", "price": 10.99, "category": "Electronics; DROP TABLE products"},
            {"name": "Test Product", "price": 10.99, "category": "Electronics", "account_id": test_accounts[1].account_id},
            {"name": "Test Product", "description": f"Description'; UPDATE products SET account_id = {test_accounts[1].account_id} WHERE account_id = {account_a.account_id}; --"}
        ]

        for injection_body in sql_injection_bodies:
            response = client.post(
                "/api/v1/products",
                headers={"Authorization": f"Bearer {token}"},
                json=injection_body
            )

            # Should not cause a server error
            assert response.status_code != 500, f"SQL injection caused server error: {injection_body}"

            # If product was created, verify it belongs to tenant A
            if response.status_code == 201:
                product_data = response.json()
                assert product_data["account_id"] == account_a.account_id

                # Verify no data was leaked to tenant B
                response = client.get(
                    f"/api/v1/products/{product_data['id']}",
                    headers={"Authorization": f"Bearer {token}"}
                )
                assert response.status_code == 200

                # Get a token for tenant B and try to access the product
                # In a real test, we would use the actual email from the account
                # For this test, we'll use a mock login
                response_b = client.post(
                    "/api/v1/auth/login",
                    json={
                        "email": f"user_{test_accounts[1].account_id}@example.com",
                        "password": "test123"
                    }
                )
                token_b = response_b.json()["access_token"]

                response_b = client.get(
                    f"/api/v1/products/{product_data['id']}",
                    headers={"Authorization": f"Bearer {token_b}"}
                )
                assert response_b.status_code == 404

    async def test_rls_policy_all_models(self, db_session: AsyncSession, test_accounts: List[Account]):
        """Test RLS policies for all tenant-scoped models."""
        account_a = test_accounts[0]
        account_b = test_accounts[1]

        # Test models and their IDs for each account
        models_to_test = [
            (Product, "products"),
            (EndUser, "end_users"),
            (Interaction, "interactions"),
            (AuditLog, "audit_logs")
        ]

        for model_class, table_name in models_to_test:
            # Set tenant context to account A
            await db_session.execute(text("SET app.tenant_id = :tenant_id"), {"tenant_id": account_a.account_id})

            # Count records for account A
            result = await db_session.execute(
                text(f"SELECT COUNT(*) FROM {table_name} WHERE account_id = :account_id"),
                {"account_id": account_a.account_id}
            )
            count_a_direct = result.scalar_one()

            # Count records with RLS applied (should match direct count)
            result = await db_session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            count_a_rls = result.scalar_one()

            assert count_a_direct == count_a_rls, f"RLS policy not working correctly for {table_name} with account A"

            # Try to access account B's data with account A's context
            result = await db_session.execute(
                text(f"SELECT COUNT(*) FROM {table_name} WHERE account_id = :account_id"),
                {"account_id": account_b.account_id}
            )
            count_b_from_a = result.scalar_one()

            # Should be 0 because RLS prevents access
            assert count_b_from_a == 0, f"RLS policy not preventing cross-tenant access for {table_name}"

            # Switch to account B's context
            await db_session.execute(text("SET app.tenant_id = :tenant_id"), {"tenant_id": account_b.account_id})

            # Count records for account B
            result = await db_session.execute(
                text(f"SELECT COUNT(*) FROM {table_name} WHERE account_id = :account_id"),
                {"account_id": account_b.account_id}
            )
            count_b_direct = result.scalar_one()

            # Count records with RLS applied (should match direct count)
            result = await db_session.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            count_b_rls = result.scalar_one()

            assert count_b_direct == count_b_rls, f"RLS policy not working correctly for {table_name} with account B"

            # Try to access account A's data with account B's context
            result = await db_session.execute(
                text(f"SELECT COUNT(*) FROM {table_name} WHERE account_id = :account_id"),
                {"account_id": account_a.account_id}
            )
            count_a_from_b = result.scalar_one()

            # Should be 0 because RLS prevents access
            assert count_a_from_b == 0, f"RLS policy not preventing cross-tenant access for {table_name}"

    async def test_base_repository_tenant_filter(self, db_session: AsyncSession, test_accounts: List[Account]):
        """Test that BaseRepository._add_tenant_filter correctly filters by tenant."""
        account_a = test_accounts[0]
        account_b = test_accounts[1]

        # Create a test repository for Product model
        repo_a = BaseRepository(db=db_session, account_id=account_a.account_id, model=Product)
        repo_b = BaseRepository(db=db_session, account_id=account_b.account_id, model=Product)

        # Create a basic query
        query = select(Product)

        # Apply tenant filter for account A
        filtered_query_a = repo_a._add_tenant_filter(query)

        # Apply tenant filter for account B
        filtered_query_b = repo_b._add_tenant_filter(query)

        # Execute both queries
        result_a = await db_session.execute(filtered_query_a)
        products_a = result_a.scalars().all()

        result_b = await db_session.execute(filtered_query_b)
        products_b = result_b.scalars().all()

        # Verify that each query only returns products for the correct tenant
        for product in products_a:
            assert product.account_id == account_a.account_id, "Tenant filter failed for account A"

        for product in products_b:
            assert product.account_id == account_b.account_id, "Tenant filter failed for account B"

        # Test with a more complex query (with additional filters)
        complex_query = select(Product).where(Product.price > 10)

        # Apply tenant filters
        filtered_complex_a = repo_a._add_tenant_filter(complex_query)
        filtered_complex_b = repo_b._add_tenant_filter(complex_query)

        # Execute both queries
        result_complex_a = await db_session.execute(filtered_complex_a)
        products_complex_a = result_complex_a.scalars().all()

        result_complex_b = await db_session.execute(filtered_complex_b)
        products_complex_b = result_complex_b.scalars().all()

        # Verify tenant isolation is maintained with complex queries
        for product in products_complex_a:
            assert product.account_id == account_a.account_id, "Tenant filter failed for complex query (account A)"
            assert product.price > 10, "Additional filter not applied correctly"

        for product in products_complex_b:
            assert product.account_id == account_b.account_id, "Tenant filter failed for complex query (account B)"
            assert product.price > 10, "Additional filter not applied correctly"

    async def test_repository_without_tenant_id(self, db_session: AsyncSession):
        """Test that BaseRepository raises an error when no tenant_id is provided for tenant-scoped models."""
        # Create a repository without account_id for a tenant-scoped model
        repo = BaseRepository(db=db_session, model=Product)

        # Attempt to get all products should raise an error
        with pytest.raises(ValueError, match="Account ID is required"):
            await repo.get_all()

        # Attempt to get by ID should raise an error
        with pytest.raises(ValueError, match="Account ID is required"):
            await repo.get_by_id(1)

        # Attempt to create should raise an error
        with pytest.raises(ValueError, match="Account ID is required"):
            await repo.create({"name": "Test Product"})

    async def test_celery_task_decorator(self, test_accounts: List[Account]):
        """Test that the with_tenant_context_celery decorator correctly sets the tenant context."""
        from src.utils.tenant_context import with_tenant_context_celery
        from unittest.mock import MagicMock

        account_a = test_accounts[0]

        # Create a mock function to be decorated
        mock_func = MagicMock()
        mock_func.__name__ = "mock_celery_task"

        # Apply the decorator
        decorated_func = with_tenant_context_celery()(mock_func)

        # Call the decorated function with account_id
        decorated_func(account_id=account_a.account_id, test_arg="test")

        # Verify the original function was called with the correct arguments
        mock_func.assert_called_once_with(account_id=account_a.account_id, test_arg="test")

        # Reset the mock
        mock_func.reset_mock()

        # Test with positional arguments
        decorated_func(account_a.account_id, "test")

        # Verify the original function was called with the correct arguments
        mock_func.assert_called_once_with(account_a.account_id, "test")
