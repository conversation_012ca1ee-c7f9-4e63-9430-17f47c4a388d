"""
Service for tracking storage usage and enforcing storage limits.
"""
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, and_, text
from sqlalchemy.exc import SQLAlchemyError
from typing import Optional, Dict, Any, List
from datetime import datetime, timezone

from src.db.models import Subscription, Product, Interaction, EndUser, ModelMetadata
from src.core.exceptions import LimitExceededError
from src.utils.base_logger import log_info, log_error, log_warning
from src.core.cache_manager import CacheManager


class StorageTrackerService:
    """Service for tracking storage usage and enforcing storage limits."""

    def __init__(self, db: AsyncSession, account_id: int):
        """Initialize the service with a database session and account ID."""
        self.db = db
        self.account_id = account_id
        self._cache_manager = CacheManager()
        self._cache_prefix = "storage_tracker"

    def _get_storage_key(self) -> str:
        """Genera la clave para el uso de almacenamiento en caché."""
        return f"{self._cache_prefix}:{self.account_id}:usage"

    async def get_current_storage_usage(self) -> int:
        """
        Calculate the current storage usage for the account.

        Returns:
            int: The total storage usage in bytes
        """
        try:
            # Intentar obtener de caché
            storage_key = self._get_storage_key()
            cached_usage = await self._cache_manager.get(storage_key)
            if cached_usage is not None:
                return cached_usage

            # Calcular uso de almacenamiento desde varias tablas usando funciones nativas de PostgreSQL
            total_storage = 0

            # 1. Products table
            products_size = await self._calculate_products_size()
            total_storage += products_size

            # 2. End Users table
            end_users_size = await self._calculate_end_users_size()
            total_storage += end_users_size

            # 3. Interactions table
            interactions_size = await self._calculate_interactions_size()
            total_storage += interactions_size

            # 4. Artifact Metadata table (models)
            artifacts_size = await self._calculate_artifacts_size()
            total_storage += artifacts_size

            # Guardar en caché
            await self._cache_manager.set(storage_key, total_storage, ttl=300)  # 5 minutos

            return total_storage
        except Exception as e:
            log_error(f"Error calculating storage usage: {str(e)}")
            return 0

    async def update_subscription_storage_usage(self) -> bool:
        """
        Update the storage_used field in the subscription.

        Returns:
            bool: True if the update was successful, False otherwise
        """
        try:
            # Get current storage usage
            storage_used = await self.get_current_storage_usage()

            # Update subscription
            stmt = (
                update(Subscription)
                .where(Subscription.account_id == self.account_id)
                .values(storage_used=storage_used)
            )
            await self.db.execute(stmt)
            await self.db.commit()

            log_info(
                f"Updated storage usage for account {self.account_id}: {storage_used} bytes"
            )
            return True
        except Exception as e:
            log_error(f"Error updating storage usage: {str(e)}")
            await self.db.rollback()
            return False

    async def check_storage_limit(self, additional_bytes: int = 0) -> bool:
        """
        Check if the account has exceeded its storage limit.

        Args:
            additional_bytes: Additional bytes to consider (for new data being added)

        Returns:
            bool: True if the limit is not exceeded, False otherwise

        Raises:
            LimitExceededError: If the storage limit is exceeded
        """
        try:
            # Get subscription
            stmt = select(Subscription).where(Subscription.account_id == self.account_id)
            result = await self.db.execute(stmt)
            subscription = result.scalars().first()

            if not subscription:
                log_warning(f"No subscription found for account {self.account_id}")
                return False

            # Get current storage usage
            current_usage = await self.get_current_storage_usage()

            # Check if adding additional bytes would exceed the limit
            if current_usage + additional_bytes > subscription.storage_limit:
                log_warning(
                    f"Storage limit exceeded for account {self.account_id}: "
                    f"{current_usage + additional_bytes}/{subscription.storage_limit} bytes"
                )
                raise LimitExceededError(
                    f"Storage limit exceeded: {current_usage + additional_bytes}/{subscription.storage_limit} bytes"
                )

            return True
        except LimitExceededError:
            raise
        except Exception as e:
            log_error(f"Error checking storage limit: {str(e)}")
            return False

    async def _calculate_products_size(self) -> int:
        """Calculate the storage used by products using PostgreSQL native functions."""
        try:
            # Get the partition name for this account
            partition_query = text("""
                SELECT tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'products_p%' 
                AND tablename = :partition_name
            """)
            partition_name = f"products_p{self.account_id}"
            result = await self.db.execute(partition_query, {"partition_name": partition_name})
            partition = result.scalar_one_or_none()

            if partition:
                # Get total size of the partition
                size_query = text("""
                    SELECT pg_total_relation_size(:partition_name)
                """)
                result = await self.db.execute(size_query, {"partition_name": partition})
                return result.scalar_one() or 0
            else:
                # If no partition exists, get size of rows for this account
                size_query = text("""
                    SELECT pg_total_relation_size('products')
                    WHERE account_id = :account_id
                """)
                result = await self.db.execute(size_query, {"account_id": self.account_id})
                return result.scalar_one() or 0
        except Exception as e:
            log_error(f"Error calculating products size: {str(e)}")
            return 0

    async def _calculate_end_users_size(self) -> int:
        """Calculate the storage used by end users using PostgreSQL native functions."""
        try:
            # Get the partition name for this account
            partition_query = text("""
                SELECT tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'end_users_p%' 
                AND tablename = :partition_name
            """)
            partition_name = f"end_users_p{self.account_id}"
            result = await self.db.execute(partition_query, {"partition_name": partition_name})
            partition = result.scalar_one_or_none()

            if partition:
                # Get total size of the partition
                size_query = text("""
                    SELECT pg_total_relation_size(:partition_name)
                """)
                result = await self.db.execute(size_query, {"partition_name": partition})
                return result.scalar_one() or 0
            else:
                # If no partition exists, get size of rows for this account
                size_query = text("""
                    SELECT pg_total_relation_size('end_users')
                    WHERE account_id = :account_id
                """)
                result = await self.db.execute(size_query, {"account_id": self.account_id})
                return result.scalar_one() or 0
        except Exception as e:
            log_error(f"Error calculating end users size: {str(e)}")
            return 0

    async def _calculate_interactions_size(self) -> int:
        """Calculate the storage used by interactions using PostgreSQL native functions."""
        try:
            # Get the partition name for this account
            partition_query = text("""
                SELECT tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'interactions_p%' 
                AND tablename = :partition_name
            """)
            partition_name = f"interactions_p{self.account_id}"
            result = await self.db.execute(partition_query, {"partition_name": partition_name})
            partition = result.scalar_one_or_none()

            if partition:
                # Get total size of the partition
                size_query = text("""
                    SELECT pg_total_relation_size(:partition_name)
                """)
                result = await self.db.execute(size_query, {"partition_name": partition})
                return result.scalar_one() or 0
            else:
                # If no partition exists, get size of rows for this account
                size_query = text("""
                    SELECT pg_total_relation_size('interactions')
                    WHERE account_id = :account_id
                """)
                result = await self.db.execute(size_query, {"account_id": self.account_id})
                return result.scalar_one() or 0
        except Exception as e:
            log_error(f"Error calculating interactions size: {str(e)}")
            return 0

    async def _calculate_artifacts_size(self) -> int:
        """Calculate the storage used by model artifacts using PostgreSQL native functions."""
        try:
            # Get the partition name for this account
            partition_query = text("""
                SELECT tablename 
                FROM pg_tables 
                WHERE tablename LIKE 'artifact_metadata_p%' 
                AND tablename = :partition_name
            """)
            partition_name = f"artifact_metadata_p{self.account_id}"
            result = await self.db.execute(partition_query, {"partition_name": partition_name})
            partition = result.scalar_one_or_none()

            if partition:
                # Get total size of the partition
                size_query = text("""
                    SELECT pg_total_relation_size(:partition_name)
                """)
                result = await self.db.execute(size_query, {"partition_name": partition})
                return result.scalar_one() or 0
            else:
                # If no partition exists, get size of rows for this account
                size_query = text("""
                    SELECT pg_total_relation_size('artifact_metadata')
                    WHERE account_id = :account_id
                """)
                result = await self.db.execute(size_query, {"account_id": self.account_id})
                return result.scalar_one() or 0
        except Exception as e:
            log_error(f"Error calculating artifacts size: {str(e)}")
            return 0

    async def invalidate_cache(self) -> None:
        """
        Invalidate storage usage cache.
        """
        storage_key = self._get_storage_key()
        await self._cache_manager.delete(storage_key)
