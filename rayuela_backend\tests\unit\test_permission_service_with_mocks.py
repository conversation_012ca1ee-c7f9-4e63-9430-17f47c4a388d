"""
Tests unitarios para el PermissionService usando mocks.
Este archivo usa el enfoque de mocking para probar el servicio aisladamente.
"""
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Set, List

from src.services.permission_service import PermissionService
from src.db.enums import PermissionType, RoleType
from src.db.models import Role, Permission


class TestPermissionService:
    """Tests for the PermissionService class."""

    @pytest_asyncio.fixture
    async def service_setup(self):
        """Setup for permission service tests."""
        # Mock database session
        mock_db = AsyncMock()
        mock_db.begin = AsyncMock().__aenter__.return_value
        
        # Mock repositories
        mock_user_role_repo = AsyncMock()
        mock_role_permission_repo = AsyncMock()
        
        # Create service instance
        service = PermissionService(db=mock_db, account_id=1)
        service._user_role_repo = mock_user_role_repo
        service._role_permission_repo = mock_role_permission_repo
        
        return service, mock_db, mock_user_role_repo, mock_role_permission_repo

    @pytest.mark.asyncio
    async def test_get_user_permissions(self, service_setup):
        """Test get_user_permissions method."""
        # Setup
        service, mock_db, mock_user_role_repo, _ = service_setup
        
        # Create mock roles with permissions
        mock_permission1 = MagicMock(spec=Permission)
        mock_permission1.name = "READ_PRODUCTS"
        
        mock_permission2 = MagicMock(spec=Permission)
        mock_permission2.name = "WRITE_PRODUCTS"
        
        mock_role1 = MagicMock(spec=Role)
        mock_role1.permissions = [mock_permission1]
        
        mock_role2 = MagicMock(spec=Role)
        mock_role2.permissions = [mock_permission2]
        
        # Configure mock repository
        mock_user_role_repo.get_user_roles.return_value = [mock_role1, mock_role2]
        
        # Execute
        result = await service.get_user_permissions(user_id=1)
        
        # Assert
        assert isinstance(result, set)
        assert "READ_PRODUCTS" in result
        assert "WRITE_PRODUCTS" in result
        assert len(result) == 2
        mock_user_role_repo.get_user_roles.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_has_permission_true(self, service_setup):
        """Test has_permission method when user has permission."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_user_permissions
        with patch.object(service, 'get_user_permissions', return_value={"READ_PRODUCTS", "WRITE_PRODUCTS"}):
            # Execute
            result = await service.has_permission(user_id=1, permission_name="READ_PRODUCTS")
            
            # Assert
            assert result
            service.get_user_permissions.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_has_permission_false(self, service_setup):
        """Test has_permission method when user doesn't have permission."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_user_permissions
        with patch.object(service, 'get_user_permissions', return_value={"WRITE_PRODUCTS"}):
            # Execute
            result = await service.has_permission(user_id=1, permission_name="READ_PRODUCTS")
            
            # Assert
            assert not result
            service.get_user_permissions.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_has_role_true(self, service_setup):
        """Test has_role method when user has role."""
        # Setup
        service, mock_db, mock_user_role_repo, _ = service_setup
        
        # Create mock roles
        mock_role = MagicMock(spec=Role)
        mock_role.name = "ADMIN"
        
        # Configure mock repository
        mock_user_role_repo.get_user_roles.return_value = [mock_role]
        
        # Execute
        result = await service.has_role(user_id=1, role_name="ADMIN")
        
        # Assert
        assert result
        mock_user_role_repo.get_user_roles.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_has_role_false(self, service_setup):
        """Test has_role method when user doesn't have role."""
        # Setup
        service, mock_db, mock_user_role_repo, _ = service_setup
        
        # Create mock roles
        mock_role = MagicMock(spec=Role)
        mock_role.name = "USER"
        
        # Configure mock repository
        mock_user_role_repo.get_user_roles.return_value = [mock_role]
        
        # Execute
        result = await service.has_role(user_id=1, role_name="ADMIN")
        
        # Assert
        assert not result
        mock_user_role_repo.get_user_roles.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_check_permissions_all_true(self, service_setup):
        """Test check_permissions method when user has all required permissions."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_user_permissions
        with patch.object(service, 'get_user_permissions', return_value={"READ_PRODUCTS", "WRITE_PRODUCTS"}):
            # Create required permissions
            required_permissions = {
                PermissionType.READ_PRODUCTS,
                PermissionType.WRITE_PRODUCTS
            }
            
            # Execute
            result = await service.check_permissions(user_id=1, required_permissions=required_permissions)
            
            # Assert
            assert result
            service.get_user_permissions.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_check_permissions_some_false(self, service_setup):
        """Test check_permissions method when user doesn't have all required permissions."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_user_permissions
        with patch.object(service, 'get_user_permissions', return_value={"READ_PRODUCTS"}):
            # Create required permissions
            required_permissions = {
                PermissionType.READ_PRODUCTS,
                PermissionType.WRITE_PRODUCTS
            }
            
            # Execute
            result = await service.check_permissions(user_id=1, required_permissions=required_permissions)
            
            # Assert
            assert not result
            service.get_user_permissions.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_get_role_permissions(self, service_setup):
        """Test get_role_permissions method."""
        # Setup
        service, mock_db, _, mock_role_permission_repo = service_setup
        
        # Create mock permissions
        mock_permission1 = MagicMock(spec=Permission)
        mock_permission1.name = "READ_PRODUCTS"
        
        mock_permission2 = MagicMock(spec=Permission)
        mock_permission2.name = "WRITE_PRODUCTS"
        
        # Configure mock repository
        mock_role_permission_repo.get_role_permissions.return_value = [mock_permission1, mock_permission2]
        
        # Execute
        result = await service.get_role_permissions(role_id=1)
        
        # Assert
        assert len(result) == 2
        assert result[0].name == "READ_PRODUCTS"
        assert result[1].name == "WRITE_PRODUCTS"
        mock_role_permission_repo.get_role_permissions.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_role_has_permission(self, service_setup):
        """Test role_has_permission method."""
        # Setup
        service, mock_db, _, mock_role_permission_repo = service_setup
        
        # Configure mock repository
        mock_role_permission_repo.has_permission.return_value = True
        
        # Execute
        result = await service.role_has_permission(role_id=1, permission_name="READ_PRODUCTS")
        
        # Assert
        assert result
        mock_role_permission_repo.has_permission.assert_called_once_with(1, "READ_PRODUCTS")

    @pytest.mark.asyncio
    async def test_assign_permission_to_role(self, service_setup):
        """Test assign_permission_to_role method."""
        # Setup
        service, mock_db, _, mock_role_permission_repo = service_setup
        
        # Execute
        await service.assign_permission_to_role(role_id=1, permission_id=2)
        
        # Assert
        mock_role_permission_repo.assign_permission.assert_called_once_with(1, 2)

    @pytest.mark.asyncio
    async def test_remove_permission_from_role(self, service_setup):
        """Test remove_permission_from_role method."""
        # Setup
        service, mock_db, _, mock_role_permission_repo = service_setup
        
        # Execute
        await service.remove_permission_from_role(role_id=1, permission_id=2)
        
        # Assert
        mock_role_permission_repo.remove_permission.assert_called_once_with(1, 2)

    @pytest.mark.asyncio
    async def test_check_resource_permission_true(self, service_setup):
        """Test check_resource_permission method when user has permission."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_required_permissions_for_resource
        required_permissions = {PermissionType.READ_PRODUCTS}
        with patch('src.services.permission_service.get_required_permissions_for_resource', 
                  return_value=required_permissions):
            
            # Mock get_user_permissions
            with patch.object(service, 'get_user_permissions', return_value={"READ_PRODUCTS"}):
                # Execute
                result = await service.check_resource_permission(
                    user_id=1, 
                    resource_type="product", 
                    action="read"
                )
                
                # Assert
                assert result
                service.get_user_permissions.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_check_resource_permission_false(self, service_setup):
        """Test check_resource_permission method when user doesn't have permission."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_required_permissions_for_resource
        required_permissions = {PermissionType.WRITE_PRODUCTS}
        with patch('src.services.permission_service.get_required_permissions_for_resource', 
                  return_value=required_permissions):
            
            # Mock get_user_permissions
            with patch.object(service, 'get_user_permissions', return_value={"READ_PRODUCTS"}):
                # Execute
                result = await service.check_resource_permission(
                    user_id=1, 
                    resource_type="product", 
                    action="write"
                )
                
                # Assert
                assert not result
                service.get_user_permissions.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_check_resource_permission_no_mapping(self, service_setup):
        """Test check_resource_permission method when no mapping exists."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_required_permissions_for_resource
        with patch('src.services.permission_service.get_required_permissions_for_resource', 
                  return_value=None):
            
            # Execute
            result = await service.check_resource_permission(
                user_id=1, 
                resource_type="unknown", 
                action="read"
            )
            
            # Assert
            assert not result

    @pytest.mark.asyncio
    async def test_get_user_roles(self, service_setup):
        """Test get_user_roles method."""
        # Setup
        service, mock_db, mock_user_role_repo, _ = service_setup
        
        # Create mock roles
        mock_role1 = MagicMock(spec=Role)
        mock_role1.name = "ADMIN"
        
        mock_role2 = MagicMock(spec=Role)
        mock_role2.name = "USER"
        
        # Configure mock repository
        mock_user_role_repo.get_user_roles.return_value = [mock_role1, mock_role2]
        
        # Execute
        result = await service.get_user_roles(user_id=1)
        
        # Assert
        assert isinstance(result, list)
        assert "ADMIN" in result
        assert "USER" in result
        assert len(result) == 2
        mock_user_role_repo.get_user_roles.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_assign_default_permissions_to_role(self, service_setup):
        """Test assign_default_permissions_to_role method."""
        # Setup
        service, mock_db, _, _ = service_setup
        
        # Mock get_permissions_for_role
        default_permissions = {PermissionType.READ_PRODUCTS, PermissionType.READ_USERS}
        with patch('src.core.permissions.get_permissions_for_role', return_value=default_permissions):
            
            # Mock get_role_permissions
            mock_permission = MagicMock(spec=Permission)
            mock_permission.name = "READ_PRODUCTS"
            
            with patch.object(service, 'get_role_permissions', return_value=[mock_permission]):
                # Execute
                await service.assign_default_permissions_to_role(role_id=1, role_type=RoleType.ADMIN)
                
                # Assert
                service.get_role_permissions.assert_called_once_with(1)
