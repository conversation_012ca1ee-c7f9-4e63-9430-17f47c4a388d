#!/usr/bin/env python3
"""
Script de prueba para verificar que el flujo de registro funciona correctamente
y que el account_name se está guardando en la base de datos.
"""

import asyncio
import json
from datetime import datetime, timezone


# Simulación del flujo de registro
def test_register_payload():
    """Prueba que el payload de registro incluye todos los campos necesarios."""

    # Datos que envía el frontend
    frontend_data = {
        "accountName": "Mi Empresa Test",
        "email": "<EMAIL>",
        "password": "SecurePass123",
    }

    # Payload que debería enviarse al backend
    backend_payload = {
        "account_name": frontend_data["accountName"],
        "email": frontend_data["email"],
        "password": frontend_data["password"],
    }

    print("✅ Datos del frontend:")
    print(json.dumps(frontend_data, indent=2))

    print("\n✅ Payload enviado al backend:")
    print(json.dumps(backend_payload, indent=2))

    # Verificar que todos los campos necesarios están presentes
    required_fields = ["account_name", "email", "password"]
    missing_fields = [
        field for field in required_fields if field not in backend_payload
    ]

    if missing_fields:
        print(f"\n❌ Campos faltantes: {missing_fields}")
        return False
    else:
        print(f"\n✅ Todos los campos requeridos están presentes: {required_fields}")
        return True


def test_account_creation():
    """Simula la creación de una cuenta con el account_name."""

    # Datos que recibiría el servicio AuthService.register_account
    account_name = "Mi Empresa Test"
    email = "<EMAIL>"
    password = "SecurePass123"

    # Simulación de AccountCreate
    account_data = {
        "name": account_name,  # ← Este es el campo clave que se estaba perdiendo
        "api_key_hash": "",
        "api_key_prefix": "",
        "api_key_last_chars": "",
        "api_key_created_at": datetime.now(timezone.utc).isoformat(),
        "api_key_revealed": False,
        "is_active": True,
    }

    print("✅ Datos para crear la cuenta (AccountCreate):")
    print(json.dumps(account_data, indent=2, default=str))

    # Verificar que el nombre de la cuenta se está usando
    if account_data["name"] == account_name:
        print(
            f"\n✅ El nombre de la cuenta '{account_name}' se está guardando correctamente"
        )
        return True
    else:
        print(
            f"\n❌ El nombre de la cuenta no coincide. Esperado: '{account_name}', Actual: '{account_data['name']}'"
        )
        return False


def main():
    """Ejecuta todas las pruebas."""
    print("🧪 Probando el flujo de registro corregido...\n")

    print("=" * 60)
    print("PRUEBA 1: Verificar payload del frontend al backend")
    print("=" * 60)
    test1_passed = test_register_payload()

    print("\n" + "=" * 60)
    print("PRUEBA 2: Verificar creación de cuenta con account_name")
    print("=" * 60)
    test2_passed = test_account_creation()

    print("\n" + "=" * 60)
    print("RESUMEN")
    print("=" * 60)

    if test1_passed and test2_passed:
        print("✅ TODAS LAS PRUEBAS PASARON")
        print("✅ El flujo de registro está funcionando correctamente")
        print("✅ El account_name se está enviando y guardando correctamente")
    else:
        print("❌ ALGUNAS PRUEBAS FALLARON")
        if not test1_passed:
            print("❌ Problema con el payload del frontend")
        if not test2_passed:
            print("❌ Problema con la creación de la cuenta")


if __name__ == "__main__":
    main()
