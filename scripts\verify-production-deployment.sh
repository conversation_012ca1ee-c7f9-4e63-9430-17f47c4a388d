#!/bin/bash

# Script para verificar deployment de producción
# Ejecuta verificaciones comprehensivas post-deployment

set -e

echo "🔍 VERIFICACIÓN DE DEPLOYMENT DE PRODUCCIÓN"
echo "==========================================="

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Función para logging
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Variables
PROJECT_ID=$(gcloud config get-value project)
REGION="us-central1"
BACKEND_SERVICE="rayuela-backend"
FRONTEND_SERVICE="rayuela-frontend"

# Verificar configuración
if [ -z "$PROJECT_ID" ]; then
    log_error "No hay un proyecto configurado. Ejecuta 'gcloud config set project YOUR_PROJECT_ID'"
    exit 1
fi

log_info "Verificando deployment en proyecto: $PROJECT_ID"
log_info "Región: $REGION"

# Función para verificar servicio de Cloud Run
verify_cloud_run_service() {
    local service_name=$1
    local service_type=$2
    
    log_info "Verificando servicio $service_type: $service_name"
    
    # Verificar que el servicio existe
    if ! gcloud run services describe "$service_name" --region="$REGION" >/dev/null 2>&1; then
        log_error "Servicio $service_name no encontrado"
        return 1
    fi
    
    # Obtener URL del servicio
    local service_url=$(gcloud run services describe "$service_name" --region="$REGION" --format="value(status.url)")
    log_info "URL del servicio: $service_url"
    
    # Verificar que el servicio responde
    log_info "Verificando conectividad..."
    if curl -f -s "$service_url/health" >/dev/null 2>&1; then
        log_success "✅ $service_type responde correctamente"
    else
        log_warning "⚠️ $service_type no responde en /health"
        
        # Intentar endpoint raíz
        if curl -f -s "$service_url" >/dev/null 2>&1; then
            log_success "✅ $service_type responde en endpoint raíz"
        else
            log_error "❌ $service_type no responde"
            return 1
        fi
    fi
    
    # Verificar configuración del servicio
    local cpu=$(gcloud run services describe "$service_name" --region="$REGION" --format="value(spec.template.spec.containers[0].resources.limits.cpu)")
    local memory=$(gcloud run services describe "$service_name" --region="$REGION" --format="value(spec.template.spec.containers[0].resources.limits.memory)")
    local min_instances=$(gcloud run services describe "$service_name" --region="$REGION" --format="value(spec.template.metadata.annotations.'autoscaling.knative.dev/minScale')")
    local max_instances=$(gcloud run services describe "$service_name" --region="$REGION" --format="value(spec.template.metadata.annotations.'autoscaling.knative.dev/maxScale')")
    
    log_info "Configuración del servicio:"
    log_info "  CPU: $cpu"
    log_info "  Memoria: $memory"
    log_info "  Instancias mín: ${min_instances:-0}"
    log_info "  Instancias máx: ${max_instances:-100}"
    
    return 0
}

# Función para verificar logs recientes
verify_logs() {
    local service_name=$1
    local service_type=$2
    
    log_info "Verificando logs recientes de $service_type..."
    
    # Obtener logs de los últimos 10 minutos
    local recent_logs=$(gcloud run services logs read "$service_name" --region="$REGION" --limit=10 --format="value(textPayload)" 2>/dev/null || echo "")
    
    if [ -n "$recent_logs" ]; then
        log_success "✅ Logs disponibles para $service_type"
        
        # Buscar errores en logs
        if echo "$recent_logs" | grep -i "error\|exception\|failed" >/dev/null; then
            log_warning "⚠️ Se encontraron posibles errores en logs de $service_type"
        else
            log_success "✅ No se encontraron errores evidentes en logs de $service_type"
        fi
    else
        log_warning "⚠️ No hay logs recientes disponibles para $service_type"
    fi
}

# Función para verificar base de datos
verify_database() {
    log_info "Verificando conectividad de base de datos..."
    
    # Intentar obtener información de Cloud SQL
    local sql_instances=$(gcloud sql instances list --format="value(name)" 2>/dev/null || echo "")
    
    if [ -n "$sql_instances" ]; then
        log_success "✅ Instancias de Cloud SQL encontradas:"
        echo "$sql_instances" | while read instance; do
            if [ -n "$instance" ]; then
                local status=$(gcloud sql instances describe "$instance" --format="value(state)" 2>/dev/null || echo "unknown")
                log_info "  - $instance: $status"
            fi
        done
    else
        log_warning "⚠️ No se encontraron instancias de Cloud SQL o no hay permisos"
    fi
}

# Función para verificar secretos
verify_secrets() {
    log_info "Verificando secretos de producción..."
    
    local required_secrets=(
        "POSTGRES_USER" "POSTGRES_PASSWORD" "POSTGRES_SERVER" "POSTGRES_PORT" "POSTGRES_DB"
        "REDIS_HOST" "REDIS_PORT" "REDIS_URL" "REDIS_PASSWORD"
        "SECRET_KEY" "MERCADOPAGO_ACCESS_TOKEN" "GCS_BUCKET_NAME"
    )
    
    local secrets_ok=true
    
    for secret in "${required_secrets[@]}"; do
        if gcloud secrets describe "$secret" >/dev/null 2>&1; then
            log_success "✅ Secreto $secret configurado"
        else
            log_error "❌ Secreto $secret faltante"
            secrets_ok=false
        fi
    done
    
    if [ "$secrets_ok" = true ]; then
        log_success "✅ Todos los secretos están configurados"
    else
        log_error "❌ Faltan secretos por configurar"
        return 1
    fi
}

# Función para verificar APIs habilitadas
verify_apis() {
    log_info "Verificando APIs habilitadas..."
    
    local required_apis=(
        "run.googleapis.com"
        "cloudbuild.googleapis.com"
        "secretmanager.googleapis.com"
        "sqladmin.googleapis.com"
    )
    
    for api in "${required_apis[@]}"; do
        if gcloud services list --enabled --filter="name:$api" --format="value(name)" | grep -q "$api"; then
            log_success "✅ API $api habilitada"
        else
            log_warning "⚠️ API $api no habilitada"
        fi
    done
}

# Función para verificar IAM
verify_iam() {
    log_info "Verificando configuración de IAM..."
    
    local cloudbuild_sa="$(gcloud projects describe $PROJECT_ID --format='value(projectNumber)')@cloudbuild.gserviceaccount.com"
    
    log_info "Cloud Build Service Account: $cloudbuild_sa"
    
    # Verificar roles críticos
    local roles=(
        "roles/secretmanager.secretAccessor"
        "roles/cloudsql.client"
        "roles/run.admin"
    )
    
    for role in "${roles[@]}"; do
        if gcloud projects get-iam-policy $PROJECT_ID --flatten="bindings[].members" --format="table(bindings.role)" --filter="bindings.members:$cloudbuild_sa AND bindings.role:$role" | grep -q "$role"; then
            log_success "✅ Rol $role asignado a Cloud Build"
        else
            log_warning "⚠️ Rol $role no asignado a Cloud Build"
        fi
    done
}

# Función principal de verificación
main() {
    log_info "Iniciando verificación comprehensiva..."
    echo ""
    
    # Verificar APIs
    verify_apis
    echo ""
    
    # Verificar IAM
    verify_iam
    echo ""
    
    # Verificar secretos
    verify_secrets
    echo ""
    
    # Verificar base de datos
    verify_database
    echo ""
    
    # Verificar servicios de Cloud Run
    verify_cloud_run_service "$BACKEND_SERVICE" "Backend"
    echo ""
    
    verify_cloud_run_service "$FRONTEND_SERVICE" "Frontend"
    echo ""
    
    # Verificar logs
    verify_logs "$BACKEND_SERVICE" "Backend"
    echo ""
    
    verify_logs "$FRONTEND_SERVICE" "Frontend"
    echo ""
    
    # Resumen final
    log_info "=== RESUMEN DE VERIFICACIÓN ==="
    
    # Obtener URLs de servicios
    local backend_url=$(gcloud run services describe "$BACKEND_SERVICE" --region="$REGION" --format="value(status.url)" 2>/dev/null || echo "No disponible")
    local frontend_url=$(gcloud run services describe "$FRONTEND_SERVICE" --region="$REGION" --format="value(status.url)" 2>/dev/null || echo "No disponible")
    
    echo ""
    log_success "🌐 URLs de Servicios:"
    log_info "  Backend:  $backend_url"
    log_info "  Frontend: $frontend_url"
    
    echo ""
    log_success "🔗 Enlaces Útiles:"
    log_info "  Cloud Console: https://console.cloud.google.com/run?project=$PROJECT_ID"
    log_info "  Cloud Build: https://console.cloud.google.com/cloud-build/builds?project=$PROJECT_ID"
    log_info "  Secret Manager: https://console.cloud.google.com/security/secret-manager?project=$PROJECT_ID"
    
    echo ""
    log_success "✅ Verificación de deployment completada"
}

# Ejecutar verificación principal
main
