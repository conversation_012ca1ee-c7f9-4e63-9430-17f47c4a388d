from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from src.utils.base_logger import log_info, log_error, log_warning
from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id


class TenantMiddleware(BaseHTTPMiddleware):
    """
    Middleware para establecer el account_id en cada solicitud usando contextvars.
    Este es el ÚNICO punto que debe establecer y limpiar el contextvar.
    Esto permite:
    1. Que las políticas RLS de PostgreSQL funcionen correctamente.
    2. Que los repositorios puedan acceder al account_id actual.
    3. Que las tareas asíncronas puedan propagar el account_id.
    4. Aislamiento completo entre solicitudes concurrentes.
    """

    async def dispatch(self, request: Request, call_next):
        try:
            # Lista de rutas que no requieren middleware de tenant
            excluded_paths = [
                "/health",
                "/docs", 
                "/api/docs",
                "/api/openapi.json",
                "/api/v1/health",
                "/api/v1/auth/"
            ]
            
            # Verificar si la ruta está excluida
            path = request.url.path
            is_excluded = any(path.startswith(excluded_path) for excluded_path in excluded_paths)
            
            if is_excluded:
                # Saltarse el middleware para endpoints excluidos
                response = await call_next(request)
                return response

            # Para rutas que requieren tenant, simplemente limpiar el contexto
            # El tenant será establecido por las dependencias de FastAPI cuando sea necesario
            set_current_tenant_id(None)

            # Continuar con la solicitud
            response = await call_next(request)

            return response

        except Exception as e:
            log_error(f"Error en middleware de tenant: {str(e)}")
            # En caso de error, asegurarse de limpiar el contexto
            set_current_tenant_id(None)
            raise
        finally:
            # Limpiar el contexto al finalizar la solicitud
            set_current_tenant_id(None)
