# 📖 Guía de Deployment - Rayuela

## ✅ **Estado Actual: COMPLETAMENTE DESPLEGADO**

**🎉 La aplicación está 100% operativa en producción!**

- **Frontend:** https://rayuela-frontend-lrw7xazcbq-uc.a.run.app ✅
- **Backend:** https://rayuela-backend-lrw7xazcbq-uc.a.run.app ✅
- **API Service:** https://rayuela-api-lrw7xazcbq-uc.a.run.app ✅

---

## 🎯 **Opciones de Deployment**

### **🚀 Deployment Automático (Recomendado)**
```bash
# Simplemente push a main - el CI/CD se encarga del resto
git add .
git commit -m "feat: nueva funcionalidad"
git push origin main

# Monitorear el progreso
gcloud builds list --ongoing
```

### **⚡ Deployment Manual Rápido**
```bash
# Usando el script optimizado
./scripts/deploy-production.sh --direct

# O usando cloudbuild directamente
gcloud builds submit --config cloudbuild-deploy-production.yaml
```

### **🧪 Deployment Completo con Tests**
```bash
# Pipeline completo con verificaciones de calidad
gcloud builds submit --config cloudbuild.yaml
```

---

## 🏗️ **Arquitectura de Deployment**

### **🔄 Pipeline CI/CD**
```
📝 Git Push → 🏗️ Cloud Build → 🐳 Docker Build → 📦 Artifact Registry → 🚀 Cloud Run
```

**Componentes:**
- **Cloud Build:** Pipeline de CI/CD automatizado
- **Artifact Registry:** Registry privado de imágenes Docker
- **Cloud Run:** Hosting serverless de containers
- **Secret Manager:** Gestión segura de credenciales

### **🌐 Servicios Desplegados**

| Servicio | Imagen | Recursos | Auto-scaling |
|----------|--------|----------|--------------|
| **Frontend** | `rayuela-frontend:latest` | 1GB RAM, 1 CPU | 0-5 instancias |
| **Backend** | `rayuela-backend:latest` | 4GB RAM, 2 CPU | 0-10 instancias |
| **API** | `rayuela-api:latest` | 2GB RAM, 1 CPU | 0-5 instancias |

---

## 🔧 **Requisitos Previos**

### **✅ GCP Setup (Ya completado)**
- [x] Proyecto GCP: `lateral-insight-461112-g9`
- [x] Billing habilitado
- [x] APIs habilitadas (Cloud Run, Cloud Build, etc.)
- [x] Región: `us-central1`

### **✅ Infraestructura (Ya desplegada)**
- [x] Cloud SQL PostgreSQL: `*************`
- [x] Redis Memorystore: `*************`
- [x] Cloud Storage: `gs://lateral-insight-461112-g9-rayuela-storage`
- [x] Artifact Registry: `rayuela-repo`

### **✅ Secretos (Ya configurados)**
- [x] `DB_PASSWORD`
- [x] `REDIS_PASSWORD`
- [x] `SECRET_KEY`
- [x] `APP_DB_PASSWORD`
- [x] `MAINTENANCE_DB_PASSWORD`

---

## 📂 **Estructura de Archivos de Deployment**

```
rayuela/
├── 🔧 cloudbuild.yaml                    # Pipeline principal (tests + deploy)
├── 🚀 cloudbuild-deploy-production.yaml  # Pipeline rápido (solo deploy)
├── 
├── 📁 config/
│   └── config-production.env.example     # Template de variables de entorno
├── 
├── 🛠️ scripts/
│   ├── deploy-production.sh              # Script de deployment manual
│   ├── verify-deployment.sh              # Verificación post-deployment
│   ├── setup-monitoring.sh               # Configuración de monitoreo
│   └── setup-custom-domain.sh            # Setup de dominio personalizado
└── 
└── 📖 docs/
    ├── DEPLOYMENT_GUIDE.md               # Esta guía
    ├── ci_cd_pipeline.md                 # Documentación del pipeline
    └── CLOUD_RUN_DEPLOYMENT.md           # Detalles específicos de Cloud Run
```

---

## 🚀 **Proceso de Deployment Detallado**

### **Fase 1: Preparación (automática)**
1. **Código fuente** → Git push a `main`
2. **Trigger** → Cloud Build inicia automáticamente
3. **Environment setup** → Configuración de variables

### **Fase 2: Testing y Quality (5m)**
```bash
# Tests de calidad (non-blocking)
- black --check src/         # Formateo de código
- isort --check src/         # Ordenamiento de imports  
- flake8 src/               # Linting
- bandit -r src/            # Security scan
- pytest tests/             # Unit tests
```

### **Fase 3: Build (8m)**
```bash
# Build imágenes Docker en paralelo
docker build rayuela_backend/    # Backend API
docker build rayuela_frontend/   # Frontend Next.js
```

### **Fase 4: Deploy (4m)**
```bash
# Deploy a Cloud Run
gcloud run deploy rayuela-backend   # API
gcloud run deploy rayuela-frontend  # Frontend
```

### **Fase 5: Verificación (1m)**
```bash
# Health checks automáticos
curl https://rayuela-backend-*/health    # Backend health
curl https://rayuela-frontend-*/         # Frontend status
```

---

## 🔍 **Monitoreo del Deployment**

### **📊 Dashboards Disponibles**
- **Cloud Build:** https://console.cloud.google.com/cloud-build
- **Cloud Run:** https://console.cloud.google.com/run
- **Monitoring:** https://console.cloud.google.com/monitoring

### **📝 Logs y Debugging**
```bash
# Ver builds recientes
gcloud builds list --limit=10

# Ver logs de un build específico
gcloud builds log BUILD_ID

# Ver logs de servicios
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=rayuela-backend" --limit=10

# Verificación completa automatizada
./scripts/verify-deployment.sh
```

---

## 🛠️ **Troubleshooting**

### **❌ Build Failures**

#### **1. Tests Failing**
```bash
# Los tests son non-blocking, pero revisar para mejorar calidad
cd rayuela_backend
python -m pytest tests/ -v

# Arreglar formateo
black src/ tests/
isort src/ tests/
```

#### **2. Docker Build Issues**
```bash
# Test build localmente
docker build -t rayuela-backend-test ./rayuela_backend/
docker build -t rayuela-frontend-test ./rayuela_frontend/
```

#### **3. Deploy Failures**
```bash
# Ver detalles del deployment
gcloud run services describe rayuela-backend --region=us-central1

# Ver logs de deployment
gcloud logging read "resource.type=cloud_run_revision" --limit=20
```

### **⚠️ Service Issues**

#### **1. Health Check Failures**
```bash
# Test manual del health endpoint
curl -f https://rayuela-backend-lrw7xazcbq-uc.a.run.app/health

# Revisar logs de la aplicación
gcloud run services logs read rayuela-backend --region=us-central1
```

#### **2. Memory/CPU Issues**
```bash
# Ajustar recursos en cloudbuild.yaml
--memory=4Gi    # Aumentar memoria
--cpu=2         # Aumentar CPU cores
```

#### **3. Database Connection Issues**
```bash
# Verificar conectividad a Cloud SQL
gcloud sql instances describe rayuela-postgres

# Verificar secretos
gcloud secrets versions access latest --secret="DB_PASSWORD"
```

---

## 🔄 **Rollback Procedures**

### **🔙 Rollback Rápido**
```bash
# Usar imagen anterior (tags disponibles)
gcloud run deploy rayuela-backend \
  --image=us-central1-docker.pkg.dev/lateral-insight-461112-g9/rayuela-repo/rayuela-backend:PREVIOUS_TAG \
  --region=us-central1

gcloud run deploy rayuela-frontend \
  --image=us-central1-docker.pkg.dev/lateral-insight-461112-g9/rayuela-repo/rayuela-frontend:PREVIOUS_TAG \
  --region=us-central1
```

### **⏮️ Rollback Completo**
```bash
# Re-deploy último build conocido funcionando
gcloud builds submit --config cloudbuild.yaml --substitutions=BUILD_ID=KNOWN_GOOD_BUILD
```

---

## 🎯 **Configuraciones Avanzadas**

### **🌍 Custom Domain Setup**
```bash
# Configurar dominio personalizado
./scripts/setup-custom-domain.sh yourdomain.com
```

### **📊 Monitoring Setup**
```bash
# Configurar alertas y dashboards
./scripts/setup-monitoring.sh
```

### **🔐 Security Hardening**
```bash
# Configurar WAF y security policies
gcloud run services update rayuela-backend \
  --ingress=all \
  --region=us-central1
```

---

## 🚀 **Best Practices**

### **📝 Development Workflow**
1. **Feature branch** → Desarrollar en branch separado
2. **Local testing** → Tests locales antes de push
3. **Pull request** → Code review antes de merge
4. **Deployment** → Auto-deploy al merge a main

### **🔄 CI/CD Optimization**
- ✅ **Parallel builds** habilitado
- ✅ **Docker layer caching** configurado
- ✅ **Non-blocking tests** implementado
- ✅ **Health checks** automatizados

### **📊 Monitoring Best Practices**
- ✅ **Structured logging** implementado
- ✅ **Health endpoints** disponibles
- ✅ **Error tracking** configurado
- ✅ **Performance metrics** monitoreados

---

## 📞 **Soporte y Referencias**

### **🆘 En Caso de Problemas**
1. **Ejecutar verificación:** `./scripts/verify-deployment.sh`
2. **Revisar logs:** Ver comandos de troubleshooting arriba
3. **Consultar documentación:** `docs/` directory
4. **Rollback si necesario:** Usar procedimientos arriba

### **📚 Enlaces Útiles**
- **GCP Console:** https://console.cloud.google.com
- **Cloud Build:** https://console.cloud.google.com/cloud-build
- **Cloud Run:** https://console.cloud.google.com/run
- **Monitoring:** https://console.cloud.google.com/monitoring

### **📖 Documentación Relacionada**
- [🔄 CI/CD Pipeline](ci_cd_pipeline.md) - Detalles del pipeline
- [🏗️ Arquitectura](ARCHITECTURE.md) - Arquitectura del sistema
- [🌐 Cloud Run Deployment](CLOUD_RUN_DEPLOYMENT.md) - Detalles de Cloud Run
- [🔒 Security Guide](MULTI_TENANCY_SECURITY_GUIDE.md) - Guía de seguridad

---

## 🎉 **Resumen**

**✅ DEPLOYMENT COMPLETAMENTE EXITOSO**

La aplicación Rayuela está desplegada y funcionando al 100% con:
- Pipeline CI/CD automatizado y optimizado
- Infraestructura cloud escalable y robusta  
- Monitoreo y logging completo
- Procedimientos de rollback documentados
- Seguridad multi-tenant implementada

**🚀 Ready for production use!**

---

*Última actualización: 30 de Mayo, 2025*  
*Versión: 2.0 (Estable en producción)* 