"""remove_redundant_indices

Revision ID: remove_redundant_indices
Revises: afdd0755dc91
Create Date: 2023-06-18 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "bfdd0755dc92"  # ID corto en lugar del nombre descriptivo largo
down_revision: Union[str, None] = "afdd0755dc91"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Remove redundant indices and constraints."""
    # Drop the redundant index on accounts table
    # The UniqueConstraint already creates an index
    op.drop_index("idx_account_api_key_hash", table_name="accounts")

    # Drop the incorrect unique constraint on system_users table
    # It includes id which is already part of the primary key
    op.drop_constraint("uq_system_user_id_email", "system_users", type_="unique")


def downgrade() -> None:
    """Add back the redundant indices and constraints."""
    # Add back the redundant index on accounts table
    op.create_index(
        "idx_account_api_key_hash",
        "accounts",
        ["account_id", "api_key_hash"],
        unique=False
    )

    # Add back the incorrect unique constraint on system_users table
    op.create_unique_constraint(
        "uq_system_user_id_email",
        "system_users",
        ["account_id", "id", "email"]
    )
