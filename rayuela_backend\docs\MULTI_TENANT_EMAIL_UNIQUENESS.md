# Unicidad de Email en Sistema Multi-tenant

Este documento explica la implementación correcta de la unicidad de email para usuarios del sistema en un entorno multi-tenant.

## Problema Identificado

En la definición original del modelo `SystemUser`, la columna `email` estaba definida con el argumento `unique=True`:

```python
email = Column(String(255), nullable=False, unique=True)
```

En un sistema multi-tenant, esto implica que una dirección de email solo puede existir *una vez en toda la base de datos*, impidiendo que usuarios de *diferentes cuentas* (empresas cliente) puedan registrarse con el mismo email (ej. `admin@company_a.com` y `admin@company_b.com`).

Esta restricción global es incorrecta para un sistema B2B multi-tenant, donde la unicidad del email debería ser *por cuenta* (tenant), no global.

## Solución Implementada

Se realizaron los siguientes cambios para corregir este problema:

1. **Eliminación del argumento `unique=True` en la definición del modelo**:
   ```python
   # Antes
   email = Column(String(255), nullable=False, unique=True)
   
   # Después
   email = Column(String(255), nullable=False)
   ```

2. **Verificación de la eliminación de la constraint UNIQUE global**:
   - La migración `remove_redundant_indices.py` ya había eliminado la constraint incorrecta `uq_system_user_id_email` de la base de datos.
   - Esta migración asegura que no exista una restricción de unicidad global en la columna `email`.

3. **Confirmación del índice compuesto correcto**:
   - El índice compuesto `idx_system_user_email` en `(account_id, email)` está correctamente definido en el modelo `SystemUser`.
   - Este índice asegura la unicidad del email *dentro* de cada cuenta, que es el comportamiento deseado.

## Validación de Unicidad en el Código

Además de la estructura de la base de datos, el código de la aplicación también valida la unicidad del email dentro de cada cuenta:

1. **En el repositorio `SystemUserRepository`**:
   ```python
   async def create(self, user_create: schemas.SystemUserCreate) -> models.SystemUser:
       # Verificar si el email ya existe en esta cuenta
       query = select(models.SystemUser).filter(
           models.SystemUser.email == user_create.email,
           models.SystemUser.account_id == self.account_id,
       )
       result = await self.db.execute(query)
       existing_user = result.scalars().first()
       if existing_user:
           raise HTTPException(status_code=400, detail="Email already registered")
       # ...
   ```

2. **En el endpoint de creación de usuarios**:
   ```python
   @router.post("/", response_model=schemas.SystemUser)
   async def create_system_user(
       user: schemas.SystemUserCreate,
       db: AsyncSession = Depends(get_db),
       current_account: models.Account = Depends(get_current_account),
   ):
       # Verificar si el email ya existe en esta cuenta
       existing_user = await db.execute(
           select(models.SystemUser).where(
               models.SystemUser.email == user.email,
               models.SystemUser.account_id == current_account.account_id,
           )
       )
       if existing_user.scalar_one_or_none():
           raise DuplicateEntryError("Email already registered")
       # ...
   ```

## Beneficios del Cambio

1. **Soporte adecuado para multi-tenancy**:
   - Diferentes cuentas pueden tener usuarios con el mismo email.
   - Cada cuenta mantiene la unicidad de email dentro de su propio dominio.

2. **Mejor experiencia para clientes B2B**:
   - Las empresas cliente pueden usar sus propias convenciones de email.
   - No hay conflictos entre diferentes organizaciones.

3. **Consistencia con el diseño de la base de datos**:
   - Alineado con el enfoque multi-tenant del resto del sistema.
   - Coherente con otros modelos que mantienen unicidad por cuenta.

## Consideraciones Adicionales

- **Migración de Datos**: No se requiere migración de datos, ya que la constraint incorrecta ya había sido eliminada.
- **Impacto en Aplicaciones Existentes**: No hay impacto en aplicaciones existentes, ya que el comportamiento esperado (unicidad por cuenta) ya estaba implementado a nivel de código.
- **Seguridad**: La validación a nivel de código proporciona mensajes de error más claros y específicos que las violaciones de constraint a nivel de base de datos.

## Conclusión

Este cambio asegura que el sistema maneje correctamente la unicidad de email en un entorno multi-tenant, permitiendo que diferentes cuentas tengan usuarios con el mismo email mientras se mantiene la unicidad dentro de cada cuenta individual.
