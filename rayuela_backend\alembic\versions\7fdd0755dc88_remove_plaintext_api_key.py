"""Remove plaintext API key

Revision ID: 7fdd0755dc88
Revises: 6fdd0755dc87
Create Date: 2025-04-10 15:39:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = "7fdd0755dc88"
down_revision: Union[str, None] = "6fdd0755dc87"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to remove plaintext API key."""
    # Eliminar índices y restricciones relacionados con la columna api_key
    op.drop_constraint("uq_account_api_key", "accounts", type_="unique")
    op.drop_index("idx_account_api_key", table_name="accounts")

    # Eliminar la columna api_key
    op.drop_column("accounts", "api_key")


def downgrade() -> None:
    """Downgrade schema to add back plaintext API key."""
    # Añadir la columna api_key
    op.add_column("accounts", sa.Column("api_key", sa.String(), nullable=True))

    # Recrear índices y restricciones
    op.create_index("idx_account_api_key", "accounts", ["account_id", "api_key"])
    op.create_unique_constraint(
        "uq_account_api_key", "accounts", ["account_id", "api_key"]
    )
