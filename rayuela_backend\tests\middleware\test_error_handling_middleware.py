import pytest
import os
from unittest.mock import patch, MagicMock
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.testclient import <PERSON><PERSON>lient
from sqlalchemy.exc import SQLAlchemyError
from src.middleware.error_handling import ErrorHandlingMiddleware
from src.core.exceptions import <PERSON><PERSON>r<PERSON><PERSON>, CustomHTTPException


class TestErrorHandlingMiddleware:
    """Tests for ErrorHandlingMiddleware with environment-based error responses."""

    @pytest.fixture
    def app(self):
        """Create a test FastAPI app with ErrorHandlingMiddleware."""
        app = FastAPI()
        app.add_middleware(ErrorHandlingMiddleware)
        
        @app.get("/test-custom-error")
        async def test_custom_error():
            raise CustomHTTPException(
                status_code=400,
                detail={"message": "Custom error", "error_code": ErrorCode.VALIDATION_ERROR}
            )
        
        @app.get("/test-sqlalchemy-error")
        async def test_sqlalchemy_error():
            raise SQLAlchemyError("Database connection failed")
        
        @app.get("/test-generic-error")
        async def test_generic_error():
            raise ValueError("Something went wrong")
        
        return app

    @pytest.fixture
    def client(self, app):
        """Create a test client."""
        return TestClient(app)

    def test_custom_http_exception_passthrough(self, client):
        """Test that CustomHTTPException passes through unchanged."""
        response = client.get("/test-custom-error")
        
        assert response.status_code == 400
        data = response.json()
        assert data["message"] == "Custom error"
        assert data["error_code"] == ErrorCode.VALIDATION_ERROR
        assert data["path"] == "/test-custom-error"

    @patch.dict(os.environ, {"ENV": "development"})
    def test_sqlalchemy_error_development(self, client):
        """Test SQLAlchemy error handling in development environment."""
        response = client.get("/test-sqlalchemy-error")
        
        assert response.status_code == 500
        data = response.json()
        assert data["message"] == "Database error occurred"
        assert data["error_code"] == ErrorCode.DATABASE_ERROR
        assert data["path"] == "/test-sqlalchemy-error"

    @patch.dict(os.environ, {"ENV": "production"})
    def test_sqlalchemy_error_production(self, client):
        """Test SQLAlchemy error handling in production environment."""
        response = client.get("/test-sqlalchemy-error")
        
        assert response.status_code == 500
        data = response.json()
        assert data["message"] == "Internal server error"
        assert data["error_code"] == ErrorCode.DATABASE_ERROR
        assert data["path"] == "/test-sqlalchemy-error"

    @patch.dict(os.environ, {"ENV": "development"})
    def test_generic_error_development(self, client):
        """Test generic error handling in development environment."""
        response = client.get("/test-generic-error")
        
        assert response.status_code == 500
        data = response.json()
        assert "Something went wrong" in data["message"]
        assert data["error_code"] == ErrorCode.INTERNAL_ERROR
        assert data["path"] == "/test-generic-error"

    @patch.dict(os.environ, {"ENV": "production"})
    def test_generic_error_production(self, client):
        """Test generic error handling in production environment."""
        response = client.get("/test-generic-error")
        
        assert response.status_code == 500
        data = response.json()
        assert data["message"] == "Internal server error"
        assert data["error_code"] == ErrorCode.INTERNAL_ERROR
        assert data["path"] == "/test-generic-error"

    @patch("src.middleware.error_handling.logger")
    def test_error_logging(self, mock_logger, client):
        """Test that errors are logged with full details internally."""
        response = client.get("/test-sqlalchemy-error")
        
        # Verify that logger.error was called with detailed information
        mock_logger.error.assert_called()
        call_args = mock_logger.error.call_args[0][0]
        assert "Database error:" in call_args
        assert "Stack trace:" in call_args

    @patch("src.middleware.error_handling.logger")
    def test_request_logging(self, mock_logger, client):
        """Test that requests are logged."""
        response = client.get("/test-custom-error")
        
        # Verify that logger.info was called for request processing
        mock_logger.info.assert_called_with("Processing request: GET /test-custom-error")

    @patch.dict(os.environ, {"ENV": "production"})
    @patch("src.middleware.error_handling.logger")
    def test_production_error_sanitization(self, mock_logger, client):
        """Test that production errors are sanitized but still logged internally."""
        response = client.get("/test-generic-error")
        
        # Response should be sanitized
        data = response.json()
        assert data["message"] == "Internal server error"
        assert "Something went wrong" not in data["message"]
        
        # But error should still be logged with full details
        mock_logger.error.assert_called()
        call_args = mock_logger.error.call_args[0][0]
        assert "Something went wrong" in call_args

    def test_error_response_format(self, client):
        """Test that error responses have consistent format."""
        response = client.get("/test-custom-error")
        
        data = response.json()
        required_fields = ["message", "error_code", "path"]
        for field in required_fields:
            assert field in data, f"Missing required field: {field}"
