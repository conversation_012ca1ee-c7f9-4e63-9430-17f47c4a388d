#!/usr/bin/env python
"""
Script para ejecutar los tests de multi-tenancy.
Este script configura la base de datos de prueba y ejecuta los tests de multi-tenancy.
"""

import os
import sys
import asyncio
import subprocess
import click
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.core.config import settings
from src.utils.base_logger import logger


def setup_test_database():
    """Configurar base de datos de prueba"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    # Crear base de datos de prueba si no existe
    try:
        # Usar el script simplificado para crear la base de datos
        subprocess.run([
            "python", "-m", "scripts.init_db_simple",
            "--db-name", "rayuela_test"
        ], check=True, env=env)

        logger.info("Base de datos de prueba configurada correctamente.")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error al configurar la base de datos de prueba: {e}")
        return False


def run_multi_tenancy_tests(verbose=False):
    """Ejecutar tests de multi-tenancy"""
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["pytest", "tests/integration/test_multi_tenancy.py"]

    if verbose:
        cmd.append("-v")

    logger.info("Ejecutando tests de multi-tenancy...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


def run_multi_tenancy_security_tests(verbose=False):
    """Ejecutar tests de seguridad de multi-tenancy"""
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["pytest", "tests/integration/test_multi_tenancy_security.py"]

    if verbose:
        cmd.append("-v")

    logger.info("Ejecutando tests de seguridad de multi-tenancy...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


def run_celery_tenant_isolation_tests(verbose=False):
    """Ejecutar tests de aislamiento de tenant en Celery"""
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = ["pytest", "tests/integration/test_celery_tenant_isolation.py"]

    if verbose:
        cmd.append("-v")

    logger.info("Ejecutando tests de aislamiento de tenant en Celery...")
    result = subprocess.run(cmd, env=env)
    return result.returncode == 0


@click.command()
@click.option("--verbose", "-v", is_flag=True, help="Mostrar salida detallada")
@click.option("--skip-setup", is_flag=True, help="Omitir la configuración de la base de datos")
@click.option("--test-type", type=click.Choice(['basic', 'security', 'celery', 'all']), default='all',
              help="Tipo de test a ejecutar (basic, security, celery, all)")
def main(verbose, skip_setup, test_type):
    """Ejecutar tests de multi-tenancy"""
    if not skip_setup:
        if not setup_test_database():
            sys.exit(1)

    success = True

    if test_type in ['basic', 'all']:
        if not run_multi_tenancy_tests(verbose):
            success = False
            logger.error("Tests de multi-tenancy fallaron.")

    if test_type in ['security', 'all']:
        if not run_multi_tenancy_security_tests(verbose):
            success = False
            logger.error("Tests de seguridad de multi-tenancy fallaron.")

    if test_type in ['celery', 'all']:
        if not run_celery_tenant_isolation_tests(verbose):
            success = False
            logger.error("Tests de aislamiento de tenant en Celery fallaron.")

    if success:
        logger.info("Todos los tests de multi-tenancy pasaron correctamente.")
        sys.exit(0)
    else:
        logger.error("Algunos tests de multi-tenancy fallaron.")
        sys.exit(1)


if __name__ == "__main__":
    main()
