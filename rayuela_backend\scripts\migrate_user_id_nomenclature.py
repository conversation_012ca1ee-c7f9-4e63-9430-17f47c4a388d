#!/usr/bin/env python3
"""
Script de migración para unificar la nomenclatura de user_id en modelos de base de datos.

Este script genera las migraciones de Alembic necesarias para renombrar
end_user_id a user_id en todas las tablas relevantes.

IMPORTANTE: Este script debe ejecutarse con cuidado en producción.
Se recomienda hacer backup de la base de datos antes de ejecutar.
"""

import os
import sys
from pathlib import Path

# Agregar el directorio raíz al path
sys.path.append(str(Path(__file__).parent.parent))


def generate_migration_content():
    """Genera el contenido de la migración de Alembic."""

    migration_content = '''"""Rename end_user_id to user_id for consistency

Revision ID: unify_user_id_nomenclature
Revises: [PREVIOUS_REVISION]
Create Date: [CURRENT_DATE]

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'unify_user_id_nomenclature'
down_revision = '[PREVIOUS_REVISION]'  # Reemplazar con la revisión anterior
branch_labels = None
depends_on = None


def upgrade():
    """Renombra end_user_id a user_id en todas las tablas relevantes."""
    
    # 1. Renombrar columna en tabla interactions
    print("Renombrando end_user_id a user_id en tabla interactions...")
    op.alter_column('interactions', 'end_user_id', new_column_name='user_id')
    
    # 2. Renombrar columna en tabla recommendations  
    print("Renombrando end_user_id a user_id en tabla recommendations...")
    op.alter_column('recommendations', 'end_user_id', new_column_name='user_id')
    
    # 3. Renombrar columna en tabla searches
    print("Renombrando end_user_id a user_id en tabla searches...")
    op.alter_column('searches', 'end_user_id', new_column_name='user_id')
    
    # 4. Actualizar índices en interactions
    print("Actualizando índices en tabla interactions...")
    op.drop_index('idx_interaction_account_user', table_name='interactions')
    op.create_index('idx_interaction_account_user', 'interactions', ['account_id', 'user_id'])
    
    # 5. Actualizar índices en recommendations
    print("Actualizando índices en tabla recommendations...")
    op.drop_index('idx_recommendation_user', table_name='recommendations')
    op.create_index('idx_recommendation_user', 'recommendations', ['account_id', 'user_id'])
    
    # 6. Actualizar foreign keys en interactions
    print("Actualizando foreign keys en tabla interactions...")
    op.drop_constraint('fk_interaction_end_user', 'interactions', type_='foreignkey')
    op.create_foreign_key(
        'fk_interaction_user', 
        'interactions', 
        'end_users', 
        ['account_id', 'user_id'], 
        ['account_id', 'id'],
        ondelete='CASCADE'
    )
    
    # 7. Actualizar foreign keys en recommendations
    print("Actualizando foreign keys en tabla recommendations...")
    op.drop_constraint('fk_recommendation_end_user', 'recommendations', type_='foreignkey')
    op.create_foreign_key(
        'fk_recommendation_user', 
        'recommendations', 
        'end_users', 
        ['account_id', 'user_id'], 
        ['account_id', 'id'],
        ondelete='CASCADE'
    )
    
    print("✅ Migración completada exitosamente!")


def downgrade():
    """Revierte los cambios (renombra user_id de vuelta a end_user_id)."""
    
    # Revertir en orden inverso
    
    # 1. Revertir foreign keys en recommendations
    print("Revirtiendo foreign keys en tabla recommendations...")
    op.drop_constraint('fk_recommendation_user', 'recommendations', type_='foreignkey')
    op.create_foreign_key(
        'fk_recommendation_end_user', 
        'recommendations', 
        'end_users', 
        ['account_id', 'end_user_id'], 
        ['account_id', 'id'],
        ondelete='CASCADE'
    )
    
    # 2. Revertir foreign keys en interactions
    print("Revirtiendo foreign keys en tabla interactions...")
    op.drop_constraint('fk_interaction_user', 'interactions', type_='foreignkey')
    op.create_foreign_key(
        'fk_interaction_end_user', 
        'interactions', 
        'end_users', 
        ['account_id', 'end_user_id'], 
        ['account_id', 'id'],
        ondelete='CASCADE'
    )
    
    # 3. Revertir índices en recommendations
    print("Revirtiendo índices en tabla recommendations...")
    op.drop_index('idx_recommendation_user', table_name='recommendations')
    op.create_index('idx_recommendation_user', 'recommendations', ['account_id', 'end_user_id'])
    
    # 4. Revertir índices en interactions
    print("Revirtiendo índices en tabla interactions...")
    op.drop_index('idx_interaction_account_user', table_name='interactions')
    op.create_index('idx_interaction_account_user', 'interactions', ['account_id', 'end_user_id'])
    
    # 5. Revertir columna en tabla searches
    print("Revirtiendo end_user_id en tabla searches...")
    op.alter_column('searches', 'user_id', new_column_name='end_user_id')
    
    # 6. Revertir columna en tabla recommendations
    print("Revirtiendo end_user_id en tabla recommendations...")
    op.alter_column('recommendations', 'user_id', new_column_name='end_user_id')
    
    # 7. Revertir columna en tabla interactions
    print("Revirtiendo end_user_id en tabla interactions...")
    op.alter_column('interactions', 'user_id', new_column_name='end_user_id')
    
    print("✅ Reversión completada exitosamente!")
'''

    return migration_content


def create_migration_file():
    """Crea el archivo de migración de Alembic."""

    # Directorio de migraciones
    migrations_dir = Path(__file__).parent.parent / "alembic" / "versions"

    if not migrations_dir.exists():
        print(f"❌ Error: Directorio de migraciones no encontrado: {migrations_dir}")
        return False

    # Generar nombre de archivo
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{timestamp}_unify_user_id_nomenclature.py"
    filepath = migrations_dir / filename

    # Generar contenido
    content = generate_migration_content()

    # Reemplazar placeholders
    current_date = datetime.now().isoformat()
    content = content.replace("[CURRENT_DATE]", current_date)

    # Escribir archivo
    try:
        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        print(f"✅ Archivo de migración creado: {filepath}")
        print(f"📝 Recuerda actualizar '[PREVIOUS_REVISION]' con la revisión anterior")
        return True

    except Exception as e:
        print(f"❌ Error creando archivo de migración: {e}")
        return False


def print_instructions():
    """Imprime instrucciones para completar la migración."""

    print("\n" + "=" * 60)
    print("📋 INSTRUCCIONES PARA COMPLETAR LA MIGRACIÓN")
    print("=" * 60)

    print("\n1. 📝 EDITAR ARCHIVO DE MIGRACIÓN:")
    print("   - Abrir el archivo de migración generado")
    print("   - Reemplazar '[PREVIOUS_REVISION]' con la revisión anterior")
    print("   - Verificar que los nombres de tablas y columnas son correctos")

    print("\n2. 🔍 VERIFICAR MIGRACIÓN:")
    print("   cd rayuela_backend")
    print("   alembic check")
    print("   alembic show head")

    print("\n3. 🧪 PROBAR EN DESARROLLO:")
    print("   # Hacer backup de la BD de desarrollo")
    print("   alembic upgrade head")
    print("   # Verificar que todo funciona")
    print("   # Si hay problemas: alembic downgrade -1")

    print("\n4. 🚀 APLICAR EN PRODUCCIÓN:")
    print("   # IMPORTANTE: Hacer backup completo de la BD")
    print("   # Aplicar en horario de bajo tráfico")
    print("   alembic upgrade head")

    print("\n5. 📊 VERIFICAR DESPUÉS DE LA MIGRACIÓN:")
    print("   - Verificar que las columnas se renombraron correctamente")
    print("   - Verificar que los índices funcionan")
    print("   - Verificar que las foreign keys están intactas")
    print("   - Ejecutar tests de integración")

    print("\n⚠️  ADVERTENCIAS:")
    print("   - Esta migración puede tomar tiempo en tablas grandes")
    print("   - Puede requerir downtime en producción")
    print("   - Siempre hacer backup antes de aplicar")
    print("   - Probar primero en ambiente de staging")


def main():
    """Función principal del script."""

    print("🔄 Generando migración para unificar nomenclatura user_id...")

    # Crear archivo de migración
    if create_migration_file():
        print_instructions()
        return 0
    else:
        print("❌ Error generando migración")
        return 1


if __name__ == "__main__":
    sys.exit(main())
