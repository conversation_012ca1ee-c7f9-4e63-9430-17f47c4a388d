# src/core/redis_manager.py
import redis.asyncio as aioredis
from src.core.config import settings
import json
from typing import Any, Optional, List, Dict, Union
from src.utils.base_logger import log_info, log_error


class RedisManager:
    _instance = None
    _redis_client = None

    @classmethod
    async def get_instance(cls):
        if not cls._instance:
            cls._instance = cls()
            await cls._instance.initialize()
        return cls._instance

    async def initialize(self):
        if not self._redis_client:
            self._redis_client = await aioredis.from_url(
                settings.REDIS_URL, encoding="utf8", decode_responses=True
            )
            log_info("Redis connection initialized")

    @property
    def client(self):
        return self._redis_client

    # Métodos básicos de Redis
    async def get(self, key: str, default: Any = None) -> Any:
        """Obtiene un valor de Redis"""
        try:
            value = await self._redis_client.get(key)
            return value if value is not None else default
        except Exception as e:
            log_error(f"Error getting key {key} from Redis: {e}")
            return default

    async def set(self, key: str, value: Any, expire: Optional[int] = None) -> bool:
        """Establece un valor en Redis con opción de expiración"""
        try:
            await self._redis_client.set(key, value, ex=expire)
            return True
        except Exception as e:
            log_error(f"Error setting key {key} in Redis: {e}")
            return False

    async def delete(self, key: str) -> bool:
        """Elimina una clave de Redis"""
        try:
            await self._redis_client.delete(key)
            return True
        except Exception as e:
            log_error(f"Error deleting key {key} from Redis: {e}")
            return False

    async def exists(self, key: str) -> bool:
        """Verifica si una clave existe en Redis"""
        try:
            return await self._redis_client.exists(key) > 0
        except Exception as e:
            log_error(f"Error checking existence of key {key} in Redis: {e}")
            return False

    async def incr(self, key: str) -> int:
        """Incrementa un contador en Redis"""
        try:
            return await self._redis_client.incr(key)
        except Exception as e:
            log_error(f"Error incrementing key {key} in Redis: {e}")
            return 0

    async def expire(self, key: str, seconds: int) -> bool:
        """Establece un tiempo de expiración para una clave"""
        try:
            return await self._redis_client.expire(key, seconds)
        except Exception as e:
            log_error(f"Error setting expiration for key {key} in Redis: {e}")
            return False

    # Métodos para manejar JSON
    async def get_json(self, key: str, default: Any = None) -> Any:
        """Obtiene un valor JSON de Redis"""
        try:
            value = await self.get(key)
            if value is None:
                return default
            return json.loads(value)
        except Exception as e:
            log_error(f"Error getting JSON for key {key} from Redis: {e}")
            return default

    async def set_json(
        self, key: str, value: Any, expire: Optional[int] = None
    ) -> bool:
        """Establece un valor JSON en Redis"""
        try:
            json_value = json.dumps(value)
            return await self.set(key, json_value, expire)
        except Exception as e:
            log_error(f"Error setting JSON for key {key} in Redis: {e}")
            return False

    # Métodos para manejar pipelines
    async def execute_pipeline(self, commands: List[Dict[str, Any]]) -> List[Any]:
        """Ejecuta una serie de comandos en un pipeline de Redis"""
        try:
            pipe = self._redis_client.pipeline()
            for cmd in commands:
                method = getattr(pipe, cmd["method"])
                args = cmd.get("args", [])
                kwargs = cmd.get("kwargs", {})
                method(*args, **kwargs)
            return await pipe.execute()
        except Exception as e:
            log_error(f"Error executing Redis pipeline: {e}")
            return []

    # Métodos para manejar patrones de claves
    async def keys(self, pattern: str) -> List[str]:
        """Obtiene todas las claves que coinciden con un patrón"""
        try:
            return await self._redis_client.keys(pattern)
        except Exception as e:
            log_error(f"Error getting keys with pattern {pattern} from Redis: {e}")
            return []

    async def delete_pattern(self, pattern: str) -> int:
        """Elimina todas las claves que coinciden con un patrón"""
        try:
            keys = await self.keys(pattern)
            if keys:
                return await self._redis_client.delete(*keys)
            return 0
        except Exception as e:
            log_error(f"Error deleting keys with pattern {pattern} from Redis: {e}")
            return 0

    async def close(self):
        """Cierra la conexión a Redis"""
        if self._redis_client:
            await self._redis_client.close()
            log_info("Redis connection closed")
