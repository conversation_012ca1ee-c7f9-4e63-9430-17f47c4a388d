"""Use server_default for timestamps

Revision ID: use_server_default_for_timestamps
Revises: 8fdd0755dc89
Create Date: 2023-06-16 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "9fdd0755dc90"  # ID corto en lugar del nombre descriptivo largo
down_revision: Union[str, None] = "8fdd0755dc89"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to use server_default for timestamps."""
    # Account
    op.alter_column('accounts', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))
    op.alter_column('accounts', 'updated_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # SystemUser
    op.alter_column('system_users', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))
    op.alter_column('system_users', 'updated_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # Notification
    op.alter_column('notifications', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # AuditLog
    op.alter_column('audit_logs', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # EndUser
    op.alter_column('end_users', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # Product
    op.alter_column('products', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))
    op.alter_column('products', 'updated_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # Subscription - Solo modificamos created_at ya que updated_at no existe
    op.alter_column('subscriptions', 'created_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # AccountUsageMetrics - Omitimos last_billing_cycle que no existe
    op.alter_column('account_usage_metrics', 'last_reset',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))
    op.alter_column('account_usage_metrics', 'updated_at',
               existing_type=sa.DateTime(),
               type_=sa.DateTime(timezone=True),
               existing_nullable=True,
               server_default=sa.text('now()'))

    # EndpointMetrics - Omitimos porque la tabla no existe
    # op.alter_column('endpoint_metrics', 'last_called_at',
    #            existing_type=sa.DateTime(),
    #            type_=sa.DateTime(timezone=True),
    #            existing_nullable=True,
    #            server_default=sa.text('now()'))
    # op.alter_column('endpoint_metrics', 'updated_at',
    #            existing_type=sa.DateTime(),
    #            type_=sa.DateTime(timezone=True),
    #            existing_nullable=True,
    #            server_default=sa.text('now()'))


def downgrade() -> None:
    """Downgrade schema to remove server_default for timestamps."""
    # Account
    op.alter_column('accounts', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))
    op.alter_column('accounts', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # SystemUser
    op.alter_column('system_users', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))
    op.alter_column('system_users', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # Notification
    op.alter_column('notifications', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # AuditLog
    op.alter_column('audit_logs', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # EndUser
    op.alter_column('end_users', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # Product
    op.alter_column('products', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))
    op.alter_column('products', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # Subscription - Solo modificamos created_at ya que updated_at no existe
    op.alter_column('subscriptions', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # AccountUsageMetrics - Omitimos last_billing_cycle que no existe
    op.alter_column('account_usage_metrics', 'last_reset',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))
    op.alter_column('account_usage_metrics', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True,
               server_default=sa.text('NULL'))

    # EndpointMetrics - Omitimos porque la tabla no existe
    # op.alter_column('endpoint_metrics', 'last_called_at',
    #            existing_type=sa.DateTime(timezone=True),
    #            type_=sa.DateTime(),
    #            existing_nullable=True,
    #            server_default=sa.text('NULL'))
    # op.alter_column('endpoint_metrics', 'updated_at',
    #            existing_type=sa.DateTime(timezone=True),
    #            type_=sa.DateTime(),
    #            existing_nullable=True,
    #            server_default=sa.text('NULL'))
