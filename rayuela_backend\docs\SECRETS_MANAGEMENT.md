# Gestión Segura de Secretos

Este documento describe las mejores prácticas para la gestión de secretos en la aplicación, con énfasis especial en la clave secreta para JWT (`SECRET_KEY`).

## Importancia de la Gestión Segura de Secretos

Los secretos como `SECRET_KEY` son críticos para la seguridad de la aplicación:

- `SECRET_KEY` se utiliza para firmar tokens JWT, que autentican usuarios y protegen rutas
- Si un atacante obtiene la clave secreta, puede forjar tokens y acceder a recursos protegidos
- Una clave débil o comprometida puede llevar a vulnerabilidades de seguridad graves

## Generación de Claves Seguras

### Requisitos para una Clave Segura

- **Alta entropía**: Generada con un generador de números aleatorios criptográficamente seguro
- **Longitud adecuada**: Mínimo 32 caracteres, recomendado 64+ caracteres
- **Unicidad**: Diferente para cada entorno (desarrollo, staging, producción)
- **Complejidad**: Mezcla de caracteres alfanuméricos y especiales

### Herramienta de Generación

Hemos implementado una herramienta para generar claves seguras:

```bash
# Generar una clave y mostrarla
python -m scripts.generate_secret_key

# Generar una clave para producción
python -m scripts.generate_secret_key --env production

# Generar una clave y actualizar el archivo .env.development
python -m scripts.generate_secret_key --env development --update
```

## Almacenamiento Seguro de Secretos

### Desarrollo Local

- Almacenar en archivos `.env.{ENV}` que **NO** se suben al repositorio
- Asegurarse de que estos archivos estén en `.gitignore`
- Usar `.env.example` como plantilla, pero nunca incluir secretos reales

### Entorno de Producción

En producción, **NUNCA** almacenar secretos en:

- Archivos de configuración
- Variables de entorno hardcodeadas
- Código fuente
- Logs o bases de datos

En su lugar, usar **Google Cloud Secret Manager**:

## Configuración Automatizada de Secretos

Para configurar todos los secretos de forma automatizada:

```bash
# Configurar todos los secretos requeridos
python -m scripts.setup_secrets --project-id your-project-id

# Verificar que todos los secretos estén configurados
python -m scripts.verify_secrets --project-id your-project-id
```

## Configuración Manual de Secretos

Si prefieres configurar los secretos manualmente:

```bash
# Secretos de base de datos
gcloud secrets create DB_PASSWORD --replication-policy=automatic
gcloud secrets create POSTGRES_SERVER --replication-policy=automatic
gcloud secrets create POSTGRES_USER --replication-policy=automatic
gcloud secrets create POSTGRES_DB --replication-policy=automatic
gcloud secrets create POSTGRES_PORT --replication-policy=automatic

# Secretos de Redis
gcloud secrets create REDIS_PASSWORD --replication-policy=automatic
gcloud secrets create REDIS_HOST --replication-policy=automatic
gcloud secrets create REDIS_PORT --replication-policy=automatic
gcloud secrets create REDIS_URL --replication-policy=automatic

# Secretos de seguridad
gcloud secrets create SECRET_KEY --replication-policy=automatic

# Configuración de Google Cloud
gcloud secrets create GCS_BUCKET_NAME --replication-policy=automatic

# Credenciales de MercadoPago (opcionales)
gcloud secrets create MERCADOPAGO_ACCESS_TOKEN --replication-policy=automatic
gcloud secrets create MERCADOPAGO_PUBLIC_KEY --replication-policy=automatic
gcloud secrets create MERCADOPAGO_WEBHOOK_SECRET --replication-policy=automatic

# Añadir valores a los secretos
echo -n 'tu_valor_secreto' | gcloud secrets versions add SECRET_NAME --data-file=-
```

### Configuración en Cloud Run

La aplicación carga automáticamente todos los secretos desde Secret Manager en producción.
Los archivos de Cloud Build han sido actualizados para **NO** exponer credenciales sensibles.

**Configuración segura actual:**

```bash
# Solo variables no sensibles se pasan como env vars
--set-env-vars=ENV=production,GCP_PROJECT_ID=$PROJECT_ID,GCP_REGION=us-central1,DEBUG=False,LOG_LEVEL=INFO

# Solo las contraseñas críticas se mantienen como secrets (por compatibilidad)
--set-secrets=POSTGRES_PASSWORD=DB_PASSWORD:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest
```

**⚠️ IMPORTANTE:** Todas las demás credenciales (hosts, usuarios, puertos, tokens de terceros)
se cargan automáticamente desde Secret Manager por la aplicación en tiempo de ejecución.

```bash
gcloud run deploy rayuela-api-service \
    --image YOUR_REGION-docker.pkg.dev/YOUR_PROJECT_ID/YOUR_REPO_NAME/rayuela-api:latest \
    --set-secrets="SECRET_KEY=jwt-secret-key:latest" \
    # Otros parámetros...
```

## Rotación de Secretos

### Cuándo Rotar Secretos

- Periódicamente (cada 90-180 días)
- Cuando un miembro del equipo con acceso a los secretos deja la organización
- Si se sospecha que un secreto ha sido comprometido
- Después de un incidente de seguridad

### Proceso de Rotación

1. Generar una nueva clave secreta
2. Actualizar el secreto en Secret Manager o el archivo `.env` correspondiente
3. Reiniciar los servicios para que carguen la nueva clave
4. Invalidar todos los tokens JWT existentes (opcional, pero recomendado)

## Validación de Secretos

La aplicación valida automáticamente la `SECRET_KEY` al inicio:

- Verifica que no esté vacía
- Comprueba que tenga al menos 32 caracteres
- Detecta claves de desarrollo en entornos de producción
- Evalúa la entropía básica de la clave

Si la validación falla, la aplicación no se iniciará, lo que evita desplegar con secretos inseguros.

## Acceso a Secretos

- Limitar el acceso a los secretos solo a quienes lo necesitan
- Usar IAM para controlar el acceso a Secret Manager
- Nunca compartir secretos por correo electrónico, chat o documentos
- Considerar el uso de herramientas como HashiCorp Vault para equipos grandes

## Referencias

- [OWASP Secrets Management](https://cheatsheetseries.owasp.org/cheatsheets/Secrets_Management_Cheat_Sheet.html)
- [Google Cloud Secret Manager Best Practices](https://cloud.google.com/secret-manager/docs/best-practices)
- [NIST Guidelines for Password Management](https://pages.nist.gov/800-63-3/sp800-63b.html)
