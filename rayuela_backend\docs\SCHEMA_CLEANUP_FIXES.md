# Corrección del Desajuste entre RLS SQL y Esquema ORM

## Resumen del Problema

La función `cleanup_old_data` en `rayuela_backend/alembic/versions/secure_rls_functions.py` tenía un desajuste con el esquema actual de la base de datos, intentando actualizar columnas que no existían:

1. **Columnas inexistentes**: `status`, `last_interaction_at`, `last_login_at`
2. **Nombres de tabla incorrectos**: Referencia a tabla `users` en lugar de `system_users` y `end_users`
3. **Lógica de estado incorrecta**: Uso de `status = 'archived'/'inactive'` en lugar de `is_active = FALSE`

## Correcciones Implementadas

### 1. Migración: Añadir Columnas de Tracking (`add_tracking_columns_for_cleanup.py`)

**Archivo**: `rayuela_backend/alembic/versions/add_tracking_columns_for_cleanup.py`

**Cambios**:
- Añadida columna `last_interaction_at` a la tabla `products`
- Añadida columna `last_login_at` a la tabla `system_users`
- Añadida columna `last_activity_at` a la tabla `end_users`
- Creados índices compuestos para optimizar las consultas de limpieza:
  - `idx_products_account_last_interaction`
  - `idx_system_users_account_last_login`
  - `idx_end_users_account_last_activity`

### 2. Actualización de Modelos ORM

#### Product Model (`src/db/models/product.py`)
```python
last_interaction_at = Column(
    DateTime(timezone=True), 
    nullable=True, 
    comment='Timestamp of last interaction with this product'
)
```

#### SystemUser Model (`src/db/models/system_user.py`)
```python
last_login_at = Column(
    DateTime(timezone=True), 
    nullable=True, 
    comment='Timestamp of last login by this user'
)
```

#### EndUser Model (`src/db/models/end_user.py`)
```python
last_activity_at = Column(
    DateTime(timezone=True), 
    nullable=True, 
    comment='Timestamp of last activity by this end user'
)
```

### 3. Migración: Corrección de la Función de Limpieza (`fix_cleanup_schema_mismatch.py`)

**Archivo**: `rayuela_backend/alembic/versions/fix_cleanup_schema_mismatch.py`

**Correcciones en la función SQL**:

1. **Productos**: 
   - ❌ `SET status = 'archived'` → ✅ `SET is_active = FALSE`
   - ❌ `WHERE status != 'archived'` → ✅ `WHERE is_active = TRUE AND deleted_at IS NULL`

2. **Usuarios del Sistema**:
   - ❌ `SET status = 'inactive'` → ✅ `SET is_active = FALSE`
   - ❌ `WHERE status != 'inactive'` → ✅ `WHERE is_active = TRUE AND deleted_at IS NULL`

3. **Usuarios Finales**:
   - ✅ Añadido manejo separado para la tabla `end_users`
   - ✅ Uso de `last_activity_at` para determinar inactividad

4. **Robustez**:
   - ✅ Verificación de existencia de columnas antes de usarlas
   - ✅ Fallback a `updated_at` si las columnas de tracking no existen
   - ✅ Manejo separado de `system_users` y `end_users`

### 4. Actualización de Servicios para Mantener Tracking

#### InteractionService (`src/services/interaction_service.py`)
```python
# Actualizar last_interaction_at en el producto
await self.db.execute(
    update(Product)
    .where(
        Product.account_id == self.account_id,
        Product.id == interaction_data.product_id
    )
    .values(
        last_interaction_at=current_time,
        updated_at=current_time
    )
)

# Actualizar last_activity_at en el usuario final
await self.db.execute(
    update(EndUser)
    .where(
        EndUser.account_id == self.account_id,
        EndUser.id == interaction_data.end_user_id
    )
    .values(
        last_activity_at=current_time,
        updated_at=current_time
    )
)
```

#### AuthService (`src/services/auth_service.py`)
```python
# Actualizar last_login_at después de login exitoso
await self.db.execute(
    update(SystemUser)
    .where(
        SystemUser.account_id == user.account_id,
        SystemUser.id == user.id
    )
    .values(
        last_login_at=current_time,
        updated_at=current_time
    )
)
```

## Beneficios de las Correcciones

### 1. **Funcionalidad Restaurada**
- La función `cleanup_old_data` ahora funciona correctamente
- Los cronjobs de mantenimiento pueden ejecutarse sin errores
- Se previene la acumulación de datos inactivos

### 2. **Rendimiento Optimizado**
- Índices compuestos para consultas de limpieza eficientes
- Uso de columnas específicas de tracking en lugar de `updated_at` genérico

### 3. **Mantenimiento Automático**
- Las columnas de tracking se actualizan automáticamente:
  - `last_interaction_at` cuando se crea una interacción
  - `last_login_at` cuando un usuario se loguea
  - `last_activity_at` cuando un usuario final interactúa

### 4. **Compatibilidad hacia Atrás**
- La función incluye fallbacks para entornos donde las nuevas columnas no existen
- Verificación de existencia de columnas antes de usarlas

## Orden de Aplicación de Migraciones

1. `add_tracking_columns_for_cleanup.py` - Añade las columnas necesarias
2. `fix_cleanup_schema_mismatch.py` - Corrige la función de limpieza

## Verificación

Para verificar que las correcciones funcionan:

```sql
-- Verificar que las columnas existen
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name IN ('products', 'system_users', 'end_users') 
AND column_name IN ('last_interaction_at', 'last_login_at', 'last_activity_at');

-- Probar la función de limpieza (en un entorno de prueba)
SELECT cleanup_old_data(1, 30); -- Limpiar datos de más de 30 días para account_id=1
```

## Impacto en Seguridad

- ✅ Mantiene el aislamiento de tenants (RLS)
- ✅ Preserva los permisos de la función (`maintenance_role`)
- ✅ No introduce vulnerabilidades de seguridad
- ✅ Mejora la auditabilidad con timestamps precisos

## Resolución del Error de Migración

**Problema**: El ID de revisión original `fix_cleanup_function_schema_mismatch` (34 caracteres) excedía el límite de 32 caracteres del campo `version_num` en la tabla `alembic_version`.

**Solución**: Se acortó el ID de revisión a `fix_cleanup_schema_mismatch` (27 caracteres) para cumplir con la restricción de la base de datos. 