"""Use enums for roles, permissions and subscriptions

Revision ID: use_enums_for_roles_permissions_subscriptions
Revises: 7fdd0755dc88
Create Date: 2023-06-15 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "8fdd0755dc89"  # ID corto en lugar del nombre descriptivo largo
down_revision: Union[str, None] = "7fdd0755dc88"  # Ajusta esto según la última migración
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to use enums for roles, permissions and subscriptions."""
    # Crear los tipos enum si no existen usando un enfoque compatible con todas las versiones de PostgreSQL

    # RoleType enum
    op.execute("""
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'roletype') THEN
            CREATE TYPE roletype AS ENUM ('ADMIN', 'EDITOR', 'VIEWER');
        END IF;
    END
    $$;
    """)

    # PermissionType enum
    op.execute("""
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'permissiontype') THEN
            CREATE TYPE permissiontype AS ENUM (
                'READ', 'WRITE', 'DELETE', 'ADMIN',
                'PRODUCT_READ', 'PRODUCT_CREATE', 'PRODUCT_UPDATE', 'PRODUCT_DELETE',
                'USER_READ', 'USER_CREATE', 'USER_UPDATE', 'USER_DELETE',
                'SYSTEM_USER_READ', 'SYSTEM_USER_CREATE', 'SYSTEM_USER_UPDATE', 'SYSTEM_USER_DELETE',
                'ROLE_READ', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_DELETE', 'PERMISSION_ASSIGN',
                'ANALYTICS_READ',
                'MODEL_READ', 'MODEL_CREATE', 'MODEL_UPDATE', 'MODEL_DELETE',
                'TRAINING_JOB_READ', 'TRAINING_JOB_CREATE', 'TRAINING_JOB_UPDATE', 'TRAINING_JOB_CANCEL'
            );
        END IF;
    END
    $$;
    """)

    # SubscriptionPlan enum
    op.execute("""
    DO $$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'subscriptionplan') THEN
            CREATE TYPE subscriptionplan AS ENUM ('FREE', 'BASIC', 'PRO', 'ENTERPRISE');
        END IF;
    END
    $$;
    """)

    # Modificar la columna name en la tabla roles
    # Primero crear una columna temporal
    op.add_column('roles', sa.Column('name_enum', postgresql.ENUM('ADMIN', 'EDITOR', 'VIEWER', name='roletype'), nullable=True))

    # Actualizar la columna temporal con los valores de la columna original
    op.execute("UPDATE roles SET name_enum = name::roletype")

    # Eliminar la columna original
    op.drop_column('roles', 'name')

    # Renombrar la columna temporal
    op.alter_column('roles', 'name_enum', new_column_name='name')

    # Hacer la columna no nullable
    op.alter_column('roles', 'name', nullable=False)

    # Modificar la columna name en la tabla permissions
    # Primero crear una columna temporal
    op.add_column('permissions', sa.Column('name_enum', postgresql.ENUM(
        'READ', 'WRITE', 'DELETE', 'ADMIN',
        'PRODUCT_READ', 'PRODUCT_CREATE', 'PRODUCT_UPDATE', 'PRODUCT_DELETE',
        'USER_READ', 'USER_CREATE', 'USER_UPDATE', 'USER_DELETE',
        'SYSTEM_USER_READ', 'SYSTEM_USER_CREATE', 'SYSTEM_USER_UPDATE', 'SYSTEM_USER_DELETE',
        'ROLE_READ', 'ROLE_CREATE', 'ROLE_UPDATE', 'ROLE_DELETE', 'PERMISSION_ASSIGN',
        'ANALYTICS_READ',
        'MODEL_READ', 'MODEL_CREATE', 'MODEL_UPDATE', 'MODEL_DELETE',
        'TRAINING_JOB_READ', 'TRAINING_JOB_CREATE', 'TRAINING_JOB_UPDATE', 'TRAINING_JOB_CANCEL',
        name='permissiontype'), nullable=True))

    # Actualizar la columna temporal con los valores de la columna original
    op.execute("UPDATE permissions SET name_enum = name::permissiontype")

    # Eliminar la columna original
    op.drop_column('permissions', 'name')

    # Renombrar la columna temporal
    op.alter_column('permissions', 'name_enum', new_column_name='name')

    # Hacer la columna no nullable
    op.alter_column('permissions', 'name', nullable=False)

    # Modificar la columna plan_type en la tabla subscriptions
    # Primero crear una columna temporal
    op.add_column('subscriptions', sa.Column('plan_type_enum', postgresql.ENUM('FREE', 'BASIC', 'PRO', 'ENTERPRISE', name='subscriptionplan'), nullable=True))

    # Actualizar la columna temporal con los valores de la columna original
    op.execute("UPDATE subscriptions SET plan_type_enum = plan_type::subscriptionplan")

    # Eliminar la columna original
    op.drop_column('subscriptions', 'plan_type')

    # Renombrar la columna temporal
    op.alter_column('subscriptions', 'plan_type_enum', new_column_name='plan_type')

    # Hacer la columna no nullable
    op.alter_column('subscriptions', 'plan_type', nullable=False)

    # Recrear los índices
    op.create_index('idx_role_name', 'roles', ['name'])
    op.create_index('idx_permission_name', 'permissions', ['name'])
    op.create_index('ix_subscriptions_plan_type', 'subscriptions', ['plan_type'])


def downgrade() -> None:
    """Downgrade schema to use strings instead of enums."""
    # Modificar la columna name en la tabla roles
    # Primero crear una columna temporal
    op.add_column('roles', sa.Column('name_str', sa.String(100), nullable=True))

    # Actualizar la columna temporal con los valores de la columna original
    op.execute("UPDATE roles SET name_str = name::text")

    # Eliminar la columna original
    op.drop_column('roles', 'name')

    # Renombrar la columna temporal
    op.alter_column('roles', 'name_str', new_column_name='name')

    # Hacer la columna no nullable
    op.alter_column('roles', 'name', nullable=False)

    # Modificar la columna name en la tabla permissions
    # Primero crear una columna temporal
    op.add_column('permissions', sa.Column('name_str', sa.String(), nullable=True))

    # Actualizar la columna temporal con los valores de la columna original
    op.execute("UPDATE permissions SET name_str = name::text")

    # Eliminar la columna original
    op.drop_column('permissions', 'name')

    # Renombrar la columna temporal
    op.alter_column('permissions', 'name_str', new_column_name='name')

    # Hacer la columna no nullable
    op.alter_column('permissions', 'name', nullable=False)

    # Modificar la columna plan_type en la tabla subscriptions
    # Primero crear una columna temporal
    op.add_column('subscriptions', sa.Column('plan_type_str', sa.String(), nullable=True))

    # Actualizar la columna temporal con los valores de la columna original
    op.execute("UPDATE subscriptions SET plan_type_str = plan_type::text")

    # Eliminar la columna original
    op.drop_column('subscriptions', 'plan_type')

    # Renombrar la columna temporal
    op.alter_column('subscriptions', 'plan_type_str', new_column_name='plan_type')

    # Hacer la columna no nullable
    op.alter_column('subscriptions', 'plan_type', nullable=False)

    # Recrear los índices
    op.create_index('idx_role_name', 'roles', ['name'])
    op.create_index('idx_permission_name', 'permissions', ['name'])
    op.create_index('ix_subscriptions_plan_type', 'subscriptions', ['plan_type'])

    # No eliminamos los tipos enum por si son utilizados en otras tablas
