# Gestión de Particiones en la Base de Datos

Este documento describe el sistema de gestión de particiones implementado para las tablas de alto volumen en la base de datos de Rayuela.

## Resumen

El sistema de particionamiento divide las tablas de alto volumen en particiones más pequeñas basadas en el `account_id`. Esto mejora el rendimiento de las consultas y facilita el mantenimiento de la base de datos.

El sistema incluye:
1. Creación proactiva de particiones
2. Archivado de particiones de cuentas inactivas
3. Automatización mediante CronJobs de Kubernetes

## Tablas Particionadas

Las siguientes tablas están configuradas para usar particionamiento por rango de `account_id`:

- `accounts`
- `subscriptions`
- `system_users`
- `roles`
- `permissions`
- `role_permissions`
- `system_user_roles`
- `audit_logs`
- `end_users`
- `products`
- `interactions`
- `searches`
- `artifact_metadata`
- `training_jobs`
- `account_usage_metrics`

## Estrategia de Particionamiento

- Cada partición contiene un rango de `account_id` definido por `PARTITION_SIZE` (por defecto 1000)
- Las particiones se nombran según el patrón `{table_name}_p_{start_range}_{end_range}`
- Ejemplo: `interactions_p_0_1000` contiene interacciones para cuentas con IDs del 0 al 999

## Creación Proactiva de Particiones

El sistema crea automáticamente particiones antes de que sean necesarias:

1. Determina el `account_id` máximo actual
2. Crea particiones para ese ID y un número adicional de particiones (buffer)
3. Esto asegura que siempre haya particiones disponibles para nuevas cuentas

## Archivado de Particiones

Para optimizar el almacenamiento y el rendimiento, el sistema puede archivar particiones de cuentas inactivas:

1. Identifica cuentas que no han tenido actividad durante un período definido (por defecto 365 días)
2. Verifica que no haya otras cuentas activas en el mismo rango de partición
3. Desconecta la partición de la tabla principal (`DETACH PARTITION`)
4. Mueve la partición a un esquema de archivo (`archived_partitions`)

Este proceso:
- Reduce el tamaño de las tablas principales
- Mejora el rendimiento de las consultas
- Mantiene los datos históricos accesibles si son necesarios

## Uso del Script de Gestión de Particiones

El script `scripts/manage_partitions.py` proporciona una interfaz para gestionar particiones:

```bash
# Crear particiones proactivamente
python -m scripts.manage_partitions --create

# Archivar particiones de cuentas inactivas (modo simulación)
python -m scripts.manage_partitions --archive

# Archivar particiones de cuentas inactivas con un umbral personalizado
python -m scripts.manage_partitions --archive --inactivity-days 180

# Ejecutar cambios reales (no simulación)
python -m scripts.manage_partitions --archive --execute

# Crear y archivar en una sola operación
python -m scripts.manage_partitions --create --archive --execute
```

## Automatización con Celery

El sistema utiliza **Celery workers** para la gestión automatizada de particiones, lo cual es más simple y eficiente que Kubernetes CronJobs:

### ✅ **Ventajas de Celery vs Kubernetes CronJobs**
- **Menor costo**: Solo Cloud Run (que ya usas) vs Cloud Run + Kubernetes
- **Menor complejidad**: Una sola tecnología de orquestación
- **Mejor monitoreo**: Celery Flower, logs integrados
- **Retry automático**: Celery maneja fallos automáticamente
- **Escalabilidad**: Workers pueden escalar según demanda

### 🔧 **Configuración Actual**

La tarea `manage-partitions` está configurada en `src/workers/celery_app.py`:

```python
"manage-partitions": {
    "task": "src.workers.celery_tasks_partition.manage_partitions_task",
    "schedule": crontab(hour=1, minute=0),  # Daily at 1:00 AM
    "options": {"queue": "maintenance"},
},
```

### 🚀 **Despliegue Automático**

Los workers de Celery se despliegan automáticamente como parte del pipeline de CI/CD:

1. **Worker de Mantenimiento**: `rayuela-worker-maintenance` (Cloud Run)
2. **Celery Beat Scheduler**: `rayuela-beat` (Cloud Run)

```bash
# Desplegar todo (incluyendo workers)
./scripts/deploy-production.sh --direct

# Verificar que los workers estén funcionando
./scripts/verify-celery-workers.sh
```

## Manejo de Errores

El script incluye manejo robusto de errores:

1. Verificación de la existencia de particiones antes de operaciones
2. Verificación de que las operaciones se completen correctamente
3. Registro detallado de errores y advertencias
4. Modo de simulación para probar cambios sin aplicarlos

## Consideraciones de Rendimiento

- Las operaciones de particionamiento pueden ser intensivas en recursos
- El CronJob está configurado para ejecutarse en horas de bajo tráfico
- Los límites de recursos están configurados para minimizar el impacto en el sistema

## Recuperación de Datos Archivados

Si es necesario acceder a datos en particiones archivadas:

```sql
-- Consultar datos de una partición archivada
SELECT * FROM archived_partitions.interactions_p_1000_2000 WHERE account_id = 1500;

-- Restaurar una partición archivada (si es necesario)
ALTER TABLE interactions ATTACH PARTITION archived_partitions.interactions_p_1000_2000
FOR VALUES FROM (1000) TO (2000);
```

## Monitoreo y Mantenimiento

Se recomienda:

1. Revisar periódicamente los logs del CronJob
2. Ajustar los parámetros según el crecimiento de la base de datos
3. Verificar el espacio de almacenamiento utilizado por las particiones archivadas
4. Considerar la eliminación permanente de particiones muy antiguas según la política de retención de datos
