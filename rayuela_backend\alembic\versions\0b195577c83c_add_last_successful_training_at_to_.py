"""add_last_successful_training_at_to_subscription

Revision ID: 0b195577c83c
Revises: 60753112de16
Create Date: 2025-04-21 23:10:09.425317

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '0b195577c83c'
down_revision: Union[str, None] = '60753112de16'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to add last_successful_training_at to subscription."""
    # Add the new column to the subscriptions table
    op.add_column('subscriptions', sa.Column('last_successful_training_at', sa.DateTime(timezone=True), nullable=True))

    # Add a comment to explain the purpose of the new column
    op.execute("COMMENT ON COLUMN subscriptions.last_successful_training_at IS 'Fecha del último entrenamiento exitoso';")


def downgrade() -> None:
    """Downgrade schema to remove last_successful_training_at from subscription."""
    # Remove the column from the subscriptions table
    op.drop_column('subscriptions', 'last_successful_training_at')
