"""merge_heads_for_api_key_revealed

Revision ID: af19ff6c9c1a
Revises: add_api_key_revealed_field, add_optimized_indexes
Create Date: 2025-04-21 03:14:45.953809

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'af19ff6c9c1a'
down_revision: Union[str, None] = ('add_api_key_revealed_field', 'add_optimized_indexes')
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    pass


def downgrade() -> None:
    """Downgrade schema."""
    pass
