# Data Retention Optimization Configuration
# Copy these values to your .env file or set as environment variables

# =============================================================================
# DATA RETENTION SETTINGS - OPTIMIZED FOR STARTUP COSTS
# =============================================================================

# Soft Delete Physical Cleanup (reduced from 365 to 90 days)
SOFT_DELETE_RETENTION_DAYS=90
SOFT_DELETE_ARCHIVE_BEFORE_DELETION=true
SOFT_DELETE_BATCH_SIZE=1000

# Data Archival Configuration
ARCHIVAL_STRATEGY=gcs
ARCHIVAL_FORMAT=parquet
ARCHIVAL_VERIFY_EXPORT=true
ARCHIVAL_COMPRESSION=gzip

# =============================================================================
# CELERY TASK OVERRIDES (Optional - defaults are already optimized)
# =============================================================================

# These are now handled in code, but can be overridden if needed:
# AUDIT_LOGS_RETENTION_DAYS=30      # Default: 30 days (was 90)
# INTERACTIONS_RETENTION_DAYS=60    # Default: 60 days (was 180)
# SECURE_CLEANUP_RETENTION_DAYS=90  # Default: 90 days (was 365)

# =============================================================================
# MONITORING & SAFETY
# =============================================================================

# Enable detailed logging for archival operations
LOG_LEVEL=INFO

# Batch sizes for safe processing
ARCHIVAL_BATCH_SIZE=10000
CLEANUP_BATCH_SIZE=10000

# =============================================================================
# COST OPTIMIZATION NOTES
# =============================================================================

# Expected savings:
# - Cloud SQL storage: 60-75% reduction
# - IOPS costs: Significant reduction due to smaller tables
# - Backup costs: Reduced backup size and time
# - Query performance: Improved due to smaller indexes

# Data safety:
# - All data is archived to GCS before deletion
# - Archival verification is enabled
# - Recovery possible from GCS if needed

# Privacy compliance:
# - Update Privacy Policy to reflect new retention periods
# - Inform users about data availability windows
# - Maintain audit trail of all changes
