import pytest
from sqlalchemy import select
from src.db.models import Product, EndUser, Interaction, Account
from src.services.product_service import ProductService
from src.services.user_service import UserService
from src.services.interaction_service import InteractionService
from src.core.exceptions import NotFoundError, ValidationError

class TestDBInteraction:
    """Tests de integración para la interacción con la base de datos."""
    
    async def test_product_service_repository_interaction(
        self,
        db_session,
        test_accounts,
        test_products
    ):
        """Test para la interacción entre ProductService y su repositorio."""
        # Inicializar servicio
        product_service = ProductService(db_session)
        account = test_accounts[0]
        
        # Crear nuevo producto
        new_product = await product_service.create(
            account_id=account.id,
            data={
                "name": "Test Product",
                "description": "Test Description",
                "price": 99.99,
                "category": "test"
            }
        )
        assert new_product.name == "Test Product"
        assert new_product.account_id == account.id
        
        # Verificar que el producto se guardó en la base de datos
        saved_product = await db_session.get(Product, new_product.id)
        assert saved_product is not None
        assert saved_product.name == new_product.name
        
        # Actualizar producto
        updated_product = await product_service.update(
            account_id=account.id,
            product_id=new_product.id,
            data={"name": "Updated Product"}
        )
        assert updated_product.name == "Updated Product"
        
        # Verificar que la actualización se guardó en la base de datos
        saved_product = await db_session.get(Product, new_product.id)
        assert saved_product.name == "Updated Product"
        
        # Eliminar producto
        await product_service.delete(account_id=account.id, product_id=new_product.id)
        
        # Verificar que el producto se eliminó de la base de datos
        saved_product = await db_session.get(Product, new_product.id)
        assert saved_product is None
    
    async def test_user_service_repository_interaction(
        self,
        db_session,
        test_accounts,
        test_end_users
    ):
        """Test para la interacción entre UserService y su repositorio."""
        # Inicializar servicio
        user_service = UserService(db_session)
        account = test_accounts[0]
        
        # Crear nuevo usuario final
        new_user = await user_service.create(
            account_id=account.id,
            data={
                "email": "<EMAIL>",
                "name": "Test User",
                "metadata": {"age": 25}
            }
        )
        assert new_user.email == "<EMAIL>"
        assert new_user.account_id == account.id
        
        # Verificar que el usuario se guardó en la base de datos
        saved_user = await db_session.get(EndUser, new_user.id)
        assert saved_user is not None
        assert saved_user.email == new_user.email
        
        # Actualizar usuario
        updated_user = await user_service.update(
            account_id=account.id,
            user_id=new_user.id,
            data={"name": "Updated User"}
        )
        assert updated_user.name == "Updated User"
        
        # Verificar que la actualización se guardó en la base de datos
        saved_user = await db_session.get(EndUser, new_user.id)
        assert saved_user.name == "Updated User"
        
        # Eliminar usuario
        await user_service.delete(account_id=account.id, user_id=new_user.id)
        
        # Verificar que el usuario se eliminó de la base de datos
        saved_user = await db_session.get(EndUser, new_user.id)
        assert saved_user is None
    
    async def test_interaction_service_repository_interaction(
        self,
        db_session,
        test_accounts,
        test_products,
        test_end_users
    ):
        """Test para la interacción entre InteractionService y su repositorio."""
        # Inicializar servicio
        interaction_service = InteractionService(db_session)
        account = test_accounts[0]
        product = test_products[account.id][0]
        user = test_end_users[account.id][0]
        
        # Crear nueva interacción
        new_interaction = await interaction_service.create(
            account_id=account.id,
            data={
                "user_id": user.id,
                "product_id": product.id,
                "interaction_type": "view",
                "metadata": {"duration": 30}
            }
        )
        assert new_interaction.user_id == user.id
        assert new_interaction.product_id == product.id
        assert new_interaction.account_id == account.id
        
        # Verificar que la interacción se guardó en la base de datos
        saved_interaction = await db_session.get(Interaction, new_interaction.id)
        assert saved_interaction is not None
        assert saved_interaction.interaction_type == "view"
        
        # Actualizar interacción
        updated_interaction = await interaction_service.update(
            account_id=account.id,
            interaction_id=new_interaction.id,
            data={"interaction_type": "purchase"}
        )
        assert updated_interaction.interaction_type == "purchase"
        
        # Verificar que la actualización se guardó en la base de datos
        saved_interaction = await db_session.get(Interaction, new_interaction.id)
        assert saved_interaction.interaction_type == "purchase"
        
        # Eliminar interacción
        await interaction_service.delete(
            account_id=account.id,
            interaction_id=new_interaction.id
        )
        
        # Verificar que la interacción se eliminó de la base de datos
        saved_interaction = await db_session.get(Interaction, new_interaction.id)
        assert saved_interaction is None
    
    async def test_tenant_filtering_in_queries(
        self,
        db_session,
        test_accounts,
        test_products
    ):
        """Test para verificar que el filtrado por tenant se aplica en las consultas."""
        # Obtener productos de diferentes tenants
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        
        # Consultar productos de tenant A
        stmt = select(Product).where(Product.account_id == account_a.id)
        result = await db_session.execute(stmt)
        products_a = result.scalars().all()
        
        # Verificar que solo se obtienen productos de tenant A
        assert all(p.account_id == account_a.id for p in products_a)
        assert not any(p.account_id == account_b.id for p in products_a)
        
        # Consultar productos de tenant B
        stmt = select(Product).where(Product.account_id == account_b.id)
        result = await db_session.execute(stmt)
        products_b = result.scalars().all()
        
        # Verificar que solo se obtienen productos de tenant B
        assert all(p.account_id == account_b.id for p in products_b)
        assert not any(p.account_id == account_a.id for p in products_b)
    
    async def test_cascade_deletion(
        self,
        db_session,
        test_accounts,
        test_products,
        test_end_users,
        test_interactions
    ):
        """Test para verificar la eliminación en cascada."""
        account = test_accounts[0]
        
        # Eliminar la cuenta
        await db_session.delete(account)
        await db_session.commit()
        
        # Verificar que se eliminaron todos los recursos asociados
        # Productos
        stmt = select(Product).where(Product.account_id == account.id)
        result = await db_session.execute(stmt)
        products = result.scalars().all()
        assert len(products) == 0
        
        # Usuarios finales
        stmt = select(EndUser).where(EndUser.account_id == account.id)
        result = await db_session.execute(stmt)
        users = result.scalars().all()
        assert len(users) == 0
        
        # Interacciones
        stmt = select(Interaction).where(Interaction.account_id == account.id)
        result = await db_session.execute(stmt)
        interactions = result.scalars().all()
        assert len(interactions) == 0
    
    async def test_transaction_rollback(
        self,
        db_session,
        test_accounts
    ):
        """Test para verificar el rollback de transacciones."""
        account = test_accounts[0]
        
        try:
            # Iniciar transacción
            async with db_session.begin():
                # Crear producto válido
                product1 = Product(
                    name="Valid Product",
                    description="Test",
                    price=99.99,
                    category="test",
                    account_id=account.id
                )
                db_session.add(product1)
                
                # Intentar crear producto inválido (sin nombre)
                product2 = Product(
                    description="Test",
                    price=99.99,
                    category="test",
                    account_id=account.id
                )
                db_session.add(product2)
                
                # Esto debería fallar y hacer rollback
                await db_session.flush()
        except Exception:
            pass
        
        # Verificar que ningún producto se guardó
        stmt = select(Product).where(Product.account_id == account.id)
        result = await db_session.execute(stmt)
        products = result.scalars().all()
        assert len(products) == 0 