from abc import ABC, abstractmethod
from typing import Dict, Any, <PERSON>ple
import pandas as pd
from src.utils.base_logger import log_info, log_error


class BaseTrainer(ABC):
    """Interfaz base para los trainers de modelos"""

    def __init__(self, account_id: int):
        self.account_id = account_id
        self.model = None

    @abstractmethod
    def prepare_data(self, data: pd.DataFrame) -> <PERSON><PERSON>:
        """Prepara los datos para el entrenamiento"""
        pass

    @abstractmethod
    def train_model(
        self, data: pd.DataFrame, params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Entrena el modelo con los datos proporcionados"""
        pass

    @abstractmethod
    def _calculate_metrics(self, test_data: pd.DataFrame, **kwargs) -> Dict[str, float]:
        """Calcula métricas de rendimiento del modelo"""
        pass

    def validate_data(self, data: pd.DataFrame) -> bool:
        """Valida que los datos cumplan con los requisitos mínimos"""
        try:
            if data.empty:
                log_error(
                    f"No hay datos para entrenar el modelo para la cuenta {self.account_id}"
                )
                return False

            required_columns = self.get_required_columns()
            missing_columns = [
                col for col in required_columns if col not in data.columns
            ]

            if missing_columns:
                log_error(f"Faltan columnas requeridas: {missing_columns}")
                return False

            return True

        except Exception as e:
            log_error(f"Error validando datos: {str(e)}")
            return False

    @abstractmethod
    def get_required_columns(self) -> list:
        """Retorna las columnas requeridas para el entrenamiento"""
        pass

    def get_model_parameters(self) -> Dict[str, Any]:
        """Retorna los parámetros del modelo"""
        if not self.model:
            return {}
        return self.model.get_params()

    def save_model(self, path: str):
        """Guarda el modelo entrenado"""
        if not self.model:
            raise ValueError("No hay modelo para guardar")
        self.model.save(path)

    def load_model(self, path: str):
        """Carga un modelo guardado"""
        self.model.load(path)

    async def train(
        self, data: pd.DataFrame, params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Método principal para entrenar el modelo.

        Este método orquesta el proceso de entrenamiento completo:
        1. Validación de datos
        2. Preparación de datos
        3. Entrenamiento del modelo

        Args:
            data: DataFrame con los datos de entrenamiento
            params: Parámetros opcionales para el entrenamiento

        Returns:
            Diccionario con los artefactos del modelo entrenado
        """
        try:
            log_info(f"Iniciando entrenamiento para cuenta {self.account_id}")

            # Validar datos
            if not self.validate_data(data):
                raise ValueError(
                    f"Datos inválidos para entrenamiento de cuenta {self.account_id}"
                )

            # Entrenar modelo
            artifacts = self.train_model(data, params)

            log_info(f"Entrenamiento completado para cuenta {self.account_id}")
            return artifacts

        except Exception as e:
            log_error(f"Error en entrenamiento para cuenta {self.account_id}: {str(e)}")
            raise
