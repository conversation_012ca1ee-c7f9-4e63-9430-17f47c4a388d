#!/usr/bin/env python
"""
Script simplificado para iniciar la API de Rayuela.
Este script crea una API FastAPI básica sin cargar todos los módulos del proyecto.
"""

import os
import sys
import uvicorn
from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

# Crear la aplicación FastAPI
app = FastAPI(
    title="Rayuela API (Simplified)",
    description="API simplificada para pruebas locales",
    version="0.1.0",
    docs_url="/api/docs",
    openapi_url="/api/openapi.json",
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Modelos de datos
class Product(BaseModel):
    product_id: str
    name: str
    category: Optional[str] = None
    description: Optional[str] = None

class User(BaseModel):
    user_id: str

class Interaction(BaseModel):
    user_id: str
    product_id: str
    interaction_type: str

class Recommendation(BaseModel):
    product_id: str
    score: float
    name: Optional[str] = None
    category: Optional[str] = None

# Datos en memoria para pruebas
products = [
    {"product_id": "p1", "name": "Product 1", "category": "Category A", "description": "Description of product 1"},
    {"product_id": "p2", "name": "Product 2", "category": "Category B", "description": "Description of product 2"},
    {"product_id": "p3", "name": "Product 3", "category": "Category A", "description": "Description of product 3"},
]

users = [
    {"user_id": "u1"},
    {"user_id": "u2"},
    {"user_id": "u3"},
]

interactions = [
    {"user_id": "u1", "product_id": "p1", "interaction_type": "view"},
    {"user_id": "u1", "product_id": "p2", "interaction_type": "purchase"},
    {"user_id": "u2", "product_id": "p1", "interaction_type": "view"},
    {"user_id": "u2", "product_id": "p3", "interaction_type": "view"},
    {"user_id": "u3", "product_id": "p2", "interaction_type": "purchase"},
]

# Endpoints
@app.get("/health", tags=["Health"])
async def health_check():
    return {"status": "healthy"}

@app.get("/api/v1/products", response_model=List[Product], tags=["Products"])
async def get_products():
    return products

@app.post("/api/v1/products", response_model=Product, tags=["Products"])
async def create_product(product: Product):
    products.append(product.dict())
    return product

@app.get("/api/v1/users", response_model=List[User], tags=["Users"])
async def get_users():
    return users

@app.post("/api/v1/users", response_model=User, tags=["Users"])
async def create_user(user: User):
    users.append(user.dict())
    return user

@app.get("/api/v1/interactions", response_model=List[Interaction], tags=["Interactions"])
async def get_interactions():
    return interactions

@app.post("/api/v1/interactions", response_model=Interaction, tags=["Interactions"])
async def create_interaction(interaction: Interaction):
    interactions.append(interaction.dict())
    return interaction

@app.get("/api/v1/recommendations/personalized", response_model=List[Recommendation], tags=["Recommendations"])
async def get_personalized_recommendations(user_id: str, limit: int = 10):
    # Simulación simple de recomendaciones
    # En un sistema real, esto usaría un modelo de recomendación
    recommendations = []
    
    # Encontrar productos con los que el usuario no ha interactuado
    user_interactions = [i["product_id"] for i in interactions if i["user_id"] == user_id]
    unseen_products = [p for p in products if p["product_id"] not in user_interactions]
    
    # Si no hay productos sin interacciones, devolver todos los productos
    if not unseen_products:
        unseen_products = products
    
    # Crear recomendaciones con puntuaciones simuladas
    for i, product in enumerate(unseen_products[:limit]):
        recommendations.append({
            "product_id": product["product_id"],
            "name": product["name"],
            "category": product["category"],
            "score": 1.0 - (i * 0.1)  # Puntuación simulada
        })
    
    return recommendations

@app.get("/api/v1/recommendations/similar", response_model=List[Recommendation], tags=["Recommendations"])
async def get_similar_products(product_id: str, limit: int = 10):
    # Simulación simple de productos similares
    # En un sistema real, esto usaría un modelo de similitud
    
    # Encontrar el producto de referencia
    reference_product = next((p for p in products if p["product_id"] == product_id), None)
    if not reference_product:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Product with ID {product_id} not found"
        )
    
    # Encontrar productos de la misma categoría
    similar_products = [p for p in products if p["category"] == reference_product["category"] and p["product_id"] != product_id]
    
    # Si no hay productos similares, devolver productos aleatorios
    if not similar_products:
        similar_products = [p for p in products if p["product_id"] != product_id]
    
    # Crear recomendaciones con puntuaciones simuladas
    recommendations = []
    for i, product in enumerate(similar_products[:limit]):
        recommendations.append({
            "product_id": product["product_id"],
            "name": product["name"],
            "category": product["category"],
            "score": 1.0 - (i * 0.1)  # Puntuación simulada
        })
    
    return recommendations

@app.get("/api/v1/recommendations/popular", response_model=List[Recommendation], tags=["Recommendations"])
async def get_popular_products(limit: int = 10):
    # Simulación simple de productos populares
    # En un sistema real, esto se basaría en el número de interacciones
    
    # Contar interacciones por producto
    product_counts = {}
    for interaction in interactions:
        product_id = interaction["product_id"]
        product_counts[product_id] = product_counts.get(product_id, 0) + 1
    
    # Ordenar productos por número de interacciones
    sorted_products = sorted(products, key=lambda p: product_counts.get(p["product_id"], 0), reverse=True)
    
    # Crear recomendaciones
    recommendations = []
    for i, product in enumerate(sorted_products[:limit]):
        recommendations.append({
            "product_id": product["product_id"],
            "name": product["name"],
            "category": product["category"],
            "score": product_counts.get(product["product_id"], 0)  # Puntuación basada en el número de interacciones
        })
    
    return recommendations

if __name__ == "__main__":
    # Obtener puerto de las variables de entorno o usar 8001 por defecto
    port = int(os.environ.get("API_PORT", 8001))
    host = os.environ.get("API_HOST", "0.0.0.0")
    
    print(f"Starting API server on {host}:{port}")
    print(f"API documentation available at http://localhost:{port}/api/docs")
    
    uvicorn.run(app, host=host, port=port)
