#!/usr/bin/env python
"""
Script para analizar el rendimiento de consultas en tablas de alto volumen.
Utiliza EXPLAIN ANALYZE para evaluar el rendimiento y recomendar índices.

Uso:
    python -m scripts.analyze_query_performance --table interactions
    python -m scripts.analyze_query_performance --table audit_logs
    python -m scripts.analyze_query_performance --table products
    python -m scripts.analyze_query_performance --all
"""

import os
import sys
import asyncio
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from tabulate import tabulate

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.db.session import get_db
from src.utils.base_logger import logger
from src.core.config import settings


# Consultas comunes para cada tabla
COMMON_QUERIES = {
    "interactions": [
        {
            "name": "Interacciones recientes por cuenta",
            "query": """
                SELECT * FROM interactions 
                WHERE account_id = :account_id 
                ORDER BY timestamp DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1},
            "expected_index": "idx_interaction_account_timestamp",
        },
        {
            "name": "Interacciones por usuario y cuenta",
            "query": """
                SELECT * FROM interactions 
                WHERE account_id = :account_id AND end_user_id = :user_id 
                ORDER BY timestamp DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1, "user_id": 1},
            "expected_index": "idx_interaction_account_user",
        },
        {
            "name": "Interacciones por producto y cuenta",
            "query": """
                SELECT * FROM interactions 
                WHERE account_id = :account_id AND product_id = :product_id 
                ORDER BY timestamp DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1, "product_id": 1},
            "expected_index": "idx_interaction_account_product",
        },
        {
            "name": "Interacciones por tipo y cuenta",
            "query": """
                SELECT * FROM interactions 
                WHERE account_id = :account_id AND interaction_type = :interaction_type 
                ORDER BY timestamp DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1, "interaction_type": "VIEW"},
            "expected_index": "idx_interaction_account_type_timestamp",
        },
        {
            "name": "Interacciones por usuario, producto y cuenta",
            "query": """
                SELECT * FROM interactions 
                WHERE account_id = :account_id 
                AND end_user_id = :user_id 
                AND product_id = :product_id 
                ORDER BY timestamp DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1, "user_id": 1, "product_id": 1},
            "expected_index": "idx_interaction_account_user_product_timestamp",
        },
    ],
    "audit_logs": [
        {
            "name": "Logs de auditoría recientes por cuenta",
            "query": """
                SELECT * FROM audit_logs 
                WHERE account_id = :account_id 
                ORDER BY created_at DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1},
            "expected_index": "idx_audit_account_timestamp",
        },
        {
            "name": "Logs de auditoría por entidad y cuenta",
            "query": """
                SELECT * FROM audit_logs 
                WHERE account_id = :account_id 
                AND entity_type = :entity_type 
                AND entity_id = :entity_id 
                ORDER BY created_at DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1, "entity_type": "product", "entity_id": "1"},
            "expected_index": "idx_audit_account_entity_created",
        },
        {
            "name": "Logs de auditoría por usuario y cuenta",
            "query": """
                SELECT * FROM audit_logs 
                WHERE account_id = :account_id 
                AND performed_by = :performed_by 
                ORDER BY created_at DESC 
                LIMIT 100
            """,
            "params": {"account_id": 1, "performed_by": "<EMAIL>"},
            "expected_index": "idx_audit_account_performer_created",
        },
    ],
    "products": [
        {
            "name": "Productos por cuenta",
            "query": """
                SELECT * FROM products 
                WHERE account_id = :account_id 
                LIMIT 100
            """,
            "params": {"account_id": 1},
            "expected_index": "PRIMARY KEY (account_id, id)",
        },
        {
            "name": "Productos por categoría y cuenta",
            "query": """
                SELECT * FROM products 
                WHERE account_id = :account_id 
                AND category = :category 
                LIMIT 100
            """,
            "params": {"account_id": 1, "category": "electronics"},
            "expected_index": "idx_product_account_category",
        },
        {
            "name": "Productos por nombre",
            "query": """
                SELECT * FROM products 
                WHERE account_id = :account_id 
                AND name LIKE :name 
                LIMIT 100
            """,
            "params": {"account_id": 1, "name": "%smartphone%"},
            "expected_index": "idx_product_name",
        },
    ],
}


async def analyze_query(
    db: AsyncSession, query_info: Dict[str, Any]
) -> Dict[str, Any]:
    """Analiza una consulta usando EXPLAIN ANALYZE y devuelve los resultados."""
    query = query_info["query"]
    params = query_info["params"]
    
    # Construir la consulta EXPLAIN ANALYZE
    explain_query = f"EXPLAIN ANALYZE {query}"
    
    try:
        # Ejecutar EXPLAIN ANALYZE
        result = await db.execute(text(explain_query), params)
        explain_output = [row[0] for row in result]
        
        # Analizar el resultado para determinar qué índices se están utilizando
        used_indexes = []
        scan_type = "Sequential Scan"  # Por defecto, asumimos el peor caso
        estimated_cost = None
        actual_time = None
        
        for line in explain_output:
            if "Index Scan" in line or "Index Only Scan" in line:
                scan_type = "Index Scan"
                # Extraer el nombre del índice
                index_start = line.find("using") + 6 if "using" in line.lower() else -1
                if index_start > 0:
                    index_end = line.find(" ", index_start)
                    if index_end > 0:
                        used_indexes.append(line[index_start:index_end].strip())
            
            if "cost=" in line:
                cost_start = line.find("cost=") + 5
                cost_end = line.find(" ", cost_start)
                if cost_end > 0:
                    cost_range = line[cost_start:cost_end]
                    estimated_cost = cost_range.split("..")[-1]
            
            if "actual time=" in line:
                time_start = line.find("actual time=") + 12
                time_end = line.find(" ", time_start)
                if time_end > 0:
                    time_range = line[time_start:time_end]
                    actual_time = time_range.split("..")[-1]
        
        # Determinar si se está utilizando el índice esperado
        expected_index = query_info.get("expected_index", "")
        is_using_expected_index = any(expected_index in idx for idx in used_indexes) if used_indexes else False
        
        return {
            "name": query_info["name"],
            "scan_type": scan_type,
            "used_indexes": used_indexes,
            "expected_index": expected_index,
            "is_using_expected_index": is_using_expected_index,
            "estimated_cost": estimated_cost,
            "actual_time": actual_time,
            "explain_output": explain_output,
        }
    except Exception as e:
        logger.error(f"Error analyzing query: {e}")
        return {
            "name": query_info["name"],
            "error": str(e),
            "explain_output": [],
        }


async def analyze_table_queries(db: AsyncSession, table: str) -> List[Dict[str, Any]]:
    """Analiza todas las consultas comunes para una tabla específica."""
    if table not in COMMON_QUERIES:
        logger.error(f"No common queries defined for table: {table}")
        return []
    
    results = []
    for query_info in COMMON_QUERIES[table]:
        logger.info(f"Analyzing query: {query_info['name']}")
        result = await analyze_query(db, query_info)
        results.append(result)
    
    return results


def print_analysis_results(results: List[Dict[str, Any]]):
    """Imprime los resultados del análisis en formato tabular."""
    if not results:
        print("No results to display.")
        return
    
    # Preparar datos para la tabla
    table_data = []
    for result in results:
        if "error" in result:
            table_data.append([
                result["name"],
                "ERROR",
                "N/A",
                "N/A",
                "N/A",
                result["error"],
            ])
        else:
            table_data.append([
                result["name"],
                result["scan_type"],
                ", ".join(result["used_indexes"]) if result["used_indexes"] else "None",
                result["expected_index"],
                "✓" if result["is_using_expected_index"] else "✗",
                f"{result['estimated_cost']} / {result['actual_time']}",
            ])
    
    # Imprimir la tabla
    headers = ["Query", "Scan Type", "Used Indexes", "Expected Index", "Using Expected", "Cost/Time"]
    print(tabulate(table_data, headers=headers, tablefmt="grid"))
    
    # Imprimir recomendaciones
    print("\nRecomendaciones:")
    for result in results:
        if "error" in result:
            continue
        
        if not result["is_using_expected_index"]:
            print(f"- La consulta '{result['name']}' no está utilizando el índice esperado ({result['expected_index']}). "
                  f"Está utilizando: {', '.join(result['used_indexes']) if result['used_indexes'] else 'ningún índice'}.")
            
            # Sugerir un nuevo índice si no se está utilizando ninguno
            if not result["used_indexes"] or "Sequential Scan" in result["scan_type"]:
                print(f"  Considere revisar o crear el índice {result['expected_index']}.")
    
    # Imprimir EXPLAIN ANALYZE detallado para cada consulta
    for result in results:
        if "error" in result:
            continue
        
        print(f"\nEXPLAIN ANALYZE detallado para '{result['name']}':")
        for line in result["explain_output"]:
            print(f"  {line}")


async def main():
    parser = argparse.ArgumentParser(description="Analyze query performance on high-volume tables")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--table", choices=["interactions", "audit_logs", "products"], help="Table to analyze")
    group.add_argument("--all", action="store_true", help="Analyze all tables")
    
    args = parser.parse_args()
    
    tables_to_analyze = list(COMMON_QUERIES.keys()) if args.all else [args.table]
    
    async for db in get_db():
        for table in tables_to_analyze:
            logger.info(f"Analyzing queries for table: {table}")
            results = await analyze_table_queries(db, table)
            
            print(f"\n{'='*80}")
            print(f"Análisis de rendimiento para la tabla: {table}")
            print(f"{'='*80}")
            print_analysis_results(results)
            print(f"{'='*80}\n")
        
        # Solo necesitamos una iteración
        break


if __name__ == "__main__":
    asyncio.run(main())
