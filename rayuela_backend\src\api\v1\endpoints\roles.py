from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from src.core.deps import (
    get_current_account,
    get_current_admin_user,
    get_permission_service,
    require_resource_permission,
)
from src.db import models, schemas
from src.db.session import get_db
from src.db.repositories.auth import RoleRepository, SystemUserRoleRepository
from src.services.permission_service import PermissionService
from src.utils.base_logger import log_info, log_error

router = APIRouter()


@router.get("/", response_model=List[schemas.Role])
async def list_roles(
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    List all roles available for the current account.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        roles = await role_repo.get_all()
        return roles
    except Exception as e:
        log_error(f"Error listing roles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error listing roles"
        )


@router.get("/{role_id}", response_model=schemas.Role)
async def get_role(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    Get a specific role by ID.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        role = await role_repo.get_by_id(role_id)
        
        if not role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
            
        return role
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting role {role_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting role"
        )


@router.post("/", response_model=schemas.Role)
async def create_role(
    role: schemas.RoleCreate,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    Create a new role.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        
        # Check if role with same name already exists
        existing_role = await role_repo.get_by_name(role.name)
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Role with name '{role.name}' already exists"
            )
        
        # Create the role
        db_role = await role_repo.create(role)
        
        # Log the action
        log_info(f"Role '{role.name}' created by user {current_user.id}")
        
        return db_role
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error creating role: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating role"
        )


@router.put("/{role_id}", response_model=schemas.Role)
async def update_role(
    role_id: int,
    role_update: schemas.RoleCreate,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    Update an existing role.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        
        # Check if role exists
        existing_role = await role_repo.get_by_id(role_id)
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
        
        # Check if another role with the same name exists
        if role_update.name != existing_role.name:
            role_with_same_name = await role_repo.get_by_name(role_update.name)
            if role_with_same_name and role_with_same_name.id != role_id:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=f"Another role with name '{role_update.name}' already exists"
                )
        
        # Update the role
        updated_role = await role_repo.update(role_id, role_update.model_dump())
        
        # Log the action
        log_info(f"Role {role_id} updated by user {current_user.id}")
        
        return updated_role
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error updating role {role_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating role"
        )


@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    Delete a role.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        
        # Check if role exists
        existing_role = await role_repo.get_by_id(role_id)
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
        
        # Delete the role
        await role_repo.delete(role_id)
        
        # Log the action
        log_info(f"Role {role_id} deleted by user {current_user.id}")
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error deleting role {role_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting role"
        )


@router.get("/{role_id}/permissions", response_model=List[schemas.Permission])
async def get_role_permissions(
    role_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
    permission_service: PermissionService = Depends(get_permission_service),
):
    """
    Get all permissions assigned to a role.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        
        # Check if role exists
        existing_role = await role_repo.get_by_id(role_id)
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
        
        # Get role permissions
        permissions = await permission_service.get_role_permissions(role_id)
        
        return permissions
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting permissions for role {role_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting role permissions"
        )


@router.post("/{role_id}/permissions/{permission_id}", status_code=status.HTTP_200_OK)
async def assign_permission_to_role(
    role_id: int,
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
    permission_service: PermissionService = Depends(get_permission_service),
):
    """
    Assign a permission to a role.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        
        # Check if role exists
        existing_role = await role_repo.get_by_id(role_id)
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
        
        # Check if permission exists
        from src.db.repositories.auth import PermissionRepository
        permission_repo = PermissionRepository(db, account_id=current_account.account_id)
        existing_permission = await permission_repo.get_by_id(permission_id)
        if not existing_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Permission with ID {permission_id} not found"
            )
        
        # Assign permission to role
        await permission_service.assign_permission_to_role(role_id, permission_id)
        
        # Log the action
        log_info(f"Permission {permission_id} assigned to role {role_id} by user {current_user.id}")
        
        return {"message": "Permission assigned to role successfully"}
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error assigning permission {permission_id} to role {role_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error assigning permission to role"
        )


@router.delete("/{role_id}/permissions/{permission_id}", status_code=status.HTTP_204_NO_CONTENT)
async def remove_permission_from_role(
    role_id: int,
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
    permission_service: PermissionService = Depends(get_permission_service),
):
    """
    Remove a permission from a role.
    
    Requires admin privileges.
    """
    try:
        role_repo = RoleRepository(db, account_id=current_account.account_id)
        
        # Check if role exists
        existing_role = await role_repo.get_by_id(role_id)
        if not existing_role:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Role with ID {role_id} not found"
            )
        
        # Check if permission exists
        from src.db.repositories.auth import PermissionRepository
        permission_repo = PermissionRepository(db, account_id=current_account.account_id)
        existing_permission = await permission_repo.get_by_id(permission_id)
        if not existing_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Permission with ID {permission_id} not found"
            )
        
        # Remove permission from role
        await permission_service.remove_permission_from_role(role_id, permission_id)
        
        # Log the action
        log_info(f"Permission {permission_id} removed from role {role_id} by user {current_user.id}")
        
        return None
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error removing permission {permission_id} from role {role_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error removing permission from role"
        )
