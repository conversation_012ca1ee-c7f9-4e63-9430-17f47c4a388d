import numpy as np
import time
from typing import List, Dict, Set, Tuple, Optional, Sequence, Any, Union, Callable
from collections import Counter
from sklearn.metrics import (
    ndcg_score,
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
)
from scipy.spatial.distance import pdist, squareform
from src.utils.base_logger import log_info, log_error
from sklearn.model_selection import KFold


def calculate_metrics(y_true: np.ndarray, y_pred: np.ndarray) -> Dict[str, float]:
    """
    Calcula métricas de evaluación para un modelo de recomendación.

    Args:
        y_true: Valores reales (ground truth)
        y_pred: Valores predichos por el modelo

    Returns:
        Dict con las métricas calculadas (accuracy, precision, recall, f1)
    """
    return {
        "accuracy": float(accuracy_score(y_true, y_pred)),
        "precision": float(precision_score(y_true, y_pred, zero_division=0)),
        "recall": float(recall_score(y_true, y_pred, zero_division=0)),
        "f1": float(f1_score(y_true, y_pred, zero_division=0)),
    }


def cross_validate(
    X: np.ndarray, y: np.ndarray, model_func: Callable, n_splits: int = 5
) -> Dict[str, float]:
    """
    Realiza validación cruzada para un modelo de recomendación.

    Args:
        X: Características de entrada
        y: Etiquetas objetivo
        model_func: Función que implementa el modelo a evaluar
        n_splits: Número de divisiones para la validación cruzada

    Returns:
        Dict con las métricas promedio de la validación cruzada
    """
    kf = KFold(n_splits=n_splits, shuffle=True, random_state=42)
    metrics_list = []

    for train_index, test_index in kf.split(X):
        X_train, X_test = X[train_index], X[test_index]
        y_train, y_test = y[train_index], y[test_index]

        y_pred = model_func(X_train, y_train, X_test)
        metrics = calculate_metrics(y_test, y_pred)
        metrics_list.append(metrics)

    # Calcular promedio de métricas
    avg_metrics = {}
    for metric in metrics_list[0].keys():
        avg_metrics[metric] = sum(m[metric] for m in metrics_list) / len(metrics_list)

    return avg_metrics


async def evaluate_recommendations(db_session, account_id: int) -> Dict[str, float]:
    """
    Evalúa las recomendaciones existentes para una cuenta.

    Args:
        db_session: Sesión de base de datos
        account_id: ID de la cuenta

    Returns:
        Dict con métricas de evaluación
    """
    from src.db.models import Recommendation, Interaction
    from src.db.enums import InteractionType

    try:
        # Obtener recomendaciones
        recommendations = await db_session.execute(
            "SELECT end_user_id, product_id, score FROM recommendation WHERE account_id = :account_id",
            {"account_id": account_id},
        )
        recommendations = recommendations.fetchall()

        # Obtener interacciones reales con tipo
        interactions = await db_session.execute(
            "SELECT end_user_id, product_id, interaction_type FROM interaction WHERE account_id = :account_id",
            {"account_id": account_id},
        )
        interactions = interactions.fetchall()

        # Preparar datos para evaluación
        true_interactions = set(
            (int(i.end_user_id), int(i.product_id)) for i in interactions
        )

        # Generar diccionario de tipos de interacción
        interaction_types = {}
        for i in interactions:
            interaction_types[(int(i.end_user_id), int(i.product_id))] = str(
                i.interaction_type
            ).lower()

        # Generar diccionarios necesarios para RecommendationEvaluator
        user_history = {}
        for user_id, product_id in true_interactions:
            if user_id not in user_history:
                user_history[user_id] = set()
            user_history[user_id].add(product_id)

        # Obtener categorías de productos
        products = await db_session.execute(
            "SELECT id, category FROM product WHERE account_id = :account_id",
            {"account_id": account_id},
        )
        products = products.fetchall()
        item_categories = {p.id: p.category for p in products}

        # Convertir recomendaciones al formato esperado
        predicted_recommendations = [
            {"user_id": r.end_user_id, "item_id": r.product_id, "score": r.score}
            for r in recommendations
        ]

        # Evaluar usando RecommendationEvaluator
        evaluator = RecommendationEvaluator(k=10)
        metrics = evaluator.evaluate(
            true_interactions=true_interactions,
            predicted_recommendations=predicted_recommendations,
            user_history=user_history,
            item_categories=item_categories,
            total_items=len(products),
            interaction_types=interaction_types,
        )

        return metrics

    except Exception as e:
        log_error(f"Error evaluando recomendaciones: {str(e)}")
        return {
            "precision": 0.0,
            "recall": 0.0,
            "ndcg": 0.0,
            "test_conversion_rate": 0.0,
            "error": str(e),
        }


async def calculate_diversity(db_session, account_id: int) -> float:
    """
    Calcula la diversidad de las recomendaciones para una cuenta.

    Args:
        db_session: Sesión de base de datos
        account_id: ID de la cuenta

    Returns:
        Valor de diversidad entre 0 y 1
    """
    from src.db.models import Recommendation, Product

    try:
        # Obtener recomendaciones
        recommendations = await db_session.execute(
            "SELECT product_id FROM recommendation WHERE account_id = :account_id",
            {"account_id": account_id},
        )
        product_ids = [r.product_id for r in recommendations.fetchall()]

        if not product_ids:
            return 0.0

        # Obtener categorías de productos recomendados
        products = await db_session.execute(
            "SELECT id, category FROM product WHERE id IN :product_ids AND account_id = :account_id",
            {
                "product_ids": (
                    tuple(product_ids) if len(product_ids) > 1 else product_ids + [-1]
                ),
                "account_id": account_id,
            },
        )
        products = products.fetchall()

        # Calcular diversidad de categorías
        categories = [p.category for p in products]
        category_counter = Counter(categories)

        # Usar el índice de diversidad (1 - concentración)
        num_categories = len(category_counter)
        if num_categories <= 1:
            return 0.0

        entropy = sum(
            -(count / len(categories)) * np.log2(count / len(categories))
            for count in category_counter.values()
        )
        max_entropy = np.log2(num_categories)  # Máxima entropía posible

        # Normalizar entre 0 y 1
        return entropy / max_entropy if max_entropy > 0 else 0.0

    except Exception as e:
        log_error(f"Error calculando diversidad: {str(e)}")
        return 0.0


async def calculate_novelty(db_session, account_id: int) -> float:
    """
    Calcula la novedad de las recomendaciones para una cuenta.

    Args:
        db_session: Sesión de base de datos
        account_id: ID de la cuenta

    Returns:
        Valor de novedad entre 0 y 1
    """
    from src.db.models import Recommendation, Interaction

    try:
        # Obtener todas las interacciones
        interactions = await db_session.execute(
            "SELECT end_user_id, product_id FROM interaction WHERE account_id = :account_id",
            {"account_id": account_id},
        )
        interactions = interactions.fetchall()

        # Obtener recomendaciones
        recommendations = await db_session.execute(
            "SELECT end_user_id, product_id FROM recommendation WHERE account_id = :account_id",
            {"account_id": account_id},
        )
        recommendations = recommendations.fetchall()

        if not recommendations:
            return 0.0

        # Crear historial de usuario
        user_history = {}
        for i in interactions:
            if i.end_user_id not in user_history:
                user_history[i.end_user_id] = set()
            user_history[i.end_user_id].add(i.product_id)

        # Calcular novedad
        novelty_scores = []
        for r in recommendations:
            # Si el usuario no tiene historial, la novedad es 1
            if r.end_user_id not in user_history:
                novelty_scores.append(1.0)
                continue

            # Si el producto ya está en el historial del usuario, novedad = 0
            # De lo contrario, novedad = 1
            novelty = 0.0 if r.product_id in user_history[r.end_user_id] else 1.0
            novelty_scores.append(novelty)

        # Promedio de novedad
        return sum(novelty_scores) / len(novelty_scores) if novelty_scores else 0.0

    except Exception as e:
        log_error(f"Error calculando novedad: {str(e)}")
        return 0.0


class RecommendationEvaluator:
    """Evaluador de recomendaciones con múltiples métricas"""

    def __init__(self, k: int = 10, total_catalog_size: int = 1000):
        """
        Inicializa el evaluador de recomendaciones.

        Args:
            k: Número de recomendaciones a considerar para las métricas
            total_catalog_size: Tamaño total del catálogo (para métricas de cobertura)
        """
        self.k = k
        self.total_catalog_size = total_catalog_size
        self.start_time = None
        self.end_time = None
        self.memory_usage = None

    def evaluate(
        self,
        true_interactions: Set[Tuple[int, int]],
        predicted_recommendations: Sequence[Dict[str, Any]],
        user_history: Dict[int, Set[int]],
        item_categories: Dict[int, str],
        total_items: Optional[int] = None,
        interaction_types: Optional[Dict[Tuple[int, int], str]] = None,
    ) -> Dict[str, float]:
        """
        Evalúa las recomendaciones usando múltiples métricas.

        Args:
            true_interactions: Conjunto de tuplas (user_id, item_id) con interacciones reales
            predicted_recommendations: Lista de diccionarios con recomendaciones
            user_history: Diccionario de usuario_id a conjunto de items con los que ha interactuado
            item_categories: Diccionario mapping item_id a su categoría
            total_items: Número total de items en el catálogo (opcional)
            interaction_types: Diccionario de (user_id, item_id) a tipo de interacción
        """
        try:
            # Iniciar medición de tiempo
            self.start_time = time.time()

            # Actualizar tamaño del catálogo si se proporciona
            if total_items:
                self.total_catalog_size = total_items

            metrics = {}

            # Extraer item_ids de las recomendaciones para facilitar cálculos
            item_ids = [
                rec.get("item_id")
                for rec in predicted_recommendations
                if "item_id" in rec
            ]

            # Obtener usuarios únicos de las recomendaciones
            user_ids = set()
            for rec in predicted_recommendations:
                if "user_id" in rec:
                    user_ids.add(rec["user_id"])

            # Métricas de ranking
            metrics["ndcg"] = self._calculate_ndcg(
                true_interactions, predicted_recommendations
            )
            metrics["map"] = self._calculate_map(true_interactions, item_ids)

            # Métricas de cobertura
            metrics["catalog_coverage"] = self._calculate_catalog_coverage(
                item_ids, self.total_catalog_size
            )
            metrics["user_coverage"] = self._calculate_user_coverage(
                user_ids, len(user_history) if user_history else 1
            )

            # Métricas de diversidad
            metrics["category_diversity"] = self._calculate_category_diversity(
                item_ids, item_categories
            )
            metrics["item_diversity"] = self._calculate_item_diversity(item_ids)

            # Métricas de serendipidad
            metrics["serendipity"] = self._calculate_serendipity(
                item_ids, user_history, item_categories
            )

            # Métricas de novedad
            metrics["novelty"] = self._calculate_novelty(item_ids, user_history)

            # NUEVAS MÉTRICAS PROXY DE NEGOCIO

            # Tasa de conversión en test set (proxy de negocio)
            metrics["test_conversion_rate"] = self._calculate_test_conversion_rate(
                predicted_recommendations, true_interactions, interaction_types
            )

            # Tasa de engagement estimada
            metrics["estimated_engagement_rate"] = self._calculate_estimated_engagement(
                predicted_recommendations, true_interactions, interaction_types
            )

            # ROI estimado basado en métricas históricas si se proporcionan tipos de interacción
            metrics["estimated_roi"] = self._calculate_estimated_roi(
                predicted_recommendations, true_interactions, interaction_types
            )

            # Valor de vida del cliente estimado (CLV)
            metrics["estimated_customer_value"] = (
                self._calculate_estimated_customer_value(
                    predicted_recommendations, user_history
                )
            )

            # Finalizar medición de tiempo
            self.end_time = time.time()
            metrics["evaluation_time"] = self.end_time - self.start_time

            # Intentar obtener uso de memoria
            try:
                import psutil
                import os

                process = psutil.Process(os.getpid())
                self.memory_usage = process.memory_info().rss / 1024 / 1024  # MB
                metrics["memory_usage_mb"] = self.memory_usage
            except ImportError:
                # Si psutil no está disponible, omitir esta métrica
                pass

            return metrics

        except Exception as e:
            log_error(f"Error en evaluación de recomendaciones: {str(e)}")
            return {}

    def _calculate_ndcg(
        self,
        true_interactions: Set[Tuple[int, int]],
        predicted_recommendations: Sequence[Dict[str, Any]],
    ) -> float:
        """
        Calcula el NDCG@K (Normalized Discounted Cumulative Gain)

        Esta métrica evalúa la calidad del ranking, dando más peso a los aciertos
        en posiciones más altas.
        """
        try:
            # Extraer los item_ids de las recomendaciones
            item_ids = [
                rec.get("item_id")
                for rec in predicted_recommendations
                if "item_id" in rec
            ]
            if not item_ids:
                return 0.0

            # Obtener el user_id de la primera recomendación (asumimos que todas son para el mismo usuario)
            user_id = None
            for rec in predicted_recommendations:
                if "user_id" in rec:
                    user_id = rec.get("user_id")
                    break

            if user_id is None:
                return 0.0

            # Convertir a formato requerido por sklearn
            y_true = [
                [
                    1 if (user_id, item_id) in true_interactions else 0
                    for item_id in item_ids[: self.k]
                ]
            ]
            y_score = [[1.0 / (i + 1) for i in range(len(item_ids[: self.k]))]]

            # Convertir a arrays numpy para evitar problemas de tipo
            y_true_np = np.array(y_true)
            y_score_np = np.array(y_score)

            if y_true_np.size == 0 or y_score_np.size == 0:
                return 0.0

            return float(ndcg_score(y_true_np, y_score_np))
        except Exception as e:
            log_error(f"Error calculando NDCG: {str(e)}")
            return 0.0

    def _calculate_map(
        self,
        true_interactions: Set[Tuple[int, int]],
        item_ids: Sequence[Union[int, Any]],
    ) -> float:
        """
        Calcula el Mean Average Precision@K

        Esta métrica evalúa la precisión promedio en diferentes puntos de corte
        del ranking.
        """
        try:
            if not item_ids:
                return 0.0

            # Obtener el user_id de las interacciones (asumimos que todas son para el mismo usuario)
            user_ids = set(user_id for user_id, _ in true_interactions)
            if not user_ids:
                return 0.0

            user_id = next(iter(user_ids))

            ap = 0.0
            hits = 0

            for i, item_id in enumerate(item_ids[: self.k]):
                if (user_id, item_id) in true_interactions:
                    hits += 1
                    ap += hits / (i + 1)

            return (
                ap / min(len(true_interactions), self.k) if true_interactions else 0.0
            )
        except Exception as e:
            log_error(f"Error calculando MAP: {str(e)}")
            return 0.0

    def _calculate_catalog_coverage(
        self, item_ids: Sequence[Union[int, Any]], total_catalog_size: int
    ) -> float:
        """
        Calcula la cobertura del catálogo

        Esta métrica evalúa qué porcentaje del catálogo total se está recomendando.
        Un valor alto indica que el sistema recomienda una variedad amplia de productos.
        """
        try:
            if not item_ids or total_catalog_size <= 0:
                return 0.0

            unique_items = len(set(item_ids))
            return unique_items / total_catalog_size
        except Exception as e:
            log_error(f"Error calculando cobertura de catálogo: {str(e)}")
            return 0.0

    def _calculate_user_coverage(self, user_ids: Set[int], total_users: int) -> float:
        """
        Calcula la cobertura de usuarios

        Esta métrica evalúa qué porcentaje de usuarios reciben recomendaciones.
        Un valor alto indica que el sistema puede generar recomendaciones para
        la mayoría de los usuarios.
        """
        try:
            if not user_ids or total_users <= 0:
                return 0.0

            return len(user_ids) / total_users
        except Exception as e:
            log_error(f"Error calculando cobertura de usuarios: {str(e)}")
            return 0.0

    def _calculate_category_diversity(
        self, item_ids: Sequence[Union[int, Any]], item_categories: Dict[int, str]
    ) -> float:
        """
        Calcula la diversidad de categorías

        Esta métrica evalúa qué tan variadas son las categorías de los productos
        recomendados. Un valor alto indica que el sistema recomienda productos
        de diferentes categorías.
        """
        try:
            if not item_ids:
                return 0.0

            # Obtener categorías de los items recomendados
            categories = [
                item_categories.get(item_id)
                for item_id in item_ids
                if item_id in item_categories
            ]

            if not categories:
                return 0.0

            # Calcular diversidad como proporción de categorías únicas
            unique_categories = len(set(categories))

            # Calcular el índice de Gini para medir la distribución de categorías
            category_counts = Counter(categories)
            total_count = len(categories)

            # Normalizar los conteos
            category_probs = [count / total_count for count in category_counts.values()]

            # Calcular el índice de Gini (1 - suma de probabilidades al cuadrado)
            gini_index = 1 - sum(p * p for p in category_probs)

            # Combinar ambas métricas (proporción de categorías únicas y distribución)
            diversity = 0.5 * (unique_categories / len(categories)) + 0.5 * gini_index

            return diversity
        except Exception as e:
            log_error(f"Error calculando diversidad de categorías: {str(e)}")
            return 0.0

    def _calculate_item_diversity(self, item_ids: Sequence[Union[int, Any]]) -> float:
        """
        Calcula la diversidad de items

        Esta métrica evalúa qué tan variados son los items recomendados.
        Un valor alto indica que el sistema recomienda productos diversos.
        """
        try:
            if not item_ids:
                return 0.0

            # Calcular la diversidad usando el índice de Gini
            item_counts = Counter(item_ids)
            total_count = len(item_ids)

            # Normalizar los conteos
            item_probs = [count / total_count for count in item_counts.values()]

            # Calcular el índice de Gini (1 - suma de probabilidades al cuadrado)
            gini_index = 1 - sum(p * p for p in item_probs)

            return gini_index
        except Exception as e:
            log_error(f"Error calculando diversidad de items: {str(e)}")
            return 0.0

    def _calculate_novelty(
        self,
        item_ids: Sequence[Union[int, Any]],
        user_history: Dict[int, Set[int]],
        item_popularity: Optional[Dict[int, int]] = None,
    ) -> float:
        """
        Calcula la novedad de las recomendaciones basada en popularidad inversa.

        Esta métrica evalúa qué tan novedosos son los items recomendados.
        Un valor alto indica que el sistema recomienda productos menos populares
        o menos conocidos, lo que puede ayudar a los usuarios a descubrir nuevos items.

        Args:
            item_ids: IDs de los items recomendados
            user_history: Historial de interacciones de los usuarios
            item_popularity: Diccionario opcional con la popularidad de cada item (conteo de interacciones)
        """
        try:
            if not item_ids:
                return 0.0

            # Si no se proporciona popularidad, calcularla a partir del historial
            if not item_popularity:
                item_popularity = {}
                # Contar apariciones de cada item en el historial de todos los usuarios
                for user_items in user_history.values():
                    for item in user_items:
                        item_popularity[item] = item_popularity.get(item, 0) + 1

            # Si no hay datos de popularidad, no podemos calcular novedad
            if not item_popularity:
                return 0.0

            # Calcular popularidad máxima para normalización
            max_popularity = max(item_popularity.values())
            if max_popularity == 0:
                return 0.0

            # Calcular novedad como promedio de popularidad inversa normalizada
            novelty_score = 0.0
            count = 0

            for item_id in item_ids:
                # Obtener popularidad del item (default a 1 si no está en el historial)
                popularity = item_popularity.get(item_id, 1)

                # Calcular popularidad inversa normalizada (1 - popularidad_normalizada)
                # Items menos populares tendrán valores más altos
                inverse_popularity = 1.0 - (popularity / max_popularity)
                novelty_score += inverse_popularity
                count += 1

            if count == 0:
                return 0.0

            return novelty_score / count
        except Exception as e:
            log_error(f"Error calculando novedad: {str(e)}")
            return 0.0

    def _calculate_serendipity(
        self,
        item_ids: Sequence[Union[int, Any]],
        user_history: Dict[int, Set[int]],
        item_categories: Dict[int, str],
    ) -> float:
        """
        Calcula la serendipidad de las recomendaciones

        Esta métrica evalúa qué tan sorprendentes pero relevantes son las recomendaciones.
        Un valor alto indica que el sistema recomienda productos que el usuario
        probablemente no habría descubierto por sí mismo.
        """
        try:
            if not item_ids or not user_history:
                return 0.0

            # Obtener el primer usuario (asumimos que todas las recomendaciones son para el mismo usuario)
            user_id = next(iter(user_history.keys())) if user_history else None
            if user_id is None:
                return 0.0

            user_items = user_history.get(user_id, set())
            if not user_items:
                return 0.0

            serendipity_score = 0.0
            total_items = len(item_ids)

            # Obtener categorías que el usuario ha interactuado
            user_categories = {
                item_categories.get(h_item)
                for h_item in user_items
                if h_item in item_categories
            }
            user_categories.discard(None)  # Eliminar None si existe

            for item_id in item_ids:
                # Item no está en el historial del usuario
                if item_id not in user_items:
                    # Item pertenece a una categoría diferente
                    item_category = item_categories.get(item_id)

                    if item_category and item_category not in user_categories:
                        serendipity_score += 1.0

            if total_items == 0:
                return 0.0

            return serendipity_score / total_items
        except Exception as e:
            log_error(f"Error calculando serendipidad: {str(e)}")
            return 0.0

    def _calculate_test_conversion_rate(
        self,
        predicted_recommendations: Sequence[Dict[str, Any]],
        true_interactions: Set[Tuple[int, int]],
        interaction_types: Optional[Dict[Tuple[int, int], str]] = None,
    ) -> float:
        """
        Calcula la tasa de conversión en el conjunto de prueba como métrica proxy de negocio.

        Esta métrica evalúa el porcentaje de recomendaciones que resultarían en una conversión
        (compra, suscripción, etc.) basado en los datos históricos de interacciones.

        Args:
            predicted_recommendations: Lista de diccionarios con recomendaciones
            true_interactions: Conjunto de tuplas (user_id, item_id) con interacciones reales
            interaction_types: Diccionario de (user_id, item_id) a tipo de interacción

        Returns:
            Tasa de conversión estimada (0-1)
        """
        try:
            if not predicted_recommendations:
                return 0.0

            # Si no se proporcionan tipos de interacción, considerar todas las interacciones como conversiones
            if interaction_types is None:
                # Contar cuántas recomendaciones coinciden con interacciones reales
                conversions = 0
                total_recommendations = 0

                for rec in predicted_recommendations:
                    if "user_id" in rec and "item_id" in rec:
                        user_id = rec["user_id"]
                        item_id = rec["item_id"]

                        total_recommendations += 1
                        if (user_id, item_id) in true_interactions:
                            conversions += 1

                return (
                    conversions / total_recommendations
                    if total_recommendations > 0
                    else 0.0
                )
            else:
                # Filtrar solo las interacciones que representan conversiones (compra, suscripción, etc.)
                conversion_interactions = set(
                    interaction
                    for interaction, type_ in interaction_types.items()
                    if type_ in {"purchase", "subscription", "conversion"}
                )

                conversions = 0
                total_recommendations = 0

                for rec in predicted_recommendations:
                    if "user_id" in rec and "item_id" in rec:
                        user_id = rec["user_id"]
                        item_id = rec["item_id"]

                        total_recommendations += 1
                        if (user_id, item_id) in conversion_interactions:
                            conversions += 1

                return (
                    conversions / total_recommendations
                    if total_recommendations > 0
                    else 0.0
                )
        except Exception as e:
            log_error(f"Error calculando tasa de conversión en test: {str(e)}")
            return 0.0

    def _calculate_estimated_engagement(
        self,
        predicted_recommendations: Sequence[Dict[str, Any]],
        true_interactions: Set[Tuple[int, int]],
        interaction_types: Optional[Dict[Tuple[int, int], str]] = None,
    ) -> float:
        """
        Calcula una tasa de engagement estimada basada en diferentes tipos de interacciones.

        Esta métrica considera diferentes pesos para distintos tipos de interacción:
        - Vistas/clics: peso bajo
        - Tiempo de visualización: peso medio
        - Compras/conversiones: peso alto

        Args:
            predicted_recommendations: Lista de diccionarios con recomendaciones
            true_interactions: Conjunto de tuplas (user_id, item_id) con interacciones reales
            interaction_types: Diccionario de (user_id, item_id) a tipo de interacción

        Returns:
            Tasa de engagement estimada (0-1)
        """
        try:
            if not predicted_recommendations:
                return 0.0

            # Si no hay información de tipos de interacción, usar una métrica simplificada
            if interaction_types is None:
                # Usar la misma que la tasa de conversión en test
                return self._calculate_test_conversion_rate(
                    predicted_recommendations, true_interactions, interaction_types
                )

            # Definir pesos para diferentes tipos de interacción
            interaction_weights = {
                "view": 0.2,
                "click": 0.5,
                "wishlist": 0.7,
                "cart": 0.8,
                "purchase": 1.0,
                "subscription": 1.0,
                "conversion": 1.0,
                # Valores predeterminados para otros tipos
                "default": 0.5,
            }

            weighted_engagement = 0.0
            total_weight_possible = 0.0

            for rec in predicted_recommendations:
                if "user_id" in rec and "item_id" in rec:
                    user_id = rec["user_id"]
                    item_id = rec["item_id"]
                    interaction = (user_id, item_id)

                    # La máxima interacción posible para cada recomendación
                    total_weight_possible += 1.0

                    # Si hay interacción y conocemos su tipo
                    if interaction in interaction_types:
                        interaction_type = interaction_types[interaction]
                        weight = interaction_weights.get(
                            interaction_type, interaction_weights["default"]
                        )
                        weighted_engagement += weight

            return (
                weighted_engagement / total_weight_possible
                if total_weight_possible > 0
                else 0.0
            )
        except Exception as e:
            log_error(f"Error calculando engagement estimado: {str(e)}")
            return 0.0

    def _calculate_estimated_roi(
        self,
        predicted_recommendations: Sequence[Dict[str, Any]],
        true_interactions: Set[Tuple[int, int]],
        interaction_types: Optional[Dict[Tuple[int, int], str]] = None,
    ) -> float:
        """
        Calcula un ROI estimado basado en la tasa de conversión y valor promedio estimado.

        Esta es una métrica proxy que intenta estimar el retorno de inversión
        de las recomendaciones implementadas.

        Args:
            predicted_recommendations: Lista de diccionarios con recomendaciones
            true_interactions: Conjunto de tuplas (user_id, item_id) con interacciones reales
            interaction_types: Diccionario de (user_id, item_id) a tipo de interacción

        Returns:
            ROI estimado (como multiplicador: 1.5 = 50% de retorno)
        """
        try:
            # Valor promedio estimado de una conversión (en unidades arbitrarias)
            avg_conversion_value = 100.0

            # Costo promedio estimado por recomendación (en las mismas unidades)
            avg_recommendation_cost = 1.0

            # Calcular tasa de conversión estimada
            conversion_rate = self._calculate_test_conversion_rate(
                predicted_recommendations, true_interactions, interaction_types
            )

            # Calcular ingresos estimados
            estimated_revenue = (
                len(predicted_recommendations) * conversion_rate * avg_conversion_value
            )

            # Calcular costos estimados
            estimated_cost = len(predicted_recommendations) * avg_recommendation_cost

            # Calcular ROI: (ingresos - costos) / costos
            if estimated_cost > 0:
                roi = (estimated_revenue - estimated_cost) / estimated_cost
                return max(
                    0.0, roi
                )  # Asegurar que no sea negativo para interpretabilidad

            return 0.0
        except Exception as e:
            log_error(f"Error calculando ROI estimado: {str(e)}")
            return 0.0

    def _calculate_estimated_customer_value(
        self,
        predicted_recommendations: Sequence[Dict[str, Any]],
        user_history: Dict[int, Set[int]],
    ) -> float:
        """
        Calcula el valor estimado del cliente basado en las recomendaciones y su historial.

        Esta métrica proxy intenta estimar cómo las recomendaciones pueden incrementar
        el valor de vida del cliente (CLV).

        Args:
            predicted_recommendations: Lista de diccionarios con recomendaciones
            user_history: Diccionario de usuario_id a conjunto de items con los que ha interactuado

        Returns:
            Valor estimado de incremento de CLV (0-1)
        """
        try:
            if not predicted_recommendations or not user_history:
                return 0.0

            # Factor de aumento de CLV basado en la tasa de recomendaciones nuevas vs repetidas
            total_recommendations = 0
            new_recommendations = 0

            for rec in predicted_recommendations:
                if "user_id" in rec and "item_id" in rec:
                    user_id = rec["user_id"]
                    item_id = rec["item_id"]

                    total_recommendations += 1

                    # Verificar si es una recomendación de algo nuevo para el usuario
                    if user_id in user_history and item_id not in user_history[user_id]:
                        new_recommendations += 1

            # Calcular ratio de nuevas recomendaciones
            new_recommendation_ratio = (
                new_recommendations / total_recommendations
                if total_recommendations > 0
                else 0.0
            )

            # Calcular valor estimado (factor arbitrario para demostración)
            # Mayor diversificación de catálogo = mayor valor del cliente a largo plazo
            customer_value_factor = 0.5 + (0.5 * new_recommendation_ratio)

            return customer_value_factor
        except Exception as e:
            log_error(f"Error calculando valor estimado del cliente: {str(e)}")
            return 0.0
