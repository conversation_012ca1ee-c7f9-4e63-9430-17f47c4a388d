"""
Utilidades para la gestión de tokens JWT.
"""

import secrets
from jose import JWTError, jwt
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from src.core.config import settings
from redis.asyncio import Redis
from src.core.redis_utils import get_redis


def create_access_token(
    data: dict, expires_delta: Optional[timedelta] = None
) -> str:
    """
    Crea un token de acceso JWT con datos adicionales.

    Args:
        data: Datos a incluir en el token
        expires_delta: Tiempo de expiración opcional

    Returns:
        Token JWT codificado
    """
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES
        )

    # Añadir jti (JWT ID) único
    to_encode.update(
        {
            "exp": expire,
            "iat": datetime.now(timezone.utc),
            "jti": secrets.token_urlsafe(32),
        }
    )

    try:
        encoded_jwt = jwt.encode(
            to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM
        )
        return encoded_jwt
    except JWTError as e:
        # Loggear el error sería bueno aquí
        raise ValueError(f"Error creating JWT token: {e}")


def decode_access_token(token: str) -> Optional[Dict[str, Any]]:
    """
    Decodifica un token de acceso JWT y retorna el payload si es válido y no expirado.

    Args:
        token: Token JWT a decodificar

    Returns:
        Payload del token o None si no es válido
    """
    try:
        # algorithmS, no algorithm
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )
        return payload
    except JWTError:  # Captura errores de firma, expiración, formato, etc.
        return None


async def is_token_revoked(token: str) -> bool:
    """
    Verifica si un token ha sido revocado usando Redis.

    Args:
        token: Token JWT a verificar

    Returns:
        True si el token está revocado, False en caso contrario
    """
    try:
        redis: Redis = await get_redis()
        payload = decode_access_token(token)
        if not payload or "jti" not in payload:
            return True

        # Verificar si el token está en la lista negra
        is_revoked = await redis.exists(f"revoked_token:{payload['jti']}")
        return bool(is_revoked)
    except Exception:
        return True


async def revoke_token(token: str) -> bool:
    """
    Revoca un token añadiéndolo a la lista negra en Redis.

    Args:
        token: Token JWT a revocar

    Returns:
        True si el token fue revocado correctamente, False en caso contrario
    """
    try:
        redis: Redis = await get_redis()
        payload = decode_access_token(token)
        if not payload or "jti" not in payload:
            return False

        # Calcular el tiempo restante del token
        exp = payload.get("exp")
        if not exp:
            return False

        ttl = exp - int(datetime.now(timezone.utc).timestamp())
        if ttl <= 0:
            return False

        # Añadir el token a la lista negra con el mismo TTL que el token
        await redis.setex(f"revoked_token:{payload['jti']}", ttl, "1")
        return True
    except Exception:
        return False
