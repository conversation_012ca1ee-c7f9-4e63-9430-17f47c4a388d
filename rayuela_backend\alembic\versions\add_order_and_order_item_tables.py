"""Add order and order_item tables

Revision ID: add_order_tables_001
Revises:
Create Date: 2024-12-19 10:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "add_order_tables_001"
down_revision: Union[str, None] = "add_email_verification_fields"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""

    # Create OrderStatus enum if it doesn't exist
    op.execute(
        """
        DO $$ BEGIN
            CREATE TYPE orderstatus AS ENUM ('PENDING', 'CONFIRMED', 'PROCESSING', 'SHIPPED', 'DELIVERED', 'CANCELED', 'REFUNDED');
        EXCEPTION
            WHEN duplicate_object THEN null;
        END $$;
    """
    )

    # Check if orders table exists, if not create it
    op.execute(
        """
        DO $$ BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'orders') THEN
                CREATE TABLE orders (
                    account_id INTEGER NOT NULL,
                    id SERIAL NOT NULL,
                    order_number VARCHAR(50) NOT NULL,
                    user_id VARCHAR(255) NOT NULL,
                    status orderstatus NOT NULL DEFAULT 'PENDING',
                    total_amount DECIMAL(10, 2) NOT NULL,
                    currency VARCHAR(3) NOT NULL DEFAULT 'USD',
                    shipping_address TEXT,
                    billing_address TEXT,
                    order_date TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
                    confirmed_at TIMESTAMP WITH TIME ZONE,
                    shipped_at TIMESTAMP WITH TIME ZONE,
                    delivered_at TIMESTAMP WITH TIME ZONE,
                    canceled_at TIMESTAMP WITH TIME ZONE,
                    notes TEXT,
                    payment_method VARCHAR(50),
                    tracking_number VARCHAR(100),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    PRIMARY KEY (account_id, id),
                    FOREIGN KEY (account_id) REFERENCES accounts(account_id),
                    FOREIGN KEY (user_id, account_id) REFERENCES end_users(user_id, account_id)
                );
                
                -- Create indexes
                CREATE INDEX IF NOT EXISTS idx_orders_account_id ON orders (account_id);
                CREATE INDEX IF NOT EXISTS idx_orders_user_id ON orders (user_id);
                CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status);
                CREATE INDEX IF NOT EXISTS idx_orders_order_date ON orders (order_date);
                CREATE INDEX IF NOT EXISTS idx_orders_order_number ON orders (order_number);
                CREATE UNIQUE INDEX IF NOT EXISTS uq_order_number ON orders (order_number);
            END IF;
        END $$;
    """
    )

    # Check if order_items table exists, if not create it
    op.execute(
        """
        DO $$ BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'order_items') THEN
                CREATE TABLE order_items (
                    account_id INTEGER NOT NULL,
                    id SERIAL NOT NULL,
                    order_id INTEGER NOT NULL,
                    product_id VARCHAR(255) NOT NULL,
                    quantity INTEGER NOT NULL DEFAULT 1,
                    unit_price DECIMAL(10, 2) NOT NULL,
                    total_price DECIMAL(10, 2) NOT NULL,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    PRIMARY KEY (account_id, id),
                    FOREIGN KEY (account_id) REFERENCES accounts(account_id),
                    FOREIGN KEY (account_id, order_id) REFERENCES orders(account_id, id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id, account_id) REFERENCES products(product_id, account_id) ON DELETE CASCADE
                );
                
                -- Create indexes
                CREATE INDEX IF NOT EXISTS idx_order_items_account_id ON order_items (account_id);
                CREATE INDEX IF NOT EXISTS idx_order_items_order_id ON order_items (order_id);
                CREATE INDEX IF NOT EXISTS idx_order_items_product_id ON order_items (product_id);
            END IF;
        END $$;
    """
    )


def downgrade() -> None:
    """Downgrade schema."""
    # Drop tables if they exist
    op.execute("DROP TABLE IF EXISTS order_items CASCADE")
    op.execute("DROP TABLE IF EXISTS orders CASCADE")

    # Drop enum if it exists
    op.execute("DROP TYPE IF EXISTS orderstatus")
