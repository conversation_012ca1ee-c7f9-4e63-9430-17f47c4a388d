"""fix_cleanup_schema_mismatch

Revision ID: fix_cleanup_schema_mismatch
Revises: add_tracking_columns_for_cleanup
Create Date: 2025-01-23 15:45:00.000000

"""

from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
import logging

# revision identifiers, used by Alembic.
revision = "fix_cleanup_schema_mismatch"
down_revision = "add_tracking_columns_for_cleanup"
branch_labels = None
depends_on = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def upgrade() -> None:
    """Fix cleanup_old_data function to match current database schema."""
    logger.info("Fixing cleanup_old_data function schema mismatch")

    # Drop the existing function
    op.execute("DROP FUNCTION IF EXISTS cleanup_old_data(integer, integer)")

    # Create the corrected function
    _create_corrected_cleanup_function()

    # Update permissions
    op.execute(
        "GRANT EXECUTE ON FUNCTION cleanup_old_data(integer, integer) TO maintenance_role"
    )
    op.execute(
        "REVOKE EXECUTE ON FUNCTION cleanup_old_data(integer, integer) FROM PUBLIC"
    )

    logger.info("cleanup_old_data function corrected successfully")


def downgrade() -> None:
    """Revert to the previous (broken) cleanup_old_data function."""
    logger.info("Reverting to previous cleanup_old_data function")

    # Drop the corrected function
    op.execute("DROP FUNCTION IF EXISTS cleanup_old_data(integer, integer)")

    # Recreate the old broken function (for rollback purposes)
    _recreate_broken_cleanup_function()

    logger.info("Reverted to previous cleanup_old_data function")


def _create_corrected_cleanup_function() -> None:
    """Create the corrected cleanup_old_data function that matches the current schema."""
    logger.info("Creating corrected cleanup_old_data function")

    op.execute(
        """
        CREATE OR REPLACE FUNCTION cleanup_old_data(
            p_account_id integer,
            p_days_old integer
        )
        RETURNS json AS $$
        DECLARE
            v_deleted_interactions integer := 0;
            v_archived_products integer := 0;
            v_inactivated_system_users integer := 0;
            v_inactivated_end_users integer := 0;
            v_result json;
            v_table_exists boolean;
            v_column_exists boolean;
            v_cutoff_date timestamp with time zone;
        BEGIN
            -- Validate input parameters
            IF p_account_id IS NULL OR p_account_id <= 0 THEN
                RAISE EXCEPTION 'Invalid account_id: %. Must be a positive integer.', p_account_id;
            END IF;
            
            IF p_days_old IS NULL OR p_days_old < 1 OR p_days_old > 3650 THEN
                RAISE EXCEPTION 'Invalid days_old: %. Must be between 1 and 3650.', p_days_old;
            END IF;
            
            -- Calculate cutoff date using a safe method
            v_cutoff_date := NOW() - (p_days_old * INTERVAL '1 day');
            
            -- Log operation for audit purposes
            RAISE NOTICE 'Cleanup operation started for account_id=% with days_old=%', p_account_id, p_days_old;
            
            -- Execute as superuser to bypass RLS
            SET SESSION AUTHORIZATION DEFAULT;
            
            -- 1. Clean up old interactions
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'interactions'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                WITH deleted AS (
                    DELETE FROM interactions
                    WHERE account_id = p_account_id
                    AND created_at < v_cutoff_date
                    RETURNING *
                )
                SELECT COUNT(*) INTO v_deleted_interactions FROM deleted;
                
                RAISE NOTICE 'Deleted % old interactions for account_id=%', v_deleted_interactions, p_account_id;
            END IF;
            
            -- 2. Archive inactive products (set is_active = FALSE instead of status = 'archived')
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'products'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                -- Check if last_interaction_at column exists
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'products' AND column_name = 'last_interaction_at'
                ) INTO v_column_exists;
                
                IF v_column_exists THEN
                    -- Use last_interaction_at if available
                    WITH updated AS (
                        UPDATE products
                        SET is_active = FALSE, updated_at = NOW()
                        WHERE account_id = p_account_id
                        AND last_interaction_at < v_cutoff_date
                        AND is_active = TRUE
                        AND deleted_at IS NULL
                        RETURNING *
                    )
                    SELECT COUNT(*) INTO v_archived_products FROM updated;
                ELSE
                    -- Fallback to updated_at if last_interaction_at doesn't exist
                    WITH updated AS (
                        UPDATE products
                        SET is_active = FALSE, updated_at = NOW()
                        WHERE account_id = p_account_id
                        AND updated_at < v_cutoff_date
                        AND is_active = TRUE
                        AND deleted_at IS NULL
                        RETURNING *
                    )
                    SELECT COUNT(*) INTO v_archived_products FROM updated;
                END IF;
                
                RAISE NOTICE 'Archived % inactive products for account_id=%', v_archived_products, p_account_id;
            END IF;
            
            -- 3. Inactivate inactive system_users (set is_active = FALSE instead of status = 'inactive')
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'system_users'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                -- Check if last_login_at column exists
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'system_users' AND column_name = 'last_login_at'
                ) INTO v_column_exists;
                
                IF v_column_exists THEN
                    -- Use last_login_at if available
                    WITH updated AS (
                        UPDATE system_users
                        SET is_active = FALSE, updated_at = NOW()
                        WHERE account_id = p_account_id
                        AND last_login_at < v_cutoff_date
                        AND is_active = TRUE
                        AND deleted_at IS NULL
                        RETURNING *
                    )
                    SELECT COUNT(*) INTO v_inactivated_system_users FROM updated;
                ELSE
                    -- Fallback to updated_at if last_login_at doesn't exist
                    WITH updated AS (
                        UPDATE system_users
                        SET is_active = FALSE, updated_at = NOW()
                        WHERE account_id = p_account_id
                        AND updated_at < v_cutoff_date
                        AND is_active = TRUE
                        AND deleted_at IS NULL
                        RETURNING *
                    )
                    SELECT COUNT(*) INTO v_inactivated_system_users FROM updated;
                END IF;
                
                RAISE NOTICE 'Inactivated % inactive system users for account_id=%', v_inactivated_system_users, p_account_id;
            END IF;
            
            -- 4. Inactivate inactive end_users (set is_active = FALSE)
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'end_users'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                -- Check if last_activity_at column exists
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_name = 'end_users' AND column_name = 'last_activity_at'
                ) INTO v_column_exists;
                
                IF v_column_exists THEN
                    -- Use last_activity_at if available
                    WITH updated AS (
                        UPDATE end_users
                        SET is_active = FALSE, updated_at = NOW()
                        WHERE account_id = p_account_id
                        AND last_activity_at < v_cutoff_date
                        AND is_active = TRUE
                        AND deleted_at IS NULL
                        RETURNING *
                    )
                    SELECT COUNT(*) INTO v_inactivated_end_users FROM updated;
                ELSE
                    -- Fallback to updated_at if last_activity_at doesn't exist
                    WITH updated AS (
                        UPDATE end_users
                        SET is_active = FALSE, updated_at = NOW()
                        WHERE account_id = p_account_id
                        AND updated_at < v_cutoff_date
                        AND is_active = TRUE
                        AND deleted_at IS NULL
                        RETURNING *
                    )
                    SELECT COUNT(*) INTO v_inactivated_end_users FROM updated;
                END IF;
                
                RAISE NOTICE 'Inactivated % inactive end users for account_id=%', v_inactivated_end_users, p_account_id;
            END IF;
            
            -- Create result JSON
            v_result := json_build_object(
                'account_id', p_account_id,
                'days_old', p_days_old,
                'deleted_interactions', v_deleted_interactions,
                'archived_products', v_archived_products,
                'inactivated_system_users', v_inactivated_system_users,
                'inactivated_end_users', v_inactivated_end_users,
                'timestamp', NOW()
            );
            
            RETURN v_result;
        EXCEPTION
            WHEN OTHERS THEN
                -- Log error and re-raise
                RAISE NOTICE 'Error in cleanup_old_data: %', SQLERRM;
                RAISE;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """
    )

    # Add comment to function
    op.execute(
        """
        COMMENT ON FUNCTION cleanup_old_data(integer, integer) IS 
        'Securely cleans up old data for a specific account using the correct schema. 
        Requires maintenance_role privileges. 
        Parameters: 
        - p_account_id: The account ID to clean up data for
        - p_days_old: Number of days to keep data (1-3650)
        Returns a JSON object with operation results.
        
        Schema corrections:
        - Uses is_active instead of status column
        - Uses last_interaction_at for products (with fallback to updated_at)
        - Uses last_login_at for system_users (with fallback to updated_at)
        - Uses last_activity_at for end_users (with fallback to updated_at)
        - Handles both system_users and end_users tables separately';
    """
    )


def _recreate_broken_cleanup_function() -> None:
    """Recreate the previous broken function for rollback purposes."""
    logger.info("Recreating previous (broken) cleanup_old_data function")

    op.execute(
        """
        CREATE OR REPLACE FUNCTION cleanup_old_data(
            p_account_id integer,
            p_days_old integer
        )
        RETURNS json AS $$
        DECLARE
            v_deleted_interactions integer := 0;
            v_archived_products integer := 0;
            v_inactivated_users integer := 0;
            v_result json;
            v_table_exists boolean;
            v_cutoff_date timestamp with time zone;
        BEGIN
            -- Validate input parameters
            IF p_account_id IS NULL OR p_account_id <= 0 THEN
                RAISE EXCEPTION 'Invalid account_id: %. Must be a positive integer.', p_account_id;
            END IF;
            
            IF p_days_old IS NULL OR p_days_old < 1 OR p_days_old > 3650 THEN
                RAISE EXCEPTION 'Invalid days_old: %. Must be between 1 and 3650.', p_days_old;
            END IF;
            
            -- Calculate cutoff date using a safe method
            v_cutoff_date := NOW() - (p_days_old * INTERVAL '1 day');
            
            -- Log operation for audit purposes
            RAISE NOTICE 'Cleanup operation started for account_id=% with days_old=%', p_account_id, p_days_old;
            
            -- Execute as superuser to bypass RLS
            SET SESSION AUTHORIZATION DEFAULT;
            
            -- Check if interactions table exists and clean up old interactions
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'interactions'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                WITH deleted AS (
                    DELETE FROM interactions
                    WHERE account_id = p_account_id
                    AND created_at < v_cutoff_date
                    RETURNING *
                )
                SELECT COUNT(*) INTO v_deleted_interactions FROM deleted;
                
                RAISE NOTICE 'Deleted % old interactions for account_id=%', v_deleted_interactions, p_account_id;
            END IF;
            
            -- Check if products table exists and archive inactive products
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'products'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                WITH updated AS (
                    UPDATE products
                    SET status = 'archived'
                    WHERE account_id = p_account_id
                    AND last_interaction_at < v_cutoff_date
                    AND status != 'archived'
                    RETURNING *
                )
                SELECT COUNT(*) INTO v_archived_products FROM updated;
                
                RAISE NOTICE 'Archived % inactive products for account_id=%', v_archived_products, p_account_id;
            END IF;
            
            -- Check if system_users table exists and inactivate inactive users
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'system_users'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                WITH updated AS (
                    UPDATE system_users
                    SET status = 'inactive'
                    WHERE account_id = p_account_id
                    AND last_login_at < v_cutoff_date
                    AND status != 'inactive'
                    RETURNING *
                )
                SELECT COUNT(*) INTO v_inactivated_users FROM updated;
                
                RAISE NOTICE 'Inactivated % inactive users for account_id=%', v_inactivated_users, p_account_id;
            END IF;
            
            -- Create result JSON
            v_result := json_build_object(
                'account_id', p_account_id,
                'days_old', p_days_old,
                'deleted_interactions', v_deleted_interactions,
                'archived_products', v_archived_products,
                'inactivated_users', v_inactivated_users,
                'timestamp', NOW()
            );
            
            RETURN v_result;
        EXCEPTION
            WHEN OTHERS THEN
                -- Log error and re-raise
                RAISE NOTICE 'Error in cleanup_old_data: %', SQLERRM;
                RAISE;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """
    )
