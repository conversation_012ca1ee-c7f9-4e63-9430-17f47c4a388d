"""
Servicio para la ingesta de datos en lote.
"""

import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status
from redis.asyncio import Redis

from src.db import schemas
from src.core.exceptions import LimitExceededError
from src.db.repositories import BatchIngestionJobRepository
from src.utils.base_logger import log_info, log_error, log_warning
from src.services import LimitService
from src.services.redis_storage_meter_service import RedisStorageMeterService
from src.services.batch_data_storage_service import BatchDataStorageService


class DataIngestionService:
    """
    Servicio para la ingesta de datos en lote.

    Este servicio maneja la validación de límites, creación de trabajos de ingesta
    y envío de tareas a Celery para procesamiento asíncrono.
    """

    def __init__(
        self,
        db: AsyncSession,
        account_id: int,
        limit_service: LimitService,
        redis: Redis = None,
        storage_service: BatchDataStorageService = None,
    ):
        """
        Inicializa el servicio de ingesta de datos.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            limit_service: Servicio de límites
            redis: Cliente Redis (opcional)
            storage_service: Servicio de almacenamiento de datos (opcional)
        """
        self.db = db
        self.account_id = account_id
        self.limit_service = limit_service
        self.redis = redis
        self.job_repo = BatchIngestionJobRepository(db, account_id=account_id)

        # Initialize Redis storage meter if Redis client is provided
        self.storage_meter = None
        if redis:
            self.storage_meter = RedisStorageMeterService(redis, account_id)

        # Initialize storage service
        self.storage_service = storage_service or BatchDataStorageService()

    def _calculate_payload_size(self, data: schemas.BatchIngestionRequest) -> int:
        """
        Calcula el tamaño estimado del payload en bytes.

        Args:
            data: Datos de ingesta en lote

        Returns:
            Tamaño estimado en bytes
        """
        estimated_size = 0

        # Calcular tamaño de usuarios
        if data.users:
            users_json = json.dumps([user.model_dump() for user in data.users])
            estimated_size += len(users_json.encode("utf-8"))

        # Calcular tamaño de productos
        if data.products:
            products_json = json.dumps(
                [product.model_dump() for product in data.products]
            )
            estimated_size += len(products_json.encode("utf-8"))

        # Calcular tamaño de interacciones
        if data.interactions:
            interactions_json = json.dumps(
                [interaction.model_dump() for interaction in data.interactions]
            )
            estimated_size += len(interactions_json.encode("utf-8"))

        # Añadir un margen de seguridad del 20% y asegurar un mínimo de 1KB
        return max(1024, int(estimated_size * 1.2))

    async def validate_ingestion_limits(
        self, users_count: int, products_count: int, estimated_size_bytes: int
    ) -> None:
        """
        Valida los límites de ingesta de datos.

        Args:
            users_count: Número de usuarios a ingerir
            products_count: Número de productos a ingerir
            estimated_size_bytes: Tamaño estimado en bytes

        Raises:
            HTTPException: Si se excede algún límite
        """
        # Validar límites de usuarios
        if users_count > 0:
            try:
                await self.limit_service.validate_user_limit()
            except LimitExceededError:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Users limit exceeded for your subscription plan",
                )

        # Validar límites de productos
        if products_count > 0:
            try:
                await self.limit_service.validate_product_limit()
            except LimitExceededError:
                raise HTTPException(
                    status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                    detail="Products limit exceeded for your subscription plan",
                )

        # Validar límites de almacenamiento
        try:
            # If Redis storage meter is available, use it
            if self.storage_meter:
                # Get subscription limits
                limits = await self.limit_service._get_subscription_limits()
                storage_limit_bytes = int(
                    limits["storage_mb"] * 1024 * 1024
                )  # Convert MB to bytes

                # Check storage limit using Redis meter
                await self.storage_meter.check_storage_limit(
                    storage_limit_bytes=storage_limit_bytes,
                    additional_bytes=estimated_size_bytes,
                )
                log_info(
                    f"Storage limit validated using Redis meter for account {self.account_id}"
                )
            else:
                # Fallback to traditional limit service
                await self.limit_service.validate_storage_limit(
                    estimated_size_bytes / (1024 * 1024)  # Convertir a MB
                )
                log_info(
                    f"Storage limit validated using limit service for account {self.account_id}"
                )
        except LimitExceededError as e:
            log_warning(
                f"Storage limit exceeded for account {self.account_id}: {str(e)}"
            )
            raise HTTPException(
                status_code=status.HTTP_429_TOO_MANY_REQUESTS,
                detail=f"Storage limit exceeded for your subscription plan: {str(e)}",
            )

    async def create_batch_ingestion_job(
        self, data: schemas.BatchIngestionRequest
    ) -> Dict[str, Any]:
        """
        Crea un trabajo de ingesta en lote y envía una tarea a Celery.

        Args:
            data: Datos de ingesta en lote

        Returns:
            Información del trabajo creado

        Raises:
            HTTPException: Si ocurre un error durante la creación del trabajo
        """
        try:
            # Validar límites
            await self.validate_ingestion_limits(
                users_count=len(data.users) if data.users else 0,
                products_count=len(data.products) if data.products else 0,
                estimated_size_bytes=self._calculate_payload_size(data),
            )

            # Preparar datos para el trabajo
            job_data = {
                "status": "pending",
                "parameters": {
                    "estimated_size": self._calculate_payload_size(data),
                    "total_users": len(data.users) if data.users else 0,
                    "total_products": len(data.products) if data.products else 0,
                    "total_interactions": (
                        len(data.interactions) if data.interactions else 0
                    ),
                },
            }

            # Preparar datos para almacenar
            task_data = {
                "users": (
                    [user.model_dump() for user in data.users] if data.users else []
                ),
                "products": (
                    [product.model_dump() for product in data.products]
                    if data.products
                    else []
                ),
                "interactions": (
                    [interaction.model_dump() for interaction in data.interactions]
                    if data.interactions
                    else []
                ),
                "estimated_size": self._calculate_payload_size(data),
            }

            # Crear el trabajo en la base de datos
            async with self.db.begin():
                batch_job = await self.job_repo.create(job_data)

                # Almacenar datos en GCS o sistema de archivos local
                file_path = await self.storage_service.store_batch_data(
                    account_id=self.account_id, job_id=batch_job.id, data=task_data
                )

                # Actualizar el trabajo con la ruta del archivo
                batch_job.data_file_path = file_path

                # Iniciar la tarea Celery (ahora solo pasamos el ID del trabajo y la ruta del archivo)
                from src.workers.celery_app import celery_app

                task = celery_app.send_task(
                    "process_batch_data",
                    args=[self.account_id, batch_job.id, file_path],
                    queue="batch_processing",
                )

                # Actualizar el trabajo con el ID de la tarea
                batch_job.task_id = task.id
                await self.db.flush()

            log_info(
                f"Batch ingestion task {task.id} started for account {self.account_id}, job {batch_job.id}"
            )

            return {
                "message": "Data ingestion started",
                "status": "processing",
                "job_id": batch_job.id,
                "task_id": task.id,
                "total_users": len(data.users) if data.users else 0,
                "total_products": len(data.products) if data.products else 0,
                "total_interactions": (
                    len(data.interactions) if data.interactions else 0
                ),
            }

        except HTTPException:
            # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
            raise
        except Exception as e:
            log_error(f"Error in batch data ingestion: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error processing batch data: {str(e)}",
            )

    async def get_batch_job_status(self, job_id: int) -> Dict[str, Any]:
        """
        Obtiene el estado de un trabajo de ingesta en lote.

        Args:
            job_id: ID del trabajo

        Returns:
            Estado del trabajo

        Raises:
            HTTPException: Si el trabajo no existe o ocurre un error
        """
        try:
            # Obtener el trabajo de ingesta masiva en una transacción para garantizar consistencia
            # Usamos una transacción normal ya que SQLAlchemy AsyncSession no soporta readonly=True
            async with self.db.begin():
                # Obtener el trabajo de ingesta masiva
                batch_job = await self.job_repo.get_by_id(job_id)

                if not batch_job:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Batch ingestion job {job_id} not found",
                    )

            # Preparar la respuesta base
            response = {
                "job_id": batch_job.id,
                "status": batch_job.status,
                "created_at": batch_job.created_at,
                "started_at": batch_job.started_at,
                "completed_at": batch_job.completed_at,
                "error_message": batch_job.error_message,
                "task_id": batch_job.task_id,
                "processed_count": batch_job.processed_count or {},
                "parameters": batch_job.parameters or {},
            }

            # Calcular campos adicionales para proporcionar más información

            # 1. Calcular porcentaje de progreso
            if batch_job.processed_count and batch_job.parameters:
                total_expected = 0
                total_processed = 0

                # Obtener totales esperados de los parámetros
                if "total_users" in batch_job.parameters:
                    total_expected += batch_job.parameters["total_users"]
                if "total_products" in batch_job.parameters:
                    total_expected += batch_job.parameters["total_products"]
                if "total_interactions" in batch_job.parameters:
                    total_expected += batch_job.parameters["total_interactions"]

                # Obtener totales procesados
                if "users" in batch_job.processed_count:
                    total_processed += batch_job.processed_count["users"]
                if "products" in batch_job.processed_count:
                    total_processed += batch_job.processed_count["products"]
                if "interactions" in batch_job.processed_count:
                    total_processed += batch_job.processed_count["interactions"]

                # Calcular porcentaje si hay datos esperados
                if total_expected > 0:
                    response["progress_percentage"] = (
                        total_processed / total_expected
                    ) * 100

            # 2. Calcular tiempo estimado restante
            if (
                batch_job.started_at
                and batch_job.status == "processing"
                and "progress_percentage" in response
            ):
                elapsed_time = datetime.now() - batch_job.started_at
                elapsed_seconds = elapsed_time.total_seconds()
                progress = response["progress_percentage"] / 100

                if progress > 0 and progress < 1:
                    # Regla de tres: si para llegar a progress% tomó elapsed_seconds,
                    # para llegar al 100% tomará X segundos
                    total_estimated_seconds = elapsed_seconds / progress
                    remaining_seconds = total_estimated_seconds - elapsed_seconds
                    response["estimated_remaining_time"] = remaining_seconds

            # 3. Calcular tasa de éxito y conteo de errores
            if "errors" in batch_job.processed_count:
                errors = batch_job.processed_count["errors"]
                total = batch_job.processed_count.get("total", 0)

                response["error_count"] = errors

                if total > 0:
                    response["success_rate"] = ((total - errors) / total) * 100

                # 4. Detalles de errores si están disponibles
                if "error_details" in batch_job.processed_count:
                    response["error_details"] = batch_job.processed_count[
                        "error_details"
                    ]

            return response

        except HTTPException:
            # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
            raise
        except Exception as e:
            log_error(f"Error getting batch job status: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting batch job status: {str(e)}",
            )
