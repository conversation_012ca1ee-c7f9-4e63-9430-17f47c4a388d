"""
Repositorios para la gestión de roles, permisos y autenticación.
"""

from sqlalchemy import select, delete
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Set
from fastapi import HTTPException
from src.db import models, schemas
from src.db.enums import PermissionType, RoleType
from .base import BaseRepository
from sqlalchemy.orm import joinedload


class RoleRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.Role)

    async def create(self, role_create: schemas.RoleCreate) -> models.Role:
        try:
            role = models.Role(**role_create.model_dump(), account_id=self.account_id)
            self.db.add(role)
            await self.db.refresh(role)
            return role
        except SQLAlchemyError as e:
            await self._handle_error("creating role", e)

    async def get_by_name(self, name: RoleType) -> Optional[models.Role]:
        """Obtener rol por nombre."""
        try:
            query = select(models.Role).filter(
                models.Role.name == name,
                models.Role.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching role by name", e)

    async def get_role_with_permissions(self, role_id: int) -> Optional[models.Role]:
        """Obtener rol con sus permisos."""
        try:
            query = select(models.Role).filter(
                models.Role.role_id == role_id,
                models.Role.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            role = result.scalars().first()

            if not role:
                return None

            # Los permisos se cargan automáticamente gracias a la relación
            return role
        except SQLAlchemyError as e:
            await self._handle_error("fetching role with permissions", e)

    async def get_users_with_role(self, role_id: int) -> List[models.SystemUser]:
        """Obtener usuarios que tienen un rol específico."""
        try:
            query = (
                select(models.SystemUser)
                .join(
                    models.SystemUserRole,
                    models.SystemUser.id
                    == models.SystemUserRole.system_user_id,
                )
                .filter(
                    models.SystemUserRole.role_id == role_id,
                    models.SystemUserRole.account_id == self.account_id,
                )
            )
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching users with role", e)
            return []


class PermissionRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.Permission)

    async def create(
        self, permission_create: schemas.PermissionCreate
    ) -> models.Permission:
        try:
            permission = models.Permission(
                **permission_create.model_dump(), account_id=self.account_id
            )
            self.db.add(permission)
            await self.db.refresh(permission)
            return permission
        except SQLAlchemyError as e:
            await self._handle_error("creating permission", e)

    async def get_by_name(self, name: PermissionType) -> Optional[models.Permission]:
        """Obtener permiso por nombre."""
        try:
            query = select(models.Permission).filter(
                models.Permission.name == name,
                models.Permission.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching permission by name", e)

    async def get_by_type(
        self, permission_type: PermissionType
    ) -> List[models.Permission]:
        """Obtener permisos por tipo."""
        try:
            query = select(models.Permission).filter(
                models.Permission.type == permission_type,
                models.Permission.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching permissions by type", e)
            return []

    async def get_roles_with_permission(self, permission_id: int) -> List[models.Role]:
        """Obtener roles que tienen un permiso específico."""
        try:
            query = (
                select(models.Role)
                .join(
                    models.role_permissions,
                    models.Role.role_id == models.role_permissions.c.role_id,
                )
                .filter(
                    models.role_permissions.c.permission_id == permission_id,
                    models.role_permissions.c.account_id == self.account_id,
                )
            )
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching roles with permission", e)
            return []


class RolePermissionRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.role_permissions)

    async def assign_permission(self, role_id: int, permission_id: int) -> None:
        try:
            # Insertar directamente en la tabla de asociación
            stmt = models.role_permissions.insert().values(
                role_id=role_id, permission_id=permission_id, account_id=self.account_id
            )
            await self.db.execute(stmt)
        except SQLAlchemyError as e:
            await self._handle_error("assigning permission to role", e)

    async def remove_permission(self, role_id: int, permission_id: int) -> None:
        try:
            # Eliminar de la tabla de asociación
            stmt = models.role_permissions.delete().where(
                models.role_permissions.c.role_id == role_id,
                models.role_permissions.c.permission_id == permission_id,
                models.role_permissions.c.account_id == self.account_id,
            )
            await self.db.execute(stmt)
        except SQLAlchemyError as e:
            await self._handle_error("removing permission from role", e)

    async def get_role_permissions(self, role_id: int) -> List[models.Permission]:
        """Obtener permisos de un rol."""
        try:
            query = (
                select(models.Permission)
                .join(
                    models.role_permissions,
                    models.Permission.permission_id
                    == models.role_permissions.c.permission_id,
                )
                .filter(
                    models.role_permissions.c.role_id == role_id,
                    models.role_permissions.c.account_id == self.account_id,
                )
            )
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching role permissions", e)
            return []

    async def has_permission(self, role_id: int, permission_name: PermissionType) -> bool:
        """Verificar si un rol tiene un permiso específico."""
        try:
            # Primero obtener el ID del permiso
            query = select(models.Permission.permission_id).filter(
                models.Permission.name == permission_name,
                models.Permission.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            permission_id = result.scalar_one_or_none()

            if not permission_id:
                return False

            # Luego verificar si existe la relación
            query = select(models.role_permissions).filter(
                models.role_permissions.c.role_id == role_id,
                models.role_permissions.c.permission_id == permission_id,
                models.role_permissions.c.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            return result.first() is not None
        except SQLAlchemyError as e:
            await self._handle_error("checking role permission", e)
            return False


class SystemUserRoleRepository(BaseRepository):

    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.SystemUserRole)

    async def assign_role(self, user_id: int, role_id: int) -> None:
        try:
            # Crear una nueva instancia de SystemUserRole
            user_role = models.SystemUserRole(
                system_user_id=user_id, role_id=role_id, account_id=self.account_id
            )
            self.db.add(user_role)
        except SQLAlchemyError as e:
            await self._handle_error("assigning role to user", e)

    async def remove_role(self, user_id: int, role_id: int) -> None:
        try:
            # Eliminar de la tabla de asociación
            stmt = delete(models.SystemUserRole).where(
                models.SystemUserRole.system_user_id == user_id,
                models.SystemUserRole.role_id == role_id,
                models.SystemUserRole.account_id == self.account_id,
            )
            await self.db.execute(stmt)
        except SQLAlchemyError as e:
            await self._handle_error("removing role from user", e)

    async def get_user_roles(self, user_id: int) -> List[models.Role]:
        """Obtener roles de un usuario."""
        try:
            query = (
                select(models.Role)
                .join(
                    models.SystemUserRole,
                    models.Role.id == models.SystemUserRole.role_id,
                )
                .filter(
                    models.SystemUserRole.system_user_id == user_id,
                    models.SystemUserRole.account_id == self.account_id,
                )
            )
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching user roles", e)
            return []

    async def get_user_permissions(self, user_id: int) -> Set[PermissionType]:
        """Obtener todos los permisos de un usuario a través de sus roles."""
        try:
            # Obtener roles del usuario con sus permisos cargados en una sola consulta
            query = (
                select(models.Role)
                .join(
                    models.SystemUserRole,
                    models.Role.id == models.SystemUserRole.role_id,
                )
                .options(joinedload(models.Role.permissions))
                .filter(
                    models.SystemUserRole.system_user_id == user_id,
                    models.SystemUserRole.account_id == self.account_id,
                )
            )
            result = await self.db.execute(query)
            roles = result.unique().scalars().all()

            # Obtener permisos de cada rol
            permissions = set()
            for role in roles:
                for permission in role.permissions:
                    permissions.add(permission.name)

            return permissions
        except SQLAlchemyError as e:
            await self._handle_error("fetching user permissions", e)
            return set()

    async def has_permission(self, user_id: int, permission_name: PermissionType) -> bool:
        """Verificar si un usuario tiene un permiso específico."""
        try:
            permissions = await self.get_user_permissions(user_id)
            return permission_name in permissions
        except SQLAlchemyError as e:
            await self._handle_error("checking user permission", e)
            return False

    async def has_role(self, user_id: int, role_name: RoleType) -> bool:
        """Verificar si un usuario tiene un rol específico."""
        try:
            roles = await self.get_user_roles(user_id)
            return any(role.name == role_name for role in roles)
        except SQLAlchemyError as e:
            await self._handle_error("checking user role", e)
            return False
