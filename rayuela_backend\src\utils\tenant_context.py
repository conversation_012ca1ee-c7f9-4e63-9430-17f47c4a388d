"""
Utilidades para manejar el contexto de tenant en tareas asíncronas y Celery.
"""

from typing import Optional, Callable, Any, TypeVar, Awaitable, Dict
from functools import wraps
from sqlalchemy.ext.asyncio import AsyncSession
from asgiref.sync import async_to_sync
from src.db.session import get_async_session_factory
from src.core.tenant_context import get_current_tenant_id, set_current_tenant_id
from src.utils.base_logger import log_info, log_error, log_warning

T = TypeVar("T")


async def with_tenant_context(
    db: AsyncSession,
    account_id: int,
    func: Callable[..., Awaitable[T]],
    *args,
    **kwargs,
) -> T:
    """
    Ejecuta una función asíncrona con el contexto de tenant establecido usando contextvars.

    Args:
        db: Sesión de base de datos asíncrona
        account_id: ID de la cuenta (tenant)
        func: Función asíncrona a ejecutar
        *args: Argumentos posicionales para la función
        **kwargs: Argumentos de palabra clave para la función

    Returns:
        El resultado de la función
    """
    # Guardar el contexto anterior (por si acaso)
    previous_account_id = get_current_tenant_id()

    try:
        # Establecer el account_id en el contexto usando contextvars
        if account_id is not None:
            log_info(f"Estableciendo tenant_id={account_id} para operación asíncrona")
            set_current_tenant_id(account_id)
            # También establecer en la sesión de PostgreSQL
            await db.execute(
                "SET app.tenant_id = :account_id", {"account_id": account_id}
            )

        # Ejecutar la función con el contexto establecido
        return await func(*args, **kwargs)
    except Exception as e:
        log_error(f"Error ejecutando función con tenant_id={account_id}: {str(e)}")
        raise
    finally:
        # Restaurar el contexto anterior
        set_current_tenant_id(previous_account_id)


def with_tenant_context_decorator(account_id_param: str = "account_id"):
    """
    Decorador para funciones asíncronas que necesitan establecer el contexto de tenant usando contextvars.

    Args:
        account_id_param: Nombre del parámetro que contiene el account_id

    Returns:
        Decorador configurado
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Obtener el account_id de los argumentos
            account_id = None
            if account_id_param in kwargs:
                account_id = kwargs[account_id_param]

            # Obtener la sesión de base de datos
            db = None
            for arg in args:
                if isinstance(arg, AsyncSession):
                    db = arg
                    break

            if "db" in kwargs and isinstance(kwargs["db"], AsyncSession):
                db = kwargs["db"]

            if not db:
                log_error(
                    f"No se encontró sesión de base de datos en los argumentos de {func.__name__}"
                )
                return await func(*args, **kwargs)

            if not account_id:
                log_error(
                    f"No se encontró account_id en los argumentos de {func.__name__}"
                )
                return await func(*args, **kwargs)

            # Ejecutar con el contexto de tenant usando contextvars
            return await with_tenant_context(db, account_id, func, *args, **kwargs)

        return wrapper

    return decorator


def with_tenant_context_celery(account_id_arg_name: str = "account_id"):
    """
    Decorador para tareas Celery que necesitan establecer el contexto de tenant usando contextvars.

    Este decorador asegura que las tareas Celery que operan sobre datos específicos
    de un tenant establezcan correctamente el contexto de tenant usando contextvars.

    Args:
        account_id_arg_name: Nombre del parámetro que contiene el account_id

    Returns:
        Decorador configurado para tareas Celery
    """

    def decorator(task_func):
        @wraps(task_func)
        def wrapper(*args, **kwargs):
            # Obtener el account_id de los argumentos
            account_id = None

            # Buscar en kwargs
            if account_id_arg_name in kwargs:
                account_id = kwargs[account_id_arg_name]

            # Buscar en args (asumiendo que es el primer argumento después de self si la tarea está vinculada)
            elif (
                len(args) > 1 and task_func.__name__ != "run"
            ):  # Si es una tarea vinculada (bind=True)
                account_id = args[
                    1
                ]  # args[0] es self, args[1] es el primer argumento real
            elif len(args) > 0 and task_func.__name__ == "run":  # Si es una clase Task
                account_id = args[0]

            # Si no se encontró account_id, registrar advertencia y continuar
            if account_id is None:
                log_warning(
                    f"No se encontró account_id en los argumentos de la tarea Celery {task_func.__name__}"
                )
                return task_func(*args, **kwargs)

            log_info(
                f"Ejecutando tarea Celery {task_func.__name__} con account_id={account_id}"
            )

            # Establecer el contexto de tenant para toda la duración de la tarea
            previous_account_id = get_current_tenant_id()
            try:
                set_current_tenant_id(account_id)
                return task_func(*args, **kwargs)
            except Exception as e:
                log_error(
                    f"Error en tarea Celery {task_func.__name__} con account_id={account_id}: {str(e)}"
                )
                raise
            finally:
                # Restaurar el contexto anterior
                set_current_tenant_id(previous_account_id)

        return wrapper

    return decorator


def wrap_celery_task_with_tenant_context(
    task_func: Callable, account_id_arg_name: str = "account_id"
) -> Callable:
    """
    Envuelve una tarea Celery con el contexto de tenant.

    Esta función es útil para aplicar el decorador with_tenant_context_celery a una tarea Celery
    que ya ha sido definida con el decorador @celery_app.task.

    Args:
        task_func: Función de tarea Celery a envolver
        account_id_arg_name: Nombre del parámetro que contiene el account_id

    Returns:
        Función de tarea Celery envuelta con el contexto de tenant
    """
    return with_tenant_context_celery(account_id_arg_name)(task_func)


async def run_with_tenant_context(
    account_id: int, async_func: Callable, *args, **kwargs
) -> Any:
    """
    Ejecuta una función asíncrona con el contexto de tenant establecido usando contextvars.

    Esta función es útil para las funciones asíncronas que son llamadas por tareas Celery
    y necesitan establecer el contexto de tenant usando contextvars.

    Args:
        account_id: ID de la cuenta (tenant)
        async_func: Función asíncrona a ejecutar
        *args: Argumentos posicionales para la función
        **kwargs: Argumentos de palabra clave para la función

    Returns:
        El resultado de la función
    """
    # Guardar el contexto anterior
    previous_account_id = get_current_tenant_id()

    # Obtener una sesión de base de datos
    session_factory = await get_async_session_factory()
    async with session_factory() as db:
        try:
            # Establecer el contexto de tenant usando contextvars
            if account_id is not None:
                log_info(
                    f"Estableciendo tenant_id={account_id} para operación asíncrona"
                )
                set_current_tenant_id(account_id)
                # También establecer en la sesión de PostgreSQL
                await db.execute(
                    "SET app.tenant_id = :account_id", {"account_id": account_id}
                )

            # Ejecutar la función con el contexto establecido
            if "db" in kwargs:
                # Si ya hay un parámetro db, no lo sobreescribimos
                return await async_func(*args, **kwargs)
            else:
                # Pasar la sesión de base de datos como primer argumento
                return await async_func(db, *args, **kwargs)
        except Exception as e:
            log_error(f"Error ejecutando función con tenant_id={account_id}: {str(e)}")
            raise
        finally:
            # Restaurar el contexto anterior
            set_current_tenant_id(previous_account_id)
