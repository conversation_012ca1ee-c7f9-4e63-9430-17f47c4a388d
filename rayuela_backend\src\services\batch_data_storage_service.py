"""
Servicio para almacenar y recuperar datos de ingesta en lote de forma segura.
"""
import json
import os
import asyncio
import uuid
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from fastapi import HTTPException, status
from google.cloud import storage
from src.core.config import settings
from src.utils.base_logger import log_info, log_error, log_warning

class BatchDataStorageService:
    """
    Servicio para almacenar y recuperar datos de ingesta en lote de forma segura.
    
    Este servicio se encarga de:
    1. Subir datos de ingesta en lote a Google Cloud Storage (en producción) o al sistema de archivos local (en desarrollo)
    2. Recuperar datos de ingesta en lote desde Google Cloud Storage o el sistema de archivos local
    3. Eliminar datos de ingesta en lote después de su procesamiento
    """
    
    def __init__(self):
        """Inicializa el servicio de almacenamiento de datos de ingesta en lote."""
        self.storage_client = storage.Client() if settings.ENV == "production" else None
        self.bucket_name = settings.GCS_BUCKET_NAME
        self.base_path = "batch_data"
        
        # Crear directorio local para desarrollo si no existe
        if settings.ENV != "production":
            os.makedirs(os.path.join("data", self.base_path), exist_ok=True)
    
    async def store_batch_data(self, account_id: int, job_id: int, data: Dict[str, Any]) -> str:
        """
        Almacena datos de ingesta en lote de forma segura.
        
        Args:
            account_id: ID de la cuenta
            job_id: ID del trabajo de ingesta en lote
            data: Datos a almacenar
            
        Returns:
            Ruta del archivo almacenado
        """
        try:
            # Generar nombre de archivo único
            timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            file_path = f"{self.base_path}/account_{account_id}/job_{job_id}_{timestamp}_{unique_id}.json"
            
            # Convertir datos a JSON
            json_data = json.dumps(data)
            
            # Almacenar datos según el entorno
            if settings.ENV == "production":
                await self._store_in_gcs(file_path, json_data)
            else:
                await self._store_locally(file_path, json_data)
                
            log_info(f"Batch data stored successfully at {file_path} for account {account_id}, job {job_id}")
            return file_path
            
        except Exception as e:
            log_error(f"Error storing batch data for account {account_id}, job {job_id}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error storing batch data: {str(e)}"
            )
    
    async def retrieve_batch_data(self, file_path: str) -> Dict[str, Any]:
        """
        Recupera datos de ingesta en lote almacenados.
        
        Args:
            file_path: Ruta del archivo a recuperar
            
        Returns:
            Datos recuperados
        """
        try:
            # Recuperar datos según el entorno
            if settings.ENV == "production":
                json_data = await self._retrieve_from_gcs(file_path)
            else:
                json_data = await self._retrieve_locally(file_path)
                
            # Convertir JSON a diccionario
            data = json.loads(json_data)
            
            log_info(f"Batch data retrieved successfully from {file_path}")
            return data
            
        except Exception as e:
            log_error(f"Error retrieving batch data from {file_path}: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error retrieving batch data: {str(e)}"
            )
    
    async def delete_batch_data(self, file_path: str) -> bool:
        """
        Elimina datos de ingesta en lote almacenados.
        
        Args:
            file_path: Ruta del archivo a eliminar
            
        Returns:
            True si se eliminó correctamente, False en caso contrario
        """
        try:
            # Eliminar datos según el entorno
            if settings.ENV == "production":
                await self._delete_from_gcs(file_path)
            else:
                await self._delete_locally(file_path)
                
            log_info(f"Batch data deleted successfully from {file_path}")
            return True
            
        except Exception as e:
            log_error(f"Error deleting batch data from {file_path}: {str(e)}")
            return False
    
    async def _store_in_gcs(self, file_path: str, data: str):
        """Almacena datos en Google Cloud Storage."""
        bucket = self.storage_client.bucket(self.bucket_name)
        blob = bucket.blob(file_path)
        
        # Usar asyncio para no bloquear
        await asyncio.to_thread(blob.upload_from_string, data, content_type="application/json")
    
    async def _store_locally(self, file_path: str, data: str):
        """Almacena datos en el sistema de archivos local."""
        local_path = os.path.join("data", file_path)
        
        # Crear directorio si no existe
        os.makedirs(os.path.dirname(local_path), exist_ok=True)
        
        # Usar asyncio para no bloquear
        await asyncio.to_thread(self._write_file, local_path, data)
    
    def _write_file(self, path: str, data: str):
        """Escribe datos en un archivo."""
        with open(path, "w") as f:
            f.write(data)
    
    async def _retrieve_from_gcs(self, file_path: str) -> str:
        """Recupera datos desde Google Cloud Storage."""
        bucket = self.storage_client.bucket(self.bucket_name)
        blob = bucket.blob(file_path)
        
        # Usar asyncio para no bloquear
        return await asyncio.to_thread(blob.download_as_text)
    
    async def _retrieve_locally(self, file_path: str) -> str:
        """Recupera datos desde el sistema de archivos local."""
        local_path = os.path.join("data", file_path)
        
        # Usar asyncio para no bloquear
        return await asyncio.to_thread(self._read_file, local_path)
    
    def _read_file(self, path: str) -> str:
        """Lee datos desde un archivo."""
        with open(path, "r") as f:
            return f.read()
    
    async def _delete_from_gcs(self, file_path: str):
        """Elimina datos desde Google Cloud Storage."""
        bucket = self.storage_client.bucket(self.bucket_name)
        blob = bucket.blob(file_path)
        
        # Usar asyncio para no bloquear
        await asyncio.to_thread(blob.delete)
    
    async def _delete_locally(self, file_path: str):
        """Elimina datos desde el sistema de archivos local."""
        local_path = os.path.join("data", file_path)
        
        # Usar asyncio para no bloquear
        await asyncio.to_thread(os.remove, local_path)
