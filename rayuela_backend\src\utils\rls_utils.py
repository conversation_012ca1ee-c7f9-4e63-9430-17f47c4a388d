"""
Utilities for working with Row-Level Security (RLS) in PostgreSQL.

This module provides functions for securely interacting with the
SECURITY DEFINER functions in PostgreSQL that are used for maintenance
operations that require bypassing RLS.
"""
from typing import Dict, Any, Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
import json
import logging
from datetime import datetime, timezone, timedelta

from src.utils.base_logger import log_info, log_error, log_warning

async def execute_cleanup_old_data(
    db: AsyncSession,
    account_id: int,
    days_to_keep: int
) -> Dict[str, Any]:
    """
    Executes the cleanup_old_data PostgreSQL function securely.
    
    This function calls the SECURITY DEFINER function in PostgreSQL
    that cleans up old data for a specific account. It handles the
    result parsing and error handling.
    
    Args:
        db: Database session
        account_id: ID of the account to clean up data for
        days_to_keep: Number of days to keep data (1-3650)
        
    Returns:
        Dictionary with operation results
        
    Raises:
        ValueError: If parameters are invalid
        Exception: If the database operation fails
    """
    try:
        # Validate parameters
        if account_id <= 0:
            raise ValueError(f"Invalid account_id: {account_id}. Must be a positive integer.")
        
        if days_to_keep < 1 or days_to_keep > 3650:
            raise ValueError(f"Invalid days_to_keep: {days_to_keep}. Must be between 1 and 3650.")
        
        log_info(f"Executing cleanup_old_data for account_id={account_id} with days_to_keep={days_to_keep}")
        
        # Execute the function
        result = await db.execute(
            text("SELECT cleanup_old_data(:account_id, :days_old)"),
            {"account_id": account_id, "days_old": days_to_keep}
        )
        
        # Get the result
        json_result = result.scalar_one()
        
        # Parse the JSON result
        if json_result:
            cleanup_data = json.loads(json_result)
            log_info(f"Cleanup completed: {cleanup_data}")
            return cleanup_data
        else:
            log_warning(f"Cleanup returned no result for account_id={account_id}")
            return {
                "account_id": account_id,
                "days_old": days_to_keep,
                "success": False,
                "error": "No result returned",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    except ValueError as e:
        log_error(f"Invalid parameters for cleanup_old_data: {str(e)}")
        raise
    except Exception as e:
        log_error(f"Error executing cleanup_old_data: {str(e)}")
        return {
            "account_id": account_id,
            "days_old": days_to_keep,
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

class RLSBypassContext:
    """
    Context manager for safely bypassing RLS for a specific account.
    
    This class provides a context manager that safely bypasses RLS
    for a specific account and ensures that the session is properly
    restored when the context is exited.
    
    Example:
        ```python
        async with RLSBypassContext(db, account_id):
            # Code here runs with RLS bypassed for the specified account
            await db.execute(...)
        # RLS is restored here
        ```
    """
    
    def __init__(self, db: AsyncSession, account_id: int):
        """
        Initialize the context manager.
        
        Args:
            db: Database session
            account_id: ID of the account to bypass RLS for
        """
        self.db = db
        self.account_id = account_id
    
    async def __aenter__(self):
        """
        Enter the context, bypassing RLS for the specified account.
        
        Returns:
            The database session
        """
        try:
            # Validate account_id
            if self.account_id <= 0:
                raise ValueError(f"Invalid account_id: {self.account_id}. Must be a positive integer.")
            
            log_info(f"Bypassing RLS for account_id={self.account_id}")
            
            # Call the bypass_rls function
            await self.db.execute(
                text("SELECT bypass_rls(:account_id)"),
                {"account_id": self.account_id}
            )
            
            return self.db
        except Exception as e:
            log_error(f"Error bypassing RLS: {str(e)}")
            raise
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """
        Exit the context, restoring the session.
        """
        try:
            # Reset the session authorization
            await self.db.execute(text("RESET SESSION AUTHORIZATION"))
            log_info(f"RLS bypass ended for account_id={self.account_id}")
        except Exception as e:
            log_error(f"Error resetting session after RLS bypass: {str(e)}")
            # Don't suppress the original exception
            return False
