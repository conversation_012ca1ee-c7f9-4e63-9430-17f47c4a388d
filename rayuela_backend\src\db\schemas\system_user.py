from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime
from src.db.schemas.role_permission import Role


class SystemUserBase(BaseModel):
    """Esquema base para usuarios del sistema"""

    email: EmailStr


class SystemUserCreate(SystemUserBase):
    password: str


class SystemUserUpdate(SystemUserBase):
    password: Optional[str] = None


class SystemUser(SystemUserBase):
    account_id: int
    id: int
    is_active: bool
    is_admin: bool
    created_at: datetime
    updated_at: datetime
    deleted_at: Optional[datetime] = None
    roles: List[Role] = []

    class ConfigDict:
        from_attributes = True


class SystemUserResponse(SystemUser):
    """Esquema de respuesta para usuarios del sistema que excluye campos sensibles"""
    
    class ConfigDict:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "id": 1,
                "account_id": 1,
                "email": "<EMAIL>",
                "is_active": True,
                "is_admin": False,
                "created_at": "2024-01-01T00:00:00",
                "updated_at": "2024-01-01T00:00:00",
                "deleted_at": None,
                "roles": []
            }
        }
