"""
Service for interacting with the Redis storage meter.
"""
from redis.asyncio import <PERSON><PERSON>
from typing import Dict, Any, Optional
import json
from datetime import datetime, timezone

from src.core.exceptions import LimitExceededError
from src.utils.base_logger import log_info, log_error, log_warning


class RedisStorageMeterService:
    """Service for interacting with the Redis storage meter."""

    def __init__(self, redis: Redis, account_id: int):
        """Initialize the service with a Redis client and account ID."""
        self.redis = redis
        self.account_id = account_id

    async def get_storage_usage_gb(self) -> float:
        """
        Get the current storage usage for the account in GB.
        
        Returns:
            float: The storage usage in GB
        """
        try:
            # Get from Redis
            redis_key = f"usage:{self.account_id}:storage_gb"
            storage_gb = await self.redis.get(redis_key)
            
            if storage_gb is not None:
                return float(storage_gb)
            else:
                # If not in Redis, return 0 and log a warning
                log_warning(f"Storage usage for account {self.account_id} not found in Redis")
                return 0.0
        except Exception as e:
            log_error(f"Error getting storage usage from Redis: {str(e)}")
            return 0.0

    async def get_storage_usage_bytes(self) -> int:
        """
        Get the current storage usage for the account in bytes.
        
        Returns:
            int: The storage usage in bytes
        """
        storage_gb = await self.get_storage_usage_gb()
        return int(storage_gb * 1024 * 1024 * 1024)  # Convert GB to bytes

    async def get_storage_details(self) -> Optional[Dict[str, Any]]:
        """
        Get detailed storage usage information.
        
        Returns:
            Dict or None: Detailed storage usage information
        """
        try:
            # Get from Redis
            details_key = f"usage:{self.account_id}:storage_details"
            details_json = await self.redis.get(details_key)
            
            if details_json:
                return json.loads(details_json)
            else:
                return None
        except Exception as e:
            log_error(f"Error getting storage details from Redis: {str(e)}")
            return None

    async def check_storage_limit(self, storage_limit_bytes: int, additional_bytes: int = 0) -> bool:
        """
        Check if the account has exceeded its storage limit.
        
        Args:
            storage_limit_bytes: The storage limit in bytes
            additional_bytes: Additional bytes to consider (for new data being added)
            
        Returns:
            bool: True if the limit is not exceeded, False otherwise
            
        Raises:
            LimitExceededError: If the storage limit is exceeded
        """
        try:
            # Get current storage usage
            current_usage = await self.get_storage_usage_bytes()
            
            # Check if adding additional bytes would exceed the limit
            if current_usage + additional_bytes > storage_limit_bytes:
                log_warning(
                    f"Storage limit exceeded for account {self.account_id}: "
                    f"{current_usage + additional_bytes}/{storage_limit_bytes} bytes"
                )
                raise LimitExceededError(
                    f"Storage limit exceeded: {current_usage + additional_bytes}/{storage_limit_bytes} bytes"
                )
                
            return True
        except LimitExceededError:
            raise
        except Exception as e:
            log_error(f"Error checking storage limit: {str(e)}")
            return False

    async def estimate_data_size(self, data: Dict[str, Any]) -> int:
        """
        Estimate the size of data to be ingested.
        
        Args:
            data: Data to be ingested
            
        Returns:
            int: Estimated size in bytes
        """
        try:
            # If the data already has an estimated_size field, use it
            if "estimated_size" in data and isinstance(data["estimated_size"], int):
                return data["estimated_size"]
            
            # Otherwise, estimate based on the data
            total_size = 0
            
            # Estimate size of users
            if "users" in data and data["users"]:
                users_count = len(data["users"])
                total_size += users_count * 100  # ~100 bytes per user
            
            # Estimate size of products
            if "products" in data and data["products"]:
                products_count = len(data["products"])
                total_size += products_count * 500  # ~500 bytes per product
            
            # Estimate size of interactions
            if "interactions" in data and data["interactions"]:
                interactions_count = len(data["interactions"])
                total_size += interactions_count * 50  # ~50 bytes per interaction
            
            return total_size
        except Exception as e:
            log_error(f"Error estimating data size: {str(e)}")
            return 1024  # Default to 1KB if estimation fails
