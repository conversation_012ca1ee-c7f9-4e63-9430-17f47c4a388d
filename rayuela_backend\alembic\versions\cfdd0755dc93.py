"""simplify_subscription_primary_key

Revision ID: cfdd0755dc93
Revises:
Create Date: 2024-06-15 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'cfdd0755dc93'  # ID corto en lugar del nombre descriptivo largo
down_revision = 'bfdd0755dc92'  # Actualizar con la revisión anterior
branch_labels = None
depends_on = None


def upgrade():
    # Verificar si la restricción existe antes de intentar eliminarla
    conn = op.get_bind()
    result = conn.execute(sa.text("""
        SELECT 1 FROM pg_constraint
        WHERE conname = 'subscriptions_pkey'
        AND conrelid = 'subscriptions'::regclass::oid
    """))

    if result.scalar():
        # Eliminar la clave primaria compuesta existente solo si existe
        op.drop_constraint('subscriptions_pkey', 'subscriptions', type_='primary')

    # Eliminar la columna id
    op.drop_column('subscriptions', 'id')

    # Crear la nueva clave primaria solo en account_id
    op.create_primary_key('subscriptions_pkey', 'subscriptions', ['account_id'])


def downgrade():
    # Verificar si la restricción existe antes de intentar eliminarla
    conn = op.get_bind()
    result = conn.execute(sa.text("""
        SELECT 1 FROM pg_constraint
        WHERE conname = 'subscriptions_pkey'
        AND conrelid = 'subscriptions'::regclass::oid
    """))

    if result.scalar():
        # Eliminar la clave primaria simple solo si existe
        op.drop_constraint('subscriptions_pkey', 'subscriptions', type_='primary')

    # Añadir de nuevo la columna id
    op.add_column('subscriptions', sa.Column('id', sa.Integer(), nullable=False))

    # Actualizar los valores de id (en una migración real, esto sería más complejo)
    op.execute("UPDATE subscriptions SET id = 1")

    # Recrear la clave primaria compuesta
    op.create_primary_key('subscriptions_pkey', 'subscriptions', ['account_id', 'id'])
