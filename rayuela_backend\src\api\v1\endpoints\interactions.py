from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis
from src.db import schemas
from src.db.session import get_db
from src.core.deps import get_current_account, get_limit_service
from src.core.redis_utils import get_redis
from src.services import LimitService, InteractionService
from src.utils.pagination import get_pagination_params
from src.utils.cache_decorators import invalidate_cache_after
from src.core.exceptions import (
    ResourceNotFoundError,
    RateLimitExceededError,
    ProductNotFoundException,
    LimitExceededError,
)
from src.utils.base_logger import logger
from src.db.models.account import Account

router = APIRouter()


@router.post("/", response_model=schemas.Interaction)
@invalidate_cache_after()
async def create_interaction(
    interaction: schemas.InteractionCreate,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_account: Account = Depends(get_current_account),
    redis: Redis = Depends(get_redis),
    limit_service: LimitService = Depends(get_limit_service),
):
    """Create a new interaction."""
    try:
        # Crear servicio de interacciones
        interaction_service = InteractionService(
            db=db,
            account_id=int(current_account.account_id),
            redis=redis,
            limit_service=limit_service
        )

        # Usar el servicio para crear la interacción
        return await interaction_service.create_interaction(interaction)

    except (ResourceNotFoundError, ProductNotFoundException, RateLimitExceededError, LimitExceededError) as e:
        # Manejar excepciones específicas
        if isinstance(e, LimitExceededError):
            raise HTTPException(status_code=429, detail=str(e))
        else:
            # Re-lanzar otras excepciones para mantener el mismo comportamiento
            raise
    except Exception as e:
        logger.error(f"Error creating interaction: {str(e)}")
        raise HTTPException(status_code=500, detail="Error creating interaction")


@router.get("/", response_model=schemas.PaginatedResponse[schemas.Interaction])
async def read_interactions(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    redis: Redis = Depends(get_redis),
    limit_service: LimitService = Depends(get_limit_service),
    pagination: tuple = Depends(get_pagination_params),
):
    """Get all interactions for the current account."""
    try:
        skip, limit = pagination

        # Crear servicio de interacciones
        interaction_service = InteractionService(
            db=db,
            account_id=int(account.account_id),
            redis=redis,
            limit_service=limit_service
        )

        # Usar el servicio para obtener las interacciones
        return await interaction_service.get_interactions(skip, limit)

    except Exception as e:
        logger.error(f"Error getting interactions: {str(e)}")
        raise HTTPException(status_code=500, detail="Error getting interactions")
