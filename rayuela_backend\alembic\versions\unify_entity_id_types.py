"""Unify entity ID types (user_id, product_id) to Integer across all tables

Revision ID: unify_entity_id_types
Revises: fix_composite_foreign_keys
Create Date: 2025-01-24 10:00:00.000000

This migration resolves the critical data type inconsistency between:
- EndUser.user_id and Product.product_id (currently String(255))
- References in Interaction, Recommendation, Search tables (currently Integer)

The migration:
1. Adds temporary Integer columns for user_id and product_id
2. Migrates data with proper ID mapping
3. Updates all foreign key references
4. Drops old columns and renames new ones
5. Recreates proper foreign key constraints

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy import text


# revision identifiers, used by Alembic.
revision: str = 'unify_entity_id_types'
down_revision: Union[str, None] = 'fix_composite_foreign_keys'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to unify entity ID types to Integer."""
    
    conn = op.get_bind()
    
    # Step 1: Add temporary Integer columns to EndUser and Product tables
    print("Step 1: Adding temporary Integer ID columns...")
    
    # Add temporary user_id_new to end_users
    op.add_column('end_users', 
        sa.Column('user_id_new', sa.Integer(), nullable=True)
    )
    
    # Add temporary product_id_new to products  
    op.add_column('products',
        sa.Column('product_id_new', sa.Integer(), nullable=True)
    )
    
    # Step 2: Populate the new ID columns with sequential integers
    print("Step 2: Populating new ID columns with sequential integers...")
    
    # Create mapping for end_users - using ROW_NUMBER() to assign sequential IDs
    conn.execute(text("""
        UPDATE end_users 
        SET user_id_new = subquery.row_num
        FROM (
            SELECT user_id, account_id,
                   ROW_NUMBER() OVER (ORDER BY account_id, user_id) as row_num
            FROM end_users
        ) as subquery
        WHERE end_users.user_id = subquery.user_id 
        AND end_users.account_id = subquery.account_id
    """))
    
    # Create mapping for products - using ROW_NUMBER() to assign sequential IDs
    conn.execute(text("""
        UPDATE products 
        SET product_id_new = subquery.row_num
        FROM (
            SELECT product_id, account_id,
                   ROW_NUMBER() OVER (ORDER BY account_id, product_id) as row_num
            FROM products
        ) as subquery
        WHERE products.product_id = subquery.product_id 
        AND products.account_id = subquery.account_id
    """))
    
    # Step 3: Create mapping tables to preserve old ID relationships during migration
    print("Step 3: Creating temporary mapping tables...")
    
    op.create_table(
        'temp_user_id_mapping',
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('old_user_id', sa.String(255), nullable=False),
        sa.Column('new_user_id', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('account_id', 'old_user_id')
    )
    
    op.create_table(
        'temp_product_id_mapping', 
        sa.Column('account_id', sa.Integer(), nullable=False),
        sa.Column('old_product_id', sa.String(255), nullable=False),
        sa.Column('new_product_id', sa.Integer(), nullable=False),
        sa.PrimaryKeyConstraint('account_id', 'old_product_id')
    )
    
    # Populate mapping tables
    conn.execute(text("""
        INSERT INTO temp_user_id_mapping (account_id, old_user_id, new_user_id)
        SELECT account_id, user_id, user_id_new
        FROM end_users
        WHERE user_id_new IS NOT NULL
    """))
    
    conn.execute(text("""
        INSERT INTO temp_product_id_mapping (account_id, old_product_id, new_product_id)
        SELECT account_id, product_id, product_id_new
        FROM products
        WHERE product_id_new IS NOT NULL
    """))
    
    # Step 4: Update references in orders table (user_id String -> Integer)
    print("Step 4: Updating orders table...")
    
    # Add temporary column
    op.add_column('orders', sa.Column('user_id_new', sa.Integer(), nullable=True))
    
    # Update using mapping
    conn.execute(text("""
        UPDATE orders 
        SET user_id_new = mapping.new_user_id
        FROM temp_user_id_mapping mapping
        WHERE orders.user_id = mapping.old_user_id 
        AND orders.account_id = mapping.account_id
    """))
    
    # Step 5: Update references in order_items table (product_id String -> Integer)
    print("Step 5: Updating order_items table...")
    
    # Add temporary column
    op.add_column('order_items', sa.Column('product_id_new', sa.Integer(), nullable=True))
    
    # Update using mapping
    conn.execute(text("""
        UPDATE order_items 
        SET product_id_new = mapping.new_product_id
        FROM temp_product_id_mapping mapping
        WHERE order_items.product_id = mapping.old_product_id 
        AND order_items.account_id = mapping.account_id
    """))
    
    # Step 6: Update references in interactions table 
    print("Step 6: Updating interactions table...")
    
    # SPECIAL CASE: The interactions table already has Integer types for end_user_id and product_id,
    # but these were likely invalid due to the type mismatch. We need to handle this carefully.
    
    # First, let's check if there are any existing interactions
    result = conn.execute(text("SELECT COUNT(*) FROM interactions"))
    interaction_count = result.scalar()
    
    if interaction_count > 0:
        print(f"  Found {interaction_count} existing interactions to update...")
        
        # Add temporary columns
        op.add_column('interactions', sa.Column('end_user_id_new', sa.Integer(), nullable=True))
        op.add_column('interactions', sa.Column('product_id_new', sa.Integer(), nullable=True))
        
        # Strategy: Try to match existing Integer IDs to our new sequential IDs
        # If no match is found, these rows might need manual intervention or could be marked as invalid
        
        # Try to update based on the assumption that the original Integer IDs might correspond
        # to the ROW_NUMBER() sequence we just created
        conn.execute(text("""
            UPDATE interactions 
            SET end_user_id_new = eu.user_id_new
            FROM end_users eu
            WHERE interactions.account_id = eu.account_id
            AND interactions.end_user_id = eu.user_id_new
        """))
        
        conn.execute(text("""
            UPDATE interactions 
            SET product_id_new = p.product_id_new  
            FROM products p
            WHERE interactions.account_id = p.account_id
            AND interactions.product_id = p.product_id_new
        """))
        
        # For any interactions that couldn't be mapped, we'll set them to NULL and handle later
        # This preserves the interaction record but marks the relationships as needing attention
        print("  Note: Some interactions may have invalid references and will need manual review")
        
    else:
        print("  No existing interactions found - adding columns for future use")
        op.add_column('interactions', sa.Column('end_user_id_new', sa.Integer(), nullable=True))
        op.add_column('interactions', sa.Column('product_id_new', sa.Integer(), nullable=True))
    
    # Step 7: Update references in recommendations table
    print("Step 7: Updating recommendations table...")
    
    result = conn.execute(text("SELECT COUNT(*) FROM recommendations"))
    recommendation_count = result.scalar()
    
    if recommendation_count > 0:
        print(f"  Found {recommendation_count} existing recommendations to update...")
        
        op.add_column('recommendations', sa.Column('end_user_id_new', sa.Integer(), nullable=True))
        op.add_column('recommendations', sa.Column('product_id_new', sa.Integer(), nullable=True))
        
        # Same strategy as interactions
        conn.execute(text("""
            UPDATE recommendations 
            SET end_user_id_new = eu.user_id_new
            FROM end_users eu
            WHERE recommendations.account_id = eu.account_id
            AND recommendations.end_user_id = eu.user_id_new
        """))
        
        conn.execute(text("""
            UPDATE recommendations 
            SET product_id_new = p.product_id_new
            FROM products p
            WHERE recommendations.account_id = p.account_id
            AND recommendations.product_id = p.product_id_new
        """))
        
        print("  Note: Some recommendations may have invalid references and will need manual review")
        
    else:
        print("  No existing recommendations found - adding columns for future use")
        op.add_column('recommendations', sa.Column('end_user_id_new', sa.Integer(), nullable=True))
        op.add_column('recommendations', sa.Column('product_id_new', sa.Integer(), nullable=True))
    
    # Step 8: Update references in searches table
    print("Step 8: Updating searches table...")
    
    result = conn.execute(text("SELECT COUNT(*) FROM searches"))
    search_count = result.scalar()
    
    if search_count > 0:
        print(f"  Found {search_count} existing searches to update...")
        
        op.add_column('searches', sa.Column('end_user_id_new', sa.Integer(), nullable=True))
        
        conn.execute(text("""
            UPDATE searches 
            SET end_user_id_new = eu.user_id_new
            FROM end_users eu
            WHERE searches.account_id = eu.account_id
            AND searches.end_user_id = eu.user_id_new
        """))
        
        print("  Note: Some searches may have invalid references and will need manual review")
        
    else:
        print("  No existing searches found - adding columns for future use")
        op.add_column('searches', sa.Column('end_user_id_new', sa.Integer(), nullable=True))
    
    # Step 9: Drop existing foreign key constraints
    print("Step 9: Dropping existing foreign key constraints...")
    
    # Drop existing constraints that we know about
    try:
        op.drop_constraint('fk_order_end_user', 'orders', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_order_item_product', 'order_items', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_interaction_end_user', 'interactions', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_interaction_product', 'interactions', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_recommendation_end_user', 'recommendations', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_recommendation_product', 'recommendations', type_='foreignkey')
    except:
        pass
    
    try:
        op.drop_constraint('fk_search_end_user', 'searches', type_='foreignkey')
    except:
        pass
    
    # Step 10: Make new columns NOT NULL and set up sequences
    print("Step 10: Setting up constraints and sequences...")
    
    # First ensure all new columns are populated
    op.alter_column('end_users', 'user_id_new', nullable=False)
    op.alter_column('products', 'product_id_new', nullable=False)
    op.alter_column('orders', 'user_id_new', nullable=False)
    op.alter_column('order_items', 'product_id_new', nullable=False)
    
    # For interactions, recommendations, and searches, we need to handle potential NULL values
    # First, check if there are any NULL values and potentially clean them up
    
    # Check interactions
    null_interactions = conn.execute(text("""
        SELECT COUNT(*) FROM interactions 
        WHERE end_user_id_new IS NULL OR product_id_new IS NULL
    """)).scalar()
    
    if null_interactions > 0:
        print(f"  Warning: Found {null_interactions} interactions with NULL references")
        print("  Consider reviewing and cleaning these records before setting NOT NULL constraints")
        # For now, we'll delete invalid interactions to maintain data integrity
        conn.execute(text("""
            DELETE FROM interactions 
            WHERE end_user_id_new IS NULL OR product_id_new IS NULL
        """))
        print(f"  Removed {null_interactions} invalid interactions")
    
    # Check recommendations  
    null_recommendations = conn.execute(text("""
        SELECT COUNT(*) FROM recommendations 
        WHERE end_user_id_new IS NULL OR product_id_new IS NULL
    """)).scalar()
    
    if null_recommendations > 0:
        print(f"  Warning: Found {null_recommendations} recommendations with NULL references")
        conn.execute(text("""
            DELETE FROM recommendations 
            WHERE end_user_id_new IS NULL OR product_id_new IS NULL
        """))
        print(f"  Removed {null_recommendations} invalid recommendations")
    
    # Check searches
    null_searches = conn.execute(text("""
        SELECT COUNT(*) FROM searches 
        WHERE end_user_id_new IS NULL
    """)).scalar()
    
    if null_searches > 0:
        print(f"  Warning: Found {null_searches} searches with NULL references")
        conn.execute(text("""
            DELETE FROM searches 
            WHERE end_user_id_new IS NULL
        """))
        print(f"  Removed {null_searches} invalid searches")
    
    # Now we can safely set NOT NULL constraints
    op.alter_column('interactions', 'end_user_id_new', nullable=False)
    op.alter_column('interactions', 'product_id_new', nullable=False)
    op.alter_column('recommendations', 'end_user_id_new', nullable=False)
    op.alter_column('recommendations', 'product_id_new', nullable=False)
    op.alter_column('searches', 'end_user_id_new', nullable=False)
    
    # Step 11: Drop old columns and rename new ones
    print("Step 11: Replacing old columns with new ones...")
    
    # EndUser table
    op.drop_column('end_users', 'user_id')
    op.alter_column('end_users', 'user_id_new', new_column_name='user_id')
    
    # Product table
    op.drop_column('products', 'product_id')
    op.alter_column('products', 'product_id_new', new_column_name='product_id')
    
    # Orders table
    op.drop_column('orders', 'user_id')
    op.alter_column('orders', 'user_id_new', new_column_name='user_id')
    
    # OrderItems table
    op.drop_column('order_items', 'product_id')
    op.alter_column('order_items', 'product_id_new', new_column_name='product_id')
    
    # Interactions table
    op.drop_column('interactions', 'end_user_id')
    op.drop_column('interactions', 'product_id')
    op.alter_column('interactions', 'end_user_id_new', new_column_name='end_user_id')
    op.alter_column('interactions', 'product_id_new', new_column_name='product_id')
    
    # Recommendations table
    op.drop_column('recommendations', 'end_user_id')
    op.drop_column('recommendations', 'product_id')
    op.alter_column('recommendations', 'end_user_id_new', new_column_name='end_user_id')
    op.alter_column('recommendations', 'product_id_new', new_column_name='product_id')
    
    # Searches table
    op.drop_column('searches', 'end_user_id')
    op.alter_column('searches', 'end_user_id_new', new_column_name='end_user_id')
    
    # Step 12: Create sequences for auto-increment
    print("Step 12: Creating sequences for auto-increment...")
    
    # Get current max values to set sequence start values
    max_user_id = conn.execute(text("SELECT COALESCE(MAX(user_id), 0) FROM end_users")).scalar()
    max_product_id = conn.execute(text("SELECT COALESCE(MAX(product_id), 0) FROM products")).scalar()
    
    # Create sequences
    op.execute(f"CREATE SEQUENCE end_users_user_id_seq START {max_user_id + 1}")
    op.execute(f"CREATE SEQUENCE products_product_id_seq START {max_product_id + 1}")
    
    # Set column defaults to use sequences
    op.alter_column('end_users', 'user_id', 
                   server_default=sa.text("nextval('end_users_user_id_seq')"))
    op.alter_column('products', 'product_id',
                   server_default=sa.text("nextval('products_product_id_seq')"))
    
    # Step 13: Recreate primary key constraints
    print("Step 13: Recreating primary key constraints...")
    
    # Note: Primary keys should be preserved as they were composite keys before
    # The migration should maintain the composite nature
    
    # Step 14: Recreate foreign key constraints with correct column references
    print("Step 14: Recreating foreign key constraints...")
    
    # Orders -> EndUsers
    op.create_foreign_key(
        'fk_order_end_user',
        'orders',
        'end_users',
        ['user_id', 'account_id'],
        ['user_id', 'account_id'],
        ondelete='CASCADE'
    )
    
    # OrderItems -> Products
    op.create_foreign_key(
        'fk_order_item_product',
        'order_items',
        'products',
        ['product_id', 'account_id'],
        ['product_id', 'account_id'],
        ondelete='CASCADE'
    )
    
    # Interactions -> EndUsers
    op.create_foreign_key(
        'fk_interaction_end_user',
        'interactions',
        'end_users',
        ['end_user_id', 'account_id'],
        ['user_id', 'account_id'],
        ondelete='CASCADE'
    )
    
    # Interactions -> Products
    op.create_foreign_key(
        'fk_interaction_product',
        'interactions',
        'products',
        ['product_id', 'account_id'],
        ['product_id', 'account_id'],
        ondelete='CASCADE'
    )
    
    # Recommendations -> EndUsers
    op.create_foreign_key(
        'fk_recommendation_end_user',
        'recommendations',
        'end_users',
        ['end_user_id', 'account_id'],
        ['user_id', 'account_id'],
        ondelete='CASCADE'
    )
    
    # Recommendations -> Products
    op.create_foreign_key(
        'fk_recommendation_product',
        'recommendations',
        'products',
        ['product_id', 'account_id'],
        ['product_id', 'account_id'],
        ondelete='CASCADE'
    )
    
    # Searches -> EndUsers
    op.create_foreign_key(
        'fk_search_end_user',
        'searches',
        'end_users',
        ['end_user_id', 'account_id'],
        ['user_id', 'account_id'],
        ondelete='CASCADE'
    )
    
    # Step 15: Clean up temporary tables
    print("Step 15: Cleaning up temporary mapping tables...")
    
    op.drop_table('temp_user_id_mapping')
    op.drop_table('temp_product_id_mapping')
    
    print("Migration completed successfully!")


def downgrade() -> None:
    """Downgrade schema - convert Integer IDs back to String(255)."""
    
    # This is a complex downgrade that would require:
    # 1. Converting Integer IDs back to String(255)
    # 2. Regenerating string-based IDs (possibly UUIDs)
    # 3. Updating all references
    # 4. Recreating constraints
    
    # For now, we'll raise an error as this downgrade is destructive
    # and should be carefully planned if needed
    raise NotImplementedError(
        "Downgrade from Integer to String IDs is not implemented. "
        "This migration is one-way due to the complexity of preserving "
        "data integrity during the reverse conversion. "
        "If downgrade is needed, please create a custom migration script."
    ) 