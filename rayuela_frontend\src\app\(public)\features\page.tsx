import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { generateMetadata as generateSEOMetadata, generateJsonLd } from '@/lib/seo';
import { 
  Zap, 
  Shield, 
  BarChart3, 
  Code, 
  Globe, 
  Users,
  Brain,
  Rocket,
  Lock
} from "lucide-react";

export const metadata = generateSEOMetadata({
  title: 'Características y Funcionalidades',
  description: 'Descubre todas las características de Rayuela: API de recomendaciones en tiempo real, machine learning avanzado, escalabilidad automática y más.',
  path: '/features',
  keywords: ['características', 'funcionalidades', 'API recomendaciones', 'machine learning', 'tiempo real', 'escalabilidad'],
});

const features = [
  {
    icon: Zap,
    title: "Recomendaciones en Tiempo Real",
    description: "API ultra-rápida que entrega recomendaciones personalizadas en menos de 100ms."
  },
  {
    icon: Brain,
    title: "Machine Learning Avanzado",
    description: "Algoritmos de última generación que aprenden continuamente de las interacciones de tus usuarios."
  },
  {
    icon: Code,
    title: "API-First",
    description: "Integración simple con cualquier plataforma. SDKs disponibles para Python, JavaScript y PHP."
  },
  {
    icon: Globe,
    title: "Escalabilidad Automática",
    description: "Maneja desde 100 hasta millones de usuarios sin configuración adicional."
  },
  {
    icon: Shield,
    title: "Seguridad Empresarial",
    description: "Cifrado end-to-end, autenticación robusta y cumplimiento con estándares internacionales."
  },
  {
    icon: BarChart3,
    title: "Analytics Detallados",
    description: "Métricas en tiempo real sobre el rendimiento de tus recomendaciones y engagement de usuarios."
  },
  {
    icon: Users,
    title: "Multi-tenant",
    description: "Aislamiento completo de datos entre clientes con arquitectura multi-tenant nativa."
  },
  {
    icon: Rocket,
    title: "Despliegue Rápido",
    description: "Desde la integración hasta las primeras recomendaciones en menos de 30 minutos."
  },
  {
    icon: Lock,
    title: "Privacidad por Diseño",
    description: "Cumplimiento GDPR y control total sobre tus datos. Sin vendor lock-in."
  }
];

export default function FeaturesPage() {
  const softwareSchema = generateJsonLd('SoftwareApplication', {
    name: 'Rayuela Recommendation System',
    description: 'Sistema de recomendaciones API-first para empresas',
    featureList: features.map(f => f.title),
  });

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(softwareSchema),
        }}
      />
      
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
        <div className="container mx-auto px-4 py-16">
          {/* Hero Section */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
              Características Poderosas
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Todo lo que necesitas para implementar sistemas de recomendación de clase mundial en tu aplicación
            </p>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card key={index} className="h-full">
                  <CardHeader>
                    <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-4">
                      <Icon className="w-6 h-6 text-blue-600 dark:text-blue-400" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription className="text-base">
                      {feature.description}
                    </CardDescription>
                  </CardContent>
                </Card>
              );
            })}
          </div>

          {/* CTA Section */}
          <div className="text-center bg-white dark:bg-gray-800 rounded-lg p-8 shadow-lg">
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
              ¿Listo para empezar?
            </h2>
            <p className="text-lg text-gray-600 dark:text-gray-300 mb-6">
              Comienza gratis y escala según tus necesidades
            </p>
            <div className="flex justify-center gap-4">
              <Button asChild size="lg">
                <Link href="/register">Comenzar Gratis</Link>
              </Button>
              <Button variant="outline" size="lg" asChild>
                <Link href="/pricing">Ver Precios</Link>
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
