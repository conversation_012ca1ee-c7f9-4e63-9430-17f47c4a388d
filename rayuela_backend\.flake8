[flake8]
# Configuración de flake8 para Rayuela
# Linting estricto para garantizar code quality y security

max-line-length = 88
max-complexity = 10
select = E,W,F,C,N,B,S
ignore = 
    # E203: whitespace before ':' (conflicts with black)
    E203,
    # W503: line break before binary operator (conflicts with black) 
    W503,
    # E501: line too long (handled by black)
    E501

# Extensiones de flake8 para security y best practices
# B: flake8-bugbear
# S: flake8-bandit (security linting)
# N: flake8-naming
# C901: complexity

per-file-ignores =
    # Tests pueden tener assert statements
    tests/*:S101
    # Configuración puede tener hardcoded passwords en ejemplos
    */config.py:S105,S106
    # Migrations pueden tener SQL statements
    alembic/versions/*:S608
    # __init__.py files pueden estar vacíos
    __init__.py:F401

exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    env,
    .env,
    alembic/versions,
    build,
    dist,
    .pytest_cache,
    .mypy_cache

# Rutas específicas del proyecto
application-import-names = src
import-order-style = google

# Configuración adicional para security
# S101: Use of assert detected
# S105: Possible hardcoded password
# S106: Possible hardcoded password
# S108: Probable insecure usage of temporary file/directory
# S608: Possible SQL injection vector through string-based query construction 