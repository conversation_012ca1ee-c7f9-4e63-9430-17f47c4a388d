from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from src.db.enums import PermissionType, RoleType


class RoleBase(BaseModel):
    name: RoleType
    description: Optional[str] = Field(
        None, max_length=255, description="Descripción del rol"
    )


class RoleCreate(RoleBase):
    pass


class Role(RoleBase):
    account_id: int
    id: int
    created_at: datetime
    updated_at: datetime

    class ConfigDict:
        from_attributes = True


class PermissionCreate(BaseModel):
    name: PermissionType


class Permission(PermissionCreate):
    account_id: int
    id: int
    # name: str

    class ConfigDict:
        from_attributes = True
