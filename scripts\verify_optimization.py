#!/usr/bin/env python3
"""
Simple verification script for database connection optimization.
This script verifies that the optimization changes are correctly implemented.
"""

import os
import sys
import importlib.util

def print_status(message, status="INFO"):
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m", 
        "WARNING": "\033[93m",
        "ERROR": "\033[91m"
    }
    end_color = "\033[0m"
    print(f"{colors.get(status, '')}{status}: {message}{end_color}")

def verify_gunicorn_config():
    """Verify Gunicorn configuration is optimized."""
    print_status("Verifying Gunicorn configuration...")
    
    try:
        # Load gunicorn_conf.py
        gunicorn_path = os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', 'gunicorn_conf.py')
        spec = importlib.util.spec_from_file_location("gunicorn_conf", gunicorn_path)
        gunicorn_conf = importlib.util.module_from_spec(spec)
        
        # Clear environment to test defaults
        old_env = os.environ.get('GUNICORN_WORKER_CONNECTIONS')
        if 'GUNICORN_WORKER_CONNECTIONS' in os.environ:
            del os.environ['GUNICORN_WORKER_CONNECTIONS']
        
        spec.loader.exec_module(gunicorn_conf)
        
        # Restore environment
        if old_env:
            os.environ['GUNICORN_WORKER_CONNECTIONS'] = old_env
        
        # Check worker_connections
        if hasattr(gunicorn_conf, 'worker_connections'):
            if gunicorn_conf.worker_connections == 200:
                print_status("✓ worker_connections optimized: 200 (was 1500)", "SUCCESS")
            else:
                print_status(f"✗ worker_connections not optimized: {gunicorn_conf.worker_connections} (expected 200)", "ERROR")
                return False
        else:
            print_status("✗ worker_connections not found in gunicorn_conf", "ERROR")
            return False
        
        # Check workers setting
        if hasattr(gunicorn_conf, 'workers'):
            print_status(f"✓ workers setting: {gunicorn_conf.workers}", "SUCCESS")
        
        return True
        
    except Exception as e:
        print_status(f"✗ Error loading gunicorn_conf.py: {e}", "ERROR")
        return False

def verify_sqlalchemy_config():
    """Verify SQLAlchemy configuration in session.py."""
    print_status("Verifying SQLAlchemy configuration...")
    
    try:
        # Read the session.py file to check for optimized settings
        session_path = os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', 'src', 'db', 'session.py')
        
        with open(session_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for optimized pool_size
        if 'pool_size=8' in content:
            print_status("✓ pool_size optimized: 8 (was 20)", "SUCCESS")
        else:
            print_status("✗ pool_size not optimized (expected pool_size=8)", "ERROR")
            return False
        
        # Check for optimized max_overflow
        if 'max_overflow=5' in content:
            print_status("✓ max_overflow optimized: 5 (was 10)", "SUCCESS")
        else:
            print_status("✗ max_overflow not optimized (expected max_overflow=5)", "ERROR")
            return False
        
        # Check for optimization comments
        if 'optimizada para startup' in content.lower() or 'optimized for startup' in content.lower():
            print_status("✓ Optimization comments found", "SUCCESS")
        else:
            print_status("⚠ Optimization comments not found", "WARNING")
        
        return True
        
    except Exception as e:
        print_status(f"✗ Error reading session.py: {e}", "ERROR")
        return False

def verify_environment_files():
    """Verify that environment files include new configuration options."""
    print_status("Verifying environment configuration files...")
    
    success = True
    
    # Check .env.example
    try:
        env_example_path = os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', '.env.example')
        with open(env_example_path, 'r', encoding='utf-8') as f:
            env_content = f.read()
        
        required_vars = ['GUNICORN_WORKER_CONNECTIONS', 'GUNICORN_WORKERS', 'GUNICORN_TIMEOUT']
        for var in required_vars:
            if var in env_content:
                print_status(f"✓ {var} found in .env.example", "SUCCESS")
            else:
                print_status(f"✗ {var} missing from .env.example", "ERROR")
                success = False
                
    except Exception as e:
        print_status(f"✗ Error reading .env.example: {e}", "ERROR")
        success = False
    
    # Check production config
    try:
        prod_config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config-production.env.example')
        with open(prod_config_path, 'r', encoding='utf-8') as f:
            prod_content = f.read()
        
        if 'GUNICORN_WORKER_CONNECTIONS' in prod_content:
            print_status("✓ Gunicorn settings found in production config", "SUCCESS")
        else:
            print_status("✗ Gunicorn settings missing from production config", "ERROR")
            success = False
            
    except Exception as e:
        print_status(f"✗ Error reading production config: {e}", "ERROR")
        success = False
    
    return success

def calculate_connection_impact():
    """Calculate the impact of the optimization."""
    print_status("Calculating optimization impact...")
    
    # Previous settings
    prev_worker_connections = 1500
    prev_pool_size = 20
    prev_max_overflow = 10
    max_workers = 4
    
    # New settings
    new_worker_connections = 200
    new_pool_size = 8
    new_max_overflow = 5
    
    # Calculate theoretical maximums
    prev_theoretical_max = max_workers * prev_worker_connections
    new_theoretical_max = max_workers * new_worker_connections
    
    prev_sqlalchemy_max = max_workers * (prev_pool_size + prev_max_overflow)
    new_sqlalchemy_max = max_workers * (new_pool_size + new_max_overflow)
    
    # Calculate reductions
    theoretical_reduction = (prev_theoretical_max - new_theoretical_max) / prev_theoretical_max * 100
    sqlalchemy_reduction = (prev_sqlalchemy_max - new_sqlalchemy_max) / prev_sqlalchemy_max * 100
    
    print_status("Connection Load Analysis:", "INFO")
    print(f"  Theoretical Maximum Connections:")
    print(f"    Before: {prev_theoretical_max:,}")
    print(f"    After:  {new_theoretical_max:,}")
    print(f"    Reduction: {theoretical_reduction:.1f}%")
    print()
    print(f"  SQLAlchemy Pool Connections:")
    print(f"    Before: {prev_sqlalchemy_max}")
    print(f"    After:  {new_sqlalchemy_max}")
    print(f"    Reduction: {sqlalchemy_reduction:.1f}%")
    print()
    
    if theoretical_reduction >= 80 and sqlalchemy_reduction >= 50:
        print_status("✓ Significant connection reduction achieved", "SUCCESS")
        return True
    else:
        print_status("✗ Insufficient connection reduction", "ERROR")
        return False

def verify_documentation():
    """Verify that documentation exists."""
    print_status("Verifying documentation...")
    
    doc_path = os.path.join(os.path.dirname(__file__), '..', 'docs', 'DATABASE_CONNECTION_OPTIMIZATION.md')
    
    if os.path.exists(doc_path):
        print_status("✓ Optimization documentation created", "SUCCESS")
        
        # Check if it contains key sections
        with open(doc_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        key_sections = [
            'Cloud SQL Configuration',
            'max_connections',
            'Cost Impact',
            'Implementation Checklist'
        ]
        
        for section in key_sections:
            if section in content:
                print_status(f"✓ Documentation contains '{section}' section", "SUCCESS")
            else:
                print_status(f"⚠ Documentation missing '{section}' section", "WARNING")
        
        return True
    else:
        print_status("✗ Optimization documentation not found", "ERROR")
        return False

def main():
    """Main verification function."""
    print("=" * 60)
    print("  Rayuela Database Connection Optimization Verification")
    print("=" * 60)
    print()
    
    all_checks_passed = True
    
    # Run all verification checks
    checks = [
        ("Gunicorn Configuration", verify_gunicorn_config),
        ("SQLAlchemy Configuration", verify_sqlalchemy_config),
        ("Environment Files", verify_environment_files),
        ("Connection Impact", calculate_connection_impact),
        ("Documentation", verify_documentation)
    ]
    
    for check_name, check_func in checks:
        print(f"\n--- {check_name} ---")
        if not check_func():
            all_checks_passed = False
    
    print("\n" + "=" * 60)
    if all_checks_passed:
        print_status("🎉 All optimization checks passed!", "SUCCESS")
        print_status("Next steps:", "INFO")
        print("  1. Deploy the optimized configuration")
        print("  2. Configure Cloud SQL max_connections to 100")
        print("  3. Monitor application performance")
        print("  4. Enjoy reduced Cloud SQL costs!")
    else:
        print_status("❌ Some optimization checks failed", "ERROR")
        print_status("Please review the errors above and fix them", "ERROR")
    
    return all_checks_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
