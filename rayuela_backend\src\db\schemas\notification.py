from pydantic import BaseModel, <PERSON>
from typing import Optional
from datetime import datetime
from src.db.enums import NotificationType, Priority


class NotificationBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1)
    type: NotificationType
    priority: Priority
    user_id: int


class NotificationCreate(NotificationBase):
    pass


class Notification(NotificationBase):
    id: int
    account_id: int
    read_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class ConfigDict:
        from_attributes = True
