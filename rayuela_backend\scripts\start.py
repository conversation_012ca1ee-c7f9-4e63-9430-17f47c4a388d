import os
import sys
import asyncio
import uvicorn
import click
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from scripts.init_db import initialize_database_and_data
from scripts.reset_db import reset_database
from src.core.config import settings
from src.utils.base_logger import logger
from scripts.manage_partitions import create_partitions_proactively

# Importar las funciones auxiliares
try:
    from scripts.db_manage_utils import (
        _reset_database,
        initialize_database_schema_and_data,
        _run_alembic_upgrade,
        _create_database_if_not_exists,
    )
except ImportError:
    # Manejar si el archivo no existe o hay error
    print("Error: Could not import database management utilities.")
    sys.exit(1)

from src.core.config import settings
from src.utils.base_logger import logger


@click.group()
def cli():
    """Comandos de gestión para Rayuela"""
    pass


@cli.command()
@click.confirmation_option(
    prompt="Are you sure you want to drop and recreate the database?"
)
def reset_db():
    """Drops and recreates the database, then applies migrations."""
    logger.warning("Executing database reset...")
    try:
        reset_success = asyncio.run(_reset_database())
        if reset_success:
            # Después de recrear, aplicar migraciones
            asyncio.run(_run_alembic_upgrade())
            logger.info("Database reset and migrations applied successfully.")
        else:
            logger.error("Database reset failed.")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Error during database reset process: {e}", exc_info=True)
        sys.exit(1)


@cli.command()
def init_db():
    """Initializes the database: creates if not exists, applies migrations, adds seed data.

    ADVERTENCIA: Este comando es solo para desarrollo local o configuración inicial.
    En producción, usar SIEMPRE 'migrate_db' para aplicar migraciones.
    """
    if settings.ENV == "production":
        logger.warning(
            "ADVERTENCIA: Este comando no debe usarse en producción. "
            "Usar 'migrate_db' en su lugar."
        )
        if not click.confirm("¿Estás seguro de que quieres continuar?"):
            logger.info("Operación cancelada.")
            return

    logger.info("Executing database initialization...")
    try:
        asyncio.run(initialize_database_schema_and_data())
        logger.info("Database initialization completed successfully.")
    except Exception as e:
        logger.error(f"Error during database initialization: {e}", exc_info=True)
        sys.exit(1)


@cli.command()
def create_db():
    """Creates the database if it doesn't exist."""
    logger.info("Executing database creation check...")
    try:
        asyncio.run(_create_database_if_not_exists())
    except Exception as e:
        logger.error(f"Error during database creation check: {e}", exc_info=True)
        sys.exit(1)


@cli.command()
@click.argument("target", default="head")
def migrate_db(target):
    """Applies database migrations using Alembic."""
    logger.info(f"Executing database migration (target: {target})...")
    try:
        asyncio.run(_run_alembic_upgrade(target=target))
    except Exception as e:
        logger.error(f"Error during database migration: {e}", exc_info=True)
        sys.exit(1)


@cli.command()
def seed_db():
    """Inserts initial seed data into the database."""
    logger.info("Executing database seeding...")
    try:
        # Asumiendo que _insert_seed_data está en db_manage_utils
        from scripts.db_manage_utils import _insert_seed_data

        asyncio.run(_insert_seed_data())
    except Exception as e:
        logger.error(f"Error during database seeding: {e}", exc_info=True)
        sys.exit(1)


@cli.command()
@click.option(
    "--host", default="0.0.0.0", help="Host for the server", show_default=True
)
@click.option("--port", default=8000, help="Port for the server", show_default=True)
@click.option(
    "--workers",
    default=None,
    type=int,
    help="Number of worker processes (overrides Dockerfile in local dev)",
)
@click.option("--reload", is_flag=True, help="Enable auto-reload (for development)")
def run_server(host, port, workers, reload):
    """Starts the FastAPI application server."""
    # Determinar número de workers
    num_workers = workers
    if num_workers is None:
        if reload:
            num_workers = 1  # Reload solo funciona con 1 worker
        else:
            # Usar variable de entorno o un cálculo basado en CPU cores
            default_workers = os.cpu_count() * 2 + 1 if os.cpu_count() else 4
            num_workers = int(os.getenv("UVICORN_WORKERS", default_workers))

    log_level = "debug" if reload else "info"

    logger.info(
        f"Starting server on {host}:{port} with {num_workers} workers. Reload: {reload}. Log level: {log_level}"
    )

    uvicorn.run(
        "main:app",  # Apunta a la instancia 'app' en 'main.py'
        host=host,
        port=port,
        reload=reload,
        workers=num_workers,
        log_level=log_level.lower(),
        # proxy_headers=True, # Importante si estás detrás de un proxy como Nginx o un LB
        # forwarded_allow_ips='*' # O especifica IPs de confianza
    )


# Removed 'setup' command as 'init_db' and 'run_server' cover its functionality


async def test_database_connection():
    """Prueba la conexión a la base de datos"""
    engine = create_async_engine(settings.database_url)
    try:
        async with engine.connect() as conn:
            await conn.execute(text("SELECT 1"))
        logger.info("✓ Base de datos accesible")

        # Verificar tablas principales
        async with engine.connect() as conn:
            tables = ["accounts", "products", "end_users", "interactions"]
            for table in tables:
                try:
                    await conn.execute(text(f"SELECT 1 FROM {table} LIMIT 1"))
                    logger.info(f"✓ Tabla {table} existe y es accesible")
                except Exception as e:
                    logger.error(f"✗ Problema con la tabla {table}: {e}")
    finally:
        await engine.dispose()


# Removed 'test_db' command as 'init_db' covers its functionality


@cli.command()
@click.option(
    "--target-max-id",
    type=int,
    default=None,
    help="Create partitions up to this account ID.",
)
@click.option(
    "--buffer-count",
    type=int,
    default=10,
    help="Number of extra partitions to create beyond current max ID.",
)
def manage_partitions(target_max_id, buffer_count):
    """Creates database partitions proactively."""
    logger.info(
        f"Running partition management with target_max_id={target_max_id}, buffer_count={buffer_count}"
    )
    try:
        asyncio.run(
            create_partitions_proactively(
                target_max_account_id=target_max_id, buffer_count=buffer_count
            )
        )
        logger.info("Partition management finished successfully.")
    except Exception as e:
        logger.error(f"Error during partition management: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Configurar logging básico antes de que FastAPI lo tome si es necesario
    cli()
