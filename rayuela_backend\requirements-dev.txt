-r requirements.txt

# =============================================================================
# Testing
# =============================================================================
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-cov==6.1.1
pytest-mock==3.14.0
httpx==0.28.1
aiosqlite==0.21.0
coverage==7.8.0

# =============================================================================
# Code Quality & Formatting
# =============================================================================
black==25.1.0
isort==6.0.1
mypy==1.15.0
flake8==7.2.0
pre-commit==4.2.0

# Extensiones de flake8 para mejor linting
flake8-bugbear==24.12.12
flake8-bandit==4.1.1
flake8-naming==0.14.1
flake8-comprehensions==3.16.0
flake8-simplify==0.21.0

# =============================================================================
# Security Scanning
# =============================================================================
bandit==1.8.0
safety==4.0.1
pip-audit==2.9.0
semgrep==1.97.0

# =============================================================================
# Database Migrations
# =============================================================================
alembic==1.15.2

# =============================================================================
# Performance Testing
# =============================================================================
locust==2.34.1