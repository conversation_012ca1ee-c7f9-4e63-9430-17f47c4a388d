#!/usr/bin/env python
"""
Script para monitorizar el rendimiento del sistema de recomendaciones.

Este script recopila métricas de rendimiento del sistema de recomendaciones,
incluyendo tiempo de entrenamiento, tiempo de inferencia, uso de recursos,
y métricas de calidad de las recomendaciones.
"""
import os
import sys
import time
import json
import psutil
import argparse
import logging
from datetime import datetime, timedelta
from pathlib import Path

# Añadir el directorio raíz al path para poder importar módulos
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.db.session import get_db
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.utils.base_logger import setup_logger

# Configurar logging
logger = setup_logger("recommendation_monitor", level=logging.INFO)

class RecommendationMonitor:
    """
    Monitor de rendimiento para el sistema de recomendaciones.
    
    Recopila y registra métricas de rendimiento del sistema de recomendaciones,
    incluyendo tiempo de entrenamiento, tiempo de inferencia, uso de recursos,
    y métricas de calidad de las recomendaciones.
    """
    
    def __init__(self, output_dir: str = "logs/metrics"):
        """
        Inicializa el monitor de rendimiento.
        
        Args:
            output_dir: Directorio donde se guardarán los archivos de métricas
        """
        self.output_dir = output_dir
        self.metrics_tracker = MetricsTracker()
        self.process = psutil.Process(os.getpid())
        
        # Crear directorio de salida si no existe
        os.makedirs(output_dir, exist_ok=True)
        
        # Archivo de métricas
        self.metrics_file = os.path.join(
            output_dir, 
            f"recommendation_metrics_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        
        # Inicializar métricas
        self.metrics = {
            "timestamp": datetime.now().isoformat(),
            "system": self._get_system_info(),
            "accounts": {}
        }
    
    def _get_system_info(self) -> dict:
        """Obtiene información del sistema."""
        return {
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total / (1024 * 1024),  # MB
            "platform": sys.platform,
            "python_version": sys.version
        }
    
    def _get_resource_usage(self) -> dict:
        """Obtiene el uso actual de recursos."""
        return {
            "cpu_percent": self.process.cpu_percent(),
            "memory_usage_mb": self.process.memory_info().rss / (1024 * 1024),
            "threads": self.process.num_threads(),
            "open_files": len(self.process.open_files())
        }
    
    async def collect_metrics(self, account_ids: list, model_ids: list = None):
        """
        Recopila métricas para las cuentas especificadas.
        
        Args:
            account_ids: Lista de IDs de cuentas
            model_ids: Lista opcional de IDs de modelos
        """
        logger.info(f"Recopilando métricas para {len(account_ids)} cuentas")
        
        # Obtener sesión de base de datos
        async for db in get_db():
            # Para cada cuenta, obtener métricas
            for account_id in account_ids:
                try:
                    # Registrar uso de recursos antes de obtener métricas
                    start_time = time.time()
                    start_resources = self._get_resource_usage()
                    
                    # Obtener métricas del modelo
                    metrics = await self.metrics_tracker.get_metrics(
                        db=db,
                        account_id=account_id,
                        model_id=model_ids[0] if model_ids else None
                    )
                    
                    # Registrar uso de recursos después de obtener métricas
                    end_time = time.time()
                    end_resources = self._get_resource_usage()
                    
                    # Calcular diferencias
                    inference_time = end_time - start_time
                    memory_increase = end_resources["memory_usage_mb"] - start_resources["memory_usage_mb"]
                    
                    # Añadir métricas de recursos
                    resource_metrics = {
                        "inference_time": inference_time,
                        "memory_increase_mb": memory_increase,
                        "cpu_percent": (start_resources["cpu_percent"] + end_resources["cpu_percent"]) / 2
                    }
                    
                    # Combinar métricas
                    account_metrics = {
                        "model_metrics": metrics,
                        "resource_metrics": resource_metrics,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    # Guardar métricas para esta cuenta
                    self.metrics["accounts"][str(account_id)] = account_metrics
                    
                    logger.info(f"Métricas recopiladas para cuenta {account_id}")
                    
                except Exception as e:
                    logger.error(f"Error recopilando métricas para cuenta {account_id}: {str(e)}")
            
            # Guardar métricas en archivo
            self._save_metrics()
            
            # Salir del bucle después de usar la sesión
            break
    
    def _save_metrics(self):
        """Guarda las métricas en un archivo JSON."""
        try:
            with open(self.metrics_file, 'w') as f:
                json.dump(self.metrics, f, indent=2)
            logger.info(f"Métricas guardadas en {self.metrics_file}")
        except Exception as e:
            logger.error(f"Error guardando métricas: {str(e)}")
    
    async def run_monitoring(self, account_ids: list, interval: int = 3600, duration: int = 86400):
        """
        Ejecuta el monitoreo periódico.
        
        Args:
            account_ids: Lista de IDs de cuentas
            interval: Intervalo en segundos entre recopilaciones
            duration: Duración total del monitoreo en segundos
        """
        logger.info(f"Iniciando monitoreo para cuentas {account_ids}")
        logger.info(f"Intervalo: {interval} segundos, Duración: {duration} segundos")
        
        start_time = time.time()
        end_time = start_time + duration
        
        while time.time() < end_time:
            # Recopilar métricas
            await self.collect_metrics(account_ids)
            
            # Esperar hasta el próximo intervalo
            next_run = time.time() + interval
            sleep_time = max(0, next_run - time.time())
            
            if sleep_time > 0:
                logger.info(f"Esperando {sleep_time:.2f} segundos hasta la próxima recopilación")
                time.sleep(sleep_time)
        
        logger.info("Monitoreo finalizado")

async def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Monitor de rendimiento de recomendaciones")
    parser.add_argument("--accounts", type=int, nargs="+", required=True, help="IDs de cuentas a monitorizar")
    parser.add_argument("--interval", type=int, default=3600, help="Intervalo en segundos entre recopilaciones")
    parser.add_argument("--duration", type=int, default=86400, help="Duración total del monitoreo en segundos")
    parser.add_argument("--output-dir", type=str, default="logs/metrics", help="Directorio de salida para métricas")
    
    args = parser.parse_args()
    
    monitor = RecommendationMonitor(output_dir=args.output_dir)
    await monitor.run_monitoring(args.accounts, args.interval, args.duration)

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
