[settings]
# Configuración de isort para Rayuela
# Ordenamiento consistente de imports

# Configuración compatible con Black
profile = black
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# Configuración específica del proyecto
src_paths = src,tests
known_first_party = src
known_local_folder = src

# Secciones de imports
sections = FUTURE,STDLIB,THIRDPARTY,FIRSTPARTY,LOCALFOLDER
default_section = THIRDPARTY

# Configuración de imports específicos
known_third_party = 
    fastapi,
    pydantic,
    sqlalchemy,
    alembic,
    celery,
    redis,
    numpy,
    pandas,
    sklearn,
    pytest,
    uvicorn,
    httpx,
    psycopg2,
    google,
    mercadopago,
    stripe

# Configuración de formato
force_single_line = false
force_sort_within_sections = true
lexicographical = true
group_by_package = true
combine_as_imports = true

# Archivos a omitir
skip = 
    __init__.py,
    .venv,
    venv,
    env,
    .env,
    alembic/versions,
    migrations

# Configuración específica para diferentes tipos de archivos
skip_glob = 
    **/migrations/*.py,
    **/alembic/versions/*.py

# Comentarios para imports
import_heading_future = Future imports
import_heading_stdlib = Standard library imports
import_heading_thirdparty = Third party imports
import_heading_firstparty = Local application imports
import_heading_localfolder = Local folder imports

# Configuración adicional
atomic = true
lines_before_imports = 2
lines_after_imports = 2 