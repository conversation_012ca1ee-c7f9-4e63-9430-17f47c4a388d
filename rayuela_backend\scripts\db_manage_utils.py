# scripts/db_manage_utils.py (NUEVO ARCHIVO OPCIONAL)
import asyncpg
from sqlalchemy.ext.asyncio import create_async_engine
from sqlalchemy import text
import asyncio
import subprocess

from src.core.config import settings
from src.utils.base_logger import logger


async def _create_database_if_not_exists():
    """Crea la base de datos principal si no existe."""
    conn = None
    try:
        conn = await asyncpg.connect(
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD,
            host=settings.POSTGRES_SERVER,
            port=settings.POSTGRES_PORT,
            database="postgres",  # Conectar a 'postgres' para crear otra DB
        )
        exists = await conn.fetchval(
            "SELECT 1 FROM pg_database WHERE datname = $1", settings.POSTGRES_DB
        )
        if not exists:
            logger.info(f"Database '{settings.POSTGRES_DB}' not found. Creating...")
            await conn.execute(f'CREATE DATABASE "{settings.POSTGRES_DB}"')
            logger.info(f"Database '{settings.POSTGRES_DB}' created successfully.")
        else:
            logger.info(f"Database '{settings.POSTGRES_DB}' already exists.")
    except Exception as e:
        logger.error(f"Error creating database: {e}", exc_info=True)
        raise
    finally:
        if conn:
            await conn.close()


async def _reset_database():
    """Elimina y recrea la base de datos principal."""
    logger.warning(
        f"Attempting to drop and recreate database '{settings.POSTGRES_DB}'..."
    )
    # Usar SQLAlchemy engine para manejar la conexión y ejecución de DDL
    # Conectar a 'postgres' DB para poder hacer DROP/CREATE
    postgres_url = f"postgresql+asyncpg://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_SERVER}:{settings.POSTGRES_PORT}/postgres"
    engine = create_async_engine(
        postgres_url, isolation_level="AUTOCOMMIT"
    )  # Autocommit necesario para DROP/CREATE DB

    async with engine.connect() as conn:
        try:
            # Terminar conexiones existentes
            logger.info(
                f"Terminating existing connections to '{settings.POSTGRES_DB}'..."
            )
            await conn.execute(
                text(
                    f"""
                SELECT pg_terminate_backend(pg_stat_activity.pid)
                FROM pg_stat_activity
                WHERE pg_stat_activity.datname = '{settings.POSTGRES_DB}'
                AND pid <> pg_backend_pid();
            """
                )
            )
            logger.info("Existing connections terminated.")

            # Eliminar base de datos
            logger.info(f"Dropping database '{settings.POSTGRES_DB}'...")
            await conn.execute(
                text(f'DROP DATABASE IF EXISTS "{settings.POSTGRES_DB}"')
            )
            logger.info(f"Database '{settings.POSTGRES_DB}' dropped.")

            # Crear base de datos
            logger.info(f"Creating database '{settings.POSTGRES_DB}'...")
            await conn.execute(text(f'CREATE DATABASE "{settings.POSTGRES_DB}"'))
            logger.info(f"Database '{settings.POSTGRES_DB}' created.")
            return True

        except Exception as e:
            logger.error(f"Error resetting database: {e}", exc_info=True)
            return False
        finally:
            await engine.dispose()  # Cerrar el engine específico de postgres


async def _run_alembic_upgrade(target: str = "head"):
    """Ejecuta las migraciones de Alembic."""
    logger.info(f"Applying Alembic migrations (target: {target})...")
    try:
        # Asegúrate que el comando se ejecute desde la raíz del proyecto
        process = await asyncio.create_subprocess_exec(
            "alembic",
            "upgrade",
            target,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE,
        )
        stdout, stderr = await process.communicate()

        # Decodificar con manejo de errores
        try:
            stdout_text = stdout.decode('utf-8', errors='replace')
            stderr_text = stderr.decode('utf-8', errors='replace')
        except Exception as decode_error:
            logger.warning(f"Error decoding Alembic output: {decode_error}")
            stdout_text = str(stdout)
            stderr_text = str(stderr)

        if process.returncode != 0:
            logger.error(
                f"Alembic upgrade failed:\nSTDOUT: {stdout_text}\nSTDERR: {stderr_text}"
            )
            raise Exception("Alembic migration failed")
        else:
            logger.info(f"Alembic upgrade successful:\nSTDOUT: {stdout_text}")
            return True
    except FileNotFoundError:
        logger.error("Alembic command not found. Make sure it's installed and in PATH.")
        raise
    except Exception as e:
        logger.error(f"Error running Alembic: {e}", exc_info=True)
        raise


async def _insert_seed_data():
    """Inserta datos iniciales o de prueba (opcional)."""
    # Esta función debería usar get_db y repositorios para insertar datos
    # Ejemplo: Crear cuenta admin, planes de suscripción por defecto, etc.
    logger.info("Inserting seed data (if defined)...")
    # from src.db.session import get_db
    # from src.db.repositories import AccountRepository # etc.
    # async with get_db() as db:
    #     async with db.begin():
    #         # repo = AccountRepository(db)
    #         # await repo.create(...)
    #         pass # Implementar lógica de seed aquí
    logger.info("Seed data insertion complete.")
    return True


async def initialize_database_schema_and_data():
    """Secuencia completa de inicialización: Crear DB -> Migrar -> Seed."""
    try:
        await _create_database_if_not_exists()
        await _run_alembic_upgrade()
        await _insert_seed_data()
        logger.info("Database initialization sequence completed.")
    except Exception as e:
        logger.error(f"Database initialization sequence failed: {e}", exc_info=True)
        raise
