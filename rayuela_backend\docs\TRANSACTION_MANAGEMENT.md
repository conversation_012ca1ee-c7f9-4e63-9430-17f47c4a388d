# Gestión de Transacciones en la Aplicación

Este documento describe las mejores prácticas para la gestión de transacciones en la aplicación.

> **IMPORTANTE**: A partir de la versión actual, se ha estandarizado el uso de `async with db.begin():` para todas las operaciones de escritura. El método `DatabaseConnectionManager.get_session()` ha sido marcado como obsoleto y no debe utilizarse en código nuevo.

## Principios Generales

1. **Consistencia**: Todas las operaciones de escritura/actualización deben seguir el mismo patrón de gestión de transacciones.
2. **Claridad**: El código debe ser claro sobre dónde comienzan y terminan las transacciones.
3. **Robustez**: Las transacciones deben manejar correctamente los errores y realizar rollback cuando sea necesario.

## Patrón Recomendado

### Uso de `async with db.begin():`

Para todas las operaciones que involucren escritura o actualización en la base de datos, se debe utilizar el patrón `async with db.begin():`:

```python
@router.post("/resource", response_model=schemas.Resource)
async def create_resource(
    resource_create: schemas.ResourceCreate,
    db: AsyncSession = Depends(get_db),
):
    try:
        async with db.begin():
            # Realizar operaciones de base de datos
            repository = ResourceRepository(db)
            resource = await repository.create(resource_create)

            # No es necesario hacer commit explícito
            # El commit se realizará automáticamente al salir del bloque

        # El commit ya se ha realizado
        return resource
    except Exception as e:
        # No es necesario hacer rollback explícito
        # El rollback se realizará automáticamente si hay una excepción
        log_error(f"Error creating resource: {str(e)}")
        raise
```

### Ventajas

1. **Gestión automática de transacciones**: El commit se realiza automáticamente al salir del bloque si no hay errores, y el rollback se realiza automáticamente si hay una excepción.
2. **Código más limpio**: No es necesario llamar explícitamente a `db.commit()` o `db.rollback()`.
3. **Menos propenso a errores**: Se evitan problemas comunes como olvidar hacer commit o rollback.
4. **Mejor manejo de excepciones**: Las excepciones se propagan correctamente y se puede manejar de manera centralizada.

## Repositorios

Los repositorios no deben manejar transacciones directamente. En su lugar, deben trabajar con una sesión de base de datos proporcionada por el llamador:

```python
class ResourceRepository:
    def __init__(self, db: AsyncSession):
        self.db = db

    async def create(self, resource_create: schemas.ResourceCreate) -> models.Resource:
        # Crear el recurso
        resource = models.Resource(**resource_create.dict())
        self.db.add(resource)

        # No hacer commit ni rollback aquí
        # La transacción será gestionada por el llamador

        return resource
```

## Casos Especiales

### Operaciones de Larga Duración

Para operaciones de larga duración que podrían bloquear la base de datos durante mucho tiempo, considere dividir la operación en transacciones más pequeñas:

```python
@router.post("/batch-operation")
async def batch_operation(
    items: List[schemas.Item],
    db: AsyncSession = Depends(get_db),
):
    results = []
    for item in items:
        try:
            async with db.begin():
                # Procesar un solo item
                result = await process_item(db, item)
                results.append(result)
        except Exception as e:
            log_error(f"Error processing item {item.id}: {str(e)}")
            # Continuar con el siguiente item

    return {"processed": len(results), "total": len(items)}
```

### Operaciones Anidadas

Evite anidar bloques `async with db.begin():` ya que esto puede llevar a comportamientos inesperados. Si necesita realizar operaciones anidadas, considere refactorizar el código para evitar la anidación:

```python
# Evitar esto
async def nested_operation(db: AsyncSession):
    async with db.begin():
        # Operación 1
        async with db.begin():  # ¡Esto puede causar problemas!
            # Operación 2
```

## Migración de Código Existente

Al migrar código existente al nuevo patrón:

1. Identifique todos los endpoints que realizan operaciones de escritura/actualización.
2. Reemplace los patrones existentes con `async with db.begin():`.
3. Elimine las llamadas explícitas a `db.commit()` y `db.rollback()`.
4. Asegúrese de que los repositorios no manejen transacciones directamente.

## Ejemplos

### Antes

```python
@router.post("/resource")
async def create_resource(
    resource_create: schemas.ResourceCreate,
    db: AsyncSession = Depends(get_db),
):
    try:
        repository = ResourceRepository(db)
        resource = await repository.create(resource_create)
        await db.commit()
        return resource
    except Exception as e:
        await db.rollback()
        log_error(f"Error creating resource: {str(e)}")
        raise
```

### Después

```python
@router.post("/resource")
async def create_resource(
    resource_create: schemas.ResourceCreate,
    db: AsyncSession = Depends(get_db),
):
    try:
        async with db.begin():
            repository = ResourceRepository(db)
            resource = await repository.create(resource_create)
        return resource
    except Exception as e:
        log_error(f"Error creating resource: {str(e)}")
        raise
```

## Implementación Actual

La implementación actual de la gestión de transacciones se basa en los siguientes componentes:

1. **`get_db()`**: Esta función proporciona una sesión de base de datos asíncrona y se utiliza como dependencia en los endpoints. No maneja transacciones automáticamente, lo que requiere el uso explícito de `async with db.begin():`.

2. **`DatabaseConnectionManager`**: Esta clase gestiona la conexión a la base de datos y proporciona una fábrica de sesiones. El método `get_session()` ha sido marcado como obsoleto y no debe utilizarse en código nuevo.

3. **Repositorios**: Los repositorios no manejan transacciones directamente, sino que trabajan con una sesión proporcionada por el llamador.

### Ejemplo de Uso Correcto

```python
@router.post("/resource")
async def create_resource(
    resource_create: schemas.ResourceCreate,
    db: AsyncSession = Depends(get_db),
):
    try:
        # Iniciar transacción explícitamente
        async with db.begin():
            # Realizar operaciones de base de datos
            repository = ResourceRepository(db)
            resource = await repository.create(resource_create)

        # La transacción se ha completado aquí (commit automático)
        return resource
    except Exception as e:
        # No es necesario hacer rollback explícito
        log_error(f"Error creating resource: {str(e)}")
        raise
```

## Conclusión

Siguiendo estas mejores prácticas, se logrará una gestión de transacciones más consistente, clara y robusta en toda la aplicación. Esto reducirá la probabilidad de errores y facilitará el mantenimiento del código a largo plazo.

Recuerde siempre utilizar `async with db.begin():` para todas las operaciones de escritura y asegurarse de que los repositorios no manejen transacciones directamente.
