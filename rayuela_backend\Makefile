# Makefile para Rayuela Backend
# Comandos comunes para desarrollo y verificaciones de calidad

.PHONY: help install install-dev format lint type-check security test test-unit test-integration quality-check quality-fix clean setup-hooks

# Variables
PYTHON := python
PIP := pip
SRC_DIR := src
TESTS_DIR := tests

# Colores para output
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
NC := \033[0m # No Color

help: ## Mostrar esta ayuda
	@echo "$(BLUE)Comandos disponibles para Rayuela Backend:$(NC)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-20s$(NC) %s\n", $$1, $$2}'

install: ## Instalar dependencias de producción
	@echo "$(BLUE)Instalando dependencias de producción...$(NC)"
	$(PIP) install -r requirements.txt

install-dev: ## Instalar dependencias de desarrollo
	@echo "$(BLUE)Instalando dependencias de desarrollo...$(NC)"
	$(PIP) install -r requirements.txt
	$(PIP) install -r requirements-dev.txt

setup-hooks: install-dev ## Configurar pre-commit hooks
	@echo "$(BLUE)Configurando pre-commit hooks...$(NC)"
	pre-commit install
	@echo "$(GREEN)Pre-commit hooks configurados exitosamente$(NC)"

format: ## Formatear código (black + isort)
	@echo "$(BLUE)Formateando código...$(NC)"
	black $(SRC_DIR)/ $(TESTS_DIR)/
	isort $(SRC_DIR)/ $(TESTS_DIR)/
	@echo "$(GREEN)Código formateado exitosamente$(NC)"

format-check: ## Verificar formato de código sin modificar
	@echo "$(BLUE)Verificando formato de código...$(NC)"
	black --check $(SRC_DIR)/ $(TESTS_DIR)/
	isort --check-only $(SRC_DIR)/ $(TESTS_DIR)/

lint: ## Ejecutar linting con flake8
	@echo "$(BLUE)Ejecutando linting...$(NC)"
	flake8 $(SRC_DIR)/ $(TESTS_DIR)/

type-check: ## Verificar tipos con MyPy
	@echo "$(BLUE)Verificando tipos con MyPy...$(NC)"
	mypy $(SRC_DIR)/ $(TESTS_DIR)/ --ignore-missing-imports --show-error-codes --pretty

security: ## Ejecutar análisis de seguridad
	@echo "$(BLUE)Ejecutando análisis de seguridad...$(NC)"
	@echo "$(YELLOW)Bandit - Análisis de código:$(NC)"
	bandit -r $(SRC_DIR)/ -ll
	@echo "$(YELLOW)Safety - Vulnerabilidades en dependencias:$(NC)"
	safety check
	@echo "$(YELLOW)pip-audit - Auditoría de dependencias:$(NC)"
	pip-audit

test: test-unit test-integration ## Ejecutar todos los tests

test-unit: ## Ejecutar tests unitarios
	@echo "$(BLUE)Ejecutando tests unitarios...$(NC)"
	$(PYTHON) -m pytest $(TESTS_DIR)/unit/ -v --tb=short

test-integration: ## Ejecutar tests de integración
	@echo "$(BLUE)Ejecutando tests de integración...$(NC)"
	$(PYTHON) -m pytest $(TESTS_DIR)/integration/ -v --tb=short

test-security: ## Ejecutar tests de seguridad multi-tenant
	@echo "$(BLUE)Ejecutando tests de seguridad multi-tenant...$(NC)"
	$(PYTHON) -m pytest $(TESTS_DIR)/integration/test_multi_tenancy_*.py -v --tb=short -x

test-coverage: ## Ejecutar tests con coverage
	@echo "$(BLUE)Ejecutando tests con coverage...$(NC)"
	$(PYTHON) -m pytest $(TESTS_DIR)/ --cov=$(SRC_DIR) --cov-report=html --cov-report=term

quality-check: ## Ejecutar todas las verificaciones de calidad (como CI/CD)
	@echo "$(BLUE)Ejecutando verificaciones de calidad completas...$(NC)"
	$(PYTHON) scripts/run_code_quality_checks.py

quality-fix: ## Ejecutar verificaciones y auto-corregir cuando sea posible
	@echo "$(BLUE)Ejecutando auto-corrección de calidad...$(NC)"
	$(PYTHON) scripts/run_code_quality_checks.py --fix

security-only: ## Ejecutar solo verificaciones de seguridad
	@echo "$(BLUE)Ejecutando solo verificaciones de seguridad...$(NC)"
	$(PYTHON) scripts/run_code_quality_checks.py --security-only

pre-commit: ## Ejecutar pre-commit en todos los archivos
	@echo "$(BLUE)Ejecutando pre-commit en todos los archivos...$(NC)"
	pre-commit run --all-files

db-upgrade: ## Aplicar migraciones de base de datos
	@echo "$(BLUE)Aplicando migraciones de base de datos...$(NC)"
	alembic upgrade head

db-migrate: ## Crear nueva migración
	@echo "$(BLUE)Creando nueva migración...$(NC)"
	@read -p "Nombre de la migración: " name; \
	alembic revision --autogenerate -m "$$name"

db-downgrade: ## Revertir última migración
	@echo "$(YELLOW)Revirtiendo última migración...$(NC)"
	alembic downgrade -1

run-dev: ## Ejecutar servidor de desarrollo
	@echo "$(BLUE)Iniciando servidor de desarrollo...$(NC)"
	$(PYTHON) main.py

docker-build: ## Construir imagen Docker
	@echo "$(BLUE)Construyendo imagen Docker...$(NC)"
	docker build -t rayuela-backend .

docker-run: ## Ejecutar contenedor Docker
	@echo "$(BLUE)Ejecutando contenedor Docker...$(NC)"
	docker-compose up -d

clean: ## Limpiar archivos temporales y cache
	@echo "$(BLUE)Limpiando archivos temporales...$(NC)"
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type d -name ".pytest_cache" -exec rm -rf {} +
	find . -type d -name ".mypy_cache" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	find . -type f -name "*.coverage" -delete
	rm -rf htmlcov/
	rm -rf dist/
	rm -rf build/
	rm -rf *.egg-info/
	@echo "$(GREEN)Limpieza completada$(NC)"

setup: install-dev setup-hooks db-upgrade ## Configuración inicial completa del proyecto
	@echo "$(GREEN)Configuración inicial completada exitosamente$(NC)"
	@echo "$(YELLOW)Comandos útiles:$(NC)"
	@echo "  make quality-check  - Verificar calidad antes de commit"
	@echo "  make test          - Ejecutar todos los tests"
	@echo "  make run-dev       - Iniciar servidor de desarrollo"

ci-simulation: ## Simular pipeline de CI/CD localmente
	@echo "$(BLUE)Simulando pipeline de CI/CD completo...$(NC)"
	@echo "$(YELLOW)Paso 1: Verificaciones de calidad...$(NC)"
	$(MAKE) quality-check
	@echo "$(YELLOW)Paso 2: Tests unitarios...$(NC)"
	$(MAKE) test-unit
	@echo "$(YELLOW)Paso 3: Tests de integración...$(NC)"
	$(MAKE) test-integration
	@echo "$(YELLOW)Paso 4: Tests de seguridad multi-tenant...$(NC)"
	$(MAKE) test-security
	@echo "$(GREEN)¡Simulación de CI/CD completada exitosamente!$(NC)"

# Comando por defecto
.DEFAULT_GOAL := help 