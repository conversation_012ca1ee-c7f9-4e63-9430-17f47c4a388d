from typing import List
from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.deps import get_db, get_current_admin_user
from src.db.repositories import AuditLogRepository
from src.db.schemas.audit import <PERSON>t<PERSON>og
from src.utils.base_logger import log_error
from src.core.exceptions import handle_exceptions
from src.db.models.system_user import SystemUser

"""
Audit Logging System

This module provides endpoints for retrieving audit logs.

For writing audit logs, use one of these mechanisms:

1. API Request Auditing: Automatically handled by AuditMiddleware
   - All API requests are automatically logged
   - No manual action required

2. System Events: Use SystemEventLogger for non-request events
   - Example: model training completion, batch operations
   - Usage:
     ```python
     from src.utils.system_events import SystemEventLogger

     logger = SystemEventLogger()
     await logger.log_system_event(
         account_id=account_id,
         action="OPERATION_NAME",
         entity_type="ENTITY_TYPE",
         entity_id="entity_identifier",
         success=True,  # or False for failures
         details={"key": "value"}  # Optional details
     )
     ```

DO NOT create custom audit logging mechanisms. Use the provided tools.
"""

router = APIRouter()


@router.get("/audit", response_model=List[AuditLog])
@handle_exceptions
async def get_audit_logs(
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_admin: SystemUser = Depends(
        get_current_admin_user
    ),  # Asegura que solo administradores puedan acceder
):
    """Obtiene los registros de auditoría (Admin only).
    Requiere privilegios de administrador ya que obtiene logs de todas las cuentas.
    """
    try:
        # Usar account_id=None para obtener registros de todas las cuentas
        repository = AuditLogRepository(db, account_id=None)
        result = await repository.get_all(
            skip=skip, limit=limit
        )  # Usar get_all del BaseRepository
        return result
    except Exception as e:
        log_error(f"Error obteniendo registros de auditoría: {str(e)}")
        raise HTTPException(status_code=500, detail="Error interno del servidor")
