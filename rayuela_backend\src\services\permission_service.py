"""
Service for handling permission-related business logic.
"""

from typing import Dict, Any, Optional, Set, List, Union
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import joinedload

from src.db import models, enums
from src.db.repositories import SystemUserRoleRepository, RolePermissionRepository
from src.db.enums import PermissionType, RoleType
from src.core.permissions import get_required_permissions_for_resource


class PermissionService:
    """Service for handling permission-related business logic."""

    def __init__(self, db: AsyncSession, account_id: int):
        self.db = db
        self.account_id = account_id
        self._user_role_repo = SystemUserRoleRepository(db, account_id)
        self._role_permission_repo = RolePermissionRepository(db, account_id)

    async def get_user_permissions(self, user_id: int) -> Set[PermissionType]:
        """
        Get all permissions for a user through their roles.
        This replaces SystemUserRoleRepository.get_user_permissions.
        """
        # Usar una transacción de solo lectura para garantizar consistencia
        async with self.db.begin():
            # Get user roles with permissions loaded in a single query
            query = (
                select(models.Role)
                .join(
                    models.SystemUserRole,
                    models.Role.id == models.SystemUserRole.role_id,
                )
                .options(joinedload(models.Role.permissions))
                .filter(
                    models.SystemUserRole.system_user_id == user_id,
                    models.SystemUserRole.account_id == self.account_id,
                )
            )
            result = await self.db.execute(query)
            roles = result.unique().scalars().all()

            # Get permissions for each role
            permissions = set()
            for role in roles:
                for permission in role.permissions:
                    permissions.add(permission.name)

        return permissions

    async def has_permission(self, user_id: int, permission_name: PermissionType) -> bool:
        """
        Check if a user has a specific permission.
        This replaces SystemUserRoleRepository.has_permission.
        """
        permissions = await self.get_user_permissions(user_id)
        return permission_name in permissions

    async def has_role(self, user_id: int, role_name: RoleType) -> bool:
        """
        Check if a user has a specific role.
        This replaces SystemUserRoleRepository.has_role.
        """
        roles = await self._user_role_repo.get_user_roles(user_id)
        return any(role.name == role_name for role in roles)

    async def check_permissions(
        self, user_id: int, required_permissions: Set[PermissionType]
    ) -> bool:
        """
        Check if a user has all the required permissions.
        """
        user_permissions = await self.get_user_permissions(user_id)
        return all(perm.value in user_permissions for perm in required_permissions)

    async def get_role_permissions(self, role_id: int) -> List[models.Permission]:
        """
        Get permissions for a role.
        This replaces RolePermissionRepository.get_role_permissions.
        """
        return await self._role_permission_repo.get_role_permissions(role_id)

    async def role_has_permission(self, role_id: int, permission_name: PermissionType) -> bool:
        """
        Check if a role has a specific permission.
        This replaces RolePermissionRepository.has_permission.
        """
        return await self._role_permission_repo.has_permission(role_id, permission_name)

    async def assign_permission_to_role(self, role_id: int, permission_id: int) -> None:
        """
        Assign a permission to a role.
        This uses RolePermissionRepository.assign_permission.
        """
        # Usar una transacción explícita para garantizar atomicidad
        async with self.db.begin():
            await self._role_permission_repo.assign_permission(role_id, permission_id)

    async def remove_permission_from_role(
        self, role_id: int, permission_id: int
    ) -> None:
        """
        Remove a permission from a role.
        This uses RolePermissionRepository.remove_permission.
        """
        # Usar una transacción explícita para garantizar atomicidad
        async with self.db.begin():
            await self._role_permission_repo.remove_permission(role_id, permission_id)

    async def check_resource_permission(
        self, user_id: int, resource_type: str, action: str
    ) -> bool:
        """
        Check if a user has permission to perform an action on a resource.

        Args:
            user_id: The ID of the user
            resource_type: The type of resource (product, user, etc.)
            action: The action to perform (read, create, update, delete)

        Returns:
            True if the user has permission, False otherwise
        """
        # Get the required permissions for this resource and action
        required_permissions = get_required_permissions_for_resource(
            resource_type, action
        )

        if not required_permissions:
            # If we don't have a mapping for this resource/action, deny by default
            return False

        # Check if the user has any of the required permissions
        user_permissions = await self.get_user_permissions(user_id)

        # Convert PermissionType enum values to strings for comparison
        required_perm_values = {perm.value for perm in required_permissions}

        # Check if any of the required permissions are in the user's permissions
        return any(perm in user_permissions for perm in required_perm_values)

    async def get_user_roles(self, user_id: int) -> List[str]:
        """
        Get all roles for a user.

        Args:
            user_id: The ID of the user

        Returns:
            A list of role names as strings
        """
        # Usar una transacción de solo lectura para garantizar consistencia
        async with self.db.begin():
            roles = await self._user_role_repo.get_user_roles(user_id)
            return [role.name for role in roles]

    async def assign_default_permissions_to_role(
        self, role_id: int, role_type: RoleType
    ) -> None:
        """
        Assign default permissions to a role based on its type.

        Args:
            role_id: The ID of the role
            role_type: The type of role
        """
        from src.core.permissions import get_permissions_for_role

        # Usar una transacción explícita para garantizar atomicidad
        async with self.db.begin():
            # Get default permissions for this role type
            default_permissions = get_permissions_for_role(role_type)

            # Get existing permissions for this role
            existing_permissions = await self.get_role_permissions(role_id)
            existing_perm_names = {perm.name for perm in existing_permissions}

            # For each default permission, check if it exists and add it if not
            for perm_type in default_permissions:
                if perm_type.value not in existing_perm_names:
                    # Find or create the permission
                    # This would require a method to get permission by name
                    # For now, we'll assume the permission exists in the database
                    # TODO: Implement get_permission_by_name
                    pass

    async def validate_user_permission(self, user_id: int, permission_name: PermissionType) -> None:
        """
        Validate if a user has a specific permission.
        Raises PermissionDeniedError if the user does not have the permission.
        
        Args:
            user_id: The ID of the user
            permission_name: The name of the permission to check
            
        Raises:
            PermissionDeniedError: If the user does not have the permission
        """
        from src.core.exceptions import PermissionDeniedError
        
        has_permission = await self.has_permission(user_id, permission_name)
        if not has_permission:
            raise PermissionDeniedError(
                f"Usuario no tiene el permiso requerido: {permission_name}"
            )
