import requests
import json

BASE_URL = "http://localhost:8000"


def test_health():
    print("\nProbando endpoint /health...")
    response = requests.get(f"{BASE_URL}/health")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")


def test_login():
    print("\nProbando endpoint /auth/login...")
    data = {"username": "admin", "password": "admin"}
    response = requests.post(f"{BASE_URL}/auth/login", json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    return response.json().get("access_token") if response.status_code == 200 else None


def test_protected_endpoint(token):
    if token:
        print("\nProbando endpoint protegido /api/v1/users/me...")
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/v1/users/me", headers=headers)
        print(f"Status: {response.status_code}")
        print(f"Response: {response.json()}")


if __name__ == "__main__":
    test_health()
    token = test_login()
    if token:
        test_protected_endpoint(token)
