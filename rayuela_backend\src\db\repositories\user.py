"""
Repositorios para la gestión de usuarios del sistema y usuarios finales.
"""

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from fastapi import HTTPException
from src.db import models, schemas
from .base import BaseRepository
from src.core.security import get_password_hash, verify_password


class SystemUserRepository(BaseRepository[models.SystemUser, schemas.SystemUserCreate, schemas.SystemUserUpdate]):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.SystemUser)

    async def create(self, user_create: schemas.SystemUserCreate) -> models.SystemUser:
        """Crear un nuevo usuario del sistema con contraseña hasheada."""
        try:
            # Verificar si el email ya existe
            query = select(models.SystemUser).filter(
                models.SystemUser.email == user_create.email,
                models.SystemUser.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            existing_user = result.scalars().first()
            if existing_user:
                raise HTTPException(status_code=400, detail="Email already registered")

            # Crear el usuario con contraseña hasheada
            hashed_password = get_password_hash(user_create.password)
            user = models.SystemUser(
                email=user_create.email,
                hashed_password=hashed_password,
                account_id=self.account_id,
                is_active=True,
            )
            self.db.add(user)
            await self.db.flush()
            await self.db.refresh(user)
            return user
        except SQLAlchemyError as e:
            await self._handle_error("creating system user", e)

    async def get_by_email(self, email: str) -> Optional[models.SystemUser]:
        """Obtener usuario por email."""
        try:
            query = select(models.SystemUser).filter(
                models.SystemUser.email == email,
                models.SystemUser.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching user by email", e)

    async def authenticate(
        self, email: str, password: str
    ) -> Optional[models.SystemUser]:
        """Autenticar usuario por email y contraseña."""
        try:
            user = await self.get_by_email(email)
            if not user:
                return None
            if not verify_password(password, user.hashed_password):
                return None
            return user
        except SQLAlchemyError as e:
            await self._handle_error("authenticating user", e)

    async def update_password(self, user_id: int, new_password: str) -> bool:
        """Actualizar contraseña de usuario."""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return False
            user.hashed_password = get_password_hash(new_password)
            return True
        except SQLAlchemyError as e:
            await self._handle_error("updating user password", e)
            return False

    async def get_user_with_roles(self, user_id: int) -> Optional[Dict[str, Any]]:
        """Obtener usuario con sus roles y permisos."""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return None

            # Obtener roles del usuario
            query = (
                select(models.Role)
                .join(
                    models.SystemUserRole,
                    models.Role.id == models.SystemUserRole.role_id,
                )
                .filter(
                    models.SystemUserRole.system_user_id == user_id,
                    models.SystemUserRole.account_id == self.account_id,
                )
            )
            result = await self.db.execute(query)
            roles = result.scalars().all()

            # Obtener permisos de los roles
            permissions = set()
            for role in roles:
                for permission in role.permissions:
                    permissions.add(permission.name)

            return {
                "user": user,
                "roles": roles,
                "permissions": list(permissions),
            }
        except SQLAlchemyError as e:
            await self._handle_error("fetching user with roles", e)

    async def deactivate(self, user_id: int) -> bool:
        """Desactivar usuario."""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return False
            user.is_active = False
            return True
        except SQLAlchemyError as e:
            await self._handle_error("deactivating user", e)
            return False

    async def activate(self, user_id: int) -> bool:
        """Activar usuario."""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return False
            user.is_active = True
            return True
        except SQLAlchemyError as e:
            await self._handle_error("activating user", e)
            return False


class EndUserRepository(BaseRepository[models.EndUser, schemas.EndUserCreate, schemas.EndUserCreate]):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.EndUser)

    async def create(self, user_create: schemas.EndUserCreate) -> models.EndUser:
        try:
            user = models.EndUser(
                **user_create.model_dump(), account_id=self.account_id
            )
            self.db.add(user)
            await self.db.refresh(user)
            return user
        except SQLAlchemyError as e:
            await self._handle_error("creating end user", e)

    async def get_by_external_id(self, external_id: str) -> Optional[models.EndUser]:
        """Obtener usuario final por ID externo."""
        try:
            query = select(models.EndUser).filter(
                models.EndUser.external_id == external_id,
                models.EndUser.account_id == self.account_id,
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except SQLAlchemyError as e:
            await self._handle_error("fetching end user by external ID", e)

    async def get_or_create(self, user_data: schemas.EndUserCreate) -> models.EndUser:
        """Obtener usuario final por ID externo o crearlo si no existe."""
        try:
            user = await self.get_by_external_id(user_data.external_id)
            if user:
                return user
            return await self.create(user_data)
        except SQLAlchemyError as e:
            await self._handle_error("getting or creating end user", e)

    async def update_profile(
        self, user_id: int, profile_data: Dict[str, Any]
    ) -> Optional[models.EndUser]:
        """Actualizar perfil de usuario final."""
        try:
            user = await self.get_by_id(user_id)
            if not user:
                return None

            # Actualizar campos básicos
            for key, value in profile_data.items():
                if hasattr(user, key) and key != "external_id" and key != "account_id":
                    setattr(user, key, value)

            # Actualizar campos de perfil (JSON)
            if user.profile is None:
                user.profile = {}

            if "profile" in profile_data and isinstance(profile_data["profile"], dict):
                user.profile.update(profile_data["profile"])

            await self.db.refresh(user)
            return user
        except SQLAlchemyError as e:
            await self._handle_error("updating end user profile", e)

    async def get_user_interactions(
        self, user_id: int, limit: int = 100
    ) -> List[models.Interaction]:
        """Obtener interacciones de un usuario final."""
        try:
            query = (
                select(models.Interaction)
                .filter(
                    models.Interaction.end_user_id == user_id,
                    models.Interaction.account_id == self.account_id,
                )
                .order_by(models.Interaction.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching end user interactions", e)
            return []

    async def get_user_searches(
        self, user_id: int, limit: int = 100
    ) -> List[models.Search]:
        """Obtener búsquedas de un usuario final."""
        try:
            query = (
                select(models.Search)
                .filter(
                    models.Search.end_user_id == user_id,
                    models.Search.account_id == self.account_id,
                )
                .order_by(models.Search.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching end user searches", e)
            return []
