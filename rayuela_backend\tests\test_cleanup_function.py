#!/usr/bin/env python3
"""
Script de verificación para la función cleanup_old_data corregida.
"""

import asyncio
import sys
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text
from src.core.config import settings


async def test_cleanup_function():
    """Probar la función cleanup_old_data con el esquema corregido."""

    # Crear engine y sesión
    engine = create_async_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

    async with SessionLocal() as db:
        print("🔍 Probando la función cleanup_old_data...")

        try:
            # 1. Verificar que las nuevas columnas existen
            print("\n1. Verificando que las columnas de tracking existen...")

            check_columns_query = text(
                """
                SELECT table_name, column_name, data_type, is_nullable 
                FROM information_schema.columns 
                WHERE table_name IN ('products', 'system_users', 'end_users') 
                AND column_name IN ('last_interaction_at', 'last_login_at', 'last_activity_at')
                ORDER BY table_name, column_name;
            """
            )

            result = await db.execute(check_columns_query)
            columns = result.fetchall()

            if columns:
                print("✅ Columnas de tracking encontradas:")
                for col in columns:
                    print(
                        f"   - {col.table_name}.{col.column_name} ({col.data_type}, nullable: {col.is_nullable})"
                    )
            else:
                print("❌ No se encontraron las columnas de tracking")
                return False

            # 2. Verificar que la función cleanup_old_data existe
            print("\n2. Verificando que la función cleanup_old_data existe...")

            check_function_query = text(
                """
                SELECT routine_name, routine_type, data_type 
                FROM information_schema.routines 
                WHERE routine_name = 'cleanup_old_data'
                AND routine_schema = 'public';
            """
            )

            result = await db.execute(check_function_query)
            functions = result.fetchall()

            if functions:
                print("✅ Función cleanup_old_data encontrada:")
                for func in functions:
                    print(
                        f"   - {func.routine_name} ({func.routine_type}, retorna: {func.data_type})"
                    )
            else:
                print("❌ Función cleanup_old_data no encontrada")
                return False

            # 3. Probar la función con un account_id de prueba (usaremos 999999 que no debería existir)
            print("\n3. Probando la función cleanup_old_data...")

            # Usar un account_id que no exista para evitar afectar datos reales
            test_account_id = 999999
            test_days = 30

            cleanup_query = text(
                """
                SELECT cleanup_old_data(:account_id, :days_old) as result;
            """
            )

            result = await db.execute(
                cleanup_query, {"account_id": test_account_id, "days_old": test_days}
            )

            cleanup_result = result.fetchone()

            if cleanup_result:
                print("✅ Función ejecutada exitosamente:")
                print(f"   Resultado: {cleanup_result.result}")

                # Parsear el JSON del resultado
                import json

                result_data = cleanup_result.result
                if isinstance(result_data, str):
                    result_data = json.loads(result_data)

                print(f"   - Account ID: {result_data.get('account_id')}")
                print(f"   - Días: {result_data.get('days_old')}")
                print(
                    f"   - Interacciones eliminadas: {result_data.get('deleted_interactions')}"
                )
                print(
                    f"   - Productos archivados: {result_data.get('archived_products')}"
                )
                print(
                    f"   - Usuarios del sistema inactivados: {result_data.get('inactivated_system_users')}"
                )
                print(
                    f"   - Usuarios finales inactivados: {result_data.get('inactivated_end_users')}"
                )
                print(f"   - Timestamp: {result_data.get('timestamp')}")

                return True
            else:
                print("❌ La función no devolvió resultados")
                return False

        except Exception as e:
            print(f"❌ Error durante la prueba: {str(e)}")
            return False

        finally:
            await engine.dispose()


async def main():
    """Función principal."""
    print("🚀 Iniciando verificación de la función cleanup_old_data...")

    success = await test_cleanup_function()

    if success:
        print("\n🎉 ¡Todas las verificaciones pasaron exitosamente!")
        print(
            "   La función cleanup_old_data está funcionando correctamente con el nuevo esquema."
        )
        sys.exit(0)
    else:
        print("\n💥 Algunas verificaciones fallaron.")
        print("   Revisar los errores anteriores.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
