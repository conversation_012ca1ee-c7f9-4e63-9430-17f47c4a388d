#!/usr/bin/env python
"""
<PERSON>ript to manage Celery worker scaling.

This script provides utilities to start, stop, and scale Celery workers
based on queue load and resource usage.

Usage:
    python manage_workers.py [command] [options]

Commands:
    start       Start workers for specified queues
    stop        Stop workers for specified queues
    scale       Scale workers based on queue load
    status      Show worker status
"""

import argparse
import os
import signal
import subprocess
import sys
import time
from typing import Dict, Any, List, Optional, Tuple

from celery import Celery


# Worker configuration presets
WORKER_PRESETS = {
    "training": {
        "concurrency": 1,
        "max_tasks_per_child": 1,
        "memory_limit": "8G",
        "cpu_limit": 2.0,
    },
    "batch_processing": {
        "concurrency": 2,
        "max_tasks_per_child": 5,
        "memory_limit": "6G",
        "cpu_limit": 2.0,
    },
    "maintenance": {
        "concurrency": 4,
        "max_tasks_per_child": 10,
        "memory_limit": "4G",
        "cpu_limit": 1.0,
    },
    "default": {
        "concurrency": 2,
        "max_tasks_per_child": 10,
        "memory_limit": "3G",
        "cpu_limit": 1.0,
    },
}


def get_celery_app(broker_url: Optional[str] = None) -> Celery:
    """
    Create a Celery app instance for monitoring.

    Args:
        broker_url: Redis URL for the Celery broker (optional)

    Returns:
        Celery app instance
    """
    # Use provided broker URL or get from environment
    broker = broker_url or os.environ.get("REDIS_URL", "redis://localhost:6379/0")
    
    # Create a minimal Celery app for monitoring
    app = Celery(broker=broker)
    return app


def get_worker_status(app: Celery) -> Dict[str, Any]:
    """
    Get status of active Celery workers.

    Args:
        app: Celery app instance

    Returns:
        Dictionary with worker status
    """
    # Get active workers
    active_workers = app.control.inspect().active()
    
    # Get worker stats
    stats = app.control.inspect().stats()
    
    # Get active queues
    active_queues = app.control.inspect().active_queues()
    
    # Get queue lengths
    queue_lengths = {}
    try:
        # This requires additional configuration to work properly
        # and may not be available in all setups
        queue_lengths = app.control.inspect().get_queue_length()
    except Exception:
        pass
    
    return {
        "active_workers": active_workers or {},
        "stats": stats or {},
        "active_queues": active_queues or {},
        "queue_lengths": queue_lengths or {},
    }


def start_worker(
    queue: str,
    concurrency: int = None,
    max_tasks_per_child: int = None,
    memory_limit: str = None,
    cpu_limit: float = None,
    broker_url: Optional[str] = None,
    log_file: Optional[str] = None,
) -> subprocess.Popen:
    """
    Start a Celery worker for a specific queue.

    Args:
        queue: Queue name
        concurrency: Number of worker processes
        max_tasks_per_child: Maximum number of tasks per child process
        memory_limit: Memory limit for the worker
        cpu_limit: CPU limit for the worker
        broker_url: Redis URL for the Celery broker (optional)
        log_file: Path to log file (optional)

    Returns:
        Subprocess object for the worker process
    """
    # Use preset values if not specified
    preset = WORKER_PRESETS.get(queue, WORKER_PRESETS["default"])
    concurrency = concurrency or preset["concurrency"]
    max_tasks_per_child = max_tasks_per_child or preset["max_tasks_per_child"]
    
    # Build command
    cmd = [
        "celery",
        "-A", "src.workers.celery_app",
        "worker",
        "--loglevel=info",
        f"--concurrency={concurrency}",
        f"--max-tasks-per-child={max_tasks_per_child}",
        f"--queues={queue}",
        f"--hostname={queue}@%h",
    ]
    
    # Add broker URL if specified
    if broker_url:
        cmd.extend(["--broker", broker_url])
    
    # Set environment variables
    env = os.environ.copy()
    if broker_url:
        env["REDIS_URL"] = broker_url
    
    # Start worker process
    if log_file:
        with open(log_file, "a") as f:
            process = subprocess.Popen(
                cmd,
                stdout=f,
                stderr=f,
                env=env,
            )
    else:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            env=env,
        )
    
    print(f"Started worker for queue '{queue}' with PID {process.pid}")
    return process


def stop_worker(worker_name: str, app: Celery) -> bool:
    """
    Stop a Celery worker by name.

    Args:
        worker_name: Worker name
        app: Celery app instance

    Returns:
        True if worker was stopped, False otherwise
    """
    try:
        # Try to shutdown worker gracefully
        app.control.broadcast("shutdown", destination=[worker_name])
        print(f"Sent shutdown signal to worker '{worker_name}'")
        return True
    except Exception as e:
        print(f"Error stopping worker '{worker_name}': {e}")
        return False


def scale_workers(
    app: Celery,
    queue: str,
    min_workers: int = 1,
    max_workers: int = 5,
    broker_url: Optional[str] = None,
) -> Tuple[int, int]:
    """
    Scale workers for a specific queue based on load.

    Args:
        app: Celery app instance
        queue: Queue name
        min_workers: Minimum number of workers
        max_workers: Maximum number of workers
        broker_url: Redis URL for the Celery broker (optional)

    Returns:
        Tuple of (current_workers, target_workers)
    """
    # Get worker status
    status = get_worker_status(app)
    
    # Count current workers for this queue
    current_workers = 0
    for worker_name, queues in status.get("active_queues", {}).items():
        if any(q["name"] == queue for q in queues):
            current_workers += 1
    
    # Get queue length
    queue_length = 0
    for worker_name, lengths in status.get("queue_lengths", {}).items():
        if queue in lengths:
            queue_length += lengths[queue]
    
    # Calculate target number of workers based on queue length
    # This is a simple heuristic and can be adjusted based on your needs
    if queue_length == 0:
        target_workers = min_workers
    else:
        # Scale linearly with queue length, but cap at max_workers
        target_workers = min(max(min_workers, queue_length // 5 + 1), max_workers)
    
    # Scale up or down as needed
    if current_workers < target_workers:
        # Scale up
        for _ in range(target_workers - current_workers):
            start_worker(queue, broker_url=broker_url)
    elif current_workers > target_workers:
        # Scale down
        # Find workers for this queue
        workers_to_stop = []
        for worker_name, queues in status.get("active_queues", {}).items():
            if any(q["name"] == queue for q in queues):
                workers_to_stop.append(worker_name)
        
        # Stop excess workers
        for worker_name in workers_to_stop[:(current_workers - target_workers)]:
            stop_worker(worker_name, app)
    
    return current_workers, target_workers


def show_status(app: Celery) -> None:
    """
    Show status of Celery workers.

    Args:
        app: Celery app instance
    """
    status = get_worker_status(app)
    
    # Show active workers
    print("=== Active Workers ===")
    if not status["active_workers"]:
        print("No active workers found.")
    else:
        for worker_name, tasks in status["active_workers"].items():
            print(f"Worker: {worker_name}")
            print(f"  Active tasks: {len(tasks)}")
            for i, task in enumerate(tasks[:3], 1):  # Show only first 3 tasks
                print(f"    {i}. {task['name']} (id: {task['id'][:8]}...)")
            if len(tasks) > 3:
                print(f"    ... and {len(tasks) - 3} more tasks")
    
    # Show active queues
    print("\n=== Active Queues ===")
    if not status["active_queues"]:
        print("No active queues found.")
    else:
        # Group by queue name
        queues_by_name = {}
        for worker_name, queues in status["active_queues"].items():
            for queue in queues:
                queue_name = queue["name"]
                if queue_name not in queues_by_name:
                    queues_by_name[queue_name] = []
                queues_by_name[queue_name].append(worker_name)
        
        # Show queues and workers
        for queue_name, workers in queues_by_name.items():
            print(f"Queue: {queue_name}")
            print(f"  Workers: {len(workers)}")
            for worker_name in workers:
                print(f"    - {worker_name}")
    
    # Show queue lengths
    print("\n=== Queue Lengths ===")
    if not status["queue_lengths"]:
        print("Queue length information not available.")
    else:
        # Aggregate queue lengths
        queue_lengths = {}
        for worker_name, lengths in status["queue_lengths"].items():
            for queue_name, length in lengths.items():
                if queue_name not in queue_lengths:
                    queue_lengths[queue_name] = 0
                queue_lengths[queue_name] += length
        
        # Show queue lengths
        for queue_name, length in queue_lengths.items():
            print(f"Queue: {queue_name}")
            print(f"  Length: {length}")


def main():
    """Main entry point for the script."""
    parser = argparse.ArgumentParser(description="Manage Celery workers")
    subparsers = parser.add_subparsers(dest="command", help="Command to run")
    
    # Start command
    start_parser = subparsers.add_parser("start", help="Start workers")
    start_parser.add_argument("queue", help="Queue name")
    start_parser.add_argument("--concurrency", type=int, help="Number of worker processes")
    start_parser.add_argument("--max-tasks", type=int, help="Maximum number of tasks per child process")
    start_parser.add_argument("--broker", help="Redis URL for the Celery broker")
    start_parser.add_argument("--log-file", help="Path to log file")
    
    # Stop command
    stop_parser = subparsers.add_parser("stop", help="Stop workers")
    stop_parser.add_argument("worker", help="Worker name")
    stop_parser.add_argument("--broker", help="Redis URL for the Celery broker")
    
    # Scale command
    scale_parser = subparsers.add_parser("scale", help="Scale workers")
    scale_parser.add_argument("queue", help="Queue name")
    scale_parser.add_argument("--min", type=int, default=1, help="Minimum number of workers")
    scale_parser.add_argument("--max", type=int, default=5, help="Maximum number of workers")
    scale_parser.add_argument("--broker", help="Redis URL for the Celery broker")
    
    # Status command
    status_parser = subparsers.add_parser("status", help="Show worker status")
    status_parser.add_argument("--broker", help="Redis URL for the Celery broker")
    
    args = parser.parse_args()
    
    # Create Celery app
    app = get_celery_app(args.broker if hasattr(args, "broker") else None)
    
    # Execute command
    if args.command == "start":
        start_worker(
            args.queue,
            concurrency=args.concurrency,
            max_tasks_per_child=args.max_tasks,
            broker_url=args.broker,
            log_file=args.log_file,
        )
    elif args.command == "stop":
        stop_worker(args.worker, app)
    elif args.command == "scale":
        current, target = scale_workers(
            app,
            args.queue,
            min_workers=args.min,
            max_workers=args.max,
            broker_url=args.broker,
        )
        print(f"Scaled queue '{args.queue}' from {current} to {target} workers")
    elif args.command == "status":
        show_status(app)
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
