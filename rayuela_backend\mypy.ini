[mypy]
# Configuración de MyPy para Rayuela
# Verificación de tipos estricta para garantizar code quality

python_version = 3.12
warn_return_any = True
warn_unused_configs = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
disallow_untyped_decorators = True
no_implicit_optional = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_no_return = True
warn_unreachable = True
strict_equality = True

# Configuración específica para el proyecto
namespace_packages = True
explicit_package_bases = True

# Configuración para imports
ignore_missing_imports = True
follow_imports = normal

# Configuraciones específicas por módulo
[mypy-tests.*]
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-alembic.*]
ignore_errors = True

[mypy-sqlalchemy.*]
plugins = sqlalchemy.ext.mypy.plugin

# Módulos de terceros que pueden no tener types
[mypy-celery.*]
ignore_missing_imports = True

[mypy-redis.*]
ignore_missing_imports = True

[mypy-fastapi.*]
ignore_missing_imports = True

[mypy-pydantic.*]
ignore_missing_imports = True

[mypy-numpy.*]
ignore_missing_imports = True

[mypy-pandas.*]
ignore_missing_imports = True

[mypy-sklearn.*]
ignore_missing_imports = True

[mypy-pytest.*]
ignore_missing_imports = True 