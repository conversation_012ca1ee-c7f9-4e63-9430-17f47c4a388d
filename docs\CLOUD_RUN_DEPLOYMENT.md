# Despliegue en Cloud Run

Este documento describe el proceso de despliegue de la API de Rayuela en Google Cloud Run.

## Requisitos previos

1. Cuenta de Google Cloud Platform con facturación habilitada
2. Proyecto de GCP creado
3. Google Cloud SDK instalado y configurado
4. Permisos necesarios para:
   - Cloud Run
   - Cloud Build
   - Container Registry
   - Secret Manager
   - Cloud SQL (si se usa)
   - Redis (si se usa Memorystore)

## Configuración de secretos

Antes de desplegar, asegúrate de que los siguientes secretos estén configurados en Secret Manager:

```bash
# Crear secretos
echo "tu_contraseña_db" | gcloud secrets create DB_PASSWORD --data-file=-
echo "tu_contraseña_redis" | gcloud secrets create REDIS_PASSWORD --data-file=-
echo "tu_clave_secreta" | gcloud secrets create SECRET_KEY --data-file=-

# Dar permisos a la cuenta de servicio de Cloud Run
gcloud secrets add-iam-policy-binding DB_PASSWORD \
    --member="serviceAccount:SERVICE_ACCOUNT_EMAIL" \
    --role="roles/secretmanager.secretAccessor"

gcloud secrets add-iam-policy-binding REDIS_PASSWORD \
    --member="serviceAccount:SERVICE_ACCOUNT_EMAIL" \
    --role="roles/secretmanager.secretAccessor"

gcloud secrets add-iam-policy-binding SECRET_KEY \
    --member="serviceAccount:SERVICE_ACCOUNT_EMAIL" \
    --role="roles/secretmanager.secretAccessor"
```

## Despliegue manual

Si deseas desplegar manualmente sin usar Cloud Build:

```bash
# Construir la imagen
docker build -t gcr.io/[PROJECT_ID]/rayuela-api:latest .

# Subir la imagen a Container Registry
docker push gcr.io/[PROJECT_ID]/rayuela-api:latest

# Desplegar en Cloud Run
gcloud run deploy rayuela-api \
  --image=gcr.io/[PROJECT_ID]/rayuela-api:latest \
  --region=us-central1 \
  --platform=managed \
  --allow-unauthenticated \
  --memory=1Gi \
  --cpu=1 \
  --min-instances=0 \
  --max-instances=10 \
  --set-env-vars=ENV=production,DB_HOST=[DB_HOST],DB_PORT=[DB_PORT],DB_USER=[DB_USER],DB_NAME=[DB_NAME],REDIS_HOST=[REDIS_HOST],REDIS_PORT=[REDIS_PORT] \
  --set-secrets=DB_PASSWORD=DB_PASSWORD:latest,REDIS_PASSWORD=REDIS_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest \
  --timeout=120s \
  --concurrency=80
```

## Despliegue automatizado con Cloud Build

Para configurar el despliegue automatizado:

1. Conecta tu repositorio a Cloud Build
2. Configura un trigger que use el archivo `cloudbuild.yaml`

```bash
# Crear un trigger de Cloud Build
gcloud builds triggers create github \
  --repo=[REPO_NAME] \
  --branch-pattern="^main$" \
  --build-config=cloudbuild.yaml
```

## Variables de sustitución en Cloud Build

El archivo `cloudbuild.yaml` utiliza las siguientes variables de sustitución que puedes personalizar:

| Variable | Descripción | Valor predeterminado |
|----------|-------------|---------------------|
| _REGION | Región de GCP | us-central1 |
| _MEMORY | Memoria asignada | 1Gi |
| _CPU | CPUs asignadas | 1 |
| _MIN_INSTANCES | Instancias mínimas | 0 |
| _MAX_INSTANCES | Instancias máximas | 10 |
| _REQUEST_TIMEOUT | Tiempo de espera de solicitud | 120s |
| _CONCURRENCY | Solicitudes concurrentes por instancia | 80 |
| _MIGRATION_MEMORY | Memoria para migraciones | 512Mi |
| _DB_HOST | Host de la base de datos | localhost |
| _DB_PORT | Puerto de la base de datos | 3306 |
| _DB_USER | Usuario de la base de datos | rayuela |
| _DB_NAME | Nombre de la base de datos | rayuela |
| _REDIS_HOST | Host de Redis | localhost |
| _REDIS_PORT | Puerto de Redis | 6379 |

## Migraciones de base de datos

### IMPORTANTE: Uso exclusivo de Alembic en producción

En producción, **SIEMPRE** se debe usar Alembic para las migraciones de base de datos. Nunca se debe usar `Base.metadata.create_all()` o el script `init_db` en entornos de producción.

Las migraciones se ejecutan automáticamente como parte del proceso de despliegue mediante Cloud Run Jobs. Si necesitas ejecutar migraciones manualmente:

```bash
# Ejecutar migraciones manualmente
gcloud run jobs create rayuela-migrations \
  --image=gcr.io/[PROJECT_ID]/rayuela-api:latest \
  --region=us-central1 \
  --memory=512Mi \
  --set-env-vars=ENV=production,DB_HOST=[DB_HOST],DB_PORT=[DB_PORT],DB_USER=[DB_USER],DB_NAME=[DB_NAME] \
  --set-secrets=DB_PASSWORD=DB_PASSWORD:latest,SECRET_KEY=SECRET_KEY:latest,APP_DB_PASSWORD=APP_DB_PASSWORD:latest,MAINTENANCE_DB_PASSWORD=MAINTENANCE_DB_PASSWORD:latest \
  --command=alembic \
  --args=upgrade,head

# Ejecutar el job
gcloud run jobs execute rayuela-migrations --region=us-central1
```

#### Secretos requeridos para migraciones específicas

Algunas migraciones requieren secretos adicionales:

- **add_rls_policies.py**: Requiere `APP_DB_PASSWORD` y `MAINTENANCE_DB_PASSWORD` para crear roles de base de datos con contraseñas seguras.

Estos secretos deben estar configurados en Secret Manager antes de ejecutar las migraciones correspondientes.

### Creación de nuevas migraciones

Para crear una nueva migración después de cambios en los modelos:

```bash
# Generar una nueva migración
alembic revision --autogenerate -m "descripción del cambio"

# Revisar el archivo generado en alembic/versions/
# Editar si es necesario para ajustar la migración

# Aplicar la migración localmente para probarla
alembic upgrade head
```

### Uso del script init_db

El script `init_db` solo debe usarse para:

1. Configuración inicial de un entorno de desarrollo local
2. Creación de la base de datos por primera vez

Nunca debe usarse en producción para aplicar cambios a la estructura de la base de datos.

## Monitoreo y solución de problemas

Para monitorear tu aplicación en Cloud Run:

1. Revisa los logs en Cloud Logging
   ```bash
   gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=rayuela-api" --limit=10
   ```

2. Configura alertas para errores y latencia alta

3. Utiliza Cloud Monitoring para visualizar métricas de rendimiento

## Optimización de costos

- Configura `min-instances=0` para escalar a cero cuando no hay tráfico
- Ajusta `max-instances` según tus necesidades de tráfico
- Monitorea el uso de memoria y CPU para ajustar los recursos asignados
- Considera usar Cloud Run con compromiso para descuentos en uso predecible
