from sqlalchemy.ext.asyncio import AsyncSession
from src.db.repositories import AuditLogRepository  # Importado desde el nuevo paquete
from src.db.schemas import AuditLogCreate
from src.utils.base_logger import log_error


async def write_audit_log_to_db(audit_data: dict, db_session: AsyncSession):
    """Escribe un registro de auditoría en la base de datos.

    Args:
        audit_data: Datos del registro de auditoría
        db_session: Sesión de base de datos
    """
    try:
        # Verificar que account_id esté presente
        if "account_id" not in audit_data or audit_data["account_id"] is None:
            log_error("No se puede crear registro de auditoría sin account_id")
            return

        # Crear el esquema sin account_id
        audit_log_data = {k: v for k, v in audit_data.items() if k != "account_id"}
        audit_log = AuditLogCreate(**audit_log_data)

        # Crear el repositorio con el account_id
        repo = AuditLogRepository(db_session, account_id=audit_data["account_id"])

        # Crear el registro
        await repo.create(audit_log)
    except Exception as e:
        log_error(f"Error escribiendo registro de auditoría: {str(e)}")
        # No relanzar la excepción para no interrumpir el flujo principal
