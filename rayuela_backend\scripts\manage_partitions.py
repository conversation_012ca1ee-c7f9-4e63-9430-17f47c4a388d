import asyncio
import logging
from typing import List, Tuple, Optional
import asyncpg

from src.core.config import settings
from src.utils.partition_helper import ALLOWED_TABLES, get_partition_range
from src.core.exceptions import PartitionError, PartitionCreationError

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def _get_db_connection() -> asyncpg.Connection:
    """Obtiene una conexión a la base de datos."""
    return await asyncpg.connect(
        user=settings.POSTGRES_USER,
        password=settings.POSTGRES_PASSWORD,
        database=settings.POSTGRES_DB,
        host=settings.POSTGRES_SERVER,
        port=settings.POSTGRES_PORT,
    )


async def _get_max_account_id(conn: asyncpg.Connection) -> int:
    """Obtiene el account_id máximo actual."""
    return await conn.fetchval(
        """
        SELECT COALESCE(MAX(account_id), 0)
        FROM accounts
    """
    )


async def _get_existing_partitions(
    conn: asyncpg.Connection, table_name: str
) -> List[Tuple[int, int]]:
    """Obtiene las particiones existentes para una tabla.

    Returns:
        Lista de tuplas (start_range, end_range) de las particiones existentes.
    """
    try:
        result = await conn.fetch(
            """
            SELECT
                SPLIT_PART(inhrelid::regclass::text, '_p_', 2) as range_part
            FROM pg_inherits
            WHERE inhparent = $1::regclass
            """,
            table_name,
        )

        partition_ranges = []
        for row in result:
            if row["range_part"] and "_" in row["range_part"]:
                start_str, end_str = row["range_part"].split("_")
                try:
                    start_range = int(start_str)
                    end_range = int(end_str)
                    partition_ranges.append((start_range, end_range))
                except ValueError:
                    logger.warning(
                        f"No se pudo convertir el rango de partición: {row['range_part']}"
                    )

        return partition_ranges
    except Exception as e:
        logger.error(
            f"Error obteniendo particiones existentes para {table_name}: {str(e)}"
        )
        return []


async def _create_partition(
    conn: asyncpg.Connection, table_name: str, start_id: int, end_id: int
) -> None:
    """Crea una nueva partición RANGE para una tabla."""
    partition_name = f"{table_name}_p_{start_id}_{end_id}"

    try:
        # Crear la partición usando la sintaxis declarativa de PostgreSQL
        await conn.execute(
            f"""
            CREATE TABLE IF NOT EXISTS {partition_name}
            PARTITION OF {table_name}
            FOR VALUES FROM ({start_id}) TO ({end_id})
        """
        )

        # Verificar que la partición se haya creado correctamente
        check_query = """
            SELECT EXISTS (
                SELECT FROM pg_tables
                WHERE tablename = $1
            )
        """
        partition_exists = await conn.fetchval(check_query, partition_name)

        if not partition_exists:
            error_msg = f"Partición {partition_name} no se creó correctamente"
            logger.error(error_msg)
            raise PartitionCreationError(partition_name, "Partition creation failed verification")

        logger.info(f"Partición creada y verificada: {partition_name}")

    except asyncpg.exceptions.UniqueViolationError as e:
        # La partición ya existe (puede ocurrir con CREATE TABLE IF NOT EXISTS)
        logger.warning(f"La partición {partition_name} ya existe: {str(e)}")
        # No lanzamos excepción, ya que la partición existe
    except asyncpg.exceptions.PostgresError as e:
        error_msg = f"Error de PostgreSQL creando partición {partition_name}: {str(e)}"
        logger.error(error_msg)
        raise PartitionCreationError(partition_name, str(e))
    except Exception as e:
        error_msg = f"Error inesperado creando partición {partition_name}: {str(e)}"
        logger.error(error_msg)
        raise PartitionCreationError(partition_name, str(e))


async def _ensure_partitions(
    conn: asyncpg.Connection,
    table_name: str,
    max_account_id: int,
    partition_size: int,
    buffer_count: int,
) -> None:
    """Asegura que existan suficientes particiones para el rango de account_ids."""
    try:
        # Validar que la tabla esté en la lista de tablas permitidas
        if table_name not in ALLOWED_TABLES:
            logger.warning(
                f"Tabla {table_name} no está en la lista de tablas permitidas para particionamiento"
            )
            return

        # Obtener particiones existentes
        existing_partitions = await _get_existing_partitions(conn, table_name)

        # Obtener los rangos de inicio de las particiones existentes
        existing_starts = [start for start, _ in existing_partitions]

        # Calcular el rango actual para el max_account_id
        current_start, current_end = get_partition_range(max_account_id)

        # Asegurar que la partición actual existe
        if current_start not in existing_starts:
            await _create_partition(conn, table_name, current_start, current_end)
            logger.info(
                f"Creada partición actual para {table_name}: {current_start} a {current_end}"
            )

        # Crear particiones adicionales según el buffer
        for _ in range(1, buffer_count + 1):
            next_start = current_end
            next_end = next_start + partition_size

            if next_start not in existing_starts:
                await _create_partition(conn, table_name, next_start, next_end)
                logger.info(
                    f"Creada partición adicional para {table_name}: {next_start} a {next_end}"
                )

            current_end = next_end

        logger.info(f"Particiones verificadas para {table_name}")

    except Exception as e:
        logger.error(f"Error asegurando particiones para {table_name}: {str(e)}")
        raise


async def _get_inactive_accounts(
    conn: asyncpg.Connection,
    inactivity_days: int = 365
) -> List[int]:
    """Obtiene una lista de cuentas inactivas basadas en la última actividad.

    Args:
        conn: Conexión a la base de datos.
        inactivity_days: Número de días de inactividad para considerar una cuenta como inactiva.

    Returns:
        Lista de IDs de cuentas inactivas.
    """
    try:
        # Consulta para encontrar cuentas inactivas basadas en la última actividad
        query = """
            SELECT a.account_id
            FROM accounts a
            LEFT JOIN (
                -- Última actividad de interacciones
                SELECT account_id, MAX(timestamp) as last_activity
                FROM interactions
                GROUP BY account_id
                UNION ALL
                -- Última actividad de logs de auditoría
                SELECT account_id, MAX(created_at) as last_activity
                FROM audit_logs
                GROUP BY account_id
            ) activity ON a.account_id = activity.account_id
            WHERE activity.last_activity IS NULL OR
                  activity.last_activity < NOW() - INTERVAL '{} days'
        """.format(inactivity_days)

        result = await conn.fetch(query)
        inactive_accounts = [row['account_id'] for row in result]

        logger.info(f"Found {len(inactive_accounts)} inactive accounts")
        return inactive_accounts
    except Exception as e:
        logger.error(f"Error getting inactive accounts: {str(e)}")
        return []

async def _detach_partition(
    conn: asyncpg.Connection,
    table_name: str,
    partition_name: str
) -> bool:
    """Desconecta una partición de la tabla principal.

    Args:
        conn: Conexión a la base de datos.
        table_name: Nombre de la tabla principal.
        partition_name: Nombre de la partición a desconectar.

    Returns:
        True si la operación fue exitosa, False en caso contrario.
    """
    try:
        # Verificar que la partición existe
        check_query = """
            SELECT EXISTS (
                SELECT FROM pg_tables
                WHERE tablename = $1
            )
        """
        partition_exists = await conn.fetchval(check_query, partition_name)

        if not partition_exists:
            logger.warning(f"La partición {partition_name} no existe")
            return False

        # Desconectar la partición
        await conn.execute(f"ALTER TABLE {table_name} DETACH PARTITION {partition_name}")

        # Verificar que la partición se haya desconectado correctamente
        check_detached_query = """
            SELECT EXISTS (
                SELECT FROM pg_inherits
                WHERE inhrelid = $1::regclass
            )
        """
        is_attached = await conn.fetchval(check_detached_query, partition_name)

        if is_attached:
            logger.error(f"La partición {partition_name} no se desconectó correctamente")
            return False

        logger.info(f"Partición {partition_name} desconectada exitosamente")
        return True
    except asyncpg.exceptions.PostgresError as e:
        logger.error(f"Error de PostgreSQL desconectando partición {partition_name}: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Error inesperado desconectando partición {partition_name}: {str(e)}")
        return False

async def _archive_partition(
    conn: asyncpg.Connection,
    partition_name: str,
    archive_schema: str = "archived_partitions"
) -> bool:
    """Mueve una partición desconectada a un esquema de archivo.

    Args:
        conn: Conexión a la base de datos.
        partition_name: Nombre de la partición a archivar.
        archive_schema: Nombre del esquema donde se archivarán las particiones.

    Returns:
        True si la operación fue exitosa, False en caso contrario.
    """
    try:
        # Crear el esquema de archivo si no existe
        await conn.execute(f"CREATE SCHEMA IF NOT EXISTS {archive_schema}")

        # Mover la tabla al esquema de archivo
        archived_name = f"{archive_schema}.{partition_name}"
        await conn.execute(f"ALTER TABLE {partition_name} SET SCHEMA {archive_schema}")

        # Verificar que la tabla se haya movido correctamente
        check_query = """
            SELECT EXISTS (
                SELECT FROM pg_tables
                WHERE schemaname = $1 AND tablename = $2
            )
        """
        table_name = partition_name.split('.')[-1]  # Obtener solo el nombre de la tabla sin el esquema
        is_archived = await conn.fetchval(check_query, archive_schema, table_name)

        if not is_archived:
            logger.error(f"La partición {partition_name} no se archivó correctamente")
            return False

        logger.info(f"Partición {partition_name} archivada exitosamente como {archived_name}")
        return True
    except asyncpg.exceptions.PostgresError as e:
        logger.error(f"Error de PostgreSQL archivando partición {partition_name}: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Error inesperado archivando partición {partition_name}: {str(e)}")
        return False

async def archive_inactive_account_partitions(
    inactivity_days: int = 365,
    archive_schema: str = "archived_partitions",
    dry_run: bool = True
) -> None:
    """Archiva particiones de cuentas inactivas.

    Args:
        inactivity_days: Número de días de inactividad para considerar una cuenta como inactiva.
        archive_schema: Nombre del esquema donde se archivarán las particiones.
        dry_run: Si es True, solo muestra lo que se haría sin realizar cambios reales.
    """
    conn = await _get_db_connection()
    try:
        # Obtener cuentas inactivas
        inactive_accounts = await _get_inactive_accounts(conn, inactivity_days)

        if not inactive_accounts:
            logger.info("No se encontraron cuentas inactivas para archivar")
            return

        logger.info(f"Procesando {len(inactive_accounts)} cuentas inactivas para archivado")

        # Procesar cada cuenta inactiva
        for account_id in inactive_accounts:
            # Determinar el rango de partición para esta cuenta
            start_range, end_range = get_partition_range(account_id)

            # Verificar si la cuenta es la única en su rango de partición
            # Si hay otras cuentas activas en el mismo rango, no podemos archivar la partición
            check_other_accounts_query = """
                SELECT COUNT(*) FROM accounts
                WHERE account_id >= $1 AND account_id < $2
                AND account_id != $3
            """
            other_accounts_count = await conn.fetchval(
                check_other_accounts_query,
                start_range,
                end_range,
                account_id
            )

            if other_accounts_count > 0:
                logger.info(
                    f"No se puede archivar la partición para account_id={account_id} "
                    f"porque hay otras {other_accounts_count} cuentas activas en el mismo rango"
                )
                continue

            # Procesar cada tabla particionada
            for table_name in ALLOWED_TABLES:
                partition_name = f"{table_name}_p_{start_range}_{end_range}"

                # Verificar si la partición existe
                check_query = """
                    SELECT EXISTS (
                        SELECT FROM pg_tables
                        WHERE tablename = $1
                    )
                """
                partition_exists = await conn.fetchval(check_query, partition_name)

                if not partition_exists:
                    logger.info(f"La partición {partition_name} no existe, omitiendo")
                    continue

                if dry_run:
                    logger.info(f"[DRY RUN] Se archivaría la partición {partition_name}")
                    continue

                # Desconectar y archivar la partición
                detached = await _detach_partition(conn, table_name, partition_name)
                if detached:
                    archived = await _archive_partition(conn, partition_name, archive_schema)
                    if archived:
                        logger.info(f"Partición {partition_name} archivada exitosamente")
                    else:
                        logger.error(f"Error archivando partición {partition_name}")
                else:
                    logger.error(f"Error desconectando partición {partition_name}")

        if dry_run:
            logger.info("Ejecución en modo simulación completada. No se realizaron cambios reales.")
        else:
            logger.info("Proceso de archivado de particiones completado")

    except Exception as e:
        logger.error(f"Error archivando particiones de cuentas inactivas: {str(e)}")
        raise
    finally:
        await conn.close()

async def create_partitions_proactively(
    target_max_account_id: Optional[int] = None, buffer_count: int = 5
) -> None:
    """Crea particiones proactivamente hasta un ID máximo específico o con un buffer adicional.

    Args:
        target_max_account_id: ID máximo de cuenta hasta el que crear particiones. Si es None,
                              se usa el ID máximo actual en la base de datos.
        buffer_count: Número de particiones adicionales a crear más allá del ID máximo.
    """
    conn = await _get_db_connection()
    try:
        # Obtener el account_id máximo actual si no se especificó uno
        current_max_id = await _get_max_account_id(conn)

        # Usar el máximo proporcionado o el actual, el que sea mayor
        max_account_id = (
            max(current_max_id, target_max_account_id)
            if target_max_account_id is not None
            else current_max_id
        )

        logger.info(
            f"Creating partitions up to account_id: {max_account_id} with {buffer_count} additional buffer partitions"
        )

        # Usar las tablas definidas en ALLOWED_TABLES
        partitioned_tables = list(ALLOWED_TABLES)

        # Asegurar particiones para cada tabla
        for table in partitioned_tables:
            await _ensure_partitions(
                conn, table, max_account_id, settings.PARTITION_SIZE, buffer_count
            )

        logger.info(
            f"Partitions created successfully for all tables up to account_id: {max_account_id}"
        )

    except Exception as e:
        logger.error(f"Error creating partitions proactively: {str(e)}")
        raise
    finally:
        await conn.close()


async def manage_partitions(
    create_new: bool = True,
    archive_old: bool = False,
    inactivity_days: int = 365,
    dry_run: bool = True
) -> None:
    """Función principal para gestionar las particiones.

    Args:
        create_new: Si es True, crea nuevas particiones proactivamente.
        archive_old: Si es True, archiva particiones de cuentas inactivas.
        inactivity_days: Número de días de inactividad para considerar una cuenta como inactiva.
        dry_run: Si es True, el archivado se ejecuta en modo simulación sin realizar cambios reales.
    """
    try:
        # Crear nuevas particiones si se solicita
        if create_new:
            buffer_count = getattr(settings, "PARTITION_BUFFER_COUNT", 5)
            logger.info(f"Creando particiones proactivamente con buffer_count={buffer_count}")
            await create_partitions_proactively(buffer_count=buffer_count)

        # Archivar particiones antiguas si se solicita
        if archive_old:
            logger.info(
                f"Archivando particiones de cuentas inactivas (inactividad > {inactivity_days} días, "
                f"dry_run={dry_run})"
            )
            await archive_inactive_account_partitions(
                inactivity_days=inactivity_days,
                dry_run=dry_run
            )
    except Exception as e:
        logger.error(f"Error en manage_partitions: {str(e)}")
        raise


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Gestionar particiones de base de datos")
    parser.add_argument(
        "--create",
        action="store_true",
        help="Crear nuevas particiones proactivamente"
    )
    parser.add_argument(
        "--archive",
        action="store_true",
        help="Archivar particiones de cuentas inactivas"
    )
    parser.add_argument(
        "--inactivity-days",
        type=int,
        default=365,
        help="Número de días de inactividad para considerar una cuenta como inactiva"
    )
    parser.add_argument(
        "--execute",
        action="store_true",
        help="Ejecutar cambios reales (sin este flag, se ejecuta en modo simulación)"
    )
    parser.add_argument(
        "--buffer",
        type=int,
        default=5,
        help="Número de particiones adicionales a crear más allá del ID máximo"
    )

    args = parser.parse_args()

    # Si no se especifica ninguna acción, crear particiones por defecto
    if not args.create and not args.archive:
        args.create = True

    # Ejecutar la función principal con los argumentos proporcionados
    asyncio.run(
        manage_partitions(
            create_new=args.create,
            archive_old=args.archive,
            inactivity_days=args.inactivity_days,
            dry_run=not args.execute
        )
    )
