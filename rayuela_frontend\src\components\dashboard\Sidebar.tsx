// Ruta EXACTA: rayuela-frontend/src/components/dashboard/Sidebar.tsx
"use client";

import Link from 'next/link';
// Asegúrate de haber añadido 'button' con shadcn: npx shadcn-ui@latest add button
import { Button } from '@/components/ui/button';
import { Logo } from '@/components/ui/logo';
import { ExternalLinkIcon, MessageSquareIcon, BarChart3Icon } from 'lucide-react';

const navItems = [
  { href: '/dashboard', label: 'Dashboard' },
  { href: '/api-keys', label: 'API Keys' },
  { href: '/usage', label: 'Usage' },
  { href: '/recommendation-metrics', label: 'Metrics', icon: <BarChart3Icon className="mr-2 h-4 w-4" /> },
  { href: '/billing', label: 'Billing' },
  { href: '/models', label: 'Models' },
  { href: '/settings', label: 'Settings' },
  { href: 'https://docs.rayuela.ai', label: 'Docs', external: true }, // URL externa a la documentación
];

export default function Sidebar() {
  return (
    <aside className="w-64 bg-gray-800 dark:bg-gray-950 text-white p-4 flex flex-col h-full shrink-0"> {/* Añadido h-full para que ocupe toda la altura */}
      <div className="mb-6">
        <Logo href="/dashboard" className="text-white" />
      </div>
      <nav className="flex flex-col gap-2">
        {navItems.map((item) => (
          <Button
            key={item.href}
            variant="ghost"
            className="justify-start text-left text-white hover:bg-gray-700 hover:text-white px-3" // Ajuste de padding y hover
            asChild
          >
            {item.external ? (
              <a
                href={item.href}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center"
              >
                {item.label}
                <ExternalLinkIcon className="ml-2 h-4 w-4" />
              </a>
            ) : (
              <Link href={item.href} className="flex items-center">
                {item.icon}
                {item.label}
              </Link>
            )}
          </Button>
        ))}
      </nav>

      {/* Spacer para empujar el enlace de feedback al fondo */}
      <div className="flex-grow"></div>

      {/* Enlace de Feedback */}
      <div className="mt-6 pt-4 border-t border-gray-700 dark:border-gray-800">
        <Button
          variant="ghost"
          className="w-full justify-start text-left text-gray-300 hover:bg-gray-700 hover:text-white px-3"
          asChild
        >
          <a
            href="mailto:<EMAIL>?subject=Feedback%20sobre%20Rayuela"
            className="flex items-center"
          >
            <MessageSquareIcon className="mr-2 h-4 w-4" />
            Enviar Feedback
          </a>
        </Button>
      </div>
    </aside>
  );
}