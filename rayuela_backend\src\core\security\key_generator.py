"""
Utilidades para la generación de claves secretas seguras.
"""

import secrets
import base64


def generate_secret_key(length: int = 64) -> str:
    """
    Genera una clave secreta segura para usar como SECRET_KEY.

    Args:
        length: Longitud en bytes de la clave a generar (por defecto: 64 bytes = 512 bits)

    Returns:
        Una cadena de caracteres aleatoria y segura para usar como SECRET_KEY
    """
    # Generar bytes aleatorios con alta entropía
    random_bytes = secrets.token_bytes(length)

    # Codificar en base64 para obtener una representación en texto
    # Usar urlsafe para evitar caracteres especiales que podrían causar problemas
    encoded = base64.urlsafe_b64encode(random_bytes).decode("utf-8")

    return encoded


if __name__ == "__main__":
    # Si se ejecuta directamente, generar y mostrar una clave
    print("\nClave secreta generada (copiar a .env o Secret Manager):")
    print("-" * 80)
    print(generate_secret_key())
    print("-" * 80)
    print("\nIMPORTANTE: Guarda esta clave en un lugar seguro y no la compartas.")
    print(
        "           En producción, usa Google Cloud Secret Manager u otro sistema de gestión de secretos."
    )
