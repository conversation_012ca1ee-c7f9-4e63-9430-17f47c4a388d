"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { KeyIcon, BookOpenIcon, BarChart3Icon, CreditCardIcon } from "lucide-react";

export function QuickActions() {
  const actions = [
    {
      icon: <KeyIcon className="h-4 w-4 mr-2" />,
      label: "Generar API Key",
      href: "/api-keys",
      description: "Crear o regenerar tu API Key"
    },
    {
      icon: <BookOpenIcon className="h-4 w-4 mr-2" />,
      label: "Ver Documentación",
      href: "https://docs.rayuela.ai",
      external: true,
      description: "Consultar guías y referencias"
    },
    {
      icon: <BarChart3Icon className="h-4 w-4 mr-2" />,
      label: "Ver Uso",
      href: "/usage",
      description: "Analizar el uso de la API"
    },
    {
      icon: <CreditCardIcon className="h-4 w-4 mr-2" />,
      label: "Actualizar Plan",
      href: "/billing",
      description: "Gestionar tu suscripción"
    }
  ];

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg">Acciones Rápidas</CardTitle>
      </CardHeader>
      <CardContent className="grid grid-cols-2 gap-2">
        {actions.map((action, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            className="justify-start h-auto py-2 px-3"
            asChild
          >
            {action.external ? (
              <a
                href={action.href}
                target="_blank"
                rel="noopener noreferrer"
                className="flex flex-col items-start"
              >
                <span className="flex items-center text-sm font-medium">
                  {action.icon}
                  {action.label}
                </span>
                <span className="text-xs text-muted-foreground mt-1">{action.description}</span>
              </a>
            ) : (
              <Link href={action.href} className="flex flex-col items-start">
                <span className="flex items-center text-sm font-medium">
                  {action.icon}
                  {action.label}
                </span>
                <span className="text-xs text-muted-foreground mt-1">{action.description}</span>
              </Link>
            )}
          </Button>
        ))}
      </CardContent>
    </Card>
  );
}
