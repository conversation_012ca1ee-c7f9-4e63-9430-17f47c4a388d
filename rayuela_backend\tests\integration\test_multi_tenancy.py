"""
Tests de integración para verificar el aislamiento multi-tenant.
"""
import pytest
from typing import Dict, Any, List
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from sqlalchemy import text

from src.db.session import DatabaseConnectionManager
from src.db.models import Account, SystemUser, Product, Interaction
from src.core.config import settings
from src.utils.base_logger import log_info, log_error

class TestMultiTenancy:
    """Tests para verificar el aislamiento multi-tenant."""
    
    @pytest.fixture
    async def db(self) -> Session:
        """Fixture para la sesión de base de datos."""
        connection_manager = await DatabaseConnectionManager.get_instance()
        session = await connection_manager.get_session()
        try:
            yield session
        finally:
            await session.close()
            
    @pytest.fixture
    async def test_accounts(self, db: Session) -> List[Account]:
        """Fixture para crear cuentas de prueba."""
        accounts = []
        for i in range(2):
            account = Account(
                name=f"Test Account {i}",
                email=f"test{i}@example.com",
                status="active"
            )
            db.add(account)
            await db.commit()
            await db.refresh(account)
            accounts.append(account)
        return accounts
        
    @pytest.fixture
    async def test_users(self, db: Session, test_accounts: List[Account]) -> List[SystemUser]:
        """Fixture para crear usuarios de prueba."""
        users = []
        for account in test_accounts:
            user = SystemUser(
                account_id=account.id,
                email=f"user@{account.email}",
                status="active"
            )
            db.add(user)
            await db.commit()
            await db.refresh(user)
            users.append(user)
        return users
        
    @pytest.fixture
    async def test_products(self, db: Session, test_accounts: List[Account]) -> List[Product]:
        """Fixture para crear productos de prueba."""
        products = []
        for account in test_accounts:
            product = Product(
                account_id=account.id,
                name=f"Test Product {account.id}",
                status="active"
            )
            db.add(product)
            await db.commit()
            await db.refresh(product)
            products.append(product)
        return products
        
    async def test_cross_tenant_read(self, client: TestClient, test_accounts: List[Account], test_products: List[Product]):
        """Test para verificar que no se puede leer datos de otro tenant."""
        # Autenticar como tenant A
        account_a = test_accounts[0]
        product_a = test_products[0]
        product_b = test_products[1]
        
        # Obtener token para tenant A
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": account_a.email,
                "password": "test123"
            }
        )
        token = response.json()["access_token"]
        
        # Intentar leer producto de tenant B
        response = client.get(
            f"/api/v1/products/{product_b.id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 404
        
        # Verificar que sí puede leer su propio producto
        response = client.get(
            f"/api/v1/products/{product_a.id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 200
        
    async def test_cross_tenant_write(self, client: TestClient, test_accounts: List[Account], test_products: List[Product]):
        """Test para verificar que no se puede modificar datos de otro tenant."""
        # Autenticar como tenant A
        account_a = test_accounts[0]
        product_b = test_products[1]
        
        # Obtener token para tenant A
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": account_a.email,
                "password": "test123"
            }
        )
        token = response.json()["access_token"]
        
        # Intentar modificar producto de tenant B
        response = client.put(
            f"/api/v1/products/{product_b.id}",
            headers={"Authorization": f"Bearer {token}"},
            json={"name": "Modified by Tenant A"}
        )
        assert response.status_code == 404
        
    async def test_cross_tenant_delete(self, client: TestClient, test_accounts: List[Account], test_products: List[Product]):
        """Test para verificar que no se puede eliminar datos de otro tenant."""
        # Autenticar como tenant A
        account_a = test_accounts[0]
        product_b = test_products[1]
        
        # Obtener token para tenant A
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": account_a.email,
                "password": "test123"
            }
        )
        token = response.json()["access_token"]
        
        # Intentar eliminar producto de tenant B
        response = client.delete(
            f"/api/v1/products/{product_b.id}",
            headers={"Authorization": f"Bearer {token}"}
        )
        assert response.status_code == 404
        
    async def test_rls_direct_db_access(self, db: Session, test_accounts: List[Account], test_products: List[Product]):
        """Test para verificar RLS a nivel de base de datos."""
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        product_b = test_products[1]
        
        # Establecer tenant_id para account A
        await db.execute(text("SET app.tenant_id = :tenant_id"), {"tenant_id": account_a.id})
        
        # Intentar leer producto de tenant B
        result = await db.execute(
            text("SELECT * FROM products WHERE id = :product_id"),
            {"product_id": product_b.id}
        )
        assert result.fetchone() is None
        
        # Cambiar a tenant B
        await db.execute(text("SET app.tenant_id = :tenant_id"), {"tenant_id": account_b.id})
        
        # Ahora sí debería poder leer el producto
        result = await db.execute(
            text("SELECT * FROM products WHERE id = :product_id"),
            {"product_id": product_b.id}
        )
        assert result.fetchone() is not None
        
    async def test_admin_bypass(self, client: TestClient, test_accounts: List[Account]):
        """Test para verificar el bypass de RLS para administradores."""
        # Crear admin global
        admin_token = await self._create_admin_token(client)
        
        # Intentar acceder a datos de múltiples tenants
        response = client.get(
            "/api/v1/admin/accounts",
            headers={"Authorization": f"Bearer {admin_token}"}
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data) >= len(test_accounts)
        
    async def test_tenant_admin_limits(self, client: TestClient, test_accounts: List[Account]):
        """Test para verificar límites de admin de tenant."""
        # Crear admin de tenant
        account_a = test_accounts[0]
        tenant_admin_token = await self._create_tenant_admin_token(client, account_a.id)
        
        # Intentar acceder a datos de otro tenant
        response = client.get(
            f"/api/v1/admin/accounts/{test_accounts[1].id}",
            headers={"Authorization": f"Bearer {tenant_admin_token}"}
        )
        assert response.status_code == 403
        
    async def _create_admin_token(self, client: TestClient) -> str:
        """Crea un token de administrador global."""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": "<EMAIL>",
                "password": "admin123"
            }
        )
        return response.json()["access_token"]
        
    async def _create_tenant_admin_token(self, client: TestClient, account_id: int) -> str:
        """Crea un token de administrador de tenant."""
        response = client.post(
            "/api/v1/auth/login",
            json={
                "email": f"admin{account_id}@example.com",
                "password": "admin123"
            }
        )
        return response.json()["access_token"]
        
    async def test_redis_cache_isolation(self, db: Session, test_accounts: List[Account], mock_redis):
        """Test para verificar el aislamiento de datos en Redis."""
        from src.core.cache import RecommendationCache
        
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        
        # Crear instancia de caché
        cache = RecommendationCache()
        
        # Guardar datos en caché para tenant A
        await cache.cache_recommendations(
            account_id=account_a.id,
            user_id=1,
            recommendations=[{"product_id": 1, "score": 0.9}],
            limit=10
        )
        
        # Guardar datos en caché para tenant B
        await cache.cache_recommendations(
            account_id=account_b.id,
            user_id=1,  # Mismo user_id que tenant A
            recommendations=[{"product_id": 2, "score": 0.8}],
            limit=10
        )
        
        # Verificar que los datos de tenants están aislados
        tenant_a_recs = await cache.get_cached_recommendations(
            account_id=account_a.id,
            user_id=1,
            limit=10
        )
        assert tenant_a_recs[0]["product_id"] == 1
        
        tenant_b_recs = await cache.get_cached_recommendations(
            account_id=account_b.id,
            user_id=1,
            limit=10
        )
        assert tenant_b_recs[0]["product_id"] == 2
        
    async def test_celery_task_isolation(self, db: Session, test_accounts: List[Account]):
        """Test para verificar el aislamiento de datos en tareas de Celery."""
        from src.workers.celery_tasks import cleanup_old_interactions
        from unittest.mock import patch, MagicMock
        
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        
        # Crear mock para async_to_sync
        mock_async_to_sync = MagicMock()
        mock_async_to_sync.return_value = {"success": True, "deleted_count": 0, "expected_count": 0}
        
        # Ejecutar tarea para tenant A
        with patch("src.workers.celery_tasks.async_to_sync", return_value=mock_async_to_sync):
            result_a = cleanup_old_interactions(
                days_to_keep=30,
                account_id=account_a.id,
                batch_size=100
            )
            
        # Verificar que se llamó la función con el account_id correcto
        mock_async_to_sync.assert_called_with(
            days_to_keep=30,
            account_id=account_a.id,
            batch_size=100,
            max_retries=3,
            retry_delay=5
        )
        
        # Crear otro mock para async_to_sync
        mock_async_to_sync_b = MagicMock()
        mock_async_to_sync_b.return_value = {"success": True, "deleted_count": 0, "expected_count": 0}
        
        # Ejecutar tarea para tenant B
        with patch("src.workers.celery_tasks.async_to_sync", return_value=mock_async_to_sync_b):
            result_b = cleanup_old_interactions(
                days_to_keep=30,
                account_id=account_b.id,
                batch_size=100
            )
            
        # Verificar que se llamó la función con el account_id correcto
        mock_async_to_sync_b.assert_called_with(
            days_to_keep=30,
            account_id=account_b.id,
            batch_size=100,
            max_retries=3,
            retry_delay=5
        ) 