import time
from fastapi import Request
from src.utils.base_logger import log_info
from time import time
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.requests import Request
from starlette.responses import Response
from src.utils.base_logger import logger


class TimingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        start_time = time()  # Captura el tiempo al inicio de la solicitud
        response = await call_next(request)  # Ejecuta la solicitud
        process_time = time() - start_time  # Calcula el tiempo transcurrido

        logger.info(
            f"Request {request.method} {request.url.path} completed in {process_time:.4f} seconds"
        )

        response.headers["X-Process-Time"] = str(
            process_time
        )  # Agrega el tiempo de respuesta en los headers
        return response
