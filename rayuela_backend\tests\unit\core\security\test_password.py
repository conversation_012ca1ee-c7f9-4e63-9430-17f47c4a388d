import pytest
from src.core.security.password import verify_password, get_password_hash

def test_password_hashing():
    """Test para verificar el hashing de contraseñas."""
    # Contraseña de prueba
    password = "TestPassword123!"
    
    # Hashear la contraseña
    hashed_password = get_password_hash(password)
    
    # Verificar formato
    assert isinstance(hashed_password, str)
    assert hashed_password.startswith('$2b$')  # Formato bcrypt
    assert len(hashed_password) > len(password)  # El hash debe ser más largo

def test_password_verification():
    """Test para verificar la validación de contraseñas."""
    # Contraseña de prueba
    password = "TestPassword123!"
    
    # Hashear la contraseña
    hashed_password = get_password_hash(password)
    
    # Verificar que la contraseña original es válida
    assert verify_password(password, hashed_password) is True
    
    # Verificar que una contraseña incorrecta no es válida
    wrong_password = "WrongPassword123!"
    assert verify_password(wrong_password, hashed_password) is False

def test_password_case_sensitivity():
    """Test para verificar que las contraseñas son case-sensitive."""
    # Contraseña de prueba
    password = "TestPassword123!"
    
    # Hashear la contraseña
    hashed_password = get_password_hash(password)
    
    # Verificar que una versión en mayúsculas no es válida
    assert verify_password(password.upper(), hashed_password) is False

def test_password_special_characters():
    """Test para verificar el manejo de caracteres especiales."""
    # Contraseña con caracteres especiales
    password = "Test@Password#123$"
    
    # Hashear la contraseña
    hashed_password = get_password_hash(password)
    
    # Verificar que la contraseña original es válida
    assert verify_password(password, hashed_password) is True

def test_password_length():
    """Test para verificar el manejo de contraseñas de diferentes longitudes."""
    # Lista de contraseñas de diferentes longitudes
    passwords = [
        "a",  # Muy corta
        "a" * 100,  # Muy larga
        "Test123!",  # Longitud media
        "P@ssw0rd",  # Longitud típica
    ]
    
    for password in passwords:
        # Hashear la contraseña
        hashed_password = get_password_hash(password)
        
        # Verificar que la contraseña original es válida
        assert verify_password(password, hashed_password) is True

def test_password_unicode():
    """Test para verificar el manejo de caracteres Unicode."""
    # Contraseña con caracteres Unicode
    password = "TestPassword123!ñáéíóú"
    
    # Hashear la contraseña
    hashed_password = get_password_hash(password)
    
    # Verificar que la contraseña original es válida
    assert verify_password(password, hashed_password) is True 