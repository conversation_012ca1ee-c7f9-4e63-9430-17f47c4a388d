"""
Servicio para la integración con Mercado Pago.
"""

import mercadopago
from typing import Dict, Any, Optional, List
from datetime import datetime, timezone
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.config import settings
from src.db.repositories.account import AccountRepository
from src.db.repositories.subscription import SubscriptionRepository
from src.db.enums import SubscriptionPlan, PLAN_LIMITS
from src.utils.base_logger import log_info, log_error, log_warning


class MercadoPagoService:
    """
    Servicio para la integración con Mercado Pago.
    """
    
    def __init__(self):
        """
        Inicializa el SDK de Mercado Pago con las credenciales configuradas.
        """
        self.sdk = mercadopago.SDK(settings.MERCADOPAGO_ACCESS_TOKEN)
        
    async def create_checkout_session(
        self,
        price_id: str,
        account_id: str,
        user_id: str,
        account_email: str,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Crea una sesión de checkout de Mercado Pago.
        
        Args:
            price_id: ID del precio en Mercado Pago
            account_id: ID de la cuenta
            user_id: ID del usuario
            account_email: Email de la cuenta
            success_url: URL de redirección en caso de éxito
            cancel_url: URL de redirección en caso de cancelación
            
        Returns:
            Datos de la sesión de checkout
        """
        try:
            # Determinar el plan basado en el price_id
            plan = self._get_plan_from_price_id(price_id)
            
            # Crear preferencia de pago
            preference_data = {
                "items": [
                    {
                        "title": f"Plan {plan.name} - Rayuela",
                        "quantity": 1,
                        "currency_id": "ARS",  # Moneda Argentina
                        "unit_price": self._get_price_for_plan(plan),
                        "id": price_id,
                        "description": f"Suscripción al plan {plan.name} de Rayuela"
                    }
                ],
                "payer": {
                    "email": account_email
                },
                "back_urls": {
                    "success": success_url or f"{settings.FRONTEND_URL}/billing?success=true",
                    "failure": cancel_url or f"{settings.FRONTEND_URL}/billing?canceled=true",
                    "pending": f"{settings.FRONTEND_URL}/billing?pending=true"
                },
                "auto_return": "approved",
                "payment_methods": {
                    "excluded_payment_types": [
                        {"id": "ticket"},
                        {"id": "atm"}
                    ],
                    "installments": 1
                },
                "metadata": {
                    "account_id": account_id,
                    "user_id": user_id,
                    "price_id": price_id,
                    "plan": plan.name
                }
            }
            
            # Crear preferencia
            preference_response = self.sdk.preference().create(preference_data)
            preference = preference_response["response"]
            
            log_info(f"Mercado Pago checkout session created for account {account_id}, user {user_id}, price {price_id}")
            
            return {
                "url": preference["init_point"],
                "preference_id": preference["id"]
            }
            
        except Exception as e:
            log_error(f"Error creating Mercado Pago checkout session: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating checkout session: {str(e)}"
            )
            
    async def create_subscription(
        self,
        account_id: str,
        user_id: str,
        account_email: str,
        price_id: str,
        success_url: Optional[str] = None,
        cancel_url: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Crea una suscripción en Mercado Pago.
        
        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario
            account_email: Email de la cuenta
            price_id: ID del precio en Mercado Pago
            success_url: URL de redirección en caso de éxito
            cancel_url: URL de redirección en caso de cancelación
            
        Returns:
            Datos de la suscripción
        """
        try:
            # Determinar el plan basado en el price_id
            plan = self._get_plan_from_price_id(price_id)
            
            # Crear preferencia de pago recurrente
            preapproval_data = {
                "reason": f"Plan {plan.name} - Rayuela",
                "payer_email": account_email,
                "auto_recurring": {
                    "frequency": 1,
                    "frequency_type": "months",
                    "transaction_amount": self._get_price_for_plan(plan),
                    "currency_id": "ARS"
                },
                "back_url": success_url or f"{settings.FRONTEND_URL}/billing?success=true",
                "status": "authorized",
                "metadata": {
                    "account_id": account_id,
                    "user_id": user_id,
                    "price_id": price_id,
                    "plan": plan.name
                }
            }
            
            # Crear suscripción
            preapproval_response = self.sdk.preapproval().create(preapproval_data)
            preapproval = preapproval_response["response"]
            
            log_info(f"Mercado Pago subscription created for account {account_id}, user {user_id}, price {price_id}")
            
            return {
                "url": preapproval["init_point"],
                "subscription_id": preapproval["id"]
            }
            
        except Exception as e:
            log_error(f"Error creating Mercado Pago subscription: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating subscription: {str(e)}"
            )
            
    async def create_portal_session(
        self,
        customer_id: str,
        return_url: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Crea una sesión del portal de facturación de Mercado Pago.
        
        Args:
            customer_id: ID del cliente en Mercado Pago
            return_url: URL de retorno después de usar el portal
            
        Returns:
            URL de la sesión del portal
        """
        # Mercado Pago no tiene un portal de facturación como Stripe
        # Redirigimos al usuario a la página de suscripciones de Mercado Pago
        return {
            "url": f"https://www.mercadopago.com.ar/subscriptions"
        }
        
    async def get_or_create_customer(
        self,
        account_id: str,
        account_name: str,
        account_email: str,
        db: AsyncSession
    ) -> str:
        """
        Obtiene o crea un cliente de Mercado Pago para la cuenta.
        
        Args:
            account_id: ID de la cuenta
            account_name: Nombre de la cuenta
            account_email: Email de la cuenta
            db: Sesión de base de datos
            
        Returns:
            ID del cliente de Mercado Pago
        """
        try:
            # Obtener la cuenta
            account_repo = AccountRepository(db)
            account = await account_repo.get_by_id(account_id)
            
            # Si ya tiene un customer_id, devolverlo
            if account.mercadopago_customer_id:
                return account.mercadopago_customer_id
                
            # Crear un nuevo cliente en Mercado Pago
            customer_data = {
                "email": account_email,
                "first_name": account_name,
                "description": f"Cliente de Rayuela (ID: {account_id})"
            }
            
            customer_response = self.sdk.customer().create(customer_data)
            customer = customer_response["response"]
            
            # Actualizar la cuenta con el customer_id
            await account_repo.update(
                account_id,
                {"mercadopago_customer_id": customer["id"]}
            )
            
            return customer["id"]
            
        except Exception as e:
            log_error(f"Error getting or creating Mercado Pago customer: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting or creating customer: {str(e)}"
            )
            
    def _get_plan_from_price_id(self, price_id: str) -> SubscriptionPlan:
        """
        Determina el plan basado en el price_id.
        
        Args:
            price_id: ID del precio en Mercado Pago
            
        Returns:
            Plan de suscripción
        """
        price_map = {
            settings.MERCADOPAGO_PRICE_STARTER: SubscriptionPlan.STARTER,
            settings.MERCADOPAGO_PRICE_PRO: SubscriptionPlan.PRO,
            settings.MERCADOPAGO_PRICE_ENTERPRISE: SubscriptionPlan.ENTERPRISE
        }
        
        return price_map.get(price_id, SubscriptionPlan.FREE)
        
    def _get_price_for_plan(self, plan: SubscriptionPlan) -> float:
        """
        Obtiene el precio para un plan desde la configuración centralizada.

        Args:
            plan: Plan de suscripción

        Returns:
            Precio del plan en ARS
        """
        # Obtener el precio desde PLAN_LIMITS (fuente de verdad centralizada)
        plan_config = PLAN_LIMITS.get(plan, {})
        price = plan_config.get("price_ars", 0.0)

        # Fallback a configuración de settings si no está en PLAN_LIMITS
        if price == 0.0 and plan != SubscriptionPlan.FREE:
            price_amounts = settings.MERCADOPAGO_PRICE_AMOUNTS
            plan_name = plan.name
            price = price_amounts.get(plan_name, 0.0)

        return price
