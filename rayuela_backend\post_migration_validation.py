#!/usr/bin/env python3
"""
Script de validación post-migración para verificar que la migración de tipos de ID se completó correctamente.

Verifica:
1. Todos los IDs son ahora Integer
2. Las claves foráneas funcionan correctamente
3. Las relaciones SQLAlchemy funcionan
4. No hay pérdida de datos

Ejecutar después de la migración para confirmar el éxito.
"""

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import inspect, text, create_engine
from sqlalchemy.engine import reflection
from src.core.config import settings
from src.db.models import (
    EndUser, Product, Order, OrderItem, 
    Interaction, Recommendation, Search, Account
)

def get_sync_engine():
    """Crear un motor síncrono para las validaciones."""
    # Convertir la URL asíncrona a síncrona
    sync_url = settings.database_url.replace("postgresql+asyncpg://", "postgresql://")
    return create_engine(sync_url)

def check_table_exists(inspector, table_name):
    """Verificar si una tabla existe."""
    tables = inspector.get_table_names()
    return table_name in tables

def check_column_type(inspector, table_name, column_name):
    """Obtener el tipo de dato de una columna."""
    if not check_table_exists(inspector, table_name):
        return None
    
    columns = inspector.get_columns(table_name)
    for col in columns:
        if col['name'] == column_name:
            return str(col['type'])
    return None

def check_foreign_keys(inspector, table_name):
    """Obtener las claves foráneas de una tabla."""
    if not check_table_exists(inspector, table_name):
        return []
    
    return inspector.get_foreign_keys(table_name)

def count_records(engine, table_name):
    """Contar registros en una tabla."""
    try:
        with engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            return result.scalar()
    except Exception as e:
        print(f"  ❌ Error contando registros en {table_name}: {e}")
        return None

def verify_integer_sequences(engine):
    """Verificar que las secuencias de auto-incremento están configuradas correctamente."""
    try:
        with engine.connect() as conn:
            # Verificar secuencias principales
            sequences_to_check = [
                ('end_users_user_id_seq', 'end_users', 'user_id'),
                ('products_product_id_seq', 'products', 'product_id')
            ]
            
            results = []
            for seq_name, table_name, column_name in sequences_to_check:
                # Verificar que la secuencia existe
                seq_check = conn.execute(text(f"""
                    SELECT EXISTS (
                        SELECT 1 FROM pg_sequences 
                        WHERE sequencename = '{seq_name}'
                    )
                """)).scalar()
                
                if seq_check:
                    # Obtener el valor actual de la secuencia
                    seq_val = conn.execute(text(f"SELECT last_value FROM {seq_name}")).scalar()
                    
                    # Obtener el máximo ID actual en la tabla
                    max_id = conn.execute(text(f"SELECT COALESCE(MAX({column_name}), 0) FROM {table_name}")).scalar()
                    
                    results.append({
                        'sequence': seq_name,
                        'table': table_name,
                        'exists': True,
                        'seq_value': seq_val,
                        'max_id': max_id,
                        'sync': seq_val >= max_id
                    })
                else:
                    results.append({
                        'sequence': seq_name,
                        'table': table_name,
                        'exists': False
                    })
            
            return results
    except Exception as e:
        print(f"❌ Error verificando secuencias: {e}")
        return []

def test_referential_integrity(engine):
    """Probar que las claves foráneas funcionan correctamente."""
    try:
        with engine.connect() as conn:
            # Test 1: Verificar que no hay registros huérfanos en orders
            orphan_orders = conn.execute(text("""
                SELECT COUNT(*) FROM orders o 
                LEFT JOIN end_users eu ON o.user_id = eu.user_id 
                WHERE eu.user_id IS NULL
            """)).scalar()
            
            # Test 2: Verificar que no hay registros huérfanos en order_items
            orphan_order_items = conn.execute(text("""
                SELECT COUNT(*) FROM order_items oi 
                LEFT JOIN products p ON oi.product_id = p.product_id 
                WHERE p.product_id IS NULL
            """)).scalar()
            
            # Test 3: Verificar que no hay registros huérfanos en interactions
            orphan_interactions_users = conn.execute(text("""
                SELECT COUNT(*) FROM interactions i 
                LEFT JOIN end_users eu ON i.end_user_id = eu.user_id 
                WHERE eu.user_id IS NULL
            """)).scalar()
            
            orphan_interactions_products = conn.execute(text("""
                SELECT COUNT(*) FROM interactions i 
                LEFT JOIN products p ON i.product_id = p.product_id 
                WHERE p.product_id IS NULL
            """)).scalar()
            
            return {
                'orphan_orders': orphan_orders,
                'orphan_order_items': orphan_order_items,
                'orphan_interactions_users': orphan_interactions_users,
                'orphan_interactions_products': orphan_interactions_products
            }
    except Exception as e:
        print(f"❌ Error verificando integridad referencial: {e}")
        return None

def main():
    print("🔍 Validación Post-Migración de Tipos de ID")
    print("=" * 50)
    
    # Obtener el motor de base de datos
    try:
        engine = get_sync_engine()
        inspector = inspect(engine)
        print("✅ Conexión a la base de datos establecida")
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {e}")
        return 1
    
    print("\n📊 Verificación de Tipos de Datos:")
    print("-" * 35)
    
    # Verificar que todos los IDs principales son ahora Integer
    expected_integer_columns = {
        'end_users': ['user_id'],
        'products': ['product_id'],
        'orders': ['user_id'],
        'order_items': ['product_id'],
        'interactions': ['end_user_id', 'product_id'],
        'recommendations': ['end_user_id', 'product_id'],
        'searches': ['end_user_id'],
    }
    
    all_integers = True
    for table_name, columns in expected_integer_columns.items():
        print(f"\n🔍 Tabla: {table_name}")
        
        if not check_table_exists(inspector, table_name):
            print(f"  ❌ Tabla {table_name} no existe")
            all_integers = False
            continue
        
        for column_name in columns:
            data_type = check_column_type(inspector, table_name, column_name)
            if data_type:
                # Verificar si el tipo es Integer o equivalente
                is_integer = any(int_type in data_type.upper() for int_type in ['INTEGER', 'BIGINT', 'SERIAL', 'BIGSERIAL'])
                status = "✅" if is_integer else "❌"
                print(f"  {status} {column_name}: {data_type}")
                if not is_integer:
                    all_integers = False
            else:
                print(f"  ❌ Columna {column_name} no encontrada")
                all_integers = False
    
    print("\n🔗 Verificación de Claves Foráneas:")
    print("-" * 35)
    
    # Verificar que las claves foráneas están correctamente definidas
    expected_foreign_keys = {
        'orders': [('user_id', 'end_users', ['user_id'])],
        'order_items': [('product_id', 'products', ['product_id'])],
        'interactions': [
            ('end_user_id', 'end_users', ['user_id']),
            ('product_id', 'products', ['product_id'])
        ],
        'recommendations': [
            ('end_user_id', 'end_users', ['user_id']),
            ('product_id', 'products', ['product_id'])
        ],
        'searches': [('end_user_id', 'end_users', ['user_id'])],
    }
    
    all_fks_valid = True
    for table_name, expected_fks in expected_foreign_keys.items():
        if not check_table_exists(inspector, table_name):
            continue
            
        print(f"\n🔍 Tabla: {table_name}")
        actual_fks = check_foreign_keys(inspector, table_name)
        
        for expected_col, expected_ref_table, expected_ref_cols in expected_fks:
            # Buscar si existe la FK esperada
            fk_found = False
            for fk in actual_fks:
                if (expected_col in fk['constrained_columns'] and 
                    fk['referred_table'] == expected_ref_table and
                    set(fk['referred_columns']) == set(expected_ref_cols)):
                    fk_found = True
                    print(f"  ✅ {expected_col} -> {expected_ref_table}.{expected_ref_cols}")
                    break
            
            if not fk_found:
                print(f"  ❌ FK faltante: {expected_col} -> {expected_ref_table}.{expected_ref_cols}")
                all_fks_valid = False
    
    print("\n🔢 Verificación de Secuencias de Auto-incremento:")
    print("-" * 45)
    
    sequence_results = verify_integer_sequences(engine)
    all_sequences_ok = True
    
    for result in sequence_results:
        print(f"\n🔍 Secuencia: {result['sequence']}")
        if result['exists']:
            print(f"  ✅ Existe: Sí")
            print(f"  📊 Valor actual: {result['seq_value']}")
            print(f"  📊 Max ID en tabla: {result['max_id']}")
            if result['sync']:
                print(f"  ✅ Sincronización: OK")
            else:
                print(f"  ⚠️  Sincronización: La secuencia necesita ajuste")
                all_sequences_ok = False
        else:
            print(f"  ❌ Existe: No")
            all_sequences_ok = False
    
    print("\n🔗 Verificación de Integridad Referencial:")
    print("-" * 40)
    
    integrity_results = test_referential_integrity(engine)
    all_integrity_ok = True
    
    if integrity_results:
        print(f"🔍 Órdenes huérfanas: {integrity_results['orphan_orders']}")
        print(f"🔍 Items de órdenes huérfanos: {integrity_results['orphan_order_items']}")
        print(f"🔍 Interacciones huérfanas (usuarios): {integrity_results['orphan_interactions_users']}")
        print(f"🔍 Interacciones huérfanas (productos): {integrity_results['orphan_interactions_products']}")
        
        total_orphans = sum(integrity_results.values())
        if total_orphans == 0:
            print("✅ Integridad referencial: OK")
        else:
            print(f"❌ Se encontraron {total_orphans} registros huérfanos")
            all_integrity_ok = False
    else:
        print("❌ No se pudo verificar la integridad referencial")
        all_integrity_ok = False
    
    print("\n📊 Conteo de Registros Post-Migración:")
    print("-" * 40)
    
    # Contar registros en todas las tablas
    tables_to_count = ['end_users', 'products', 'orders', 'order_items', 
                      'interactions', 'recommendations', 'searches']
    
    total_records = 0
    for table_name in tables_to_count:
        if check_table_exists(inspector, table_name):
            count = count_records(engine, table_name)
            if count is not None:
                print(f"📊 {table_name}: {count:,} registros")
                total_records += count
    
    print(f"\n📊 Total de registros: {total_records:,}")
    
    print("\n🧪 Verificación de Modelos SQLAlchemy:")
    print("-" * 40)
    
    # Verificar que los modelos SQLAlchemy están correctos
    models_to_check = [
        ('EndUser', EndUser),
        ('Product', Product),
        ('Order', Order),
        ('OrderItem', OrderItem),
        ('Interaction', Interaction),
        ('Recommendation', Recommendation),
        ('Search', Search),
    ]
    
    all_models_ok = True
    for model_name, model_class in models_to_check:
        try:
            table = model_class.__table__
            print(f"✅ {model_name}: OK")
            
            # Verificar relaciones si existen
            if hasattr(model_class, '__mapper__'):
                relationships = [rel.key for rel in model_class.__mapper__.relationships]
                if relationships:
                    print(f"  🔗 Relaciones: {', '.join(relationships)}")
            
        except Exception as e:
            print(f"❌ Error en modelo {model_name}: {e}")
            all_models_ok = False
    
    print(f"\n{'='*50}")
    print("📋 RESUMEN DE VALIDACIÓN POST-MIGRACIÓN")
    print("=" * 50)
    
    all_passed = all([all_integers, all_fks_valid, all_sequences_ok, all_integrity_ok, all_models_ok])
    
    print(f"📊 Tipos de datos Integer: {'✅ PASÓ' if all_integers else '❌ FALLÓ'}")
    print(f"🔗 Claves foráneas: {'✅ PASÓ' if all_fks_valid else '❌ FALLÓ'}")
    print(f"🔢 Secuencias auto-incremento: {'✅ PASÓ' if all_sequences_ok else '❌ FALLÓ'}")
    print(f"🔗 Integridad referencial: {'✅ PASÓ' if all_integrity_ok else '❌ FALLÓ'}")
    print(f"🧪 Modelos SQLAlchemy: {'✅ PASÓ' if all_models_ok else '❌ FALLÓ'}")
    
    if all_passed:
        print("\n🎉 ¡MIGRACIÓN COMPLETADA EXITOSAMENTE!")
        print("✅ Todos los tipos de ID han sido unificados a Integer")
        print("✅ Las relaciones funcionan correctamente")
        print("✅ No hay pérdida de datos")
        print("\n📝 Próximos pasos:")
        print("1. Verificar que la aplicación funciona correctamente")
        print("2. Actualizar APIs que devuelven estos IDs")
        print("3. Actualizar clientes que consumen las APIs")
        return 0
    else:
        print("\n❌ LA MIGRACIÓN TIENE PROBLEMAS")
        print("🚨 Se detectaron problemas que requieren atención")
        print("📝 Revisa los errores arriba y toma las acciones necesarias")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 