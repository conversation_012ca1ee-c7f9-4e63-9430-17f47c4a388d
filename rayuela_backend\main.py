# main.py
from fastapi import FastAPI, Depends
from contextlib import asynccontextmanager
from src.api.v1.api import public_router, private_router
from src.api.v1.endpoints.health import router as health_router  # Endpoint de salud simple
from src.core.config import settings
from src.middleware.setup import setup_middleware
from src.utils.base_logger import logger
from src.utils.maintenance import cleanup_old_audit_logs  # Mantenimiento
from src.core.deps import get_current_account
from src.db.session import init_db  # Asumiendo que init_db crea tablas si no existen

# Alternativa a 'schedule' para tareas periódicas en producción:
# - Cloud Scheduler (GCP) llamando a un endpoint protegido en tu API.
# - Un worker separado (Celery Beat, APScheduler en un proceso dedicado).
# - Kubernetes CronJob.


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info(f"Starting up Rayuela - ENV: {settings.ENV}")

    # Cargar secretos de GCP en producción
    if settings.ENV == "production":
        logger.info("🔐 Cargando secretos desde GCP Secret Manager...")
        try:
            result = settings.load_secrets_from_gcp()
            if result:
                logger.info("✅ Secretos cargados correctamente desde Secret Manager")
                logger.info("🔒 Configuración de infraestructura protegida")
            else:
                logger.error("❌ Error crítico: No se pudieron cargar secretos requeridos desde Secret Manager")
                logger.error("🚨 La aplicación no puede funcionar sin acceso a la configuración de infraestructura")
                logger.error("🔧 Verifica que todos los secretos estén configurados: python -m scripts.verify_secrets --project-id YOUR_PROJECT_ID")
                raise RuntimeError("Secretos críticos no disponibles en Secret Manager")
        except Exception as e:
            logger.error(f"❌ Error crítico al cargar secretos desde GCP: {e}")
            logger.error("🚨 La aplicación no puede continuar sin acceso a Secret Manager")
            logger.error("🔧 Soluciones posibles:")
            logger.error("   1. Verificar que el service account tenga permisos de Secret Manager")
            logger.error("   2. Configurar secretos: python -m scripts.setup_secrets --project-id YOUR_PROJECT_ID")
            logger.error("   3. Verificar conectividad a Google Cloud APIs")
            raise RuntimeError(f"Error crítico en Secret Manager: {e}")
    
    # NOTA: No inicializamos la base de datos aquí.
    # En producción, las migraciones deben aplicarse durante el despliegue con:
    #     alembic upgrade head
    #
    # Para desarrollo local, usar el script init_db.py:
    #     python -m scripts.init_db
    #
    # if settings.ENV == "development":
    #     logger.info("Verificando particiones de base de datos...")
    #     try:
    #         logger.error(f"Database initialization error: {e}", exc_info=True)
    #         # En producción, fallar al arrancar si la DB no está lista puede ser deseable
    #         if settings.ENV == "production":
    #              raise RuntimeError("Failed to initialize database.") from e

    yield

    # Shutdown
    logger.info("Shutting down Rayuela")
    # Cerrar pool de conexiones si es necesario (DatabaseConnectionManager podría manejarlo)
    # connection_manager = await DatabaseConnectionManager.get_instance()
    # await connection_manager.close_engine()


app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.API_VERSION,
    docs_url="/api/docs",
    openapi_url="/api/openapi.json",
    lifespan=lifespan,
)

# Configurar TODOS los middlewares usando la función centralizada
setup_middleware(app)


# Endpoint de salud básico (fuera de /api/v1)
@app.get("/health", tags=["Health"])
async def health_check():
    # Podría incluir chequeos de DB y Redis aquí si es necesario
    return {"status": "healthy"}


# Incluir routers
# app.include_router(health_router, prefix="/api/v1") # Ya no es necesario si está arriba
app.include_router(public_router, prefix="/api/v1")  # Rutas públicas bajo /api/v1

# Incluir el webhook de Stripe como ruta pública
from src.api.v1.endpoints.billing import router as billing_router
app.include_router(billing_router, prefix="/api/v1/billing", tags=["billing"], include_in_schema=False)

# Asegúrate que get_current_account maneje la API Key
app.include_router(
    private_router, prefix="/api/v1", dependencies=[Depends(get_current_account)]
)

# Tareas de mantenimiento (ejecutar externamente)
# if settings.ENV == "production":
#     # Usa Cloud Scheduler o similar para llamar a un endpoint protegido
#     # o ejecutar un script/worker separado.
#     pass

def find_available_port(start_port: int, max_attempts: int = 10) -> int:
    """
    Busca un puerto disponible a partir del puerto inicial.

    Args:
        start_port: Puerto inicial para la búsqueda
        max_attempts: Número máximo de intentos

    Returns:
        Puerto disponible o None si no se encuentra ninguno
    """
    import socket
    from contextlib import closing

    def is_port_available(port: int) -> bool:
        with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as sock:
            try:
                sock.bind(("0.0.0.0", port))
                return True
            except socket.error:
                return False

    port = start_port
    for _ in range(max_attempts):
        if is_port_available(port):
            return port
        port += 1

    # Si no se encuentra un puerto disponible, devolver el puerto original
    # y dejar que uvicorn maneje el error
    return start_port

if __name__ == "__main__":
    import uvicorn
    import socket

    # Uvicorn se ejecuta desde Dockerfile o docker-compose
    # Usamos el host y puerto configurados en settings
    port = settings.API_PORT

    try:
        # Intentar ejecutar la aplicación en el puerto configurado
        logger.info(f"Iniciando servidor en {settings.API_HOST}:{port}")
        uvicorn.run(app, host=settings.API_HOST, port=port)
    except OSError as e:
        if "Address already in use" in str(e) or "[Errno 10048]" in str(e):
            # El puerto está ocupado, intentar encontrar uno disponible
            logger.warning(f"El puerto {port} ya está en uso. Buscando un puerto alternativo...")
            new_port = find_available_port(port + 1)
            if new_port != port:
                logger.info(f"Usando puerto alternativo: {new_port}")
                uvicorn.run(app, host=settings.API_HOST, port=new_port)
            else:
                logger.error(f"No se encontró un puerto disponible después de {port}")
                raise
        else:
            # Otro tipo de error
            logger.error(f"Error al iniciar el servidor: {e}")
            raise
