import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from src.db.enums import SubscriptionPlan, RoleType
from src.db.repositories.account import AccountRepository
from src.db.repositories.user import SystemUserRepository
from src.db.repositories.auth import RoleRepository, SystemUserRoleRepository
from src.core.security import verify_password


@pytest.mark.asyncio
async def test_register_validation(client: AsyncClient):
    """Test validation errors in the registration endpoint."""
    # Datos de prueba incompletos (falta password)
    invalid_data = {
        "account_name": "Test Account",
        "subscription_plan": SubscriptionPlan.FREE.value,
        "email": "<EMAIL>",
        # Falta password
        "first_name": "Admin",
        "last_name": "User"
    }

    # Llamar al endpoint de registro
    response = await client.post("/api/v1/auth/register", json=invalid_data)

    # Verificar que se devuelve un error de validación
    assert response.status_code == 422
    data = response.json()
    assert "detail" in data

    # Datos con password demasiado corta
    invalid_data = {
        "account_name": "Test Account",
        "subscription_plan": SubscriptionPlan.FREE.value,
        "email": "<EMAIL>",
        "password": "short",  # Menos de 8 caracteres
        "first_name": "Admin",
        "last_name": "User"
    }

    # Llamar al endpoint de registro
    response = await client.post("/api/v1/auth/register", json=invalid_data)

    # Verificar que se devuelve un error de validación
    assert response.status_code == 422
    data = response.json()
    assert "detail" in data


@pytest.mark.asyncio
async def test_register(client: AsyncClient, db_session: AsyncSession):
    """Test the registration endpoint."""
    # Datos de prueba
    register_data = {
        "account_name": "Test Account",
        "subscription_plan": SubscriptionPlan.FREE.value,
        "email": "<EMAIL>",
        "password": "securepassword123",
        "first_name": "Admin",
        "last_name": "User"
    }

    # Llamar al endpoint de registro
    response = await client.post("/api/v1/auth/register", json=register_data)

    # Verificar respuesta exitosa
    assert response.status_code == 200
    data = response.json()

    # Verificar que se devuelve la información de la cuenta
    assert data["name"] == register_data["account_name"]
    assert data["subscription_plan"] == register_data["subscription_plan"]
    assert "api_key" in data  # Verificar que se devuelve la API key

    # Verificar que la cuenta se creó en la base de datos
    account_repo = AccountRepository(db_session)
    account = await account_repo.get_by_id(data["account_id"])
    assert account is not None
    # Comparar con el valor del atributo name
    db_account_name = getattr(account, "name", None)
    assert db_account_name == register_data["account_name"]

    # Verificar que el usuario administrador se creó
    # Usar directamente el account_id del response JSON que ya es un entero
    account_id = data["account_id"]
    user_repo = SystemUserRepository(db_session, account_id=account_id)
    users = await user_repo.get_all()
    assert len(users) == 1
    admin_user = users[0]

    # Verificar datos del usuario
    assert getattr(admin_user, "email", None) == register_data["email"]
    assert getattr(admin_user, "first_name", None) == register_data["first_name"]
    assert getattr(admin_user, "last_name", None) == register_data["last_name"]
    # Verificar que es administrador
    is_admin = getattr(admin_user, "is_admin", False)
    assert is_admin is True

    # Verificar que la contraseña se hasheó correctamente
    hashed_password = getattr(admin_user, "hashed_password", "")
    if hashed_password:  # Solo verificar si hay un hash de contraseña
        assert verify_password(register_data["password"], hashed_password)

    # Verificar que se creó el rol de administrador y se asignó al usuario
    role_repo = RoleRepository(db_session, account_id=account_id)
    roles = await role_repo.get_all()
    assert len(roles) > 0

    # Buscar el rol de administrador
    admin_role = None
    for role in roles:
        if role.name == RoleType.ADMIN:
            admin_role = role
            break

    assert admin_role is not None

    # Verificar que el rol se asignó al usuario
    user_role_repo = SystemUserRoleRepository(db_session, account_id=account_id)
    has_admin_role = await user_role_repo.has_role(admin_user.id, RoleType.ADMIN.value)
    assert has_admin_role is True
