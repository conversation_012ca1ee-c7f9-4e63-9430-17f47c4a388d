import logging
import json
import traceback
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from google.cloud import logging as cloud_logging
from src.core.config import settings


class StructuredLogger:
    """Logger that supports structured logging in JSON format."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(logging.DEBUG)

        # Clear existing handlers to avoid duplicates
        if self.logger.handlers:
            self.logger.handlers.clear()

        if settings.ENV == "production":
            # In production, use Google Cloud Logging
            client = cloud_logging.Client()
            client.setup_logging()
        else:
            # In development, use a JSON formatter for console output
            handler = logging.StreamHandler()
            handler.setFormatter(JsonLogFormatter())
            self.logger.addHandler(handler)

    def _format_log_entry(
        self, message: str, extra: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Format a log entry as a structured dictionary."""
        log_entry = {
            "message": message,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "service": "rayuela-api",
            "environment": settings.ENV,
        }

        if extra:
            log_entry.update(extra)

        return log_entry

    def info(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log an info message with optional structured data."""
        log_entry = self._format_log_entry(message, extra)
        self.logger.info(json.dumps(log_entry))

    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None):
        """Log a warning message with optional structured data."""
        log_entry = self._format_log_entry(message, extra)
        self.logger.warning(json.dumps(log_entry))

    def error(
        self,
        message: str,
        extra: Optional[Dict[str, Any]] = None,
        exc_info: bool = False,
    ):
        """Log an error message with optional structured data and exception info."""
        log_entry = self._format_log_entry(message, extra)

        if exc_info:
            log_entry["exception"] = traceback.format_exc()

        self.logger.error(json.dumps(log_entry))

    def critical(
        self,
        message: str,
        extra: Optional[Dict[str, Any]] = None,
        exc_info: bool = False,
    ):
        """Log a critical message with optional structured data and exception info."""
        log_entry = self._format_log_entry(message, extra)

        if exc_info:
            log_entry["exception"] = traceback.format_exc()

        self.logger.critical(json.dumps(log_entry))


class JsonLogFormatter(logging.Formatter):
    """Custom formatter that outputs logs as JSON."""

    def format(self, record):
        """Format the log record as JSON."""
        log_entry = {}

        # Try to parse the message as JSON if it's already structured
        try:
            log_entry = json.loads(record.getMessage())
        except json.JSONDecodeError:
            # If not JSON, create a basic structured log
            log_entry = {
                "message": record.getMessage(),
                "level": record.levelname,
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "logger": record.name,
            }

            # Add exception info if available
            if record.exc_info:
                log_entry["exception"] = self.formatException(record.exc_info)

        return json.dumps(log_entry)


# Create a global instance of the structured logger
logger = StructuredLogger()


# Compatibility functions for existing code
def log_info(message: str, extra: Optional[Dict[str, Any]] = None):
    logger.info(message, extra)


def log_warning(message: str, extra: Optional[Dict[str, Any]] = None):
    logger.warning(message, extra)


def log_error(
    message: str, extra: Optional[Dict[str, Any]] = None, exc_info: bool = False
):
    logger.error(message, extra, exc_info)
