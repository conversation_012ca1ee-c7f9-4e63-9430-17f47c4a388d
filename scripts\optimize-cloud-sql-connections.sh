#!/bin/bash

# Script to optimize Cloud SQL max_connections setting for Rayuela
# This script configures the Cloud SQL instance with optimized connection limits

set -e

# Configuration
INSTANCE_NAME="rayuela-postgres"
PROJECT_ID="${GCP_PROJECT_ID:-$(gcloud config get-value project)}"
REGION="${GCP_REGION:-us-central1}"
MAX_CONNECTIONS=100

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if gcloud is installed and authenticated
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    if ! command -v gcloud &> /dev/null; then
        print_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | head -n1 > /dev/null; then
        print_error "No active gcloud authentication found. Please run 'gcloud auth login'"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to check if the Cloud SQL instance exists
check_instance() {
    print_status "Checking if Cloud SQL instance '${INSTANCE_NAME}' exists..."
    
    if ! gcloud sql instances describe "${INSTANCE_NAME}" --project="${PROJECT_ID}" > /dev/null 2>&1; then
        print_error "Cloud SQL instance '${INSTANCE_NAME}' not found in project '${PROJECT_ID}'"
        print_status "Available instances:"
        gcloud sql instances list --project="${PROJECT_ID}" --format="table(name,region,databaseVersion,state)"
        exit 1
    fi
    
    print_success "Cloud SQL instance '${INSTANCE_NAME}' found"
}

# Function to get current max_connections setting
get_current_max_connections() {
    print_status "Getting current max_connections setting..."
    
    CURRENT_MAX_CONNECTIONS=$(gcloud sql instances describe "${INSTANCE_NAME}" \
        --project="${PROJECT_ID}" \
        --format="value(settings.databaseFlags[name=max_connections].value)" 2>/dev/null || echo "default")
    
    if [ "$CURRENT_MAX_CONNECTIONS" = "default" ] || [ -z "$CURRENT_MAX_CONNECTIONS" ]; then
        print_status "Current max_connections: Default (PostgreSQL default: usually 100)"
    else
        print_status "Current max_connections: ${CURRENT_MAX_CONNECTIONS}"
    fi
}

# Function to update max_connections
update_max_connections() {
    print_status "Updating max_connections to ${MAX_CONNECTIONS}..."
    
    # Check if this would be a change
    if [ "$CURRENT_MAX_CONNECTIONS" = "$MAX_CONNECTIONS" ]; then
        print_success "max_connections is already set to ${MAX_CONNECTIONS}. No changes needed."
        return 0
    fi
    
    print_warning "This operation will restart the Cloud SQL instance. Continue? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_status "Operation cancelled by user"
        exit 0
    fi
    
    # Update the database flag
    if gcloud sql instances patch "${INSTANCE_NAME}" \
        --project="${PROJECT_ID}" \
        --database-flags="max_connections=${MAX_CONNECTIONS}" \
        --quiet; then
        print_success "max_connections updated to ${MAX_CONNECTIONS}"
        print_status "The instance is restarting. This may take a few minutes..."
    else
        print_error "Failed to update max_connections"
        exit 1
    fi
}

# Function to verify the change
verify_change() {
    print_status "Waiting for instance to be ready..."
    
    # Wait for instance to be ready
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        local state=$(gcloud sql instances describe "${INSTANCE_NAME}" \
            --project="${PROJECT_ID}" \
            --format="value(state)" 2>/dev/null || echo "UNKNOWN")
        
        if [ "$state" = "RUNNABLE" ]; then
            break
        fi
        
        print_status "Instance state: ${state}. Waiting... (${attempt}/${max_attempts})"
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        print_error "Instance did not become ready within expected time"
        exit 1
    fi
    
    # Verify the new setting
    print_status "Verifying new max_connections setting..."
    NEW_MAX_CONNECTIONS=$(gcloud sql instances describe "${INSTANCE_NAME}" \
        --project="${PROJECT_ID}" \
        --format="value(settings.databaseFlags[name=max_connections].value)" 2>/dev/null || echo "default")
    
    if [ "$NEW_MAX_CONNECTIONS" = "$MAX_CONNECTIONS" ]; then
        print_success "Verification successful! max_connections is now set to ${MAX_CONNECTIONS}"
    else
        print_error "Verification failed. Expected ${MAX_CONNECTIONS}, got ${NEW_MAX_CONNECTIONS}"
        exit 1
    fi
}

# Function to show optimization summary
show_summary() {
    print_success "Cloud SQL Connection Optimization Complete!"
    echo ""
    echo "Summary of changes:"
    echo "  - Instance: ${INSTANCE_NAME}"
    echo "  - Project: ${PROJECT_ID}"
    echo "  - max_connections: ${MAX_CONNECTIONS}"
    echo ""
    echo "This optimization aligns with the application changes:"
    echo "  - SQLAlchemy pool_size: 8 (reduced from 20)"
    echo "  - SQLAlchemy max_overflow: 5 (reduced from 10)"
    echo "  - Gunicorn worker_connections: 200 (reduced from 1500)"
    echo ""
    echo "Expected benefits:"
    echo "  - Reduced Cloud SQL instance size requirements"
    echo "  - Lower monthly costs (estimated 30-50% savings)"
    echo "  - Improved resource efficiency"
    echo ""
    print_status "Monitor your application for the next few days to ensure performance is maintained."
}

# Main execution
main() {
    echo "=============================================="
    echo "  Rayuela Cloud SQL Connection Optimizer"
    echo "=============================================="
    echo ""
    
    check_prerequisites
    check_instance
    get_current_max_connections
    update_max_connections
    verify_change
    show_summary
}

# Run main function
main "$@"
