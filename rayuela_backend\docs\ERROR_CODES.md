# Códigos de Error de la API

Este documento describe los códigos de error estandarizados que puede devolver la API. Estos códigos permiten a los desarrolladores manejar errores específicos de forma programática, sin depender del mensaje de error que podría cambiar.

## Formato de Respuesta de Error

Todas las respuestas de error siguen un formato consistente:

```json
{
  "message": "Descripción del error",
  "error_code": "CÓDIGO_DE_ERROR",
  "path": "/api/v1/ruta/del/endpoint"
}
```

- `message`: Descripción legible del error
- `error_code`: Código de error estandarizado (ver lista a continuación)
- `path`: Ruta del endpoint que generó el error

## Códigos de Error

### Errores de Autenticación y Autorización (401, 403)

| Código | Descripción | HTTP Status |
|--------|-------------|-------------|
| `INVALID_CREDENTIALS` | Credenciales inválidas (usuario/contraseña incorrectos) | 401 |
| `INVALID_TOKEN` | Token JWT inválido o expirado | 401 |
| `INVALID_API_KEY` | API Key inválida o no proporcionada | 401 |
| `INSUFFICIENT_PERMISSIONS` | Permisos insuficientes para realizar la acción | 403 |

### Errores de Recursos No Encontrados (404)

| Código | Descripción | HTTP Status |
|--------|-------------|-------------|
| `ACCOUNT_NOT_FOUND` | Cuenta no encontrada | 404 |
| `USER_NOT_FOUND` | Usuario no encontrado | 404 |
| `PRODUCT_NOT_FOUND` | Producto no encontrado | 404 |
| `RESOURCE_NOT_FOUND` | Recurso genérico no encontrado | 404 |
| `MODEL_NOT_FOUND` | Modelo no encontrado | 404 |

### Errores de Validación y Conflictos (400, 409, 422)

| Código | Descripción | HTTP Status |
|--------|-------------|-------------|
| `VALIDATION_ERROR` | Error de validación de datos | 422 |
| `DUPLICATE_ENTRY` | Entrada duplicada | 409 |
| `INVALID_DATA` | Datos inválidos | 400 |
| `CONFLICT` | Conflicto con el estado actual | 409 |

### Errores de Límites (429)

| Código | Descripción | HTTP Status |
|--------|-------------|-------------|
| `RATE_LIMIT_EXCEEDED` | Límite de tasa excedido (demasiadas peticiones) | 429 |
| `RESOURCE_LIMIT_EXCEEDED` | Límite de recursos excedido (cuota de plan) | 429 |
| `SUBSCRIPTION_LIMIT` | Límite de suscripción alcanzado | 400 |
| `TRAINING_FREQUENCY_LIMIT` | Límite de frecuencia de entrenamiento excedido | 429 |

### Errores de Recomendación (500, específicos del dominio)

| Código | Descripción | HTTP Status |
|--------|-------------|-------------|
| `RECOMMENDATION_ERROR` | Error general de recomendación | 500 |
| `MODEL_NOT_TRAINED` | Modelo no entrenado | 500 |
| `TRAINING_ERROR` | Error en el entrenamiento | 500 |

### Errores de Base de Datos y Sistema (500)

| Código | Descripción | HTTP Status |
|--------|-------------|-------------|
| `DATABASE_ERROR` | Error de base de datos | 500 |
| `PARTITION_ERROR` | Error de partición | 500 |
| `INTERNAL_ERROR` | Error interno del servidor | 500 |
| `EXTERNAL_SERVICE_ERROR` | Error de servicio externo | 500 |

## Ejemplos de Manejo de Errores

### Ejemplo en JavaScript

```javascript
async function fetchRecommendations(userId) {
  try {
    const response = await fetch(`/api/v1/recommendations/${userId}`);
    if (!response.ok) {
      const error = await response.json();
      
      switch(error.error_code) {
        case 'USER_NOT_FOUND':
          console.error('Usuario no encontrado, redirigiendo a página de registro');
          redirectToSignup();
          break;
        case 'MODEL_NOT_TRAINED':
          console.error('Modelo no entrenado, mostrando recomendaciones populares');
          showPopularItems();
          break;
        case 'RATE_LIMIT_EXCEEDED':
          console.error('Límite de tasa excedido, intentando de nuevo en 5 segundos');
          setTimeout(() => fetchRecommendations(userId), 5000);
          break;
        default:
          console.error(`Error: ${error.message}`);
          showGenericError();
      }
      return null;
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error de red:', error);
    return null;
  }
}
```

### Ejemplo en Python

```python
import requests

def get_recommendations(user_id, api_key):
    headers = {"X-API-Key": api_key}
    response = requests.get(f"https://api.example.com/api/v1/recommendations/{user_id}", headers=headers)
    
    if response.status_code != 200:
        error = response.json()
        error_code = error.get("error_code")
        
        if error_code == "USER_NOT_FOUND":
            print(f"Usuario {user_id} no encontrado")
            return []
        elif error_code == "INVALID_API_KEY":
            raise AuthenticationError("API Key inválida")
        elif error_code == "RATE_LIMIT_EXCEEDED":
            print("Límite de tasa excedido, esperando antes de reintentar")
            time.sleep(5)
            return get_recommendations(user_id, api_key)  # Reintentar
        else:
            print(f"Error: {error.get('message', 'Error desconocido')}")
            return None
    
    return response.json()
```

## Notas para Desarrolladores

- Siempre verifique el código de error (`error_code`) en lugar de analizar el mensaje de error (`message`), ya que los mensajes pueden cambiar.
- Los códigos de error son constantes y no cambiarán entre versiones de la API.
- Si recibe un error con código `INTERNAL_ERROR`, por favor repórtelo al equipo de soporte con el ID de error si está disponible.
- Para errores de validación (`VALIDATION_ERROR`), la respuesta puede incluir detalles adicionales sobre los campos específicos que fallaron la validación.
