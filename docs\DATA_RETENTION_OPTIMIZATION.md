# 📊 Data Retention Optimization for Startup Cost Reduction

## 🎯 Overview

This document outlines the data retention policy optimizations implemented to reduce Cloud SQL storage costs and IOPS for startup environments. The changes focus on reducing retention periods for high-volume tables while maintaining data archival to Google Cloud Storage (GCS).

## 📈 Cost Impact Analysis

### **Before Optimization:**
- `interactions`: 180 days retention in Cloud SQL
- `audit_logs`: 90 days retention in Cloud SQL  
- `soft_deleted_records`: 365 days retention in Cloud SQL
- `cleanup_old_data_secure`: 365 days retention in Cloud SQL

### **After Optimization:**
- `interactions`: **60 days** retention in Cloud SQL (67% reduction)
- `audit_logs`: **30 days** retention in Cloud SQL (67% reduction)
- `soft_deleted_records`: **90 days** retention in Cloud SQL (75% reduction)
- `cleanup_old_data_secure`: **90 days** retention in Cloud SQL (75% reduction)

### **Estimated Cost Savings:**
- **Storage**: 60-75% reduction in Cloud SQL storage costs for high-volume tables
- **IOPS**: Significant reduction in read/write operations due to smaller table sizes
- **Backup**: Reduced backup storage and time
- **Performance**: Faster queries due to smaller indexes and table sizes

## 🔧 Implementation Details

### **Modified Files:**

1. **`rayuela_backend/src/workers/celery_app.py`**
   - Updated Celery beat schedule with optimized retention periods
   - Added comments indicating cost optimization

2. **`rayuela_backend/src/core/config.py`**
   - Updated `SOFT_DELETE_RETENTION_DAYS` default from 365 to 90 days

3. **`rayuela_backend/src/workers/celery_tasks.py`**
   - Updated default parameters in task functions:
     - `archive_and_cleanup_old_audit_logs`: 90 → 30 days
     - `archive_and_cleanup_old_interactions`: 180 → 60 days
     - `cleanup_soft_deleted_records`: 365 → 90 days
     - `cleanup_old_data_secure_task`: 180 → 90 days

### **Archival Strategy Verification:**

✅ **Data is safely archived to GCS before deletion:**
- `DataArchivalService` exports data to GCS in Parquet format with gzip compression
- Archival path: `archived_data/{table_name}/{YYYY/MM/DD}/`
- Verification enabled: `ARCHIVAL_VERIFY_EXPORT=true`
- Archive-then-delete pattern ensures no data loss

## 📅 Task Schedule Summary

| Task | Frequency | Old Retention | New Retention | Savings |
|------|-----------|---------------|---------------|---------|
| `archive-and-cleanup-old-audit-logs` | Daily | 90 days | **30 days** | 67% |
| `archive-and-cleanup-old-interactions` | Weekly | 180 days | **60 days** | 67% |
| `cleanup-soft-deleted-records` | Monthly | 365 days | **90 days** | 75% |
| `cleanup-old-data-secure` | Bi-weekly | 365 days | **90 days** | 75% |

## 🔒 Data Safety & Compliance

### **Data Availability:**
- **Hot Data**: Available in application for 30-90 days (depending on table)
- **Archived Data**: Available in GCS indefinitely (until manually deleted)
- **Recovery**: Archived data can be restored from GCS if needed

### **Privacy Policy & ToS Updates Required:**
⚠️ **Action Required**: Update user-facing documentation to reflect new data availability periods:

```markdown
- User interaction data: Available in application for 60 days, archived thereafter
- Audit logs: Available in application for 30 days, archived thereafter  
- Soft-deleted records: Permanently deleted after 90 days (with 30-day recovery window)
```

## 🚀 Deployment Instructions

### **Environment Variables (Optional Override):**
```bash
# Override defaults if needed
SOFT_DELETE_RETENTION_DAYS=90
ARCHIVAL_VERIFY_EXPORT=true
ARCHIVAL_COMPRESSION=gzip
```

### **Monitoring:**
- Monitor Celery task execution in Flower dashboard
- Check GCS bucket for archived data
- Monitor Cloud SQL storage metrics for cost reduction

### **Rollback Plan:**
If needed, retention periods can be increased by:
1. Updating environment variables
2. Restarting Celery beat scheduler
3. Or modifying the beat schedule directly

## 📊 Expected Results

### **Week 1-2:**
- Gradual reduction in Cloud SQL storage as old data is archived/deleted
- No impact on application functionality

### **Month 1:**
- Significant reduction in Cloud SQL storage costs (30-50%)
- Improved query performance on high-volume tables
- Reduced backup time and storage

### **Ongoing:**
- Sustained cost savings
- Better database performance
- Archived data available in GCS for analytics/compliance

## 🔍 Monitoring & Validation

### **Key Metrics to Track:**
1. **Cloud SQL Storage Usage** (should decrease)
2. **GCS Storage Usage** (should increase with archived data)
3. **Query Performance** (should improve)
4. **Celery Task Success Rate** (should remain high)

### **Validation Commands:**
```bash
# Check current retention settings
python -c "from src.core.config import settings; print(f'Soft delete retention: {settings.SOFT_DELETE_RETENTION_DAYS} days')"

# Monitor task execution
celery -A src.workers.celery_app inspect active

# Check archived data in GCS
gsutil ls gs://your-bucket/archived_data/
```

---

**Implementation Date:** [Current Date]  
**Estimated Monthly Savings:** 30-50% reduction in Cloud SQL costs  
**Risk Level:** Low (data is archived before deletion)
