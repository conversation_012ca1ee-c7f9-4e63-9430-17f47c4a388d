import pytest
import httpx
from datetime import datetime
from src.core.config import settings

class TestRecommendations:
    """Tests E2E para los endpoints de recomendaciones."""
    
    @pytest.fixture
    def api_client(self):
        """Crear cliente HTTP para pruebas."""
        return httpx.AsyncClient(base_url=settings.API_V1_STR)
    
@pytest.mark.asyncio
    async def test_personalized_recommendations(
        self,
        api_client,
        test_accounts,
        test_tokens,
        test_end_users
    ):
        """Test para recomendaciones personalizadas."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        user = test_end_users[account.id][0]
        
        # Obtener recomendaciones con diferentes estrategias
        strategies = ["collaborative", "content", "hybrid"]
        for strategy in strategies:
            response = await api_client.post(
                "/recommendations/personalized/query",
                json={"user_id": user.id, "strategy": strategy},
                headers={"X-API-Key": api_key}
            )
            assert response.status_code == 200
            data = response.json()
            assert "items" in data
            assert len(data["items"]) > 0
            assert "score" in data["items"][0]
            assert "metadata" in data["items"][0]
        
        # Obtener recomendaciones con filtros
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={
                "user_id": user.id,
                "filters": {
                    "logic": "and",
                    "filters": [
                        {"field": "price", "op": "gte", "value": 10.0},
                        {"field": "price", "op": "lte", "value": 100.0},
                        {
                            "logic": "or",
                            "filters": [
                                {"field": "category", "op": "eq", "value": "electronics"},
                                {"field": "category", "op": "eq", "value": "books"}
                            ]
                        }
                    ]
                }
            },
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        data = response.json()
        for item in data["items"]:
            assert 10.0 <= item["price"] <= 100.0
            assert item["category"] in ["electronics", "books"]
        
        # Obtener recomendaciones con contexto
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={
                "user_id": user.id,
                "context": {
                    "time_of_day": "morning",
                    "device": "mobile",
                    "location": "home"
                }
            },
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert "context_metadata" in data
    
    async def test_similar_products(
        self,
        api_client,
        test_accounts,
        test_tokens,
        test_products
    ):
        """Test para productos similares."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        product = test_products[0]
        
        # Obtener productos similares
        response = await api_client.get(
            f"/recommendations/products/{product.id}/similar",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        data = response.json()
        assert "items" in data
        assert len(data["items"]) > 0
        assert "similarity_score" in data["items"][0]
        
        # Obtener productos similares con límite
        response = await api_client.get(
            f"/recommendations/products/{product.id}/similar",
            params={"limit": 5},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        data = response.json()
        assert len(data["items"]) <= 5
        
        # Obtener productos similares con filtros
        response = await api_client.get(
            f"/recommendations/products/{product.id}/similar",
            params={
                "min_similarity": 0.5,
                "categories": ["electronics"]
            },
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 200
        data = response.json()
        for item in data["items"]:
            assert item["similarity_score"] >= 0.5
            assert item["category"] == "electronics"
    
    async def test_recommendation_errors(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """Test para manejo de errores en recomendaciones."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        
        # Test usuario no encontrado
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": 999999},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 404
        
        # Test producto no encontrado
        response = await api_client.get(
            "/recommendations/products/999999/similar",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 404
        
        # Test estrategia inválida
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": 1, "strategy": "invalid"},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 422
        
        # Test filtros inválidos
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": 1, "filters": {"invalid": "filter"}},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 422
    
    async def test_recommendation_permissions(
        self,
        api_client,
        test_accounts,
        test_tokens,
        test_end_users
    ):
        """Test para permisos en recomendaciones."""
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        api_key_a = test_tokens[account_a.id]["regular"]["api_key"]
        api_key_b = test_tokens[account_b.id]["regular"]["api_key"]
        user_a = test_end_users[account_a.id][0]
        user_b = test_end_users[account_b.id][0]
        
        # Intentar acceder a recomendaciones de usuario de otro tenant
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": user_b.id},
            headers={"X-API-Key": api_key_a}
        )
        assert response.status_code == 403
        
        # Intentar acceder a recomendaciones sin autenticación
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": user_a.id}
        )
        assert response.status_code == 401
    
    async def test_recommendation_limits(
        self,
        api_client,
        test_accounts,
        test_tokens,
        test_end_users
    ):
        """Test para límites en recomendaciones."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        user = test_end_users[account.id][0]
        
        # Test límite de requests
        for _ in range(100):  # Exceder límite de requests
            await api_client.post(
                "/recommendations/personalized/query",
                json={"user_id": user.id},
                headers={"X-API-Key": api_key}
            )
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": user.id},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 429
        
        # Test límite de resultados
        response = await api_client.post(
            "/recommendations/personalized/query",
            json={"user_id": user.id, "limit": 1000},  # Exceder límite de resultados
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 422 