from enum import Enum
from pydantic import BaseModel, Field, field_validator
from typing import Optional, Dict, Any, List, Union, Literal, Set


class RecommendationStrategy(str, Enum):
    """Estrategias de recomendación predefinidas"""
    BALANCED = "balanced"  # Equilibrio entre relevancia y diversidad
    MAXIMIZE_ENGAGEMENT = "maximize_engagement"  # Prioriza items con alta probabilidad de interacción
    PROMOTE_NEW_ARRIVALS = "promote_new_arrivals"  # Da mayor peso a productos nuevos
    DISCOVER_DIVERSE = "discover_diverse"  # Maximiza la diversidad de recomendaciones


class PageType(str, Enum):
    """Tipos de páginas donde se muestran recomendaciones"""
    HOME = "home"  # Página principal
    PRODUCT_DETAIL = "product_detail"  # Página de detalle de producto
    CATEGORY = "category"  # Página de categoría
    SEARCH_RESULTS = "search_results"  # Página de resultados de búsqueda
    CART = "cart"  # Página de carrito
    CHECKOUT = "checkout"  # Página de checkout
    USER_PROFILE = "user_profile"  # Perfil de usuario


class DeviceType(str, Enum):
    """Tipos de dispositivos desde donde se solicitan recomendaciones"""
    DESKTOP = "desktop"  # Computadora de escritorio
    MOBILE = "mobile"  # Dispositivo móvil
    TABLET = "tablet"  # Tablet
    TV = "tv"  # Smart TV
    OTHER = "other"  # Otros dispositivos


class RecommendationStrategyParams(BaseModel):
    """Parámetros para cada estrategia de recomendación"""
    collab_weight: float = Field(0.5, ge=0.0, le=1.0, description="Peso para recomendaciones colaborativas")
    content_weight: float = Field(0.5, ge=0.0, le=1.0, description="Peso para recomendaciones basadas en contenido")
    diversity_lambda: float = Field(0.5, ge=0.0, le=1.0, description="Parámetro lambda para diversificación (0=máxima diversidad, 1=máxima relevancia)")
    recency_boost: float = Field(0.0, ge=0.0, le=1.0, description="Impulso para productos recientes")
    category_diversity_weight: float = Field(0.3, ge=0.0, le=1.0, description="Peso para diversidad de categorías")
    popularity_weight: float = Field(0.5, ge=0.0, le=1.0, description="Peso para popularidad de productos")

    class ConfigDict:
        from_attributes = True


class RecommendationContext(BaseModel):
    """Contexto explícito para recomendaciones personalizadas"""
    page_type: Optional[PageType] = Field(None, description="Tipo de página donde se muestran las recomendaciones")
    device: Optional[DeviceType] = Field(None, description="Tipo de dispositivo desde donde se solicitan las recomendaciones")
    source_item_id: Optional[int] = Field(None, description="ID del producto de origen (ej: en página de detalle)")
    search_query: Optional[str] = Field(None, description="Consulta de búsqueda actual")
    category_id: Optional[int] = Field(None, description="ID de la categoría actual")
    cart_item_ids: Optional[List[int]] = Field(None, description="IDs de productos en el carrito")
    recently_viewed_ids: Optional[List[int]] = Field(None, description="IDs de productos vistos recientemente")
    location: Optional[str] = Field(None, description="Ubicación geográfica del usuario")
    time_of_day: Optional[str] = Field(None, description="Momento del día (mañana, tarde, noche)")
    custom_attributes: Optional[Dict[str, Any]] = Field(None, description="Atributos personalizados adicionales")

    class ConfigDict:
        from_attributes = True
        json_schema_extra = {
            "example": {
                "page_type": "product_detail",
                "device": "mobile",
                "source_item_id": 123,
                "recently_viewed_ids": [456, 789],
                "custom_attributes": {"season": "summer", "promotion_active": True}
            }
        }


# Configuración predeterminada para cada estrategia
STRATEGY_CONFIGS: Dict[RecommendationStrategy, RecommendationStrategyParams] = {
    RecommendationStrategy.BALANCED: RecommendationStrategyParams(
        collab_weight=0.5,
        content_weight=0.5,
        diversity_lambda=0.5,
        recency_boost=0.2,
        category_diversity_weight=0.3,
        popularity_weight=0.5
    ),
    RecommendationStrategy.MAXIMIZE_ENGAGEMENT: RecommendationStrategyParams(
        collab_weight=0.7,
        content_weight=0.3,
        diversity_lambda=0.8,  # Prioriza relevancia sobre diversidad
        recency_boost=0.3,
        category_diversity_weight=0.1,
        popularity_weight=0.8  # Mayor peso a productos populares
    ),
    RecommendationStrategy.PROMOTE_NEW_ARRIVALS: RecommendationStrategyParams(
        collab_weight=0.3,
        content_weight=0.7,
        diversity_lambda=0.6,
        recency_boost=0.9,  # Fuerte impulso a productos recientes
        category_diversity_weight=0.4,
        popularity_weight=0.3
    ),
    RecommendationStrategy.DISCOVER_DIVERSE: RecommendationStrategyParams(
        collab_weight=0.4,
        content_weight=0.6,
        diversity_lambda=0.2,  # Prioriza diversidad sobre relevancia
        recency_boost=0.4,
        category_diversity_weight=0.8,  # Alto peso a diversidad de categorías
        popularity_weight=0.3
    )
}


class FilterOperator(str, Enum):
    """Operadores para filtros estructurados"""
    EQUALS = "eq"  # Igualdad
    NOT_EQUALS = "ne"  # Desigualdad
    GREATER_THAN = "gt"  # Mayor que
    GREATER_THAN_EQUALS = "gte"  # Mayor o igual que
    LESS_THAN = "lt"  # Menor que
    LESS_THAN_EQUALS = "lte"  # Menor o igual que
    IN = "in"  # Valor en lista
    NOT_IN = "nin"  # Valor no en lista
    CONTAINS = "contains"  # Contiene substring (para texto)
    STARTS_WITH = "startswith"  # Comienza con (para texto)
    ENDS_WITH = "endswith"  # Termina con (para texto)


class LogicalOperator(str, Enum):
    """Operadores lógicos para combinar filtros"""
    AND = "and"
    OR = "or"


class Filter(BaseModel):
    """Filtro para recomendaciones"""
    field: str = Field(..., description="Campo a filtrar")
    operator: FilterOperator = Field(..., description="Operador de filtrado")
    value: Any = Field(..., description="Valor a comparar")

    class ConfigDict:
        json_schema_extra = {
            "example": {
                "field": "category_id",
                "operator": "eq",
                "value": 123
            }
        }


class SortDirection(str, Enum):
    """Dirección de ordenamiento"""
    ASCENDING = "asc"
    DESCENDING = "desc"


class SortConfig(BaseModel):
    """Configuración de ordenamiento"""
    field: str = Field(..., description="Campo por el cual ordenar")
    direction: SortDirection = Field(SortDirection.DESCENDING, description="Dirección del ordenamiento")


class StandardFilter(BaseModel):
    """Filtro estándar para todos los endpoints"""
    field: str = Field(..., description="Campo a filtrar")
    op: FilterOperator = Field(..., description="Operador de filtrado")
    value: Any = Field(..., description="Valor a comparar")


class FilterGroup(BaseModel):
    """Grupo de filtros con lógica AND/OR"""
    logic: LogicalOperator = Field(LogicalOperator.AND, description="Lógica de combinación (and/or)")
    filters: List[Union[Filter, "FilterGroup"]] = Field(..., description="Lista de filtros o grupos de filtros")


class StandardQueryParams(BaseModel):
    """Parámetros de consulta estándar para todos los endpoints"""
    filters: Optional[FilterGroup] = Field(None, description="Filtros estructurados")
    sort_by: Optional[SortConfig] = Field(None, description="Configuración de ordenamiento")
    skip: int = Field(0, ge=0, description="Número de elementos a saltar")
    limit: int = Field(10, gt=0, le=100, description="Número máximo de elementos a devolver")


class ExplanationLevel(str, Enum):
    """Niveles de detalle para explicaciones de recomendaciones"""
    SIMPLE = "simple"  # Explicación básica en texto plano
    DETAILED = "detailed"  # Explicación detallada con estructura y más información


class ExplanationReason(str, Enum):
    """Razones para recomendar un producto"""
    SIMILAR_USERS = "similar_users"  # Usuarios similares compraron/vieron este producto
    SIMILAR_ITEMS = "similar_items"  # Similar a productos que le gustaron al usuario
    CATEGORY_AFFINITY = "category_affinity"  # Usuario tiene afinidad con esta categoría
    POPULAR_ITEM = "popular_item"  # Producto popular en general
    TRENDING_ITEM = "trending_item"  # Producto con tendencia al alza
    ATTRIBUTE_MATCH = "attribute_match"  # Coincide con atributos preferidos por el usuario
    COMPLEMENTARY_ITEM = "complementary_item"  # Complementa productos que el usuario ya tiene
    RECENT_INTERACTION = "recent_interaction"  # Basado en interacciones recientes
    PERSONALIZED_RANKING = "personalized_ranking"  # Ranking personalizado basado en perfil
    NEW_ITEM = "new_item"  # Producto nuevo en el catálogo
    DIVERSITY = "diversity"  # Recomendado para aumentar la diversidad
    SEASONAL = "seasonal"  # Producto de temporada
    PROMOTIONAL = "promotional"  # Producto en promoción


class ExplanationEvidence(BaseModel):
    """Evidencia que respalda una explicación"""
    type: str = Field(..., description="Tipo de evidencia (producto, categoría, atributo, etc.)")
    id: Optional[int] = Field(None, description="ID del elemento (si aplica)")
    name: str = Field(..., description="Nombre o descripción del elemento")
    relevance: float = Field(..., ge=0, le=1, description="Relevancia de esta evidencia (0-1)")

    class ConfigDict:
        json_schema_extra = {
            "example": {
                "type": "product",
                "id": 123,
                "name": "Smartphone Galaxy S21",
                "relevance": 0.85
            }
        }


class DetailedExplanation(BaseModel):
    """Explicación detallada de una recomendación"""
    primary_reason: ExplanationReason = Field(..., description="Razón principal de la recomendación")
    secondary_reasons: List[ExplanationReason] = Field([], description="Razones secundarias")
    confidence: float = Field(..., ge=0, le=1, description="Confianza en la recomendación (0-1)")
    text_explanation: str = Field(..., description="Explicación en texto plano")
    evidence: List[ExplanationEvidence] = Field([], description="Evidencia que respalda la explicación")
    source: str = Field(..., description="Fuente de la recomendación (collaborative, content, hybrid)")

    class ConfigDict:
        json_schema_extra = {
            "example": {
                "primary_reason": "similar_items",
                "secondary_reasons": ["category_affinity", "trending_item"],
                "confidence": 0.85,
                "text_explanation": "Recomendado porque es similar a productos que te han gustado",
                "evidence": [
                    {
                        "type": "product",
                        "id": 123,
                        "name": "Smartphone Galaxy S21",
                        "relevance": 0.85
                    }
                ],
                "source": "hybrid"
            }
        }
