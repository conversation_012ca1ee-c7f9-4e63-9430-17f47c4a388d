"""
Repository for handling usage metrics.
"""
from typing import Optional, List, Dict, Any
from datetime import datetime, timezone
from sqlalchemy import select, update, and_, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from src.db.models import AccountUsageMetrics, EndpointMetrics
from src.db.repositories.base import BaseRepository
from src.utils.base_logger import log_error, log_info

class UsageMetricsRepository(BaseRepository):
    """Repository for handling usage metrics."""
    
    def __init__(self, db: AsyncSession, account_id: Optional[int] = None):
        """Initialize the repository with a database session and optional account ID."""
        super().__init__(db, account_id=account_id, model=AccountUsageMetrics)
    
    async def get_account_metrics(self, account_id: int) -> Optional[AccountUsageMetrics]:
        """
        Get usage metrics for an account.
        
        Args:
            account_id: The account ID
            
        Returns:
            AccountUsageMetrics object or None if not found
        """
        try:
            stmt = select(AccountUsageMetrics).where(
                AccountUsageMetrics.account_id == account_id
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except SQLAlchemyError as e:
            await self._handle_error("getting account metrics", e)
    
    async def create_or_update_metrics(
        self, 
        account_id: int, 
        api_calls_count: Optional[int] = None,
        storage_used: Optional[int] = None,
        endpoint_usage: Optional[Dict[str, int]] = None,
        training_count: Optional[int] = None,
        recommendation_count: Optional[int] = None
    ) -> AccountUsageMetrics:
        """
        Create or update usage metrics for an account.
        
        Args:
            account_id: The account ID
            api_calls_count: Number of API calls
            storage_used: Storage used in bytes
            endpoint_usage: Dictionary of endpoint usage counts
            training_count: Number of model trainings
            recommendation_count: Number of recommendation requests
            
        Returns:
            Updated AccountUsageMetrics object
        """
        try:
            # Check if metrics exist
            metrics = await self.get_account_metrics(account_id)
            
            if metrics:
                # Update existing metrics
                update_data = {}
                
                if api_calls_count is not None:
                    update_data["api_calls_count"] = api_calls_count
                
                if storage_used is not None:
                    update_data["storage_used"] = storage_used
                
                if endpoint_usage is not None:
                    update_data["endpoint_usage"] = endpoint_usage
                
                if training_count is not None:
                    update_data["training_count"] = training_count
                
                if recommendation_count is not None:
                    update_data["recommendation_count"] = recommendation_count
                
                update_data["updated_at"] = datetime.now(timezone.utc)
                
                stmt = (
                    update(AccountUsageMetrics)
                    .where(AccountUsageMetrics.account_id == account_id)
                    .values(**update_data)
                )
                await self.db.execute(stmt)
                await self.db.commit()
                
                # Refresh metrics
                stmt = select(AccountUsageMetrics).where(
                    AccountUsageMetrics.account_id == account_id
                )
                result = await self.db.execute(stmt)
                return result.scalar_one()
            else:
                # Create new metrics
                metrics = AccountUsageMetrics(
                    account_id=account_id,
                    api_calls_count=api_calls_count or 0,
                    storage_used=storage_used or 0,
                    endpoint_usage=endpoint_usage or {},
                    training_count=training_count or 0,
                    recommendation_count=recommendation_count or 0
                )
                self.db.add(metrics)
                await self.db.commit()
                await self.db.refresh(metrics)
                return metrics
        except SQLAlchemyError as e:
            await self.db.rollback()
            await self._handle_error("creating or updating account metrics", e)
    
    async def increment_api_calls(self, account_id: int, count: int = 1) -> AccountUsageMetrics:
        """
        Increment API call count for an account.
        
        Args:
            account_id: The account ID
            count: Number of API calls to add
            
        Returns:
            Updated AccountUsageMetrics object
        """
        try:
            # Check if metrics exist
            metrics = await self.get_account_metrics(account_id)
            
            if metrics:
                # Update existing metrics
                stmt = (
                    update(AccountUsageMetrics)
                    .where(AccountUsageMetrics.account_id == account_id)
                    .values(
                        api_calls_count=AccountUsageMetrics.api_calls_count + count,
                        updated_at=datetime.now(timezone.utc)
                    )
                )
                await self.db.execute(stmt)
                await self.db.commit()
                
                # Refresh metrics
                stmt = select(AccountUsageMetrics).where(
                    AccountUsageMetrics.account_id == account_id
                )
                result = await self.db.execute(stmt)
                return result.scalar_one()
            else:
                # Create new metrics
                metrics = AccountUsageMetrics(
                    account_id=account_id,
                    api_calls_count=count,
                    storage_used=0
                )
                self.db.add(metrics)
                await self.db.commit()
                await self.db.refresh(metrics)
                return metrics
        except SQLAlchemyError as e:
            await self.db.rollback()
            await self._handle_error("incrementing API calls", e)
    
    async def reset_billing_period_metrics(self, account_id: int) -> AccountUsageMetrics:
        """
        Reset billing period metrics for an account.
        
        Args:
            account_id: The account ID
            
        Returns:
            Updated AccountUsageMetrics object
        """
        try:
            # Check if metrics exist
            metrics = await self.get_account_metrics(account_id)
            
            if metrics:
                # Update existing metrics
                stmt = (
                    update(AccountUsageMetrics)
                    .where(AccountUsageMetrics.account_id == account_id)
                    .values(
                        billing_period_api_calls=0,
                        billing_period_storage=0,
                        last_billing_cycle=datetime.now(timezone.utc),
                        updated_at=datetime.now(timezone.utc)
                    )
                )
                await self.db.execute(stmt)
                await self.db.commit()
                
                # Refresh metrics
                stmt = select(AccountUsageMetrics).where(
                    AccountUsageMetrics.account_id == account_id
                )
                result = await self.db.execute(stmt)
                return result.scalar_one()
            else:
                # Create new metrics
                metrics = AccountUsageMetrics(
                    account_id=account_id,
                    api_calls_count=0,
                    storage_used=0,
                    billing_period_api_calls=0,
                    billing_period_storage=0,
                    last_billing_cycle=datetime.now(timezone.utc)
                )
                self.db.add(metrics)
                await self.db.commit()
                await self.db.refresh(metrics)
                return metrics
        except SQLAlchemyError as e:
            await self.db.rollback()
            await self._handle_error("resetting billing period metrics", e)
