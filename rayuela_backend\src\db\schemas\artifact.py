from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from src.db.enums import TrainingJobStatus


class ModelMetadataBase(BaseModel):
    artifact_name: str = Field(
        ..., description="Name of the artifact", min_length=1, max_length=255
    )
    artifact_version: str = Field(..., pattern="^\\d+\\.\\d+\\.\\d+$")
    description: Optional[str] = None
    parameters: dict
    performance_metrics: dict
    artifacts_path: str
    created_at: datetime = Field(
        ..., description="Timestamp when the artifact was created"
    )
    updated_at: datetime = Field(
        ..., description="Timestamp when the artifact was last updated"
    )


class ModelMetadataCreate(ModelMetadataBase):
    pass


class ModelMetadata(ModelMetadataBase):
    account_id: int
    id: int
    training_date: datetime

    class ConfigDict:
        from_attributes = True


class ModelMetadataResponse(BaseModel):
    id: int
    artifact_name: str
    artifact_version: str
    description: Optional[str] = None
    training_date: datetime
    performance_metrics: dict
    parameters: dict

    class ConfigDict:
        from_attributes = True


class TrainingJobBase(BaseModel):
    artifact_metadata_id: Optional[int] = None
    status: TrainingJobStatus
    error_message: Optional[str] = None
    parameters: Optional[dict] = None
    metrics: Optional[dict] = None
    task_id: Optional[str] = None


class TrainingJobCreate(TrainingJobBase):
    pass


class TrainingJob(TrainingJobBase):
    account_id: int
    id: int
    started_at: datetime
    created_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None

    class ConfigDict:
        from_attributes = True
