{"summary": {"total_files": 59, "syntax_errors": 13, "import_issues": 5, "deprecated_patterns": 59, "test_functions": 55}, "files": {"tests\\test_recommendation_context.py": {"path": "tests\\test_recommendation_context.py", "syntax_ok": true, "imports": ["json", "pytest"], "from_imports": ["src.db.schemas.recommendation", "sqlalchemy.ext.asyncio", "unittest.mock", "src.ml_pipeline.post_processing_service", "<PERSON><PERSON><PERSON>", "src.ml_pipeline.serving_engine", "src.api.v1.endpoints.recommendations", "src.main", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\test_recommendation_strategies.py": {"path": "tests\\test_recommendation_strategies.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["src.db.schemas.recommendation", "sqlalchemy.ext.asyncio", "unittest.mock", "src.ml_pipeline.post_processing_service", "<PERSON><PERSON><PERSON>", "src.ml_pipeline.serving_engine", "httpx", "src.api.v1.endpoints.recommendations", "src.main", "datetime", "src.ml_pipeline.fallback_handler"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\test_robust_upsert.py": {"path": "tests\\test_robust_upsert.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["src.db.repositories.base", "src.db", "sqlalchemy.ext.asyncio"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\test_server.py": {"path": "tests\\test_server.py", "syntax_ok": true, "imports": ["requests", "json"], "from_imports": [], "test_functions": ["test_health", "test_login", "test_protected_endpoint"], "deprecated_patterns": [], "import_issues": []}, "tests\\test_similar_products.py": {"path": "tests\\test_similar_products.py", "syntax_ok": true, "imports": ["asyncio", "json", "httpx"], "from_imports": ["pprint"], "test_functions": [], "deprecated_patterns": [], "import_issues": []}, "tests\\test_structured_filters.py": {"path": "tests\\test_structured_filters.py", "syntax_ok": true, "imports": ["json", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "<PERSON><PERSON><PERSON>", "src.utils.filter_utils", "src.db.schemas", "src.main", "httpx"], "test_functions": ["test_apply_filter", "test_apply_filter_group", "test_apply_structured_filters", "test_parse_simple_filter_string"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\api\\v1\\endpoints\\test_auth.py": {"path": "tests\\api\\v1\\endpoints\\test_auth.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["src.core.security", "src.db.repositories.account", "src.db.repositories.auth", "sqlalchemy.ext.asyncio", "src.db.repositories.user", "src.db.enums", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\api\\v1\\endpoints\\test_billing_mercadopago_webhook.py": {"path": "tests\\api\\v1\\endpoints\\test_billing_mercadopago_webhook.py", "syntax_ok": true, "imports": ["<PERSON><PERSON><PERSON>", "json", "hmac", "pytest"], "from_imports": ["src.core.config", "sqlalchemy.ext.asyncio", "unittest.mock", "<PERSON><PERSON><PERSON>", "src.api.v1.endpoints.billing", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\api\\v1\\endpoints\\test_recommendation_explanations.py": {"path": "tests\\api\\v1\\endpoints\\test_recommendation_explanations.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["unittest.mock", "sqlalchemy.ext.asyncio", "src.ml_pipeline.serving_engine", "src.ml_pipeline.explanation_generator", "src.db.schemas", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\e2e\\test_api_flows.py": {"path": "tests\\e2e\\test_api_flows.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\e2e\\test_recommendations.py": {"path": "tests\\e2e\\test_recommendations.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_auth_flow.py": {"path": "tests\\integration\\test_auth_flow.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_cache.py": {"path": "tests\\integration\\test_cache.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_celery_tenant_isolation.py": {"path": "tests\\integration\\test_celery_tenant_isolation.py", "syntax_ok": true, "imports": ["uuid", "json", "pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "src.workers.celery_tasks", "tests.integration.conftest", "src.db.models", "src.services.batch_data_storage_service", "sqlalchemy.future", "datetime", "src.utils.tenant_context", "src.utils.maintenance"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": ["tests.integration.conftest"]}, "tests\\integration\\test_celery_tenant_isolation_extended.py": {"path": "tests\\integration\\test_celery_tenant_isolation_extended.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["src.db.models", "src.utils.tenant_context", "unittest.mock", "src.workers.celery_tasks"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_data_ingestion.py": {"path": "tests\\integration\\test_data_ingestion.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_data_ingestion_pipeline.py": {"path": "tests\\integration\\test_data_ingestion_pipeline.py", "syntax_ok": true, "imports": ["json", "pytest_asyncio", "pytest"], "from_imports": ["src.services", "unittest.mock", "<PERSON><PERSON><PERSON>", "src.api.v1.endpoints.data_ingestion", "tests.integration.conftest", "src.core.exceptions", "src.core.deps", "src.db.models", "src.main", "src.db.enums", "tests.conftest", "datetime", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": ["tests.integration.conftest", "tests.conftest"]}, "tests\\integration\\test_data_integrity.py": {"path": "tests\\integration\\test_data_integrity.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_db_interaction.py": {"path": "tests\\integration\\test_db_interaction.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_end_to_end_pipeline.py": {"path": "tests\\integration\\test_end_to_end_pipeline.py", "syntax_ok": true, "imports": ["json", "pytest_asyncio", "pytest"], "from_imports": ["src.services", "unittest.mock", "<PERSON><PERSON><PERSON>", "tests.integration.conftest", "src.core.deps", "src.main", "tests.conftest", "datetime", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": ["tests.integration.conftest", "tests.conftest"]}, "tests\\integration\\test_error_handling.py": {"path": "tests\\integration\\test_error_handling.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_gcs_integration.py": {"path": "tests\\integration\\test_gcs_integration.py", "syntax_ok": true, "imports": ["pickle", "json", "pytest_asyncio", "pytest", "os"], "from_imports": ["src.services", "unittest.mock", "<PERSON><PERSON><PERSON>", "tests.integration.conftest", "src.core.deps", "src.db.models", "src.services.batch_data_storage_service", "src.main", "tests.conftest", "datetime", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": ["tests.integration.conftest", "tests.conftest"]}, "tests\\integration\\test_hybrid_recommender_integration.py": {"path": "tests\\integration\\test_hybrid_recommender_integration.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_ltr_integration.py": {"path": "tests\\integration\\test_ltr_integration.py", "syntax_ok": true, "imports": ["pandas", "numpy", "pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "src.ml_pipeline.metrics_tracker", "src.ml_pipeline.model_artifact_manager", "src.ml_pipeline.training_pipeline", "src.ml_pipeline.learning_to_rank", "src.ml_pipeline.evaluation"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_ml_pipeline.py": {"path": "tests\\integration\\test_ml_pipeline.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_multi_tenancy.py": {"path": "tests\\integration\\test_multi_tenancy.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid", "TestClient: Ensure FastAPI TestClient is being used correctly"], "import_issues": []}, "tests\\integration\\test_multi_tenancy_security.py": {"path": "tests\\integration\\test_multi_tenancy_security.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid", "TestClient: Ensure FastAPI TestClient is being used correctly"], "import_issues": []}, "tests\\integration\\test_recommendations_api.py": {"path": "tests\\integration\\test_recommendations_api.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "src.ml_pipeline.serving_engine", "src.db", "src.main", "datetime", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_recommendation_pipeline.py": {"path": "tests\\integration\\test_recommendation_pipeline.py", "syntax_ok": true, "imports": ["json", "pytest_asyncio", "pytest"], "from_imports": ["src.services", "unittest.mock", "src.ml_pipeline.post_processing_service", "<PERSON><PERSON><PERSON>", "src.ml_pipeline.serving_engine", "src.ml_pipeline.fallback_handler", "src.ml_pipeline.model_artifact_manager", "tests.integration.conftest", "src.core.deps", "src.db.models", "src.main", "src.db.enums", "tests.conftest", "datetime", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": ["tests.integration.conftest", "tests.conftest"]}, "tests\\integration\\test_recommendation_with_ltr.py": {"path": "tests\\integration\\test_recommendation_with_ltr.py", "syntax_ok": true, "imports": ["pandas", "numpy", "pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "src.ml_pipeline.post_processing_service", "src.ml_pipeline.serving_engine", "src.ml_pipeline.model_artifact_manager", "src.ml_pipeline.learning_to_rank"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_rls_functions_security.py": {"path": "tests\\integration\\test_rls_functions_security.py", "syntax_ok": true, "imports": ["re", "json", "pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "src.db.session", "typing", "datetime", "src.db.models", "sqlalchemy"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\test_training_pipeline.py": {"path": "tests\\integration\\test_training_pipeline.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "src.ml_pipeline.training_pipeline", "src.db", "src.main", "datetime", "httpx"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\integration\\utils\\test_db.py": {"path": "tests\\integration\\utils\\test_db.py", "syntax_ok": true, "imports": ["asyncio", "pytest_asyncio", "pytest", "os"], "from_imports": ["src.core.config", "sqlalchemy.ext.asyncio", "sqlalchemy.pool", "src.db.session", "typing", "sqlalchemy.orm", "src.db.models", "src.db.enums", "src.db.base"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\middleware\\test_usage_meter_middleware.py": {"path": "tests\\middleware\\test_usage_meter_middleware.py", "syntax_ok": false, "imports": [], "from_imports": [], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\services\\test_billing_webhook_service.py": {"path": "tests\\services\\test_billing_webhook_service.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["unittest.mock", "src.services.billing_webhook_service", "src.db.models", "src.db.enums", "datetime"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\services\\test_usage_meter_service.py": {"path": "tests\\services\\test_usage_meter_service.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["src.core.exceptions", "datetime", "unittest.mock", "src.services.usage_meter_service"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_account_service.py": {"path": "tests\\unit\\test_account_service.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["unittest.mock", "sqlalchemy.exc", "src.db.models", "datetime", "src.services.account_service"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_email_verification.py": {"path": "tests\\unit\\test_email_verification.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["src.services.email_verification_service", "sqlalchemy.ext.asyncio", "unittest.mock", "src.core.exceptions", "src.db.models", "datetime"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_free_plan_abuse_prevention.py": {"path": "tests\\unit\\test_free_plan_abuse_prevention.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "<PERSON><PERSON><PERSON>", "src.middleware.api_call_counter", "src.db.models", "src.db.enums"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_hybrid_recommender.py": {"path": "tests\\unit\\test_hybrid_recommender.py", "syntax_ok": true, "imports": ["numpy", "pytest_asyncio", "pytest"], "from_imports": ["unittest.mock", "src.core.redis_manager", "src.ml_pipeline.serving_engine", "src.core.exceptions", "src.ml_pipeline.evaluation"], "test_functions": ["test_combine_recommendations", "test_calculate_weights", "test_new_user_gets_more_content_weight"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_permission_service_with_mocks.py": {"path": "tests\\unit\\test_permission_service_with_mocks.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["unittest.mock", "typing", "src.db.models", "src.services.permission_service", "src.db.enums"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_pipeline_endpoints.py": {"path": "tests\\unit\\test_pipeline_endpoints.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["unittest.mock", "<PERSON><PERSON><PERSON>", "src.core.exceptions", "datetime", "src.api.v1.endpoints.pipeline"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_pipeline_manager.py": {"path": "tests\\unit\\test_pipeline_manager.py", "syntax_ok": true, "imports": ["pandas", "pytest_asyncio", "pytest"], "from_imports": ["unittest.mock", "src.ml_pipeline.metrics_tracker", "src.core.exceptions", "src.ml_pipeline.model_artifact_manager", "src.ml_pipeline.training_pipeline", "datetime", "src.ml_pipeline.evaluation"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_register.py": {"path": "tests\\unit\\test_register.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "<PERSON><PERSON><PERSON>", "src.api.v1.endpoints.auth", "src.db.enums", "src.db.schemas.auth"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_training_data_limit.py": {"path": "tests\\unit\\test_training_data_limit.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "unittest.mock", "src.core.exceptions", "src.ml_pipeline.training_pipeline", "src.db.enums", "src.services.limit_service"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\test_usage_summary.py": {"path": "tests\\unit\\test_usage_summary.py", "syntax_ok": true, "imports": ["pytest_asyncio", "pytest"], "from_imports": ["src.api.v1.endpoints.usage_summary", "sqlalchemy.ext.asyncio", "unittest.mock", "src.db.enums", "datetime"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\api\\test_pipeline_error_handling.py": {"path": "tests\\unit\\api\\test_pipeline_error_handling.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["unittest.mock", "<PERSON><PERSON><PERSON>", "src.core.exceptions", "datetime", "src.api.v1.endpoints.pipeline"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\core\\security\\test_api_key.py": {"path": "tests\\unit\\core\\security\\test_api_key.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["src.core.security.api_key"], "test_functions": ["test_generate_api_key", "test_generate_api_key_simple", "test_hash_api_key", "test_verify_api_key", "test_api_key_uniqueness", "test_api_key_case_sensitivity"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\core\\security\\test_api_key_fixed.py": {"path": "tests\\unit\\core\\security\\test_api_key_fixed.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["src.core.security.api_key"], "test_functions": ["test_generate_api_key", "test_generate_api_key_simple", "test_hash_api_key", "test_verify_api_key", "test_api_key_uniqueness", "test_api_key_case_sensitivity"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\core\\security\\test_password.py": {"path": "tests\\unit\\core\\security\\test_password.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["src.core.security.password"], "test_functions": ["test_password_hashing", "test_password_verification", "test_password_case_sensitivity", "test_password_special_characters", "test_password_length", "test_password_unicode"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\core\\security\\test_token.py": {"path": "tests\\unit\\core\\security\\test_token.py", "syntax_ok": true, "imports": ["time", "pytest"], "from_imports": ["src.core.security.token", "datetime", "src.core.config", "jose"], "test_functions": ["test_create_access_token", "test_decode_access_token", "test_token_expiration", "test_token_revocation", "test_invalid_token", "test_token_with_invalid_signature", "test_token_with_missing_claims", "test_token_reuse_after_revocation"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\db\\repositories\\test_base.py": {"path": "tests\\unit\\db\\repositories\\test_base.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "src.db.repositories.base", "src.core.exceptions", "src.db.models", "sqlalchemy"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\ml_pipeline\\test_base_trainer.py": {"path": "tests\\unit\\ml_pipeline\\test_base_trainer.py", "syntax_ok": true, "imports": ["pandas", "numpy", "pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "src.ml_pipeline.base_trainer", "unittest.mock", "typing", "src.core.exceptions", "src.db.models", "datetime"], "test_functions": ["test_validate_data_valid", "test_validate_data_invalid_missing_columns", "test_validate_data_empty", "test_validate_data_exception", "test_get_model_parameters_no_model", "test_get_model_parameters_with_model", "test_save_model_no_model", "test_save_model_with_model", "test_load_model", "test_concrete_implementation"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\ml_pipeline\\test_evaluation.py": {"path": "tests\\unit\\ml_pipeline\\test_evaluation.py", "syntax_ok": true, "imports": ["pandas", "numpy", "pytest"], "from_imports": ["src.db.models", "datetime", "src.ml_pipeline.evaluation", "sqlalchemy.ext.asyncio"], "test_functions": ["test_calculate_metrics", "test_cross_validate", "test_calculate_metrics_with_empty_data", "test_cross_validate_with_single_class"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\ml_pipeline\\test_ltr_training_objective.py": {"path": "tests\\unit\\ml_pipeline\\test_ltr_training_objective.py", "syntax_ok": true, "imports": ["pandas", "numpy", "pytest"], "from_imports": ["src.ml_pipeline.learning_to_rank", "unittest.mock"], "test_functions": ["test_ltr_uses_target_scores_when_provided", "test_ltr_uses_weighted_combination_when_no_target_scores"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\ml_pipeline\\test_metrics_tracker.py": {"path": "tests\\unit\\ml_pipeline\\test_metrics_tracker.py", "syntax_ok": true, "imports": ["pandas", "numpy", "pytest"], "from_imports": ["src.db.models", "datetime", "src.ml_pipeline.metrics_tracker", "sqlalchemy.ext.asyncio"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\services\\test_limit_service.py": {"path": "tests\\unit\\services\\test_limit_service.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "src.core.exceptions", "src.db.models", "src.services.limit_service", "datetime"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\unit\\services\\test_permission_service.py": {"path": "tests\\unit\\services\\test_permission_service.py", "syntax_ok": true, "imports": ["pytest"], "from_imports": ["sqlalchemy.ext.asyncio", "src.core.exceptions", "src.db.models", "src.services.permission_service", "datetime"], "test_functions": [], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}, "tests\\utils\\test_mercadopago_utils.py": {"path": "tests\\utils\\test_mercadopago_utils.py", "syntax_ok": true, "imports": ["<PERSON><PERSON><PERSON>", "hmac", "pytest"], "from_imports": ["src.utils.mercadopago_utils", "<PERSON><PERSON><PERSON>", "unittest.mock"], "test_functions": ["test_extract_signature_parts_valid", "test_extract_signature_parts_invalid", "test_extract_signature_parts_missing_parts"], "deprecated_patterns": ["from src.: Check if import paths are still valid"], "import_issues": []}}, "issues": {"syntax_errors": ["tests\\e2e\\test_api_flows.py", "tests\\e2e\\test_recommendations.py", "tests\\integration\\test_auth_flow.py", "tests\\integration\\test_cache.py", "tests\\integration\\test_data_ingestion.py", "tests\\integration\\test_data_integrity.py", "tests\\integration\\test_db_interaction.py", "tests\\integration\\test_error_handling.py", "tests\\integration\\test_hybrid_recommender_integration.py", "tests\\integration\\test_ml_pipeline.py", "tests\\integration\\test_multi_tenancy.py", "tests\\integration\\test_multi_tenancy_security.py", "tests\\middleware\\test_usage_meter_middleware.py"], "import_failures": [{"file": "tests\\integration\\test_celery_tenant_isolation.py", "missing_modules": ["tests.integration.conftest"]}, {"file": "tests\\integration\\test_data_ingestion_pipeline.py", "missing_modules": ["tests.integration.conftest", "tests.conftest"]}, {"file": "tests\\integration\\test_end_to_end_pipeline.py", "missing_modules": ["tests.integration.conftest", "tests.conftest"]}, {"file": "tests\\integration\\test_gcs_integration.py", "missing_modules": ["tests.integration.conftest", "tests.conftest"]}, {"file": "tests\\integration\\test_recommendation_pipeline.py", "missing_modules": ["tests.integration.conftest", "tests.conftest"]}], "deprecated_patterns": {"tests\\test_recommendation_context.py": ["from src.: Check if import paths are still valid"], "tests\\test_recommendation_strategies.py": ["from src.: Check if import paths are still valid"], "tests\\test_robust_upsert.py": ["from src.: Check if import paths are still valid"], "tests\\test_structured_filters.py": ["from src.: Check if import paths are still valid"], "tests\\api\\v1\\endpoints\\test_auth.py": ["from src.: Check if import paths are still valid"], "tests\\api\\v1\\endpoints\\test_billing_mercadopago_webhook.py": ["from src.: Check if import paths are still valid"], "tests\\api\\v1\\endpoints\\test_recommendation_explanations.py": ["from src.: Check if import paths are still valid"], "tests\\e2e\\test_api_flows.py": ["from src.: Check if import paths are still valid"], "tests\\e2e\\test_recommendations.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_auth_flow.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_cache.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_celery_tenant_isolation.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_celery_tenant_isolation_extended.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_data_ingestion.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_data_ingestion_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_data_integrity.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_db_interaction.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_end_to_end_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_error_handling.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_gcs_integration.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_hybrid_recommender_integration.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_ltr_integration.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_ml_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_multi_tenancy.py": ["from src.: Check if import paths are still valid", "TestClient: Ensure FastAPI TestClient is being used correctly"], "tests\\integration\\test_multi_tenancy_security.py": ["from src.: Check if import paths are still valid", "TestClient: Ensure FastAPI TestClient is being used correctly"], "tests\\integration\\test_recommendations_api.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_recommendation_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_recommendation_with_ltr.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_rls_functions_security.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_training_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\utils\\test_db.py": ["from src.: Check if import paths are still valid"], "tests\\middleware\\test_usage_meter_middleware.py": ["from src.: Check if import paths are still valid"], "tests\\services\\test_billing_webhook_service.py": ["from src.: Check if import paths are still valid"], "tests\\services\\test_usage_meter_service.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_account_service.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_email_verification.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_free_plan_abuse_prevention.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_hybrid_recommender.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_permission_service_with_mocks.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_pipeline_endpoints.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_pipeline_manager.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_register.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_training_data_limit.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_usage_summary.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\api\\test_pipeline_error_handling.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_api_key.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_api_key_fixed.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_password.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_token.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\db\\repositories\\test_base.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_base_trainer.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_evaluation.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_ltr_training_objective.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_metrics_tracker.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\services\\test_limit_service.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\services\\test_permission_service.py": ["from src.: Check if import paths are still valid"], "tests\\utils\\test_mercadopago_utils.py": ["from src.: Check if import paths are still valid"]}, "missing_modules": ["tests.integration.conftest", "tests.conftest"]}, "recommendations": [{"priority": "HIGH", "category": "Syntax", "description": "Corregir 13 archivos con errores de sintaxis", "files": ["tests\\e2e\\test_api_flows.py", "tests\\e2e\\test_recommendations.py", "tests\\integration\\test_auth_flow.py", "tests\\integration\\test_cache.py", "tests\\integration\\test_data_ingestion.py", "tests\\integration\\test_data_integrity.py", "tests\\integration\\test_db_interaction.py", "tests\\integration\\test_error_handling.py", "tests\\integration\\test_hybrid_recommender_integration.py", "tests\\integration\\test_ml_pipeline.py", "tests\\integration\\test_multi_tenancy.py", "tests\\integration\\test_multi_tenancy_security.py", "tests\\middleware\\test_usage_meter_middleware.py"]}, {"priority": "HIGH", "category": "Dependencies", "description": "Instalar módulos faltantes o actualizar imports", "modules": ["tests.integration.conftest", "tests.conftest"]}, {"priority": "MEDIUM", "category": "Code Quality", "description": "Actualizar 59 patrones obsoletos", "details": {"tests\\test_recommendation_context.py": ["from src.: Check if import paths are still valid"], "tests\\test_recommendation_strategies.py": ["from src.: Check if import paths are still valid"], "tests\\test_robust_upsert.py": ["from src.: Check if import paths are still valid"], "tests\\test_structured_filters.py": ["from src.: Check if import paths are still valid"], "tests\\api\\v1\\endpoints\\test_auth.py": ["from src.: Check if import paths are still valid"], "tests\\api\\v1\\endpoints\\test_billing_mercadopago_webhook.py": ["from src.: Check if import paths are still valid"], "tests\\api\\v1\\endpoints\\test_recommendation_explanations.py": ["from src.: Check if import paths are still valid"], "tests\\e2e\\test_api_flows.py": ["from src.: Check if import paths are still valid"], "tests\\e2e\\test_recommendations.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_auth_flow.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_cache.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_celery_tenant_isolation.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_celery_tenant_isolation_extended.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_data_ingestion.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_data_ingestion_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_data_integrity.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_db_interaction.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_end_to_end_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_error_handling.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_gcs_integration.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_hybrid_recommender_integration.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_ltr_integration.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_ml_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_multi_tenancy.py": ["from src.: Check if import paths are still valid", "TestClient: Ensure FastAPI TestClient is being used correctly"], "tests\\integration\\test_multi_tenancy_security.py": ["from src.: Check if import paths are still valid", "TestClient: Ensure FastAPI TestClient is being used correctly"], "tests\\integration\\test_recommendations_api.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_recommendation_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_recommendation_with_ltr.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_rls_functions_security.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\test_training_pipeline.py": ["from src.: Check if import paths are still valid"], "tests\\integration\\utils\\test_db.py": ["from src.: Check if import paths are still valid"], "tests\\middleware\\test_usage_meter_middleware.py": ["from src.: Check if import paths are still valid"], "tests\\services\\test_billing_webhook_service.py": ["from src.: Check if import paths are still valid"], "tests\\services\\test_usage_meter_service.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_account_service.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_email_verification.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_free_plan_abuse_prevention.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_hybrid_recommender.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_permission_service_with_mocks.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_pipeline_endpoints.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_pipeline_manager.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_register.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_training_data_limit.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\test_usage_summary.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\api\\test_pipeline_error_handling.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_api_key.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_api_key_fixed.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_password.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\core\\security\\test_token.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\db\\repositories\\test_base.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_base_trainer.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_evaluation.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_ltr_training_objective.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\ml_pipeline\\test_metrics_tracker.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\services\\test_limit_service.py": ["from src.: Check if import paths are still valid"], "tests\\unit\\services\\test_permission_service.py": ["from src.: Check if import paths are still valid"], "tests\\utils\\test_mercadopago_utils.py": ["from src.: Check if import paths are still valid"]}}, {"priority": "HIGH", "category": "Environment", "description": "Usar Docker para ejecutar tests y evitar problemas de dependencias", "command": "docker-compose -f docker-compose.test.yml up --build"}]}