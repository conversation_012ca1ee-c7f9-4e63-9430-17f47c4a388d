#!/bin/bash

set -e

# Configuración
PROJECT_ID="lateral-insight-461112-g9"
REGION="us-central1"
NOTIFICATION_EMAIL="<EMAIL>"  # Cambiar por email real

echo "📊 === CONFIGURACIÓN DE MONITOREO RAYUELA ==="
echo ""

echo "🔧 Habilitando APIs necesarias..."
gcloud services enable monitoring.googleapis.com
gcloud services enable logging.googleapis.com
gcloud services enable cloudtrace.googleapis.com

echo ""
echo "📬 Configurando canal de notificación por email..."
# Crear canal de notificación por email
NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels create \
    --display-name="Rayuela Admin Email" \
    --type=email \
    --channel-labels=email_address=$NOTIFICATION_EMAIL \
    --format="value(name)" 2>/dev/null || echo "")

if [ -n "$NOTIFICATION_CHANNEL" ]; then
    echo "✅ Canal de notificación creado: $NOTIFICATION_CHANNEL"
else
    echo "⚠️ Canal de notificación ya existe o falló la creación"
    NOTIFICATION_CHANNEL=$(gcloud alpha monitoring channels list \
        --filter="displayName:'Rayuela Admin Email'" \
        --format="value(name)" | head -1)
fi

echo ""
echo "🚨 Configurando políticas de alerta..."

# Alerta 1: CPU alto en servicios
echo "📊 Creando alerta de CPU alto..."
cat > /tmp/cpu-alert.yaml << EOF
displayName: "Rayuela - CPU Alto"
documentation:
  content: "CPU usage is high on Rayuela services"
  mimeType: "text/markdown"
conditions:
  - displayName: "CPU usage high"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name=~"rayuela-.*"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0.8
      duration: 300s
      aggregations:
        - alignmentPeriod: 60s
          perSeriesAligner: ALIGN_MEAN
          crossSeriesReducer: REDUCE_MEAN
          groupByFields:
            - resource.labels.service_name
combiner: OR
enabled: true
notificationChannels:
  - $NOTIFICATION_CHANNEL
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/cpu-alert.yaml || echo "⚠️ Política de CPU ya existe"

# Alerta 2: Memoria alta
echo "📊 Creando alerta de memoria alta..."
cat > /tmp/memory-alert.yaml << EOF
displayName: "Rayuela - Memoria Alta"
documentation:
  content: "Memory usage is high on Rayuela services"
  mimeType: "text/markdown"
conditions:
  - displayName: "Memory usage high"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name=~"rayuela-.*"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 0.85
      duration: 300s
      aggregations:
        - alignmentPeriod: 60s
          perSeriesAligner: ALIGN_MEAN
          crossSeriesReducer: REDUCE_MEAN
          groupByFields:
            - resource.labels.service_name
combiner: OR
enabled: true
notificationChannels:
  - $NOTIFICATION_CHANNEL
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/memory-alert.yaml || echo "⚠️ Política de memoria ya existe"

# Alerta 3: Errores 5xx
echo "📊 Creando alerta de errores 5xx..."
cat > /tmp/error-alert.yaml << EOF
displayName: "Rayuela - Errores 5xx"
documentation:
  content: "High rate of 5xx errors on Rayuela services"
  mimeType: "text/markdown"
conditions:
  - displayName: "High error rate"
    conditionThreshold:
      filter: 'resource.type="cloud_run_revision" AND resource.labels.service_name=~"rayuela-.*" AND metric.type="run.googleapis.com/request_count"'
      comparison: COMPARISON_GREATER_THAN
      thresholdValue: 10
      duration: 180s
      aggregations:
        - alignmentPeriod: 60s
          perSeriesAligner: ALIGN_RATE
          crossSeriesReducer: REDUCE_SUM
          groupByFields:
            - resource.labels.service_name
      trigger:
        count: 1
combiner: OR
enabled: true
notificationChannels:
  - $NOTIFICATION_CHANNEL
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/error-alert.yaml || echo "⚠️ Política de errores ya existe"

# Alerta 4: Base de datos no disponible
echo "📊 Creando alerta de base de datos..."
cat > /tmp/db-alert.yaml << EOF
displayName: "Rayuela - Base de Datos No Disponible"
documentation:
  content: "Database is not responding or down"
  mimeType: "text/markdown"
conditions:
  - displayName: "Database down"
    conditionThreshold:
      filter: 'resource.type="cloudsql_database" AND resource.labels.database_id="lateral-insight-461112-g9:rayuela-postgres"'
      comparison: COMPARISON_LESS_THAN
      thresholdValue: 1
      duration: 120s
      aggregations:
        - alignmentPeriod: 60s
          perSeriesAligner: ALIGN_MEAN
          crossSeriesReducer: REDUCE_MEAN
combiner: OR
enabled: true
notificationChannels:
  - $NOTIFICATION_CHANNEL
EOF

gcloud alpha monitoring policies create --policy-from-file=/tmp/db-alert.yaml || echo "⚠️ Política de DB ya existe"

echo ""
echo "📊 Configurando dashboard personalizado..."

# Crear dashboard
cat > /tmp/rayuela-dashboard.json << EOF
{
  "displayName": "Rayuela - Dashboard Principal",
  "mosaicLayout": {
    "tiles": [
      {
        "width": 6,
        "height": 4,
        "widget": {
          "title": "CPU Usage - Cloud Run Services",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"rayuela-.*\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_MEAN",
                      "crossSeriesReducer": "REDUCE_MEAN",
                      "groupByFields": ["resource.labels.service_name"]
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "CPU Utilization",
              "scale": "LINEAR"
            }
          }
        }
      },
      {
        "width": 6,
        "height": 4,
        "xPos": 6,
        "widget": {
          "title": "Memory Usage - Cloud Run Services",
          "xyChart": {
            "dataSets": [
              {
                "timeSeriesQuery": {
                  "timeSeriesFilter": {
                    "filter": "resource.type=\"cloud_run_revision\" AND resource.labels.service_name=~\"rayuela-.*\"",
                    "aggregation": {
                      "alignmentPeriod": "60s",
                      "perSeriesAligner": "ALIGN_MEAN",
                      "crossSeriesReducer": "REDUCE_MEAN",
                      "groupByFields": ["resource.labels.service_name"]
                    }
                  }
                },
                "plotType": "LINE"
              }
            ],
            "timeshiftDuration": "0s",
            "yAxis": {
              "label": "Memory Utilization",
              "scale": "LINEAR"
            }
          }
        }
      }
    ]
  }
}
EOF

gcloud monitoring dashboards create --config-from-file=/tmp/rayuela-dashboard.json || echo "⚠️ Dashboard ya existe"

# Limpiar archivos temporales
rm -f /tmp/*-alert.yaml /tmp/rayuela-dashboard.json

echo ""
echo "✅ === MONITOREO CONFIGURADO ==="
echo ""
echo "📊 Dashboard: https://console.cloud.google.com/monitoring/dashboards"
echo "🚨 Alertas: https://console.cloud.google.com/monitoring/alerting"
echo "📧 Notificaciones: $NOTIFICATION_EMAIL"
echo ""
echo "🔍 Para ver métricas:"
echo "   gcloud alpha monitoring policies list"
echo "   gcloud alpha monitoring channels list"
echo ""
echo "⚠️  IMPORTANTE: Actualiza la variable NOTIFICATION_EMAIL en el script"
echo "   con tu email real para recibir alertas." 