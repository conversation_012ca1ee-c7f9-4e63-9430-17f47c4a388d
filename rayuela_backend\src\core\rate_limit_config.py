from typing import Optional, Dict, Any, Mapping
from src.core.redis_manager import RedisManager
from src.core.cache_manager import CacheManager
from src.core.config import settings
from src.utils.base_logger import log_info, log_error
from src.db.enums import SubscriptionPlan, PLAN_LIMITS
import time
from functools import lru_cache


class RateLimitConfig:
    """Gestor optimizado de configuración de rate limits"""

    def __init__(self):
        self._redis_manager = None
        self._cache_manager = CacheManager()
        self.prefix = "rate_limit"  # Valor fijo para evitar dependencia de settings
        self.ttl = 3600  # 1 hora por defecto

    async def _get_redis_manager(self) -> RedisManager:
        """Obtiene la instancia de RedisManager"""
        if not self._redis_manager:
            self._redis_manager = await RedisManager.get_instance()
        return self._redis_manager

    def _get_config_key(self, api_key: str) -> str:
        """Genera una key para la configuración de rate limit basada en el API key"""
        return f"{self.prefix}:config:{api_key}"

    def _get_account_config_key(self, account_id: int) -> str:
        """Genera una key para la configuración de rate limit basada en el account_id"""
        return f"{self.prefix}:account:{account_id}"

    def _get_plan_limit(self, plan_type: str) -> int:
        """Obtiene el límite de peticiones por minuto para un plan"""
        # Usar directamente los límites definidos en enums.py
        if plan_type.upper() in SubscriptionPlan.__members__:
            plan = SubscriptionPlan[plan_type.upper()]
            return PLAN_LIMITS.get(plan, {}).get(
                "max_requests_per_minute", settings.DEFAULT_RATE_LIMIT
            )

        # Fallback a valores por defecto si el plan no existe
        plan_limits = {
            "free": 60,
            "basic": 300,
            "premium": 1000,
            "enterprise": 5000,
        }
        return plan_limits.get(plan_type.lower(), settings.DEFAULT_RATE_LIMIT)

    async def get_rate_limit(self, api_key: str) -> Dict[str, Any]:
        """Obtiene la configuración de rate limit para un API key"""
        try:
            # Obtener de Redis usando CacheManager
            config_key = self._get_config_key(api_key)
            config = await self._cache_manager.get(config_key)

            if not config:
                # Configuración por defecto si no existe
                config = {
                    "requests_per_minute": settings.DEFAULT_RATE_LIMIT,
                    "plan_type": "free",
                }

            return config
        except Exception as e:
            log_error(f"Error obteniendo rate limit config: {str(e)}")
            return {
                "requests_per_minute": settings.DEFAULT_RATE_LIMIT,
                "plan_type": "free",
            }

    async def set_rate_limit(
        self, account_id: int, api_key: str, plan_type: str
    ) -> bool:
        """Actualiza la configuración de rate limit basada en el plan de suscripción"""
        try:
            # Obtener límite de peticiones para el plan
            requests_per_minute = self._get_plan_limit(plan_type)

            config = {
                "requests_per_minute": requests_per_minute,
                "plan_type": plan_type,
            }

            # Guardar configuración por API key
            config_key = self._get_config_key(api_key)
            await self._cache_manager.set(config_key, config, ttl=self.ttl)

            # Guardar configuración por account_id
            account_key = self._get_account_config_key(account_id)
            await self._cache_manager.set(account_key, config, ttl=self.ttl)

            log_info(
                f"Rate limit config actualizada para account {account_id}: {config}"
            )
            return True
        except Exception as e:
            log_error(f"Error actualizando rate limit config: {str(e)}")
            return False

    async def invalidate_config(
        self, api_key: Optional[str] = None, account_id: Optional[int] = None
    ) -> bool:
        """Invalida la configuración de rate limit"""
        try:
            # Invalidar en Redis usando CacheManager
            if api_key:
                config_key = self._get_config_key(api_key)
                await self._cache_manager.delete(config_key)

            if account_id:
                account_key = self._get_account_config_key(account_id)
                await self._cache_manager.delete(account_key)

            if not api_key and not account_id:
                # Invalidar todas las configuraciones
                pattern = f"{self.prefix}:*"
                await self._cache_manager.delete_pattern(pattern)

            return True
        except Exception as e:
            log_error(f"Error invalidando rate limit config: {str(e)}")
            return False
