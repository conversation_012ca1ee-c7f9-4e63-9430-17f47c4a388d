import pytest
import pytest_asyncio
from unittest.mock import MagicMock, patch, AsyncMock
from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>
from datetime import datetime, timezone
from src.api.v1.endpoints.pipeline import (
    train_model,
    train_artifact_for_account,
    get_training_status,
    process_training_job,
    training_callback,
)
from src.core.exceptions import ResourceNotFoundError


class TestPipelineEndpoints:
    """Tests para los endpoints del pipeline"""

    @pytest.mark.asyncio
    async def test_train_model_creates_job_and_schedules_training(self):
        """Test que verifica que train_model crea un job y programa el entrenamiento"""
        # Mock para la cuenta
        mock_account = MagicMock()
        mock_account.id = 1

        # Mock para la sesión de BD
        mock_db = AsyncMock()
        mock_db.execute = AsyncMock()
        mock_db.execute.return_value.scalar_one_or_none = MagicMock(return_value=None)
        mock_db.begin = AsyncMock().__aenter__.return_value
        mock_db.add = MagicMock()
        mock_db.commit = AsyncMock()
        mock_db.refresh = AsyncMock()

        # Mock para el servicio de límites
        mock_limit_service = AsyncMock()
        mock_limit_service.validate_api_call_limit = AsyncMock()

        # Mock para el job de entrenamiento
        mock_job = MagicMock()
        mock_job.id = 1

        # Patch para TrainingJob
        with patch(
            "src.api.v1.endpoints.pipeline.models.TrainingJob", return_value=mock_job
        ):
            # Patch para pipeline_manager
            with patch(
                "src.api.v1.endpoints.pipeline.pipeline_manager"
            ) as mock_pipeline_manager:
                mock_pipeline_manager.schedule_training = AsyncMock()
                mock_pipeline_manager.schedule_training.return_value = {
                    "job_id": 1,
                    "status": "PENDING",
                }

                # Ejecutar
                result = await train_model(
                    background_tasks=MagicMock(),
                    account=mock_account,
                    db=mock_db,
                    limit_service=mock_limit_service,
                )

                # Verificar que se validaron los límites
                mock_limit_service.validate_api_call_limit.assert_called_once_with(
                    "model_training"
                )

                # Verificar que se creó el job
                mock_db.add.assert_called_once_with(mock_job)
                mock_db.commit.assert_called_once()
                mock_db.refresh.assert_called_once_with(mock_job)

                # Verificar que se programó el entrenamiento
                mock_pipeline_manager.schedule_training.assert_called_once_with(1, 1)

                # Verificar el resultado
                assert result == {"job_id": 1, "status": "PENDING"}

    @pytest.mark.asyncio
    async def test_train_artifact_for_account_raises_404_for_nonexistent_account(self):
        """Test que verifica que train_artifact_for_account lanza 404 si la cuenta no existe"""
        # Mock para la sesión de BD
        mock_db = AsyncMock()
        mock_db.get = AsyncMock(return_value=None)

        # Mock para el usuario administrador
        mock_admin = MagicMock()

        # Mock para el servicio de límites
        mock_limit_service = AsyncMock()
        mock_limit_service.validate_api_call_limit = AsyncMock()

        # Ejecutar con error esperado
        with pytest.raises(HTTPException) as excinfo:
            await train_artifact_for_account(
                account_id=1,
                db=mock_db,
                current_user=mock_admin,
                limit_service=mock_limit_service,
            )

        # Verificar el error
        assert excinfo.value.status_code == 404
        assert "Account not found" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_get_training_status_raises_404_for_nonexistent_job(self):
        """Test que verifica que get_training_status lanza 404 si el job no existe"""
        # Mock para la cuenta
        mock_account = MagicMock()
        mock_account.id = 1

        # Mock para la sesión de BD
        mock_db = AsyncMock()
        mock_db.get = AsyncMock(return_value=None)

        # Ejecutar con error esperado
        with pytest.raises(HTTPException) as excinfo:
            await get_training_status(job_id=1, account=mock_account, db=mock_db)

        # Verificar el error
        assert excinfo.value.status_code == 404
        assert "Training job not found" in excinfo.value.detail

    @pytest.mark.asyncio
    async def test_get_training_status_returns_job_details(self):
        """Test que verifica que get_training_status devuelve los detalles del job"""
        # Mock para la cuenta
        mock_account = MagicMock()
        mock_account.id = 1

        # Mock para el job
        mock_job = MagicMock()
        mock_job.id = 1
        mock_job.status = "COMPLETED"
        mock_job.started_at = datetime.now(timezone.utc)
        mock_job.completed_at = datetime.now(timezone.utc)
        mock_job.error_message = None
        mock_job.model_metadata_id = 1

        # Mock para los metadatos del modelo
        mock_metadata = MagicMock()
        mock_metadata.id = 1
        mock_metadata.artifact_name = "test_model"
        mock_metadata.artifact_version = "1.0.0"
        mock_metadata.performance_metrics = {"precision": 0.8}

        # Mock para la sesión de BD
        mock_db = AsyncMock()
        mock_db.get = AsyncMock(side_effect=[mock_job, mock_metadata])

        # Ejecutar
        result = await get_training_status(job_id=1, account=mock_account, db=mock_db)

        # Verificar el resultado
        assert result["job_id"] == 1
        assert result["status"] == "COMPLETED"
        assert "model_metadata" in result
        assert result["model_metadata"]["id"] == 1
        assert result["model_metadata"]["name"] == "test_model"
        assert result["model_metadata"]["metrics"] == {"precision": 0.8}

    @pytest.mark.asyncio
    async def test_process_training_job_calls_run_training(self):
        """Test que verifica que process_training_job llama a run_training"""
        # Mock para la solicitud
        mock_request = MagicMock()

        # Mock para el payload
        mock_payload = {"account_id": 1, "job_id": 1, "callback_url": None}

        # Mock para la sesión de BD
        mock_db = AsyncMock()

        # Patch para pipeline_manager
        with patch(
            "src.api.v1.endpoints.pipeline.pipeline_manager"
        ) as mock_pipeline_manager:
            mock_pipeline_manager.run_training = AsyncMock()
            mock_pipeline_manager.run_training.return_value = {
                "status": "success",
                "job_id": 1,
            }

            # Ejecutar
            result = await process_training_job(
                request=mock_request, payload=mock_payload, db=mock_db
            )

            # Verificar que se llamó a run_training
            mock_pipeline_manager.run_training.assert_called_once_with(1, 1)

            # Verificar el resultado
            assert result["status"] == "success"
            assert result["job_id"] == 1

    @pytest.mark.asyncio
    async def test_process_training_job_handles_resource_not_found(self):
        """Test que verifica que process_training_job maneja ResourceNotFoundError"""
        # Mock para la solicitud
        mock_request = MagicMock()

        # Mock para el payload
        mock_payload = {"account_id": 1, "job_id": 1, "callback_url": None}

        # Mock para la sesión de BD
        mock_db = AsyncMock()

        # Patch para pipeline_manager que lanza ResourceNotFoundError
        with patch(
            "src.api.v1.endpoints.pipeline.pipeline_manager"
        ) as mock_pipeline_manager:
            mock_pipeline_manager.run_training = AsyncMock(
                side_effect=ResourceNotFoundError("Job not found")
            )

            # Patch para log_error
            with patch("src.api.v1.endpoints.pipeline.log_error") as mock_log_error:
                # Ejecutar con error esperado
                with pytest.raises(HTTPException) as excinfo:
                    await process_training_job(
                        request=mock_request, payload=mock_payload, db=mock_db
                    )

                # Verificar el error
                assert excinfo.value.status_code == 404
                assert "Job not found" in excinfo.value.detail

                # Verificar que se registró el error
                mock_log_error.assert_called_once()

    @pytest.mark.asyncio
    async def test_training_callback_updates_job_status(self):
        """Test que verifica que training_callback actualiza el estado del job"""
        # Mock para el payload
        mock_payload = {"account_id": 1, "status": "COMPLETED", "error_message": None}

        # Mock para el job
        mock_job = MagicMock()

        # Mock para la sesión de BD
        mock_db = AsyncMock()
        mock_db.get = AsyncMock(return_value=mock_job)
        mock_db.commit = AsyncMock()

        # Ejecutar
        result = await training_callback(job_id=1, payload=mock_payload, db=mock_db)

        # Verificar que se actualizó el estado del job
        assert mock_job.status == "COMPLETED"
        assert mock_job.completed_at is not None
        mock_db.commit.assert_called_once()

        # Verificar el resultado
        assert "message" in result
        assert "Callback processed successfully" in result["message"]
