from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

# No se necesitan importaciones de enums para este esquema


class AuditLogBase(BaseModel):
    action: str = Field(..., max_length=50)
    entity_type: str = Field(..., max_length=50)
    entity_id: str
    changes: Optional[dict] = None
    performed_by: str
    details: Optional[str] = None


class AuditLogCreate(AuditLogBase):
    pass


class AuditLog(AuditLogBase):
    account_id: int
    id: int
    created_at: datetime

    class ConfigDict:
        from_attributes = True


class UsageStats(BaseModel):
    """Estadísticas de uso de la API"""

    api_calls_count: int = 0
    storage_used: int = 0  # en bytes
    last_reset: Optional[datetime] = None
    updated_at: Optional[datetime] = None
