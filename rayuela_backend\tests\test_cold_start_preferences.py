"""
Tests for enhanced cold start recommendations with user preferences.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncSession

from src.ml_pipeline.fallback_handler import FallbackHandler
from src.db.schemas.end_user import EndUserCreate


class TestColdStartPreferences:
    """Test suite for cold start recommendations with user preferences."""

    @pytest.fixture
    def fallback_handler(self):
        """Create a FallbackHandler instance for testing."""
        return FallbackHandler()

    @pytest.fixture
    def mock_db(self):
        """Create a mock database session."""
        return AsyncMock(spec=AsyncSession)

    @pytest.fixture
    def sample_user_preferences(self):
        """Sample user onboarding preferences."""
        return {
            "preferred_categories": ["electronics", "books"],
            "disliked_categories": ["sports"],
            "preferred_brands": ["Apple", "Samsung"],
            "disliked_brands": ["BrandX"],
            "price_range_min": 50,
            "price_range_max": 500,
            "demographic_info": {
                "age_group": "25-34",
                "gender": "female",
                "location": "urban"
            },
            "onboarding_preferences": {
                "shopping_frequency": "weekly",
                "interests": ["technology", "reading"]
            }
        }

    @pytest.fixture
    def sample_products(self):
        """Sample products for testing."""
        products = []
        for i in range(10):
            product = MagicMock()
            product.id = i + 1
            product.name = f"Product {i + 1}"
            product.category = "electronics" if i < 5 else "books"
            product.price = 100 + (i * 50)
            product.description = f"Description for product {i + 1}"
            product.image_url = f"https://example.com/product{i + 1}.jpg"
            product.average_rating = 4.0 + (i * 0.1)
            products.append(product)
        return products

    @pytest.mark.asyncio
    async def test_preference_based_recommendations_with_categories(
        self, fallback_handler, mock_db, sample_user_preferences, sample_products
    ):
        """Test preference-based recommendations with preferred categories."""
        # Mock database query result
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = sample_products[:5]  # Electronics products
        mock_db.execute.return_value = mock_result

        # Call the preference-based recommendation method
        recommendations = await fallback_handler._get_preference_based_recommendations(
            db=mock_db,
            account_id=1,
            user_id=123,
            n_recommendations=3,
            user_onboarding_preferences=sample_user_preferences
        )

        # Assertions
        assert len(recommendations) == 3
        assert all(rec["category"] == "electronics" for rec in recommendations)
        assert all(rec["fallback_reason"] == "onboarding_preferences" for rec in recommendations)
        assert all("matched_preferences" in rec for rec in recommendations)

    @pytest.mark.asyncio
    async def test_preference_based_recommendations_with_price_range(
        self, fallback_handler, mock_db, sample_user_preferences, sample_products
    ):
        """Test preference-based recommendations with price range filtering."""
        # Filter products by price range (50-500)
        filtered_products = [p for p in sample_products if 50 <= p.price <= 500]
        
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = filtered_products
        mock_db.execute.return_value = mock_result

        recommendations = await fallback_handler._get_preference_based_recommendations(
            db=mock_db,
            account_id=1,
            user_id=123,
            n_recommendations=5,
            user_onboarding_preferences=sample_user_preferences
        )

        # Assertions
        assert len(recommendations) <= 5
        for rec in recommendations:
            assert rec["price"] >= 50
            assert rec["price"] <= 500
            assert rec["matched_preferences"]["price_in_range"] is True

    @pytest.mark.asyncio
    async def test_fallback_recommendations_with_new_user_preferences(
        self, fallback_handler, mock_db, sample_user_preferences
    ):
        """Test main fallback method with new user and preferences."""
        # Mock the preference-based strategy to return some recommendations
        fallback_handler._get_preference_based_recommendations = AsyncMock(
            return_value=[
                {
                    "item_id": 1,
                    "name": "Preferred Product",
                    "category": "electronics",
                    "price": 200,
                    "score": 0.95,
                    "fallback_reason": "onboarding_preferences"
                }
            ]
        )
        
        # Mock other strategies
        fallback_handler._get_popular_products = AsyncMock(return_value=[])
        fallback_handler._get_trending_products = AsyncMock(return_value=[])
        fallback_handler._get_new_arrivals = AsyncMock(return_value=[])

        recommendations = await fallback_handler.get_fallback_recommendations(
            db=mock_db,
            account_id=1,
            user_id=123,
            n_recommendations=5,
            is_new_user=True,
            user_onboarding_preferences=sample_user_preferences
        )

        # Assertions
        assert len(recommendations) == 1
        assert recommendations[0]["fallback_reason"] == "onboarding_preferences"
        
        # Verify that preference-based strategy was called first
        fallback_handler._get_preference_based_recommendations.assert_called_once()

    @pytest.mark.asyncio
    async def test_empty_preferences_fallback(
        self, fallback_handler, mock_db
    ):
        """Test fallback behavior when no preferences are provided."""
        recommendations = await fallback_handler._get_preference_based_recommendations(
            db=mock_db,
            account_id=1,
            user_id=123,
            n_recommendations=5,
            user_onboarding_preferences=None
        )

        # Should return empty list when no preferences
        assert recommendations == []

    def test_end_user_create_schema_validation(self):
        """Test EndUserCreate schema with preference fields."""
        user_data = {
            "external_id": "user_12345",
            "preferred_categories": ["electronics", "books"],
            "disliked_categories": ["sports"],
            "price_range_min": 10,
            "price_range_max": 500,
            "demographic_info": {
                "age_group": "25-34",
                "gender": "female"
            }
        }

        # Should validate successfully
        user_create = EndUserCreate(**user_data)
        assert user_create.external_id == "user_12345"
        assert user_create.preferred_categories == ["electronics", "books"]
        assert user_create.price_range_min == 10
        assert user_create.price_range_max == 500

    def test_end_user_create_schema_optional_fields(self):
        """Test EndUserCreate schema with only required fields."""
        user_data = {
            "external_id": "user_minimal"
        }

        # Should validate successfully with minimal data
        user_create = EndUserCreate(**user_data)
        assert user_create.external_id == "user_minimal"
        assert user_create.preferred_categories is None
        assert user_create.price_range_min is None

    def test_end_user_create_schema_price_validation(self):
        """Test price range validation in EndUserCreate schema."""
        # Valid price ranges
        user_data = {
            "external_id": "user_price_test",
            "price_range_min": 0,
            "price_range_max": 1000
        }
        
        user_create = EndUserCreate(**user_data)
        assert user_create.price_range_min == 0
        assert user_create.price_range_max == 1000

        # Test negative price (should fail validation)
        with pytest.raises(ValueError):
            EndUserCreate(
                external_id="user_invalid",
                price_range_min=-10
            )
