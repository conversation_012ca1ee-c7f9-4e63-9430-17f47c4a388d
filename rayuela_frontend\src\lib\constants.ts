/**
 * Constantes para la aplicación Rayuela
 */

// Planes de suscripción disponibles
// NOTA: Esta enumeración se mantiene por compatibilidad con código existente.
// Para nuevos desarrollos, utilizar el hook usePlans() que obtiene los planes del backend.
export enum SubscriptionPlan {
  FREE = "FREE",
  STARTER = "STARTER", // Antes BASIC
  PRO = "PRO",
  ENTERPRISE = "ENTERPRISE"
}

/**
 * Metric Recommendations Component Strings
 */
export const metricRecommendationsStrings = {
  // Component UI
  COMPONENT_TITLE: 'Recomendaciones Inteligentes',
  COMPONENT_DESCRIPTION: 'Basadas en el análisis de {count} áreas de mejora potencial',
  LOADING_TEXT: 'Analizando métricas para generar recomendaciones...',
  OPTIMIZED_TITLE: 'Sistema Optimizado',
  OPTIMIZED_DESCRIPTION: 'No se han detectado áreas de mejora significativas en este momento.',
  OPTIMIZED_MESSAGE: 'Las métricas actuales indican que su sistema de recomendación está funcionando de manera óptima. Continúe monitoreando las métricas para mantener este rendimiento.',
  RECOMMENDATION_COUNT: '{count} {count, plural, one {recomendación} other {recomendaciones}}',
  ACTIONS_TITLE: 'Acciones recomendadas:',

  // Category names
  CATEGORY_ACCURACY: 'Precisión y Relevancia',
  CATEGORY_DIVERSITY: 'Diversidad y Descubrimiento',
  CATEGORY_CONFIDENCE: 'Confianza y Fiabilidad',
  CATEGORY_PERFORMANCE: 'Rendimiento y Eficiencia',

  // Metric names
  METRIC_PRECISION: 'Precisión',
  METRIC_DIVERSITY: 'Diversidad',
  METRIC_CATALOG_COVERAGE: 'Cobertura del Catálogo',
  METRIC_NOVELTY: 'Novedad',
  METRIC_NDCG: 'NDCG',
  METRIC_SERENDIPITY: 'Serendipia',
  METRIC_AVG_CONFIDENCE: 'Confianza Promedio',
  METRIC_CONFIDENCE_CHANGE: 'Cambio en confianza',
  METRIC_TARGET: 'Meta: {value}{unit}',

  // Model types
  METRIC_MODEL_COLLABORATIVE: 'colaborativo',
  METRIC_MODEL_CONTENT: 'basado en contenido',
  METRIC_MODEL_HYBRID: 'híbrido',
  METRIC_MODEL_CONFIDENCE: 'Confianza {model}',

  // Confidence factors
  METRIC_FACTOR_USER_HISTORY: 'tamaño del historial de usuario',
  METRIC_FACTOR_ITEM_POPULARITY: 'popularidad del ítem',
  METRIC_FACTOR_CATEGORY_STRENGTH: 'fuerza de categoría',
  METRIC_FACTOR_MODEL_TYPE: 'tipo de modelo',
  METRIC_FACTOR: 'Factor {factor}',

  // Recommendation titles
  METRIC_REC_PRECISION_TITLE: 'Mejorar la precisión de las recomendaciones',
  METRIC_REC_DIVERSITY_TITLE: 'Aumentar la diversidad de recomendaciones',
  METRIC_REC_NOVELTY_TITLE: 'Incrementar la novedad de las recomendaciones',
  METRIC_REC_RANKING_TITLE: 'Mejorar la calidad del ranking de recomendaciones',
  METRIC_REC_SERENDIPITY_TITLE: 'Aumentar la serendipia en las recomendaciones',
  METRIC_REC_CONFIDENCE_TITLE: 'Aumentar la confianza en las recomendaciones',
  METRIC_REC_MODEL_TITLE: 'Mejorar el rendimiento del modelo {model}',
  METRIC_REC_CATEGORIES_TITLE: 'Mejorar recomendaciones en categorías de baja confianza',
  METRIC_REC_FACTOR_TITLE: 'Mejorar el factor de {factor}',
  METRIC_REC_TREND_TITLE: 'Abordar tendencia negativa en confianza',

  // Recommendation descriptions
  METRIC_REC_PRECISION_DESC: 'La precisión actual está por debajo del umbral recomendado del 50%. Considere ajustar los modelos para mejorar la relevancia de las recomendaciones.',
  METRIC_REC_DIVERSITY_DESC: 'Las recomendaciones actuales muestran poca diversidad, lo que puede llevar a una experiencia monótona para los usuarios.',
  METRIC_REC_NOVELTY_DESC: 'El sistema tiende a recomendar ítems muy populares, limitando el descubrimiento de nuevos productos.',
  METRIC_REC_RANKING_DESC: 'El NDCG actual indica que el orden de las recomendaciones podría no ser óptimo, afectando la experiencia del usuario.',
  METRIC_REC_SERENDIPITY_DESC: 'Las recomendaciones actuales podrían ser demasiado predecibles, limitando el descubrimiento de ítems inesperados pero relevantes.',
  METRIC_REC_CONFIDENCE_DESC: 'El nivel de confianza promedio está por debajo del umbral recomendado del 60%, lo que puede indicar incertidumbre en las predicciones.',
  METRIC_REC_MODEL_DESC: 'El modelo {model} muestra un nivel de confianza bajo, lo que reduce la efectividad general del sistema.',
  METRIC_REC_CATEGORIES_DESC: 'Algunas categorías de productos muestran niveles de confianza particularmente bajos, especialmente "{category}".',
  METRIC_REC_FACTOR_DESC: 'El factor de {factor} tiene una contribución baja a la confianza general, lo que indica un área de mejora.',
  METRIC_REC_TREND_DESC: 'La confianza promedio ha disminuido significativamente en los últimos días, lo que podría indicar un problema emergente.',

  // Recommendation actions - Precision
  METRIC_REC_PRECISION_ACTION_1: 'Ajustar los parámetros del modelo colaborativo para dar más peso a interacciones recientes',
  METRIC_REC_PRECISION_ACTION_2: 'Aumentar el tamaño del conjunto de entrenamiento con más datos de interacciones',
  METRIC_REC_PRECISION_ACTION_3: 'Implementar técnicas de filtrado para eliminar outliers en los datos de entrenamiento',

  // Recommendation actions - Diversity
  METRIC_REC_DIVERSITY_ACTION_1: 'Implementar un algoritmo de re-ranking para diversificar los resultados',
  METRIC_REC_DIVERSITY_ACTION_2: 'Ajustar los parámetros del modelo para reducir la concentración en ítems populares',
  METRIC_REC_DIVERSITY_ACTION_3: 'Introducir un factor de aleatoriedad controlada en las recomendaciones finales',
  METRIC_REC_DIVERSITY_ACTION_4: 'Considerar categorías menos representadas en las recomendaciones',

  // Recommendation actions - Novelty
  METRIC_REC_NOVELTY_ACTION_1: 'Ajustar el algoritmo para dar más peso a ítems menos populares',
  METRIC_REC_NOVELTY_ACTION_2: 'Implementar un factor de penalización para ítems extremadamente populares',
  METRIC_REC_NOVELTY_ACTION_3: 'Crear un segmento específico de "descubrimientos" con ítems de baja popularidad pero alta relevancia',
  METRIC_REC_NOVELTY_ACTION_4: 'Considerar técnicas de filtrado colaborativo basadas en vecindad para encontrar ítems nicho',

  // Recommendation actions - Ranking
  METRIC_REC_RANKING_ACTION_1: 'Implementar o mejorar algoritmos de Learning-to-Rank',
  METRIC_REC_RANKING_ACTION_2: 'Ajustar los factores de relevancia en el cálculo del ranking',
  METRIC_REC_RANKING_ACTION_3: 'Considerar señales adicionales como recencia o tendencia para el ranking',
  METRIC_REC_RANKING_ACTION_4: 'Experimentar con diferentes funciones de pérdida optimizadas para NDCG',

  // Recommendation actions - Serendipity
  METRIC_REC_SERENDIPITY_ACTION_1: 'Implementar un componente de serendipia que ocasionalmente introduzca ítems inesperados',
  METRIC_REC_SERENDIPITY_ACTION_2: 'Explorar conexiones no obvias entre preferencias de usuario y productos',
  METRIC_REC_SERENDIPITY_ACTION_3: 'Considerar técnicas de recomendación basadas en conocimiento para descubrir relaciones no evidentes',
  METRIC_REC_SERENDIPITY_ACTION_4: 'Experimentar con modelos de grafos para encontrar conexiones de segundo o tercer grado',

  // Recommendation actions - Confidence
  METRIC_REC_CONFIDENCE_ACTION_1: 'Recopilar más datos de interacciones para mejorar la base de predicciones',
  METRIC_REC_CONFIDENCE_ACTION_2: 'Ajustar los umbrales de confianza para filtrar recomendaciones de baja calidad',
  METRIC_REC_CONFIDENCE_ACTION_3: 'Implementar técnicas de ensemble para combinar múltiples modelos',
  METRIC_REC_CONFIDENCE_ACTION_4: 'Mejorar la calidad de los metadatos de productos para fortalecer el modelo basado en contenido',

  // Recommendation actions - Model Collaborative
  METRIC_REC_MODEL_COLLAB_ACTION_1: 'Ajustar los parámetros de similitud entre usuarios/ítems',
  METRIC_REC_MODEL_COLLAB_ACTION_2: 'Implementar técnicas de factorización matricial más avanzadas',
  METRIC_REC_MODEL_COLLAB_ACTION_3: 'Aumentar el número de vecinos considerados en el algoritmo KNN',
  METRIC_REC_MODEL_COLLAB_ACTION_4: 'Reducir el umbral de filtrado para interacciones mínimas',

  // Recommendation actions - Model Content
  METRIC_REC_MODEL_CONTENT_ACTION_1: 'Mejorar la calidad y cantidad de atributos de los productos',
  METRIC_REC_MODEL_CONTENT_ACTION_2: 'Implementar técnicas de procesamiento de lenguaje natural más avanzadas',
  METRIC_REC_MODEL_CONTENT_ACTION_3: 'Ajustar los pesos de los diferentes atributos en el cálculo de similitud',
  METRIC_REC_MODEL_CONTENT_ACTION_4: 'Considerar la incorporación de embeddings pre-entrenados para representar productos',

  // Recommendation actions - Model Hybrid
  METRIC_REC_MODEL_HYBRID_ACTION_1: 'Ajustar los pesos relativos de los modelos colaborativo y basado en contenido',
  METRIC_REC_MODEL_HYBRID_ACTION_2: 'Implementar un meta-modelo para seleccionar dinámicamente el mejor enfoque',
  METRIC_REC_MODEL_HYBRID_ACTION_3: 'Considerar factores contextuales adicionales en la combinación de modelos',
  METRIC_REC_MODEL_HYBRID_ACTION_4: 'Experimentar con diferentes estrategias de ensemble',

  // Recommendation actions - Categories
  METRIC_REC_CATEGORIES_ACTION_1: 'Recopilar más datos de interacciones para la categoría "{category}"',
  METRIC_REC_CATEGORIES_ACTION_2: 'Crear modelos específicos por categoría para las categorías problemáticas',
  METRIC_REC_CATEGORIES_ACTION_3: 'Mejorar los metadatos de productos en estas categorías',
  METRIC_REC_CATEGORIES_ACTION_4: 'Considerar reglas de negocio específicas para complementar el algoritmo',

  // Recommendation actions - Factor User History
  METRIC_REC_FACTOR_USER_HISTORY_ACTION_1: 'Implementar estrategias para aumentar la recopilación de interacciones de usuarios',
  METRIC_REC_FACTOR_USER_HISTORY_ACTION_2: 'Mejorar el onboarding para capturar preferencias iniciales',
  METRIC_REC_FACTOR_USER_HISTORY_ACTION_3: 'Considerar técnicas de cold-start para usuarios con poco historial',
  METRIC_REC_FACTOR_USER_HISTORY_ACTION_4: 'Implementar recomendaciones basadas en sesión para usuarios nuevos',

  // Recommendation actions - Factor Item Popularity
  METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_1: 'Balancear mejor las recomendaciones entre ítems populares y de nicho',
  METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_2: 'Implementar un sistema de boosting temporal para nuevos productos',
  METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_3: 'Crear segmentos de recomendación específicos para diferentes niveles de popularidad',
  METRIC_REC_FACTOR_ITEM_POPULARITY_ACTION_4: 'Mejorar la estrategia de exploración vs. explotación',

  // Recommendation actions - Factor Category Strength
  METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_1: 'Mejorar la taxonomía de categorías para capturar mejor las preferencias',
  METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_2: 'Implementar análisis de afinidad de categoría más sofisticado',
  METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_3: 'Considerar la jerarquía completa de categorías en el cálculo de afinidad',
  METRIC_REC_FACTOR_CATEGORY_STRENGTH_ACTION_4: 'Crear modelos específicos para categorías principales',

  // Recommendation actions - Factor Model Type
  METRIC_REC_FACTOR_MODEL_TYPE_ACTION_1: 'Experimentar con diferentes arquitecturas de modelos',
  METRIC_REC_FACTOR_MODEL_TYPE_ACTION_2: 'Implementar un sistema de selección dinámica de modelos basado en contexto',
  METRIC_REC_FACTOR_MODEL_TYPE_ACTION_3: 'Considerar modelos más avanzados como deep learning para recomendaciones',
  METRIC_REC_FACTOR_MODEL_TYPE_ACTION_4: 'Mejorar la estrategia de ensemble para combinar modelos',

  // Recommendation actions - Trend
  METRIC_REC_TREND_ACTION_1: 'Investigar cambios recientes en datos o modelos que podrían haber afectado la confianza',
  METRIC_REC_TREND_ACTION_2: 'Verificar la calidad de las interacciones recientes',
  METRIC_REC_TREND_ACTION_3: 'Considerar un rollback a una versión anterior del modelo si la tendencia persiste',
  METRIC_REC_TREND_ACTION_4: 'Implementar monitoreo en tiempo real para detectar cambios abruptos en métricas clave'
};

/**
 * Getting Started Checklist Component Strings
 */
export const checklistStrings = {
  // Component UI
  COMPONENT_TITLE: 'Primeros Pasos',
  COMPONENT_TITLE_HIGHLIGHTED: '¡Comienza Aquí!',
  COMPONENT_DESCRIPTION: 'Completa estas tareas para comenzar a usar Rayuela',
  COMPONENT_DESCRIPTION_HIGHLIGHTED: 'Sigue estos pasos para comenzar a usar tu nueva API Key',
  PROGRESS_LABEL: 'Progreso',
  PROGRESS_COUNT: '{completed}/{total} completados',
  LOADING_TEXT: 'Verificando progreso...',
  ERROR_TEXT: 'Error al verificar el progreso.',
  RETRY_TEXT: 'Reintentar',
  REFRESH_TOOLTIP: 'Actualizar progreso',
  LAST_UPDATED: 'Actualizado: {time}',
  LAST_UPDATED_TOOLTIP: 'Última verificación de progreso',
  AUTO_BADGE: 'Auto',
  AUTO_BADGE_TOOLTIP: 'Este paso se verifica automáticamente',
  VERIFY_BUTTON: 'Verificar',
  DOCS_LINK: 'Documentación',
  DOCS_LINK_TOOLTIP: 'Ver documentación detallada',
  GUIDE_LINK: 'Ver guía paso a paso',
  GO_TO_LINK: 'Ir a {destination}',

  // Checklist items
  ITEM_API_KEY_LABEL: 'Generar API Key',
  ITEM_API_KEY_DESC: 'Genera tu API Key para comenzar a usar la API',
  ITEM_API_KEY_TOOLTIP: 'Necesitas una API Key para autenticar todas tus solicitudes a la API de Rayuela',

  ITEM_DOCS_LABEL: 'Consultar Documentación',
  ITEM_DOCS_DESC: 'Lee la guía de inicio rápido para aprender a usar la API',
  ITEM_DOCS_TOOLTIP: 'Familiarízate con los conceptos básicos y la estructura de la API',

  ITEM_DATA_LABEL: 'Enviar Datos de Ejemplo',
  ITEM_DATA_DESC: 'Carga algunos datos para entrenar tu modelo',
  ITEM_DATA_TOOLTIP: 'Aprende a cargar datos de productos, usuarios e interacciones para entrenar tu modelo',

  ITEM_RECOMMENDATION_LABEL: 'Primera Recomendación',
  ITEM_RECOMMENDATION_DESC: 'Realiza tu primera llamada de recomendación',
  ITEM_RECOMMENDATION_TOOLTIP: 'Aprende a solicitar recomendaciones personalizadas para tus usuarios'
};

/**
 * Format a string with variables
 *
 * @param template The string template with placeholders like {name}
 * @param variables Object with values for the placeholders
 * @returns Formatted string
 */
export function formatString(template: string, variables: Record<string, any>): string {
  return template.replace(/{([^}]+)}/g, (match, key) => {
    // Check for plural format: {count, plural, one {singular} other {plural}}
    const pluralMatch = key.match(/^([^,]+),\s*plural,\s*one\s*{([^}]+)}\s*other\s*{([^}]+)}$/);
    if (pluralMatch) {
      const countKey = pluralMatch[1].trim();
      const singularForm = pluralMatch[2].trim();
      const pluralForm = pluralMatch[3].trim();

      const count = variables[countKey];
      return count === 1 ? singularForm : pluralForm;
    }

    // Regular variable replacement
    return variables[key] !== undefined ? variables[key] : match;
  });
}
