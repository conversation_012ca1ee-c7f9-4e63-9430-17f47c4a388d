"""
Celery tasks for storage metering.
"""
from celery import shared_task
from sqlalchemy import select, func, update, text
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from typing import Dict, Any, List, Optional
import json
from datetime import datetime, timezone

from src.db.models import (
    Account, 
    Subscription, 
    Product, 
    Interaction, 
    EndUser,
    AccountUsageMetrics
)
from src.db.session import get_db
from src.core.redis_utils import get_redis
from src.utils.base_logger import log_info, log_error, log_warning


@shared_task(name="measure_storage_usage", queue="maintenance")
def measure_storage_usage():
    """
    Measure storage usage for all accounts and store it in Redis.
    This task should be scheduled to run daily.
    """
    try:
        # Run the async function in the event loop
        asyncio.run(_measure_storage_usage_async())
        return {"status": "success", "message": "Storage usage measured successfully"}
    except Exception as e:
        log_error(f"Error measuring storage usage: {str(e)}")
        return {"status": "error", "message": str(e)}


async def _measure_storage_usage_async():
    """Async implementation of the measure_storage_usage task."""
    # Get DB session
    db_generator = get_db()
    db = await anext(db_generator)
    
    # Get Redis client
    redis = await get_redis()
    
    try:
        # Get all active accounts
        stmt = select(Account).where(Account.is_active == True, Account.deleted_at.is_(None))
        result = await db.execute(stmt)
        accounts = result.scalars().all()
        
        log_info(f"Measuring storage usage for {len(accounts)} accounts")
        
        # Get all partition sizes in a single query
        partition_sizes = await _get_all_partition_sizes(db)
        
        # Process each account
        for account in accounts:
            try:
                # Calculate storage usage for the account using partition sizes
                storage_usage = await _calculate_account_storage_from_partitions(db, account.account_id, partition_sizes)
                
                # Store in Redis with a TTL of 24 hours (86400 seconds)
                redis_key = f"usage:{account.account_id}:storage_gb"
                storage_gb = storage_usage["total_bytes"] / (1024 * 1024 * 1024)  # Convert bytes to GB
                await redis.setex(redis_key, 86400, str(storage_gb))
                
                # Store detailed usage in Redis for debugging/analytics
                details_key = f"usage:{account.account_id}:storage_details"
                await redis.setex(details_key, 86400, json.dumps(storage_usage))
                
                # Update the subscription.storage_used field
                await _update_subscription_storage(db, account.account_id, storage_usage)
                
                # Update the account_usage_metrics.storage_used field
                await _update_account_metrics(db, account.account_id, storage_usage)
                
                log_info(f"Storage usage for account {account.account_id}: {storage_gb:.2f} GB")
            except Exception as e:
                log_error(f"Error processing account {account.account_id}: {str(e)}")
                continue
        
        log_info("Storage usage measurement completed successfully")
    except Exception as e:
        log_error(f"Error in _measure_storage_usage_async: {str(e)}")
        raise
    finally:
        await db.close()


async def _get_all_partition_sizes(db: AsyncSession) -> Dict[str, int]:
    """
    Get sizes of all partitions in a single query.
    
    Args:
        db: Database session
        
    Returns:
        Dictionary mapping partition names to their sizes in bytes
    """
    try:
        # Query to get all partition sizes
        query = text("""
            SELECT 
                tablename,
                pg_total_relation_size(tablename) as size_bytes
            FROM pg_tables
            WHERE tablename LIKE 'products_p%'
               OR tablename LIKE 'end_users_p%'
               OR tablename LIKE 'interactions_p%'
               OR tablename LIKE 'artifact_metadata_p%'
        """)
        
        result = await db.execute(query)
        rows = result.fetchall()
        
        # Create a dictionary mapping partition names to sizes
        return {row[0]: row[1] for row in rows}
    except Exception as e:
        log_error(f"Error getting partition sizes: {str(e)}")
        return {}


async def _calculate_account_storage_from_partitions(
    db: AsyncSession, 
    account_id: int,
    partition_sizes: Dict[str, int]
) -> Dict[str, Any]:
    """
    Calculate storage usage for an account using pre-fetched partition sizes.
    
    Args:
        db: Database session
        account_id: Account ID
        partition_sizes: Dictionary of partition sizes
        
    Returns:
        Dictionary with storage usage details and total
    """
    storage_details = {}
    total_bytes = 0
    
    # Helper function to get partition size
    def get_partition_size(table_prefix: str) -> int:
        partition_name = f"{table_prefix}_p{account_id}"
        return partition_sizes.get(partition_name, 0)
    
    # 1. Products table
    products_size = get_partition_size("products")
    storage_details["products_bytes"] = products_size
    total_bytes += products_size
    
    # 2. End Users table
    end_users_size = get_partition_size("end_users")
    storage_details["end_users_bytes"] = end_users_size
    total_bytes += end_users_size
    
    # 3. Interactions table
    interactions_size = get_partition_size("interactions")
    storage_details["interactions_bytes"] = interactions_size
    total_bytes += interactions_size
    
    # 4. Artifact Metadata table (models)
    artifacts_size = get_partition_size("artifact_metadata")
    storage_details["artifacts_bytes"] = artifacts_size
    total_bytes += artifacts_size
    
    # Add total to the details
    storage_details["total_bytes"] = total_bytes
    storage_details["total_mb"] = total_bytes / (1024 * 1024)
    storage_details["total_gb"] = total_bytes / (1024 * 1024 * 1024)
    storage_details["measured_at"] = datetime.now(timezone.utc).isoformat()
    
    return storage_details


async def _update_subscription_storage(db: AsyncSession, account_id: int, storage_details: Dict[str, Any]) -> None:
    """
    Update the storage_used field in the subscription.
    
    Args:
        db: Database session
        account_id: Account ID
        storage_details: Storage usage details
    """
    try:
        # Update subscription
        stmt = (
            update(Subscription)
            .where(Subscription.account_id == account_id)
            .values(storage_used=storage_details["total_bytes"])
        )
        await db.execute(stmt)
        await db.commit()
    except Exception as e:
        log_error(f"Error updating subscription storage: {str(e)}")
        await db.rollback()


async def _update_account_metrics(db: AsyncSession, account_id: int, storage_details: Dict[str, Any]) -> None:
    """
    Update the storage_used field in the account_usage_metrics.
    
    Args:
        db: Database session
        account_id: Account ID
        storage_details: Storage usage details
    """
    try:
        # Check if metrics exist
        stmt = select(AccountUsageMetrics).where(
            AccountUsageMetrics.account_id == account_id
        )
        result = await db.execute(stmt)
        metrics = result.scalar_one_or_none()
        
        if metrics:
            # Update existing metrics
            stmt = (
                update(AccountUsageMetrics)
                .where(AccountUsageMetrics.account_id == account_id)
                .values(
                    storage_used=storage_details["total_bytes"],
                    updated_at=datetime.now(timezone.utc)
                )
            )
            await db.execute(stmt)
        else:
            # Create new metrics
            metrics = AccountUsageMetrics(
                account_id=account_id,
                storage_used=storage_details["total_bytes"],
                api_calls_count=0
            )
            db.add(metrics)
        
        await db.commit()
    except Exception as e:
        log_error(f"Error updating account metrics: {str(e)}")
        await db.rollback()


@shared_task(name="get_storage_usage", queue="default")
def get_storage_usage(account_id: int) -> Dict[str, Any]:
    """
    Get storage usage for an account from Redis.
    If not available in Redis, calculate it on the fly.
    
    Args:
        account_id: Account ID
        
    Returns:
        Dictionary with storage usage information
    """
    try:
        # Run the async function in the event loop
        return asyncio.run(_get_storage_usage_async(account_id))
    except Exception as e:
        log_error(f"Error getting storage usage: {str(e)}")
        return {"status": "error", "message": str(e)}


async def _get_storage_usage_async(account_id: int) -> Dict[str, Any]:
    """Async implementation of the get_storage_usage task."""
    # Get Redis client
    redis = await get_redis()
    
    try:
        # Try to get from Redis first
        redis_key = f"usage:{account_id}:storage_gb"
        storage_gb = await redis.get(redis_key)
        
        if storage_gb is not None:
            # Convert from string to float
            storage_gb = float(storage_gb)
            
            # Get details if available
            details_key = f"usage:{account_id}:storage_details"
            details_json = await redis.get(details_key)
            details = json.loads(details_json) if details_json else None
            
            return {
                "account_id": account_id,
                "storage_gb": storage_gb,
                "storage_bytes": int(storage_gb * 1024 * 1024 * 1024),
                "details": details,
                "source": "redis_cache"
            }
        else:
            # Not in Redis, calculate on the fly
            log_warning(f"Storage usage for account {account_id} not found in Redis, calculating on the fly")
            
            # Get DB session
            db_generator = get_db()
            db = await anext(db_generator)
            
            try:
                # Get all partition sizes
                partition_sizes = await _get_all_partition_sizes(db)
                
                # Calculate storage usage
                storage_details = await _calculate_account_storage_from_partitions(db, account_id, partition_sizes)
                
                # Store in Redis with a TTL of 24 hours
                storage_gb = storage_details["total_gb"]
                await redis.setex(redis_key, 86400, str(storage_gb))
                
                # Store details
                details_key = f"usage:{account_id}:storage_details"
                await redis.setex(details_key, 86400, json.dumps(storage_details))
                
                return {
                    "account_id": account_id,
                    "storage_gb": storage_gb,
                    "storage_bytes": storage_details["total_bytes"],
                    "details": storage_details,
                    "source": "calculated"
                }
            finally:
                await db.close()
    except Exception as e:
        log_error(f"Error in _get_storage_usage_async: {str(e)}")
        raise
