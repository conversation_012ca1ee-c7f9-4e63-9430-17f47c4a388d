"""
Utilidades para la integración con Mercado Pago.
"""

import hmac
import hashlib
from typing import Dict, Op<PERSON>, Tuple
from fastapi import Request, HTTPException, status
from src.core.config import settings
from src.utils.base_logger import log_info, log_error, log_warning


async def verify_mercadopago_signature(request: Request) -> bool:
    """
    Verifica la firma de un webhook de Mercado Pago.
    
    Args:
        request: Objeto Request de FastAPI
        
    Returns:
        bool: True si la firma es válida, False en caso contrario
        
    Raises:
        HTTPException: Si la firma no es válida o no se puede verificar
    """
    try:
        # Obtener la firma del header
        x_signature = request.headers.get("x-signature")
        if not x_signature:
            log_warning("No x-signature header found in Mercado Pago webhook")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing x-signature header"
            )
            
        # Obtener el request-id del header
        x_request_id = request.headers.get("x-request-id")
        if not x_request_id:
            log_warning("No x-request-id header found in Mercado Pago webhook")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Missing x-request-id header"
            )
            
        # Extraer el timestamp y la firma del header x-signature
        ts, signature = extract_signature_parts(x_signature)
        if not ts or not signature:
            log_warning("Invalid x-signature format in Mercado Pago webhook")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid signature format"
            )
            
        # Obtener el data.id de los query params
        data_id = request.query_params.get("data.id")
        if not data_id:
            log_warning("No data.id found in Mercado Pago webhook query params")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing data.id parameter"
            )
            
        # Generar el template para la verificación
        template = f"id:{data_id};request-id:{x_request_id};ts:{ts};"
        
        # Verificar la firma
        secret = settings.MERCADOPAGO_WEBHOOK_SECRET
        if not secret:
            log_error("MERCADOPAGO_WEBHOOK_SECRET not configured")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Webhook secret not configured"
            )
            
        # Calcular el HMAC
        calculated_signature = hmac.new(
            secret.encode(),
            template.encode(),
            hashlib.sha256
        ).hexdigest()
        
        # Comparar las firmas
        if calculated_signature != signature:
            log_warning(f"Invalid Mercado Pago signature. Expected: {calculated_signature}, Got: {signature}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid signature"
            )
            
        log_info("Mercado Pago webhook signature verified successfully")
        return True
        
    except HTTPException:
        # Re-lanzar las excepciones HTTP
        raise
    except Exception as e:
        log_error(f"Error verifying Mercado Pago signature: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error verifying signature"
        )


def extract_signature_parts(x_signature: str) -> Tuple[Optional[str], Optional[str]]:
    """
    Extrae el timestamp y la firma del header x-signature.
    
    Args:
        x_signature: Valor del header x-signature
        
    Returns:
        Tuple[Optional[str], Optional[str]]: Timestamp y firma
    """
    try:
        # Separar por comas
        parts = x_signature.split(',')
        
        # Inicializar variables
        ts = None
        signature = None
        
        # Iterar sobre las partes
        for part in parts:
            # Dividir cada parte en clave y valor
            key_value = part.split('=', 1)
            if len(key_value) == 2:
                key = key_value[0].strip()
                value = key_value[1].strip()
                
                if key == "ts":
                    ts = value
                elif key == "v1":
                    signature = value
                    
        return ts, signature
    except Exception as e:
        log_error(f"Error extracting signature parts: {str(e)}")
        return None, None
