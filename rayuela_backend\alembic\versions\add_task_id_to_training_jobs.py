"""add task_id to training_jobs

Revision ID: add_task_id_to_training_jobs
Revises: 7fdd0755dc88
Create Date: 2025-04-10 16:40:00.000000

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = "add_task_id_to_training_jobs"
down_revision = "7fdd0755dc88"
branch_labels = None
depends_on = None


def upgrade():
    # Add task_id column to training_jobs table
    op.add_column("training_jobs", sa.Column("task_id", sa.String(255), nullable=True))
    op.add_column("training_jobs", sa.Column("parameters", sa.JSON(), nullable=True))
    op.add_column("training_jobs", sa.Column("metrics", sa.JSON(), nullable=True))
    op.add_column(
        "training_jobs",
        sa.Column(
            "created_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
    )

    # Make artifact_metadata_id nullable
    op.alter_column("training_jobs", "artifact_metadata_id", nullable=True)

    # Add index for task_id
    op.create_index(
        "idx_training_jobs_task_id", "training_jobs", ["task_id"], unique=False
    )


def downgrade():
    # Remove task_id column from training_jobs table
    op.drop_index("idx_training_jobs_task_id", table_name="training_jobs")
    op.drop_column("training_jobs", "task_id")
    op.drop_column("training_jobs", "parameters")
    op.drop_column("training_jobs", "metrics")
    op.drop_column("training_jobs", "created_at")

    # Make artifact_metadata_id non-nullable again
    op.alter_column("training_jobs", "artifact_metadata_id", nullable=False)
