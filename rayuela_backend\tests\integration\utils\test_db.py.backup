"""
Utilities for setting up and tearing down test databases.
"""
import asyncio
import os
from typing import Dict, Any, List, Optional, AsyncGenerator
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import NullPool

from src.db.base import Base
from src.db.session import get_db
from src.db.models import (
    Account,
    SystemUser,
    Product,
    EndUser,
    Interaction,
    TrainingJob,
    BatchIngestionJob,
    Role,
    Permission,
    role_permissions,
    SystemUserRole,
    AccountUsageMetrics,
    Subscription
)
from src.db.enums import (
    PermissionType,
    RoleType,
    SubscriptionPlan,
    TrainingJobStatus,
    BatchIngestionJobStatus
)
from src.core.config import settings


# Test database URL
TEST_DATABASE_URL = settings.database_url.replace(
    settings.POSTGRES_DB, f"{settings.POSTGRES_DB}_test"
)

# Create async engine for tests
test_engine = create_async_engine(
    TEST_DATABASE_URL,
    echo=False,
    future=True,
    poolclass=NullPool
)

# Create async session factory
TestingSessionLocal = sessionmaker(
    test_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autocommit=False,
    autoflush=False
)


async def create_test_database():
    """Create test database and tables."""
    # Create tables
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
        await conn.run_sync(Base.metadata.create_all)


async def drop_test_database():
    """Drop test database tables."""
    async with test_engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)


@pytest_asyncio.fixture(scope="function")
async def db_session() -> AsyncGenerator[AsyncSession, None]:
    """
    Create a fresh database session for each test.
    
    This fixture creates a new database session for each test and
    rolls back any changes made during the test.
    """
    # Create test database
    await create_test_database()
    
    # Create session
    async with TestingSessionLocal() as session:
        yield session
    
    # Drop test database
    await drop_test_database()


@pytest.fixture(scope="function")
def override_get_db(db_session: AsyncSession):
    """
    Override the get_db dependency to use the test database session.
    
    This fixture overrides the get_db dependency to use the test database
    session instead of the production database session.
    """
    async def _override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    return _override_get_db


@pytest_asyncio.fixture(scope="function")
async def test_account(db_session: AsyncSession) -> Account:
    """
    Create a test account.
    
    This fixture creates a test account with a subscription and usage metrics.
    """
    # Create account
    account = Account(
        name="Test Account",
        is_active=True
    )
    db_session.add(account)
    await db_session.flush()
    
    # Create subscription
    subscription = Subscription(
        account_id=account.id,
        plan_type=SubscriptionPlan.PREMIUM,
        is_active=True,
        stripe_customer_id="cus_test",
        stripe_subscription_id="sub_test"
    )
    db_session.add(subscription)
    
    # Create usage metrics
    usage_metrics = AccountUsageMetrics(
        account_id=account.id,
        api_calls_count=0,
        storage_used=0
    )
    db_session.add(usage_metrics)
    
    await db_session.commit()
    await db_session.refresh(account)
    
    return account


@pytest_asyncio.fixture(scope="function")
async def test_admin_user(db_session: AsyncSession, test_account: Account) -> SystemUser:
    """
    Create a test admin user.
    
    This fixture creates a test admin user with admin role and permissions.
    """
    # Create admin role
    admin_role = Role(
        name=RoleType.ADMIN,
        account_id=test_account.id,
        description="Administrator role"
    )
    db_session.add(admin_role)
    await db_session.flush()
    
    # Create permissions
    permissions = []
    for perm_type in PermissionType:
        permission = Permission(
            name=perm_type,
            description=f"Permission to {perm_type.value}"
        )
        permissions.append(permission)
    
    db_session.add_all(permissions)
    await db_session.flush()
    
    # Assign permissions to role
    for permission in permissions:
        # Insertar directamente en la tabla de asociación
        db_session.execute(role_permissions.insert().values(
            role_id=admin_role.id,
            permission_id=permission.id,
            account_id=test_account.id
        ))
    
    # Create admin user
    admin_user = SystemUser(
        email="<EMAIL>",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # password: password
        is_active=True,
        account_id=test_account.id,
        is_superuser=True
    )
    db_session.add(admin_user)
    await db_session.flush()
    
    # Assign role to user
    user_role = SystemUserRole(
        user_id=admin_user.id,
        role_id=admin_role.id
    )
    db_session.add(user_role)
    
    await db_session.commit()
    await db_session.refresh(admin_user)
    
    return admin_user


@pytest_asyncio.fixture(scope="function")
async def test_regular_user(db_session: AsyncSession, test_account: Account) -> SystemUser:
    """
    Create a test regular user.
    
    This fixture creates a test regular user with user role and limited permissions.
    """
    # Create user role
    user_role = Role(
        name=RoleType.USER,
        account_id=test_account.id,
        description="Regular user role"
    )
    db_session.add(user_role)
    await db_session.flush()
    
    # Create permissions
    read_permissions = [
        Permission(name=PermissionType.READ_PRODUCTS, description="Permission to read products"),
        Permission(name=PermissionType.READ_USERS, description="Permission to read users"),
        Permission(name=PermissionType.READ_INTERACTIONS, description="Permission to read interactions")
    ]
    
    db_session.add_all(read_permissions)
    await db_session.flush()
    
    # Assign permissions to role
    for permission in read_permissions:
        # Insertar directamente en la tabla de asociación
        db_session.execute(role_permissions.insert().values(
            role_id=user_role.id,
            permission_id=permission.id,
            account_id=test_account.id
        ))
    
    # Create regular user
    regular_user = SystemUser(
        email="<EMAIL>",
        hashed_password="$2b$12$EixZaYVK1fsbw1ZfbX3OXePaWxn96p36WQoeG6Lruj3vjPGga31lW",  # password: password
        is_active=True,
        account_id=test_account.id,
        is_superuser=False
    )
    db_session.add(regular_user)
    await db_session.flush()
    
    # Assign role to user
    user_role_assignment = SystemUserRole(
        user_id=regular_user.id,
        role_id=user_role.id
    )
    db_session.add(user_role_assignment)
    
    await db_session.commit()
    await db_session.refresh(regular_user)
    
    return regular_user


@pytest_asyncio.fixture(scope="function")
async def test_products(db_session: AsyncSession, test_account: Account) -> List[Product]:
    """
    Create test products.
    
    This fixture creates a list of test products for the test account.
    """
    products = []
    for i in range(10):
        product = Product(
            account_id=test_account.id,
            external_id=f"product_{i}",
            name=f"Test Product {i}",
            description=f"Description for Test Product {i}",
            category=f"Category {i % 3}",
            price=10.0 + i,
            metadata={
                "color": ["red", "blue", "green"][i % 3],
                "size": ["small", "medium", "large"][i % 3],
                "tags": [f"tag_{j}" for j in range(i % 5)]
            }
        )
        products.append(product)
    
    db_session.add_all(products)
    await db_session.commit()
    
    for product in products:
        await db_session.refresh(product)
    
    return products


@pytest_asyncio.fixture(scope="function")
async def test_end_users(db_session: AsyncSession, test_account: Account) -> List[EndUser]:
    """
    Create test end users.
    
    This fixture creates a list of test end users for the test account.
    """
    end_users = []
    for i in range(20):
        end_user = EndUser(
            account_id=test_account.id,
            external_id=f"user_{i}",
            user_metadata={
                "age": 20 + (i % 50),
                "gender": ["male", "female", "other"][i % 3],
                "interests": [f"interest_{j}" for j in range(i % 5)]
            }
        )
        end_users.append(end_user)
    
    db_session.add_all(end_users)
    await db_session.commit()
    
    for end_user in end_users:
        await db_session.refresh(end_user)
    
    return end_users


@pytest_asyncio.fixture(scope="function")
async def test_interactions(
    db_session: AsyncSession,
    test_account: Account,
    test_products: List[Product],
    test_end_users: List[EndUser]
) -> List[Interaction]:
    """
    Create test interactions.
    
    This fixture creates a list of test interactions between end users and products.
    """
    interactions = []
    for i, end_user in enumerate(test_end_users):
        for j in range(5):  # Each user has 5 interactions
            product_index = (i + j) % len(test_products)
            interaction = Interaction(
                account_id=test_account.id,
                end_user_id=end_user.id,
                product_id=test_products[product_index].id,
                interaction_type="view" if j % 3 == 0 else "purchase" if j % 3 == 1 else "like",
                rating=float(1 + (i + j) % 5),
                metadata={
                    "timestamp": f"2023-01-{1 + (i + j) % 28:02d}T{(i + j) % 24:02d}:{(i + j) % 60:02d}:00Z",
                    "device": ["mobile", "desktop", "tablet"][j % 3],
                    "location": ["home", "work", "other"][(i + j) % 3]
                }
            )
            interactions.append(interaction)
    
    db_session.add_all(interactions)
    await db_session.commit()
    
    for interaction in interactions:
        await db_session.refresh(interaction)
    
    return interactions


@pytest_asyncio.fixture(scope="function")
async def test_training_job(db_session: AsyncSession, test_account: Account) -> TrainingJob:
    """
    Create a test training job.
    
    This fixture creates a test training job for the test account.
    """
    training_job = TrainingJob(
        account_id=test_account.id,
        status=TrainingJobStatus.PENDING,
        parameters={
            "model_type": "collaborative",
            "factors": 10,
            "iterations": 20,
            "regularization": 0.1
        }
    )
    
    db_session.add(training_job)
    await db_session.commit()
    await db_session.refresh(training_job)
    
    return training_job


@pytest_asyncio.fixture(scope="function")
async def test_batch_job(db_session: AsyncSession, test_account: Account) -> BatchIngestionJob:
    """
    Create a test batch ingestion job.
    
    This fixture creates a test batch ingestion job for the test account.
    """
    batch_job = BatchIngestionJob(
        account_id=test_account.id,
        status=BatchIngestionJobStatus.PENDING,
        parameters={
            "total_users": 10,
            "total_products": 20,
            "total_interactions": 50,
            "estimated_size": 1024
        }
    )
    
    db_session.add(batch_job)
    await db_session.commit()
    await db_session.refresh(batch_job)
    
    return batch_job
