// Simple script to create a placeholder OG image
// This is a basic implementation - in production you might want to use a proper image generation library

const fs = require('fs');
const path = require('path');

// Create a simple HTML canvas-based image generator
const html = `
<!DOCTYPE html>
<html>
<head>
    <style>
        body { margin: 0; padding: 0; font-family: Arial, sans-serif; }
        .og-image {
            width: 1200px;
            height: 630px;
            background: linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            padding: 80px;
            box-sizing: border-box;
            color: white;
            position: relative;
            overflow: hidden;
        }
        .og-image::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: 
                linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px),
                linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 40px 40px;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }
        .title {
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 10px;
            z-index: 1;
        }
        .subtitle {
            font-size: 24px;
            margin-bottom: 40px;
            opacity: 0.9;
            z-index: 1;
        }
        .description {
            font-size: 18px;
            opacity: 0.8;
            line-height: 1.4;
            margin-bottom: 40px;
            z-index: 1;
        }
        .features {
            display: flex;
            gap: 40px;
            z-index: 1;
        }
        .feature {
            display: flex;
            align-items: center;
            font-size: 16px;
        }
        .feature::before {
            content: '•';
            margin-right: 10px;
            font-size: 20px;
        }
        .decorative {
            position: absolute;
            right: 100px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0.1;
            font-size: 200px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="og-image">
        <div class="logo">R</div>
        <div class="title">Rayuela.ai</div>
        <div class="subtitle">Recommendation System as a Service</div>
        <div class="description">
            Sistemas de recomendación avanzados para tu negocio,<br>
            sin la complejidad de construirlos desde cero.
        </div>
        <div class="features">
            <div class="feature">API-First</div>
            <div class="feature">Machine Learning</div>
            <div class="feature">Escalable</div>
        </div>
        <div class="decorative">API</div>
    </div>
</body>
</html>
`;

// Save the HTML file for manual conversion
const outputPath = path.join(__dirname, '../../public/og-image-template.html');
fs.writeFileSync(outputPath, html);

console.log('OG image template created at:', outputPath);
console.log('To generate PNG:');
console.log('1. Open the HTML file in a browser');
console.log('2. Take a screenshot or use a tool like Puppeteer');
console.log('3. Save as og-image.png in the public folder');

// Note: For production, you might want to use:
// - Puppeteer for automated screenshot generation
// - Canvas API for programmatic image generation
// - External services like Vercel OG Image Generation
