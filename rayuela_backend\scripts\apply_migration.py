"""
Script para aplicar la migración directamente a la base de datos.
"""
import asyncio
from sqlalchemy import <PERSON>umn, JSON
from sqlalchemy.ext.asyncio import create_async_engine
from src.core.config import settings
from src.db.models import Interaction

async def apply_migration():
    """Aplica la migración directamente a la base de datos."""
    # Crear conexión a la base de datos
    engine = create_async_engine(settings.SQLALCHEMY_DATABASE_URI)
    
    # Añadir la columna recommendation_metadata a la tabla interactions
    async with engine.begin() as conn:
        try:
            # Verificar si la columna ya existe
            result = await conn.execute(
                "SELECT column_name FROM information_schema.columns "
                "WHERE table_name = 'interactions' AND column_name = 'recommendation_metadata'"
            )
            if result.rowcount == 0:
                # La columna no existe, añadirla
                await conn.execute(
                    "ALTER TABLE interactions ADD COLUMN recommendation_metadata JSONB"
                )
                print("Columna recommendation_metadata añadida correctamente.")
            else:
                print("La columna recommendation_metadata ya existe.")
        except Exception as e:
            print(f"Error al aplicar la migración: {str(e)}")
    
    await engine.dispose()

if __name__ == "__main__":
    asyncio.run(apply_migration())
