#!/usr/bin/env python3
"""
Script de validación para la migración de tipos de ID.

Verifica:
1. Estado actual de los tipos de datos en la base de datos
2. Consistencia de los modelos SQLAlchemy
3. Existencia de datos en las tablas
4. Posibles conflictos de claves foráneas

Ejecutar antes de la migración para identificar problemas potenciales.
"""

import sys
import os
import asyncio
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import inspect, text, create_engine
from sqlalchemy.engine import reflection
from src.core.config import settings
from src.db.models import (
    EndUser, Product, Order, OrderItem, 
    Interaction, Recommendation, Search, Account
)

def get_sync_engine():
    """Crear un motor síncrono para las validaciones."""
    # Convertir la URL asíncrona a síncrona
    sync_url = settings.database_url.replace("postgresql+asyncpg://", "postgresql://")
    return create_engine(sync_url)

def check_table_exists(inspector, table_name):
    """Verificar si una tabla existe."""
    tables = inspector.get_table_names()
    return table_name in tables

def check_column_type(inspector, table_name, column_name):
    """Obtener el tipo de dato de una columna."""
    if not check_table_exists(inspector, table_name):
        return None
    
    columns = inspector.get_columns(table_name)
    for col in columns:
        if col['name'] == column_name:
            return str(col['type'])
    return None

def check_foreign_keys(inspector, table_name):
    """Obtener las claves foráneas de una tabla."""
    if not check_table_exists(inspector, table_name):
        return []
    
    return inspector.get_foreign_keys(table_name)

def count_records(engine, table_name):
    """Contar registros en una tabla."""
    try:
        with engine.connect() as conn:
            result = conn.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
            return result.scalar()
    except Exception as e:
        print(f"  ❌ Error contando registros en {table_name}: {e}")
        return None

def main():
    print("🔍 Validación de Migración de Tipos de ID")
    print("=" * 50)
    
    # Obtener el motor de base de datos
    try:
        engine = get_sync_engine()
        inspector = inspect(engine)
        print("✅ Conexión a la base de datos establecida")
    except Exception as e:
        print(f"❌ Error conectando a la base de datos: {e}")
        return 1
    
    print("\n📊 Estado Actual de las Tablas:")
    print("-" * 30)
    
    # Verificar existencia y tipos de datos actuales
    tables_to_check = {
        'end_users': [('user_id', 'Primary Key'), ('account_id', 'Foreign Key')],
        'products': [('product_id', 'Primary Key'), ('account_id', 'Foreign Key')],
        'orders': [('user_id', 'Foreign Key'), ('account_id', 'Partition Key')],
        'order_items': [('product_id', 'Foreign Key'), ('account_id', 'Partition Key')],
        'interactions': [('end_user_id', 'Foreign Key'), ('product_id', 'Foreign Key'), ('account_id', 'Partition Key')],
        'recommendations': [('end_user_id', 'Foreign Key'), ('product_id', 'Foreign Key'), ('account_id', 'Partition Key')],
        'searches': [('end_user_id', 'Foreign Key'), ('account_id', 'Partition Key')],
    }
    
    for table_name, columns in tables_to_check.items():
        print(f"\n🔍 Tabla: {table_name}")
        
        if not check_table_exists(inspector, table_name):
            print(f"  ❌ Tabla {table_name} no existe")
            continue
        
        # Contar registros
        count = count_records(engine, table_name)
        if count is not None:
            print(f"  📈 Registros: {count:,}")
        
        # Verificar tipos de columnas
        for column_name, column_type in columns:
            data_type = check_column_type(inspector, table_name, column_name)
            if data_type:
                print(f"  📋 {column_name} ({column_type}): {data_type}")
            else:
                print(f"  ❌ Columna {column_name} no encontrada")
    
    print("\n🔗 Estado de las Claves Foráneas:")
    print("-" * 35)
    
    # Verificar claves foráneas existentes
    fk_tables = ['orders', 'order_items', 'interactions', 'recommendations', 'searches']
    
    for table_name in fk_tables:
        if not check_table_exists(inspector, table_name):
            continue
            
        print(f"\n🔍 Tabla: {table_name}")
        fks = check_foreign_keys(inspector, table_name)
        
        if not fks:
            print("  ⚠️  Sin claves foráneas definidas")
            continue
        
        for fk in fks:
            print(f"  🔗 {fk['name']}: {fk['constrained_columns']} -> {fk['referred_table']}.{fk['referred_columns']}")
    
    print("\n⚠️  Identificación de Inconsistencias:")
    print("-" * 40)
    
    # Verificar inconsistencias conocidas
    inconsistencies = []
    
    # Check EndUser.user_id vs referencias
    end_user_id_type = check_column_type(inspector, 'end_users', 'user_id')
    order_user_id_type = check_column_type(inspector, 'orders', 'user_id')
    interaction_end_user_id_type = check_column_type(inspector, 'interactions', 'end_user_id')
    
    if end_user_id_type and order_user_id_type:
        if end_user_id_type != order_user_id_type:
            inconsistencies.append(f"end_users.user_id ({end_user_id_type}) vs orders.user_id ({order_user_id_type})")
    
    if end_user_id_type and interaction_end_user_id_type:
        if end_user_id_type != interaction_end_user_id_type:
            inconsistencies.append(f"end_users.user_id ({end_user_id_type}) vs interactions.end_user_id ({interaction_end_user_id_type})")
    
    # Check Product.product_id vs referencias
    product_id_type = check_column_type(inspector, 'products', 'product_id')
    order_item_product_id_type = check_column_type(inspector, 'order_items', 'product_id')
    interaction_product_id_type = check_column_type(inspector, 'interactions', 'product_id')
    
    if product_id_type and order_item_product_id_type:
        if product_id_type != order_item_product_id_type:
            inconsistencies.append(f"products.product_id ({product_id_type}) vs order_items.product_id ({order_item_product_id_type})")
    
    if product_id_type and interaction_product_id_type:
        if product_id_type != interaction_product_id_type:
            inconsistencies.append(f"products.product_id ({product_id_type}) vs interactions.product_id ({interaction_product_id_type})")
    
    if inconsistencies:
        print("❌ Inconsistencias detectadas:")
        for inconsistency in inconsistencies:
            print(f"  • {inconsistency}")
    else:
        print("✅ No se detectaron inconsistencias de tipos de datos")
    
    print("\n🧪 Verificación de Modelos SQLAlchemy:")
    print("-" * 40)
    
    # Verificar que los modelos se pueden importar correctamente
    models_to_check = [
        ('EndUser', EndUser),
        ('Product', Product),
        ('Order', Order),
        ('OrderItem', OrderItem),
        ('Interaction', Interaction),
        ('Recommendation', Recommendation),
        ('Search', Search),
    ]
    
    for model_name, model_class in models_to_check:
        try:
            # Verificar que el modelo tiene las columnas esperadas
            table = model_class.__table__
            print(f"✅ {model_name}: {len(table.columns)} columnas definidas")
            
            # Verificar primary keys
            pk_cols = [col.name for col in table.primary_key.columns]
            print(f"  🔑 Primary Key: {pk_cols}")
            
            # Verificar foreign keys
            fk_cols = []
            for col in table.columns:
                if col.foreign_keys:
                    fk_refs = [f"{fk.column.table.name}.{fk.column.name}" for fk in col.foreign_keys]
                    fk_cols.append(f"{col.name} -> {fk_refs}")
            
            if fk_cols:
                for fk in fk_cols:
                    print(f"  🔗 FK: {fk}")
            
        except Exception as e:
            print(f"❌ Error en modelo {model_name}: {e}")
    
    print("\n📋 Resumen de Preparación para Migración:")
    print("-" * 45)
    
    # Verificar si hay datos que podrían causar problemas
    if inconsistencies:
        print("⚠️  ACCIÓN REQUERIDA: Hay inconsistencias de tipos de datos que serán resueltas por la migración")
        print("✅ La migración está diseñada para resolver estos problemas")
    else:
        print("⚠️  Los tipos de datos ya son consistentes - verificar si la migración es necesaria")
    
    # Verificar si hay datos
    total_records = 0
    for table_name in tables_to_check.keys():
        if check_table_exists(inspector, table_name):
            count = count_records(engine, table_name)
            if count:
                total_records += count
    
    if total_records > 0:
        print(f"📊 Total de registros a migrar: {total_records:,}")
        print("⚠️  IMPORTANTE: Haga una copia de seguridad de la base de datos antes de ejecutar la migración")
        print("⚠️  La migración modificará todos los datos existentes")
    else:
        print("ℹ️  No hay datos en las tablas principales - migración de esquema únicamente")
    
    print(f"\n{'='*50}")
    print("✅ Validación completada")
    
    if inconsistencies and total_records > 0:
        print("🚨 RECOMENDACIÓN: Ejecutar la migración en un entorno de prueba primero")
        return 2  # Warning code
    elif inconsistencies:
        print("✅ Listo para migración (solo esquema)")
        return 0
    else:
        print("ℹ️  Verificar si la migración es necesaria")
        return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 