import pytest
import pytest_asyncio
from unittest.mock import MagicMock, patch, AsyncMock
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone

from src.db import models
from src.ml_pipeline.training_pipeline import TrainingPipeline
from src.main import app


class TestTrainingPipeline:
    """Tests de integración para el pipeline de entrenamiento"""

    @pytest.fixture
    def training_pipeline(self):
        """Fixture para crear una instancia del pipeline de entrenamiento"""
        pipeline = TrainingPipeline()
        return pipeline

    @pytest.fixture
    def mock_auth_middleware(self):
        """Fixture para mockear el middleware de autenticación"""

        # Crear un middleware que simula la autenticación
        async def mock_auth_middleware(request, call_next):
            # Simular una cuenta autenticada
            request.state.account = MagicMock(id=1, account_id=1)
            request.state.user_id = 1
            return await call_next(request)

        # Guardar el middleware original
        original_middleware = app.middleware_stack

        # Reemplazar con nuestro middleware mock
        app.middleware_stack = mock_auth_middleware

        yield

        # Restaurar el middleware original
        app.middleware_stack = original_middleware

    @pytest.fixture
    def mock_training_pipeline(self):
        """Fixture para mockear el pipeline de entrenamiento"""
        with patch("src.api.v1.endpoints.pipeline.TrainingPipeline") as mock_pipeline_cls:
            # Crear una instancia mock del training pipeline
            mock_pipeline = MagicMock()
            mock_pipeline.train = AsyncMock()
            mock_pipeline.train.return_value = {
                "job_id": 1,
                "status": "PENDING",
                "message": "Training job scheduled successfully",
            }
            mock_pipeline.execute_training = AsyncMock()
            mock_pipeline.execute_training.return_value = {
                "status": "success",
                "job_id": 1,
                "model_metadata_id": 1,
            }

            # Hacer que la clase mock devuelva nuestra instancia mock
            mock_pipeline_cls.return_value = mock_pipeline
            
            return mock_pipeline

    @pytest.mark.asyncio
    async def test_complete_training_pipeline(
        self, test_client, async_db_session, mock_auth_middleware, mock_training_pipeline
    ):
        """Test que verifica el flujo completo del pipeline de entrenamiento"""
        # 1. Crear una cuenta de prueba
        account = models.Account(
            id=1,
            name="Test Account",
            subscription_plan="BASIC",
            api_key="test_api_key",
            is_active=True,
        )
        async_db_session.add(account)
        await async_db_session.commit()

        # 2. Crear un job de entrenamiento
        training_job = models.TrainingJob(
            account_id=1, id=1, status="PENDING", started_at=datetime.now(timezone.utc)
        )
        async_db_session.add(training_job)
        await async_db_session.commit()

        # 3. Solicitar entrenamiento
        response = test_client.post(
            "/api/v1/pipeline/train", headers={"X-API-Key": "test_api_key"}
        )

        # Verificar respuesta
        assert response.status_code == 200
        assert "job_id" in response.json()
        assert response.json()["status"] == "PENDING"

        # Verificar que se llamó al método train
        mock_training_pipeline.train.assert_called_once()

        # 4. Verificar estado del job
        response = test_client.get(
            f"/api/v1/pipeline/status/1", headers={"X-API-Key": "test_api_key"}
        )

        # Verificar respuesta
        assert response.status_code == 200
        assert response.json()["job_id"] == 1
        assert response.json()["status"] == "PENDING"

        # 5. Simular procesamiento del job
        response = test_client.post(
            "/api/v1/pipeline/process",
            json={"account_id": 1, "job_id": 1},
            headers={"X-API-Key": "test_api_key"},
        )

        # Verificar respuesta
        assert response.status_code == 200
        assert response.json()["status"] == "success"

        # Verificar que se llamó al método execute_training
        mock_training_pipeline.execute_training.assert_called_once_with(1, 1)

        # 6. Simular callback de finalización
        response = test_client.post(
            "/api/v1/pipeline/callback/1",
            json={"account_id": 1, "status": "COMPLETED"},
            headers={"X-API-Key": "test_api_key"},
        )

        # Verificar respuesta
        assert response.status_code == 200
        assert "Callback processed successfully" in response.json()["message"]

        # 7. Verificar estado final del job
        await async_db_session.refresh(training_job)
        assert training_job.status == "COMPLETED"
        assert training_job.completed_at is not None
