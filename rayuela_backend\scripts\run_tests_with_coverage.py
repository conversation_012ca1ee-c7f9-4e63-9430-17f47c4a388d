#!/usr/bin/env python
"""
Script para ejecutar pruebas con cobertura y generar informes.

Este script ejecuta las pruebas con pytest-cov y genera informes de cobertura
en formato de consola, HTML y XML.

Uso:
    python scripts/run_tests_with_coverage.py [--html] [--xml] [--min-coverage PERCENTAGE]
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def run_tests_with_coverage(html=False, xml=False, min_coverage=70):
    """
    Ejecuta las pruebas con cobertura y genera informes.

    Args:
        html: Si es True, genera un informe HTML
        xml: Si es True, genera un informe XML
        min_coverage: Porcentaje mínimo de cobertura requerido
    
    Returns:
        int: Código de salida (0 si todo está bien, 1 si hay errores)
    """
    # Crear directorio para informes si no existe
    reports_dir = Path("coverage_reports")
    reports_dir.mkdir(exist_ok=True)
    
    # Construir comando base
    cmd = [
        "python", "-m", "pytest",
        "--cov=src",
        "--cov-config=.coveragerc",
        "--cov-report=term",
    ]
    
    # Agregar opciones para informes
    if html:
        cmd.append(f"--cov-report=html:{reports_dir}/html")
    
    if xml:
        cmd.append(f"--cov-report=xml:{reports_dir}/coverage.xml")
    
    # Agregar opción para cobertura mínima
    cmd.append(f"--cov-fail-under={min_coverage}")
    
    # Agregar directorio de pruebas
    cmd.append("tests/")
    
    # Ejecutar comando
    print(f"Ejecutando: {' '.join(cmd)}")
    result = subprocess.run(cmd)
    
    # Mostrar mensaje sobre informes generados
    if result.returncode == 0:
        print("\n✅ Todas las pruebas pasaron y la cobertura cumple con el mínimo requerido.")
    else:
        print(f"\n❌ Hay pruebas fallidas o la cobertura está por debajo del {min_coverage}%.")
    
    if html:
        print(f"\nInforme HTML generado en: {reports_dir}/html/index.html")
    
    if xml:
        print(f"\nInforme XML generado en: {reports_dir}/coverage.xml")
    
    return result.returncode


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Ejecutar pruebas con cobertura")
    parser.add_argument("--html", action="store_true", help="Generar informe HTML")
    parser.add_argument("--xml", action="store_true", help="Generar informe XML")
    parser.add_argument("--min-coverage", type=int, default=70, help="Porcentaje mínimo de cobertura requerido")
    
    args = parser.parse_args()
    
    # Ejecutar pruebas con cobertura
    exit_code = run_tests_with_coverage(
        html=args.html,
        xml=args.xml,
        min_coverage=args.min_coverage
    )
    
    sys.exit(exit_code)


if __name__ == "__main__":
    main()
