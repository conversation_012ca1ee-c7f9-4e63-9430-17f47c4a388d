"""Unit tests for email verification."""
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from src.services.email_verification_service import EmailVerificationService
from src.db.models import SystemUser
from src.core.exceptions import EmailNotVerifiedError


@pytest_asyncio.fixture
async def mock_db():
    """Mock database session."""
    db = AsyncMock(spec=AsyncSession)
    db.begin = AsyncMock(return_value=AsyncMock(__aenter__=AsyncMock(), __aexit__=AsyncMock()))
    return db


@pytest_asyncio.fixture
async def mock_user():
    """Mock user."""
    user = MagicMock(spec=SystemUser)
    user.id = 1
    user.email = "<EMAIL>"
    user.email_verified = False
    user.verification_token = None
    user.verification_token_expires_at = None
    return user


@pytest.mark.asyncio
async def test_create_verification_token(mock_db, mock_user):
    """Test create_verification_token method."""
    # Mock the database query
    mock_db.execute = AsyncMock()
    mock_db.execute.return_value.scalars.return_value.first.return_value = mock_user
    
    # Create the service
    service = EmailVerificationService(mock_db, account_id=1)
    
    # Call the method
    token = await service.create_verification_token(user_id=1)
    
    # Verify the token was created
    assert token is not None
    assert len(token) == service.token_length
    assert mock_user.verification_token == token
    assert mock_user.verification_token_expires_at is not None
    
    # Verify the database was committed
    mock_db.commit.assert_called_once()


@pytest.mark.asyncio
async def test_verify_email_success(mock_db, mock_user):
    """Test verify_email method with a valid token."""
    # Set up the mock user with a valid token
    mock_user.verification_token = "valid_token"
    mock_user.verification_token_expires_at = datetime.now(timezone.utc) + timedelta(hours=1)
    
    # Mock the database query
    mock_db.execute = AsyncMock()
    mock_db.execute.return_value.scalars.return_value.first.return_value = mock_user
    
    # Create the service
    service = EmailVerificationService(mock_db)
    
    # Call the method
    result = await service.verify_email(token="valid_token")
    
    # Verify the email was verified
    assert result is True
    assert mock_user.email_verified is True
    assert mock_user.verification_token is None
    assert mock_user.verification_token_expires_at is None
    
    # Verify the database was committed
    mock_db.commit.assert_called_once()


@pytest.mark.asyncio
async def test_verify_email_expired_token(mock_db, mock_user):
    """Test verify_email method with an expired token."""
    # Set up the mock user with an expired token
    mock_user.verification_token = "expired_token"
    mock_user.verification_token_expires_at = datetime.now(timezone.utc) - timedelta(hours=1)
    
    # Mock the database query
    mock_db.execute = AsyncMock()
    mock_db.execute.return_value.scalars.return_value.first.return_value = mock_user
    
    # Create the service
    service = EmailVerificationService(mock_db)
    
    # Call the method and expect an exception
    with pytest.raises(Exception) as excinfo:
        await service.verify_email(token="expired_token")
    
    # Verify the exception message
    assert "Verification token has expired" in str(excinfo.value)
    
    # Verify the email was not verified
    assert mock_user.email_verified is False
    
    # Verify the database was not committed
    mock_db.commit.assert_not_called()


@pytest.mark.asyncio
async def test_verify_email_invalid_token(mock_db):
    """Test verify_email method with an invalid token."""
    # Mock the database query to return None (no user found with the token)
    mock_db.execute = AsyncMock()
    mock_db.execute.return_value.scalars.return_value.first.return_value = None
    
    # Create the service
    service = EmailVerificationService(mock_db)
    
    # Call the method and expect an exception
    with pytest.raises(Exception) as excinfo:
        await service.verify_email(token="invalid_token")
    
    # Verify the exception message
    assert "Invalid verification token" in str(excinfo.value)
    
    # Verify the database was not committed
    mock_db.commit.assert_not_called()


@pytest.mark.asyncio
async def test_send_verification_email(mock_db, mock_user):
    """Test send_verification_email method."""
    # Mock the create_verification_token method
    with patch.object(EmailVerificationService, 'create_verification_token', return_value="test_token"):
        # Create the service
        service = EmailVerificationService(mock_db, account_id=1)
        
        # Call the method
        result = await service.send_verification_email(user_id=1, email="<EMAIL>")
        
        # Verify the result
        assert result is True
