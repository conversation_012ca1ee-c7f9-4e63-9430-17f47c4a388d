from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.orm import relationship, declared_attr

ACCOUNT_ID_FK = "accounts.account_id"

ACCOUNT_RANGE = "RANGE (account_id)"


class TenantMixin:
    """Mixin para modelos con soporte multi-tenant"""

    @declared_attr
    def account_id(cls):
        """
        Columna account_id que es parte de la PK y clave de particionamiento.
        También es una Foreign Key a la tabla accounts.
        """
        return Column(
            Integer, ForeignKey(ACCOUNT_ID_FK, ondelete="CASCADE"), nullable=False
        )

    @declared_attr
    def account(cls):
        """
        Relación con el modelo Account.
        """
        return relationship(
            "Account",
            back_populates=cls.__tablename__,
            foreign_keys=[cls.account_id],
        )
