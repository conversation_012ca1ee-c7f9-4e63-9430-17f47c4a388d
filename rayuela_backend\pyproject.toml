[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "rayuela-backend"
version = "1.0.0"
description = "Sistema de recomendaciones como servicio - Backend API"
authors = [
    {name = "Rayuela Team", email = "<EMAIL>"},
]
dependencies = []

[tool.black]
# Configuración de Black para Rayuela
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
(
  /(
      \.eggs
    | \.git
    | \.hg
    | \.mypy_cache
    | \.pytest_cache
    | \.tox
    | \.venv
    | _build
    | buck-out
    | build
    | dist
    | alembic/versions
  )/
)
'''

[tool.isort]
# Configuración compatible con Black
profile = "black"
multi_line_output = 3
line_length = 88
include_trailing_comma = true
force_grid_wrap = 0
use_parentheses = true
ensure_newline_before_comments = true

# Configuración específica del proyecto
src_paths = ["src", "tests"]
known_first_party = ["src"]
known_local_folder = ["src"]

# Secciones de imports
sections = ["FUTURE", "STDLIB", "THIRDPARTY", "FIRSTPARTY", "LOCALFOLDER"]
default_section = "THIRDPARTY"

[tool.pytest.ini_options]
# Configuración de pytest
minversion = "6.0"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "-v",
    "--strict-markers",
    "--strict-config",
    "--tb=short",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "security: marks tests as security tests",
    "multi_tenancy: marks tests as multi-tenancy tests",
]

[tool.coverage.run]
# Configuración de coverage
source = ["src"]
omit = [
    "*/tests/*",
    "*/alembic/*",
    "*/__init__.py",
    "*/config.py",
]

[tool.coverage.report]
# Configuración de reportes de coverage
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.bandit]
# Configuración de Bandit
exclude_dirs = ["tests", "alembic/versions"]
skips = ["B101", "B601"]  # Skip assert_used y subprocess en contextos seguros

[tool.bandit.assert_used]
# Permitir asserts en tests
skips = ["**/tests/**", "**/test_*.py"]

[tool.mypy]
# Configuración de MyPy
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
namespace_packages = true
explicit_package_bases = true
ignore_missing_imports = true
follow_imports = "normal"

# Configuraciones específicas por módulo
[[tool.mypy.overrides]]
module = "tests.*"
disallow_untyped_defs = false
disallow_incomplete_defs = false

[[tool.mypy.overrides]]
module = "alembic.*"
ignore_errors = true

[[tool.mypy.overrides]]
module = [
    "celery.*",
    "redis.*",
    "fastapi.*",
    "pydantic.*",
    "numpy.*",
    "pandas.*",
    "sklearn.*",
    "pytest.*",
    "sqlalchemy.*",
    "psycopg2.*",
    "mercadopago.*",
    "stripe.*",
    "google.*"
]
ignore_missing_imports = true 