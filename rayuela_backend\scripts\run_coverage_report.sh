#!/bin/bash
# Script para ejecutar pruebas con cobertura y generar informes

# Crear directorio para informes si no existe
mkdir -p coverage_reports

# Ejecutar pruebas con cobertura
echo "Ejecutando pruebas con cobertura..."
python -m pytest --cov=src tests/ --cov-report=term --cov-report=html:coverage_reports/html --cov-report=xml:coverage_reports/coverage.xml

# Mostrar resumen
echo ""
echo "Informe HTML generado en: coverage_reports/html/index.html"
echo "Informe XML generado en: coverage_reports/coverage.xml"
echo ""
echo "Para ver el informe HTML, abra el archivo coverage_reports/html/index.html en su navegador."
