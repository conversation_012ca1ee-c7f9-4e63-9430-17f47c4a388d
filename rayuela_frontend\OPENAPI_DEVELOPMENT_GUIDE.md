# OpenAPI Development Guide

## Overview

This guide explains how to properly generate and maintain the OpenAPI specification and API client for the Rayuela frontend.

## Problem Solved

**US-DX-001**: Fixed the critical issue where `openapi.json` was empty, causing the API client generation to fail and forcing manual API definitions.

## Quick Start

### 1. Start Backend Server

```bash
# Terminal 1: Start the backend
cd rayuela_backend
python main.py
```

### 2. Generate API Client

```bash
# Terminal 2: Generate the API client
cd rayuela_frontend
npm run generate-api:dev
```

### 3. Verify Generation

```bash
# Check that OpenAPI spec has content
cat src/lib/openapi/openapi.json | grep -c "paths"

# Check that API client was generated
ls -la src/lib/generated/
```

## Available Scripts

### OpenAPI Fetching

```bash
# Basic fetch (requires backend running)
npm run fetch-openapi

# Verbose output with validation details
npm run fetch-openapi:verbose

# Force creation even if backend unavailable (creates minimal spec)
npm run fetch-openapi:force

# Show help
npm run fetch-openapi -- --help
```

### API Client Generation

```bash
# Full generation with validation (recommended for development)
npm run generate-api:dev

# Standard generation
npm run generate-api

# Force generation (for builds when backend unavailable)
npm run generate-api:force
```

## Development Workflow

### Daily Development

1. **Start Backend**: `cd rayuela_backend && python main.py`
2. **Generate API**: `cd rayuela_frontend && npm run generate-api:dev`
3. **Start Frontend**: `npm run dev`
4. **Code**: Use the generated API client in your components

### When Backend API Changes

1. **Restart Backend**: Ensure latest changes are running
2. **Regenerate API**: `npm run generate-api:dev`
3. **Check Changes**: Review generated files for new endpoints/schemas
4. **Update Frontend**: Adapt your code to use new API features

### Before Committing

1. **Generate Final API**: `npm run generate-api:dev`
2. **Commit Generated Files**: Include `src/lib/openapi/openapi.json` and `src/lib/generated/`
3. **Test Build**: `npm run build` should work without backend

## File Structure

```
rayuela_frontend/
├── src/
│   ├── lib/
│   │   ├── openapi/
│   │   │   └── openapi.json          # ✅ Generated OpenAPI spec
│   │   ├── generated/                # ✅ Generated API client
│   │   │   ├── api-client.ts         # Main API client
│   │   │   ├── schemas/              # TypeScript schemas
│   │   │   └── ...
│   │   ├── api.ts                    # ⚠️ Manual API (to be replaced)
│   │   └── api-wrapper.ts            # ⚠️ Manual wrapper (to be replaced)
│   └── scripts/
│       └── fetch-openapi.ts          # ✅ Enhanced fetch script
├── orval.config.ts                   # ✅ Orval configuration
└── package.json                      # ✅ Updated scripts
```

## Troubleshooting

### Empty OpenAPI Specification

**Problem**: `openapi.json` has `"paths": {}`

**Solutions**:
1. **Check Backend**: Ensure backend server is running at `http://localhost:8000`
2. **Test Health**: `curl http://localhost:8000/health`
3. **Test OpenAPI**: `curl http://localhost:8000/api/openapi.json`
4. **Check Routers**: Verify FastAPI routers are properly included in `main.py`

### Backend Server Not Available

**Problem**: `ECONNREFUSED` or `BACKEND_UNAVAILABLE` errors

**Solutions**:
1. **Start Backend**: `cd rayuela_backend && python main.py`
2. **Check Port**: Ensure backend is on port 8000
3. **Check Environment**: Verify `NEXT_PUBLIC_API_URL` if using custom URL
4. **Force Generation**: `npm run generate-api:force` (creates minimal spec)

### Invalid OpenAPI Specification

**Problem**: OpenAPI validation fails

**Solutions**:
1. **Check FastAPI Config**: Verify `openapi_url="/api/openapi.json"` in `main.py`
2. **Check Router Inclusion**: Ensure all routers are included in the FastAPI app
3. **Check Endpoint Definitions**: Verify endpoints have proper decorators and response models
4. **Skip Validation**: `npm run fetch-openapi -- --skip-validation` (temporary)

### Build Failures

**Problem**: Production build fails due to missing OpenAPI

**Solutions**:
1. **Generate in Development**: Run `npm run generate-api:dev` first
2. **Commit Generated Files**: Include `openapi.json` in version control
3. **Use Force Mode**: `prebuild` script uses `generate-api:force` automatically

## Environment Variables

```bash
# Backend URL (default: http://localhost:8000)
NEXT_PUBLIC_API_URL=http://localhost:8000

# For production builds
NODE_ENV=production
```

## Migration from Manual API

### Current State (Manual)

```typescript
// src/lib/api.ts - Manual API definitions
export const api = {
  auth: {
    login: (data) => fetch('/api/v1/auth/token', ...),
    register: (data) => fetch('/api/v1/auth/register', ...),
  },
  // ... more manual definitions
};
```

### Target State (Generated)

```typescript
// src/lib/generated/api-client.ts - Generated API client
import { authApi, usersApi } from './generated';

// Fully typed, automatically generated from OpenAPI
const loginResponse = await authApi.login({ username, password });
const users = await usersApi.getUsers();
```

### Migration Steps

1. **Generate API Client**: `npm run generate-api:dev`
2. **Update Imports**: Replace manual API imports with generated ones
3. **Update Types**: Use generated TypeScript types
4. **Test Thoroughly**: Ensure all API calls work with generated client
5. **Remove Manual Files**: Delete `api.ts` and `api-wrapper.ts` when migration complete

## Best Practices

### Development

- ✅ Always start backend before generating API
- ✅ Use `generate-api:dev` for development (includes validation)
- ✅ Commit generated files to version control
- ✅ Regenerate API when backend changes

### Production

- ✅ Ensure valid `openapi.json` exists before build
- ✅ Use `generate-api:force` in CI/CD if backend unavailable
- ✅ Monitor build logs for OpenAPI warnings
- ✅ Test API client in staging environment

### Code Quality

- ✅ Use generated TypeScript types for type safety
- ✅ Handle API errors using generated error types
- ✅ Leverage generated documentation and schemas
- ✅ Avoid manual API definitions when generated client available

## Monitoring and Maintenance

### Regular Checks

1. **Weekly**: Verify OpenAPI generation still works
2. **After Backend Changes**: Regenerate and test API client
3. **Before Releases**: Ensure generated client matches backend API
4. **CI/CD**: Monitor build logs for OpenAPI warnings

### Performance

- Generated client is optimized for tree-shaking
- TypeScript types provide compile-time validation
- Axios-based client supports interceptors and middleware
- Automatic request/response transformation

## Support

### Getting Help

1. **Check Logs**: Use `--verbose` flag for detailed output
2. **Validate Backend**: Test OpenAPI endpoint directly
3. **Check Configuration**: Verify `orval.config.ts` and `package.json`
4. **Review Generated Files**: Inspect output for issues

### Common Commands

```bash
# Full diagnostic
npm run fetch-openapi:verbose

# Quick fix for builds
npm run generate-api:force

# Help and options
npm run fetch-openapi -- --help

# Test backend connectivity
curl http://localhost:8000/health
curl http://localhost:8000/api/openapi.json
```
