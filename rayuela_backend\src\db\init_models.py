"""
Este archivo se encarga de inicializar todos los modelos de la base de datos
para asegurar que estén registrados correctamente con SQLAlchemy.
"""

# Importar la clase Base
from src.db.base import Base


# Función para inicializar los modelos
def init_models():
    """
    Inicializa todos los modelos de la base de datos.
    Esta función debe ser llamada antes de crear las tablas.
    """
    # Importar todos los modelos aquí para evitar importaciones circulares
    # Estas importaciones solo se ejecutan cuando se llama a la función
    from src.db.models.account import Account
    from src.db.models.mixins import TenantMixin
    from src.db.models.subscription import Subscription
    from src.db.models.account_usage_metrics import AccountUsageMetrics
    from src.db.models.notification import Notification
    from src.db.models.audit_log import AuditLog
    from src.db.models.system_user import SystemUser
    from src.db.models.role import Role, role_permissions
    from src.db.models.permission import Permission
    # Importamos la clase SystemUserRole
    from src.db.models.system_user_role import SystemUserRole
    from src.db.models.end_user import EndUser
    from src.db.models.product import Product
    from src.db.models.interaction import Interaction
    from src.db.models.search import Search
    from src.db.models.model_metadata import ModelMetadata
    from src.db.models.training_job import TrainingJob

    # No necesitamos hacer nada más, solo importar los modelos
    # para que se registren con SQLAlchemy
    return {
        "Account": Account,
        "TenantMixin": TenantMixin,
        "Subscription": Subscription,
        "AccountUsageMetrics": AccountUsageMetrics,
        "Notification": Notification,
        "AuditLog": AuditLog,
        "SystemUser": SystemUser,
        "Role": Role,
        "Permission": Permission,
        "role_permissions": role_permissions,
        "SystemUserRole": SystemUserRole,
        "EndUser": EndUser,
        "Product": Product,
        "Interaction": Interaction,
        "Search": Search,
        "ModelMetadata": ModelMetadata,
        "TrainingJob": TrainingJob,
    }


__all__ = ["init_models", "Base"]
