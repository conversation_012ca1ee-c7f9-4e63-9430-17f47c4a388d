from typing import Any, Optional, List, Union, Callable
from src.core.redis_manager import RedisManager
from src.core.config import settings
import json
import hashlib
from src.utils.base_logger import log_info, log_error
import functools

# Variable para permitir parches en pruebas
redis = None

class RecommendationCache:
    def __init__(self):
        self._redis_manager = None
        self.prefix = settings.CACHE_PREFIX
        self.ttl = settings.CACHE_TTL

    async def _get_redis_manager(self) -> RedisManager:
        """Obtiene la instancia de RedisManager"""
        if not self._redis_manager:
            self._redis_manager = await RedisManager.get_instance()
        return self._redis_manager

    def _generate_cache_key(self, account_id: int, user_id: int, **params) -> str:
        """Genera una key única para la caché basada en los parámetros"""
        params_str = json.dumps(params, sort_keys=True)
        key_base = f"{user_id}:{params_str}"
        hash_key = hashlib.md5(key_base.encode()).hexdigest()
        return f"{self.prefix}:{account_id}:recs:{hash_key}"

    async def get_cached_recommendations(
        self, account_id: int, user_id: int, **params
    ) -> Optional[List]:
        """Obtiene recomendaciones cacheadas"""
        try:
            redis_manager = await self._get_redis_manager()
            cache_key = self._generate_cache_key(account_id, user_id, **params)
            return await redis_manager.get_json(cache_key)
        except Exception as e:
            log_error(f"Error obteniendo recomendaciones de caché: {str(e)}")
            return None

    async def cache_recommendations(
        self, account_id: int, user_id: int, recommendations: List, **params
    ) -> bool:
        """Guarda recomendaciones en caché"""
        try:
            redis_manager = await self._get_redis_manager()
            cache_key = self._generate_cache_key(account_id, user_id, **params)
            return await redis_manager.set_json(
                cache_key, recommendations, expire=self.ttl
            )
        except Exception as e:
            log_error(f"Error guardando recomendaciones en caché: {str(e)}")
            return False

    async def invalidate_cache(
        self, account_id: int, user_id: Optional[int] = None
    ) -> int:
        """Invalida la caché para un usuario específico o toda la cuenta"""
        try:
            redis_manager = await self._get_redis_manager()
            if user_id:
                # Invalidar solo para un usuario específico
                pattern = f"{self.prefix}:{account_id}:recs:*:{user_id}:*"
            else:
                # Invalidar toda la caché de la cuenta
                pattern = f"{self.prefix}:{account_id}:recs:*"

            return await redis_manager.delete_pattern(pattern)
        except Exception as e:
            log_error(f"Error invalidando caché: {str(e)}")
            return 0

    async def get(self, key: str) -> Any:
        """Obtiene un valor de la caché por su clave.

        Este método es un alias simplificado para facilitar el uso en el servicio de recomendación.

        Args:
            key: Clave de caché

        Returns:
            Valor almacenado o None si no existe
        """
        try:
            redis_manager = await self._get_redis_manager()
            return await redis_manager.get_json(key)
        except Exception as e:
            log_error(f"Error obteniendo valor de caché: {str(e)}")
            return None

    async def set(self, key: str, value: Any, expire: int = None) -> bool:
        """Guarda un valor en la caché.

        Este método es un alias simplificado para facilitar el uso en el servicio de recomendación.

        Args:
            key: Clave de caché
            value: Valor a guardar
            expire: Tiempo de expiración en segundos (opcional)

        Returns:
            True si se guardó correctamente, False en caso contrario
        """
        try:
            redis_manager = await self._get_redis_manager()
            return await redis_manager.set_json(key, value, expire=expire or self.ttl)
        except Exception as e:
            log_error(f"Error guardando valor en caché: {str(e)}")
            return False

# Instancia singleton de RecommendationCache
_cache_instance = None

async def get_cache() -> RecommendationCache:
    """Obtiene la instancia singleton de RecommendationCache"""
    global _cache_instance
    if _cache_instance is None:
        _cache_instance = RecommendationCache()
    return _cache_instance

async def invalidate_user_cache(user_id: int) -> int:
    """Invalida la caché para un usuario específico"""
    try:
        redis_manager = await RedisManager.get_instance()
        deleted = 0
        keys_to_delete = [
            f"user:{user_id}",
            f"user_permissions:{user_id}",
            f"user_roles:{user_id}"
        ]
        for key in keys_to_delete:
            deleted += await redis_manager.delete(key)
        return deleted
    except Exception as e:
        log_error(f"Error invalidando caché de usuario {user_id}: {str(e)}")
        return 0

async def invalidate_account_cache(account_id: int) -> int:
    """Invalida la caché para una cuenta específica"""
    try:
        redis_manager = await RedisManager.get_instance()
        deleted = 0
        keys_to_delete = [
            f"account:{account_id}",
            f"account_limits:{account_id}",
            f"account_settings:{account_id}"
        ]
        for key in keys_to_delete:
            deleted += await redis_manager.delete(key)
        return deleted
    except Exception as e:
        log_error(f"Error invalidando caché de cuenta {account_id}: {str(e)}")
        return 0

def invalidate_cache_after(entity_type, cascade=None, bulk=False, condition=None):
    """Decorador para invalidar la caché después de una operación

    Args:
        entity_type: Tipo de entidad a invalidar (ej: "user", "account", "product")
        cascade: Lista de entidades relacionadas a invalidar (ej: ["users"])
        bulk: Si True, se asume que la operación es masiva
        condition: Función que evalúa si se debe invalidar la caché

    Returns:
        Decorador para funciones asíncronas
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # Ejecutar la función original
            result = await func(*args, **kwargs)
            
            # Verificar si se debe invalidar la caché basado en la condición
            if condition and not condition(result):
                return result
                
            try:
                # Identificar el ID de la entidad
                entity_id = None
                if entity_type == "user":
                    if bulk:
                        entity_ids = kwargs.get("user_ids") or args[0]
                        for user_id in entity_ids:
                            await invalidate_user_cache(user_id)
                    else:
                        entity_id = kwargs.get("user_id") or args[0]
                        await invalidate_user_cache(entity_id)
                        
                elif entity_type == "account":
                    if bulk:
                        entity_ids = kwargs.get("account_ids") or args[0]
                        for account_id in entity_ids:
                            await invalidate_account_cache(account_id)
                    else:
                        entity_id = kwargs.get("account_id") or args[0]
                        await invalidate_account_cache(entity_id)
                        
                elif entity_type == "product":
                    redis_manager = await RedisManager.get_instance()
                    if bulk:
                        entity_ids = kwargs.get("product_ids") or args[0]
                        for product_id in entity_ids:
                            await redis_manager.delete(f"product:{product_id}")
                    else:
                        entity_id = kwargs.get("product_id") or args[0]
                        await redis_manager.delete(f"product:{entity_id}")
                
                # Invalidar caché en cascada
                if cascade and entity_id:
                    if "users" in cascade and entity_type == "account":
                        # Aquí se implementaría la lógica para obtener los 
                        # IDs de usuarios asociados a la cuenta y invalidar sus cachés
                        pass
            except Exception as e:
                log_error(f"Error en invalidación de caché: {str(e)}")
                
            return result
        return wrapper
    return decorator
