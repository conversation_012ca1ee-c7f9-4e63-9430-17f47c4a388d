"""
Módulo de repositorios para acceso a datos.
Este módulo exporta todas las clases de repositorio para ser utilizadas en la aplicación.
"""

# Importar clases base
from .base import IRepository, BaseRepository

# Importar repositorios de cuentas
from .account import AccountRepository, SubscriptionRepository

# Importar repositorios de usuarios
from .user import SystemUserRepository, EndUserRepository

# Importar repositorios de autenticación y autorización
from .auth import (
    RoleRepository,
    PermissionRepository,
    RolePermissionRepository,
    SystemUserRoleRepository,
)

# Importar repositorios de productos
from .product import ProductRepository

# Importar repositorios de interacciones
from .interaction import InteractionRepository, SearchRepository

# Importar repositorios de auditoría
from .audit import AuditLogRepository, NotificationRepository

# Importar repositorios de modelos
from .model import ModelMetadataRepository, TrainingJobRepository

# Importar repositorios de ingesta masiva
from .batch_ingestion import BatchIngestionJobRepository

# Importar repositorios de métricas de uso
from .usage_metrics import UsageMetricsRepository

__all__ = [
    # Clases base
    "IRepository",
    "BaseRepository",
    # Repositorios de cuentas
    "AccountRepository",
    "SubscriptionRepository",
    # Repositorios de usuarios
    "SystemUserRepository",
    "EndUserRepository",
    # Repositorios de autenticación y autorización
    "RoleRepository",
    "PermissionRepository",
    "RolePermissionRepository",
    "SystemUserRoleRepository",
    # Repositorios de productos
    "ProductRepository",
    # Repositorios de interacciones
    "InteractionRepository",
    "SearchRepository",
    # Repositorios de auditoría
    "AuditLogRepository",
    "NotificationRepository",
    # Repositorios de modelos
    "ModelMetadataRepository",
    "TrainingJobRepository",
    # Repositorios de ingesta masiva
    "BatchIngestionJobRepository",
    # Repositorios de métricas de uso
    "UsageMetricsRepository",
]
