"""
Celery tasks for subscription management.
"""
from celery import shared_task
from sqlalchemy import update, select
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone
import asyncio
from typing import List, Dict, Any

from src.db.models import Subscription
from src.db.session import get_db
from src.utils.base_logger import log_info, log_error, log_warning


@shared_task(name="reset_monthly_api_calls")
def reset_monthly_api_calls():
    """
    Reset the monthly API call counter for all subscriptions.
    This task should be scheduled to run on the 1st of each month.
    """
    try:
        # Run the async function in the event loop
        asyncio.run(_reset_monthly_api_calls_async())
        return {"status": "success", "message": "Monthly API call counters reset successfully"}
    except Exception as e:
        log_error(f"Error resetting monthly API call counters: {str(e)}")
        return {"status": "error", "message": str(e)}


async def _reset_monthly_api_calls_async():
    """Async implementation of the reset_monthly_api_calls task."""
    # Get DB session
    db_generator = get_db()
    db = await anext(db_generator)
    
    try:
        # Get all active subscriptions
        stmt = select(Subscription).where(Subscription.is_active == True)
        result = await db.execute(stmt)
        subscriptions = result.scalars().all()
        
        # Log the number of subscriptions to reset
        log_info(f"Resetting monthly API call counters for {len(subscriptions)} subscriptions")
        
        # Update all subscriptions
        now = datetime.now(timezone.utc)
        update_stmt = (
            update(Subscription)
            .where(Subscription.is_active == True)
            .values(
                monthly_api_calls_used=0,
                last_reset_date=now,
            )
        )
        await db.execute(update_stmt)
        await db.commit()
        
        log_info("Monthly API call counters reset successfully")
    except Exception as e:
        log_error(f"Error in _reset_monthly_api_calls_async: {str(e)}")
        await db.rollback()
        raise
    finally:
        await db.close()
