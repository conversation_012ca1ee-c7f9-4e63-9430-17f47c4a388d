"""Add api_keys table for multi-API key support

Revision ID: add_api_keys_multi_001
Revises: add_order_tables_001
Create Date: 2024-12-19 15:00:00.000000

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "add_api_keys_multi_001"
down_revision: Union[str, None] = "add_order_tables_001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""

    # Create api_keys table
    op.execute(
        """
        DO $$ BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'api_keys') THEN
                CREATE TABLE api_keys (
                    account_id INTEGER NOT NULL,
                    id SERIAL NOT NULL,
                    name VA<PERSON>HA<PERSON>(255),
                    api_key_hash VARCHAR(64) NOT NULL,
                    api_key_prefix VARCHAR(10) NOT NULL,
                    api_key_last_chars VARCHAR(6) NOT NULL,
                    is_active BOOLEAN NOT NULL DEFAULT TRUE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    last_used TIMESTAMP WITH TIME ZONE,
                    PRIMARY KEY (account_id, id),
                    FOREIGN KEY (account_id) REFERENCES accounts(account_id) ON DELETE CASCADE
                );
                
                -- Create indexes
                CREATE INDEX IF NOT EXISTS idx_api_keys_account_id ON api_keys (account_id);
                CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys (is_active);
                CREATE INDEX IF NOT EXISTS idx_api_keys_created_at ON api_keys (created_at);
                
                -- Global unique constraint on api_key_hash for security
                CREATE UNIQUE INDEX IF NOT EXISTS uq_api_key_hash_global ON api_keys (api_key_hash);
                
                COMMENT ON TABLE api_keys IS 'Stores multiple API keys per account for improved security and flexibility';
                COMMENT ON COLUMN api_keys.name IS 'Descriptive name for this API key';
                COMMENT ON COLUMN api_keys.api_key_hash IS 'SHA-256 hash of the API key';
                COMMENT ON COLUMN api_keys.api_key_prefix IS 'Prefix of the API key for display';
                COMMENT ON COLUMN api_keys.api_key_last_chars IS 'Last 6 characters for display';
                COMMENT ON COLUMN api_keys.is_active IS 'Whether this API key is active';
                COMMENT ON COLUMN api_keys.last_used IS 'When this API key was last used';
            END IF;
        END $$;
    """
    )

    # Migrate existing API key data from accounts table to api_keys table
    op.execute(
        """
        DO $$ 
        DECLARE
            account_record RECORD;
        BEGIN
            -- Migrate existing API keys from accounts table
            FOR account_record IN 
                SELECT account_id, api_key_hash, api_key_prefix, api_key_last_chars, api_key_created_at
                FROM accounts 
                WHERE api_key_hash IS NOT NULL
            LOOP
                -- Insert into api_keys table
                INSERT INTO api_keys (
                    account_id, 
                    name, 
                    api_key_hash, 
                    api_key_prefix, 
                    api_key_last_chars, 
                    is_active, 
                    created_at
                ) VALUES (
                    account_record.account_id,
                    'Default API Key',
                    account_record.api_key_hash,
                    account_record.api_key_prefix,
                    account_record.api_key_last_chars,
                    TRUE,
                    COALESCE(account_record.api_key_created_at, NOW())
                )
                ON CONFLICT (api_key_hash) DO NOTHING; -- Skip if already exists
            END LOOP;
            
            RAISE NOTICE 'Migrated existing API keys to new api_keys table';
        END $$;
    """
    )


def downgrade() -> None:
    """Downgrade schema."""
    
    # Before dropping the table, migrate data back to accounts table if needed
    op.execute(
        """
        DO $$ 
        DECLARE
            api_key_record RECORD;
        BEGIN
            -- Migrate the first active API key back to accounts table for each account
            FOR api_key_record IN 
                SELECT DISTINCT ON (account_id) 
                    account_id, 
                    api_key_hash, 
                    api_key_prefix, 
                    api_key_last_chars, 
                    created_at
                FROM api_keys 
                WHERE is_active = TRUE
                ORDER BY account_id, created_at ASC
            LOOP
                -- Update accounts table with the first API key
                UPDATE accounts 
                SET 
                    api_key_hash = api_key_record.api_key_hash,
                    api_key_prefix = api_key_record.api_key_prefix,
                    api_key_last_chars = api_key_record.api_key_last_chars,
                    api_key_created_at = api_key_record.created_at
                WHERE account_id = api_key_record.account_id;
            END LOOP;
            
            RAISE NOTICE 'Migrated API keys back to accounts table';
        END $$;
    """
    )
    
    # Drop the api_keys table
    op.execute("DROP TABLE IF EXISTS api_keys CASCADE")
