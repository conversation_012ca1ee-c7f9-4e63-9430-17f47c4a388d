#!/usr/bin/env python3
"""
Script centralizado para ejecutar todas las validaciones de multi-tenancy.
Orquesta todos los tests y verificaciones de seguridad para multi-tenancy.
"""

import os
import sys
import subprocess
import json
import time
from typing import Dict, Any, List
from pathlib import Path
import argparse


class MultiTenancyValidator:
    """Validador comprehensivo de multi-tenancy."""

    def __init__(self, verbose: bool = False):
        self.verbose = verbose
        self.results = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "summary": {},
            "validations": {},
            "recommendations": [],
            "success": True,
        }

    def log(self, message: str, level: str = "INFO"):
        """Log message con timestamp."""
        timestamp = time.strftime("%H:%M:%S")
        prefix = {
            "INFO": "ℹ️",
            "SUCCESS": "✅",
            "ERROR": "❌",
            "WARNING": "⚠️",
            "DEBUG": "🔍",
        }.get(level, "•")

        print(f"[{timestamp}] {prefix} {message}")

        if self.verbose and level == "DEBUG":
            print(f"    └─ {message}")

    def run_command(
        self, command: str, description: str, timeout: int = 600
    ) -> Dict[str, Any]:
        """Ejecutar comando y capturar resultado."""
        self.log(f"Ejecutando: {description}", "INFO")

        try:
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True, timeout=timeout
            )

            success = result.returncode == 0
            output = result.stdout + result.stderr

            if success:
                self.log(f"Completado: {description}", "SUCCESS")
            else:
                self.log(f"Falló: {description}", "ERROR")
                if self.verbose:
                    self.log(f"Error output: {output[:500]}...", "DEBUG")

            return {
                "command": command,
                "description": description,
                "success": success,
                "return_code": result.returncode,
                "output": output,
                "duration": time.time(),
            }

        except subprocess.TimeoutExpired:
            self.log(f"Timeout: {description}", "ERROR")
            return {
                "command": command,
                "description": description,
                "success": False,
                "return_code": -1,
                "output": "Command timeout",
                "duration": timeout,
            }
        except Exception as e:
            self.log(f"Excepción: {description} - {str(e)}", "ERROR")
            return {
                "command": command,
                "description": description,
                "success": False,
                "return_code": -2,
                "output": str(e),
                "duration": 0,
            }

    def validate_environment(self) -> bool:
        """Validar que el entorno esté configurado correctamente."""
        self.log("Validando entorno...", "INFO")

        # Verificar Docker
        docker_result = self.run_command(
            "docker --version", "Docker availability check"
        )
        if not docker_result["success"]:
            self.log("Docker no está disponible", "ERROR")
            return False

        # Verificar docker-compose
        compose_result = self.run_command(
            "docker-compose --version", "Docker Compose availability check"
        )
        if not compose_result["success"]:
            self.log("docker-compose no está disponible", "ERROR")
            return False

        # Verificar archivos necesarios
        required_files = [
            "docker-compose.test.yml",
            "scripts/verify_rls_comprehensive.py",
            "tests/integration/test_multi_tenancy_comprehensive.py",
            "tests/middleware/test_tenant_middleware_comprehensive.py",
            "tests/unit/db/repositories/test_base_repository_tenant.py",
            "tests/integration/test_celery_tenant_isolation_extended.py",
        ]

        for file_path in required_files:
            if not os.path.exists(file_path):
                self.log(f"Archivo requerido no encontrado: {file_path}", "ERROR")
                return False

        self.log("Entorno validado correctamente", "SUCCESS")
        return True

    def run_comprehensive_multi_tenancy_tests(self) -> bool:
        """Ejecutar tests comprehensivos de multi-tenancy."""
        self.log("Ejecutando tests comprehensivos de multi-tenancy...", "INFO")

        command = """
        docker-compose -f docker-compose.test.yml run --rm test-runner \
        pytest tests/integration/test_multi_tenancy_comprehensive.py \
        -v --tb=short \
        --junitxml=/app/multi_tenancy_comprehensive_results.xml \
        --cov=src --cov-report=xml:/app/coverage_multi_tenancy_comprehensive.xml
        """

        result = self.run_command(command, "Comprehensive Multi-Tenancy Tests")
        self.results["validations"]["comprehensive_tests"] = result

        if not result["success"]:
            self.results["success"] = False
            self.results["recommendations"].append(
                "CRÍTICO: Tests comprehensivos de multi-tenancy fallaron - revisar aislamiento de datos"
            )

        return result["success"]

    def run_tenant_middleware_tests(self) -> bool:
        """Ejecutar tests de TenantMiddleware."""
        self.log("Ejecutando tests de TenantMiddleware...", "INFO")

        command = """
        docker-compose -f docker-compose.test.yml run --rm test-runner \
        pytest tests/middleware/test_tenant_middleware_comprehensive.py \
        -v --tb=short \
        --junitxml=/app/tenant_middleware_results.xml \
        --cov=src/middleware/tenant --cov-report=xml:/app/coverage_tenant_middleware.xml \
        --cov-fail-under=90
        """

        result = self.run_command(command, "Tenant Middleware Tests")
        self.results["validations"]["middleware_tests"] = result

        if not result["success"]:
            self.results["success"] = False
            self.results["recommendations"].append(
                "CRÍTICO: TenantMiddleware tests fallaron - revisar propagación de contexto"
            )

        return result["success"]

    def run_base_repository_tests(self) -> bool:
        """Ejecutar tests de BaseRepository."""
        self.log("Ejecutando tests de BaseRepository...", "INFO")

        command = """
        docker-compose -f docker-compose.test.yml run --rm test-runner \
        pytest tests/unit/db/repositories/test_base_repository_tenant.py \
        -v --tb=short \
        --junitxml=/app/base_repository_results.xml \
        --cov=src/db/repositories/base --cov-report=xml:/app/coverage_base_repository.xml \
        --cov-fail-under=85
        """

        result = self.run_command(command, "BaseRepository Tenant Tests")
        self.results["validations"]["repository_tests"] = result

        if not result["success"]:
            self.results["success"] = False
            self.results["recommendations"].append(
                "CRÍTICO: BaseRepository tests fallaron - revisar filtros de tenant"
            )

        return result["success"]

    def run_celery_isolation_tests(self) -> bool:
        """Ejecutar tests de aislamiento Celery."""
        self.log("Ejecutando tests de aislamiento Celery...", "INFO")

        command = """
        docker-compose -f docker-compose.test.yml run --rm test-runner \
        pytest tests/integration/test_celery_tenant_isolation_extended.py \
        -v --tb=short \
        --junitxml=/app/celery_isolation_results.xml \
        --cov=src/core/celery_app --cov=src/tasks \
        --cov-report=xml:/app/coverage_celery_isolation.xml
        """

        result = self.run_command(command, "Celery Tenant Isolation Tests")
        self.results["validations"]["celery_tests"] = result

        if not result["success"]:
            self.results["success"] = False
            self.results["recommendations"].append(
                "ALTO: Celery isolation tests fallaron - revisar aislamiento en tareas"
            )

        return result["success"]

    def run_rls_verification(self) -> bool:
        """Ejecutar verificación de políticas RLS."""
        self.log("Ejecutando verificación de políticas RLS...", "INFO")

        command = "python scripts/verify_rls_comprehensive.py"

        result = self.run_command(command, "RLS Policies Verification")
        self.results["validations"]["rls_verification"] = result

        if not result["success"]:
            self.results["success"] = False
            self.results["recommendations"].append(
                "CRÍTICO: Verificación RLS falló - problemas de seguridad identificados"
            )

        return result["success"]

    def run_integration_suite(self) -> bool:
        """Ejecutar suite completa de integración multi-tenancy."""
        self.log("Ejecutando suite de integración multi-tenancy...", "INFO")

        command = """
        docker-compose -f docker-compose.test.yml run --rm test-runner \
        pytest tests/integration/test_multi_tenancy_security.py \
        tests/integration/test_multi_tenancy.py \
        -v --tb=short \
        --junitxml=/app/integration_multi_tenancy_results.xml \
        --cov=src --cov-report=xml:/app/coverage_integration_multi_tenancy.xml
        """

        result = self.run_command(command, "Multi-Tenancy Integration Suite")
        self.results["validations"]["integration_suite"] = result

        if not result["success"]:
            self.results["success"] = False
            self.results["recommendations"].append(
                "ALTO: Suite de integración multi-tenancy falló"
            )

        return result["success"]

    def cleanup_environment(self):
        """Limpiar contenedores y recursos."""
        self.log("Limpiando entorno...", "INFO")

        cleanup_command = "docker-compose -f docker-compose.test.yml down -v"
        self.run_command(cleanup_command, "Environment cleanup")

    def generate_summary_report(self):
        """Generar reporte resumen."""
        self.log("Generando reporte resumen...", "INFO")

        # Contar éxitos y fallas
        total_validations = len(self.results["validations"])
        successful_validations = sum(
            1 for v in self.results["validations"].values() if v["success"]
        )

        self.results["summary"] = {
            "total_validations": total_validations,
            "successful_validations": successful_validations,
            "failed_validations": total_validations - successful_validations,
            "success_rate": (
                (successful_validations / total_validations * 100)
                if total_validations > 0
                else 0
            ),
            "overall_success": self.results["success"],
        }

        # Agregar recomendaciones generales
        if self.results["success"]:
            self.results["recommendations"].extend(
                [
                    "✅ Todas las validaciones de multi-tenancy pasaron",
                    "Continuar ejecutando estas validaciones en CI/CD",
                    "Revisar periódicamente políticas RLS después de migraciones",
                ]
            )
        else:
            self.results["recommendations"].extend(
                [
                    "❌ Se detectaron problemas críticos de multi-tenancy",
                    "NO DESPLEGAR hasta corregir todos los problemas",
                    "Revisar logs detallados de cada validación fallida",
                ]
            )

    def save_results(self, output_file: str = "multi_tenancy_validation_results.json"):
        """Guardar resultados en archivo JSON."""
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

        self.log(f"Resultados guardados en: {output_file}", "SUCCESS")

    def print_summary(self):
        """Imprimir resumen de resultados."""
        print("\n" + "=" * 80)
        print("🔒 RESUMEN DE VALIDACIÓN MULTI-TENANCY")
        print("=" * 80)

        summary = self.results["summary"]
        status_emoji = "✅" if self.results["success"] else "❌"

        print(
            f"{status_emoji} Estado general: {'EXITOSO' if self.results['success'] else 'FALLIDO'}"
        )
        print(f"📊 Validaciones totales: {summary['total_validations']}")
        print(f"✅ Validaciones exitosas: {summary['successful_validations']}")
        print(f"❌ Validaciones fallidas: {summary['failed_validations']}")
        print(f"📈 Tasa de éxito: {summary['success_rate']:.1f}%")

        if not self.results["success"]:
            print(f"\n🚨 VALIDACIONES FALLIDAS:")
            for name, validation in self.results["validations"].items():
                if not validation["success"]:
                    print(f"  • {validation['description']}")

        print(f"\n💡 RECOMENDACIONES:")
        for i, rec in enumerate(self.results["recommendations"][:5], 1):
            print(f"  {i}. {rec}")

    def run_all_validations(self) -> bool:
        """Ejecutar todas las validaciones de multi-tenancy."""
        try:
            # 1. Validar entorno
            if not self.validate_environment():
                return False

            # 2. Ejecutar todas las validaciones
            validations = [
                self.run_comprehensive_multi_tenancy_tests,
                self.run_tenant_middleware_tests,
                self.run_base_repository_tests,
                self.run_celery_isolation_tests,
                self.run_integration_suite,
                self.run_rls_verification,
            ]

            for validation in validations:
                try:
                    validation()
                except Exception as e:
                    self.log(f"Error en validación: {str(e)}", "ERROR")
                    self.results["success"] = False

            # 3. Generar resumen
            self.generate_summary_report()

            # 4. Mostrar resultados
            self.print_summary()

            # 5. Guardar resultados
            self.save_results()

            return self.results["success"]

        finally:
            # Siempre limpiar
            self.cleanup_environment()


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(
        description="Validador comprehensivo de multi-tenancy para Rayuela"
    )
    parser.add_argument(
        "--verbose", "-v", action="store_true", help="Mostrar output detallado"
    )
    parser.add_argument(
        "--output",
        "-o",
        default="multi_tenancy_validation_results.json",
        help="Archivo de salida para resultados",
    )

    args = parser.parse_args()

    print("🚀 Iniciando validación comprehensiva de multi-tenancy...")

    validator = MultiTenancyValidator(verbose=args.verbose)
    success = validator.run_all_validations()

    # Exit code para CI/CD
    exit_code = 0 if success else 1

    if success:
        print("\n🎉 ¡Todas las validaciones de multi-tenancy pasaron!")
        print("✅ Sistema listo para despliegue desde perspectiva de multi-tenancy")
    else:
        print("\n💥 Validaciones de multi-tenancy fallaron")
        print("❌ NO DESPLEGAR hasta corregir problemas de seguridad")

    sys.exit(exit_code)


if __name__ == "__main__":
    main()
