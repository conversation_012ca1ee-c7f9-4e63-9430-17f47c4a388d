"use client";

import React from 'react';
import AdminRoute from '@/components/auth/AdminRoute';
import { CurrentPlanCard } from '@/components/billing/CurrentPlanCard';
import { PlansGrid } from '@/components/billing/PlansGrid';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth, usePlans, useAccountInfo, useUsageSummary } from '@/lib/hooks';
import { InfoIcon } from 'lucide-react';

export default function BillingPage() {
  const { token, apiKey } = useAuth();
  const { plans, isLoading: plansLoading, error: plansError } = usePlans();
  const { accountData, isLoading: accountLoading, error: accountError } = useAccountInfo();
  const { usageData, isLoading: usageLoading, error: usageError } = useUsageSummary();

  // Estado de carga combinado
  const isLoading = plansLoading || accountLoading || usageLoading;
  const hasError = plansError || accountError || usageError;

  if (!token || !apiKey) {
    return (
      <AdminRoute>
        <div className="container mx-auto py-8">
          <Alert>
            <InfoIcon className="h-4 w-4" />
            <AlertDescription>
              Debes iniciar sesión para acceder a la información de facturación.
            </AlertDescription>
          </Alert>
        </div>
      </AdminRoute>
    );
  }

  return (
    <AdminRoute>
      <div className="container mx-auto py-8 space-y-8">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold tracking-tight">Planes y Facturación</h1>
          <p className="text-muted-foreground">
            Gestiona tu suscripción, revisa tu uso actual y actualiza tu plan según tus necesidades.
          </p>
        </div>

        {/* Error State */}
        {hasError && (
          <Alert variant="destructive">
            <InfoIcon className="h-4 w-4" />
            <AlertDescription>
              Error al cargar la información de facturación. Por favor, intenta recargar la página.
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {isLoading ? (
          <div className="space-y-8">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Skeleton className="h-64" />
              <Skeleton className="h-64" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Skeleton className="h-96" />
              <Skeleton className="h-96" />
              <Skeleton className="h-96" />
              <Skeleton className="h-96" />
            </div>
          </div>
        ) : (
          <>
            {/* Current Plan Section */}
            <CurrentPlanCard
              accountData={accountData}
              usageData={usageData}
              plans={plans}
            />

            {/* Available Plans Section */}
            <div className="space-y-6">
              <div className="space-y-2">
                <h2 className="text-2xl font-semibold tracking-tight">Cambiar Plan</h2>
                <p className="text-muted-foreground">
                  Elige el plan que mejor se adapte a tus necesidades. Puedes cambiar en cualquier momento.
                </p>
              </div>

              <PlansGrid
                plans={plans}
                currentPlan={accountData?.subscription?.plan || usageData?.subscription?.plan}
                accountData={accountData}
                usageData={usageData}
              />
            </div>
          </>
        )}
      </div>
    </AdminRoute>
  );
}