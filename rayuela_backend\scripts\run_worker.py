#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run the Celery worker for ML training tasks.
"""
import os
import sys
import argparse
from celery.bin.worker import worker

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.workers.celery_app import celery_app


def main():
    """Run the Celery worker."""
    parser = argparse.ArgumentParser(
        description="Run the Celery worker for ML training tasks."
    )
    parser.add_argument(
        "--concurrency",
        type=int,
        default=2,
        help="Number of worker processes/threads to use",
    )
    parser.add_argument(
        "--loglevel",
        type=str,
        default="info",
        choices=["debug", "info", "warning", "error", "critical"],
        help="Logging level",
    )
    parser.add_argument(
        "--queue",
        type=str,
        default="celery",
        help="Queue to consume from",
    )
    args = parser.parse_args()

    # Create worker instance
    worker_instance = worker(app=celery_app)

    # Configure worker
    worker_instance.setup_defaults(
        concurrency=args.concurrency,
        loglevel=args.loglevel,
        queues=args.queue,
    )

    # Start worker
    worker_instance.run()


if __name__ == "__main__":
    main()
