"""
Endpoints for storage usage information.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from redis.asyncio import Redis
from typing import Dict, Any

from src.core.deps import (
    get_current_active_user,
    get_db,
    get_subscription_service,
    get_storage_tracker_service,
)
from src.core.redis_utils import get_redis
from src.db.models import SystemUser
from src.services import SubscriptionService
from src.services.storage_tracker_service import StorageTrackerService
from src.services.redis_storage_meter_service import RedisStorageMeterService
from src.utils.base_logger import log_info, log_error

router = APIRouter()


@router.get("/usage", response_model=Dict[str, Any])
async def get_storage_usage(
    current_user: SystemUser = Depends(get_current_active_user),
    subscription_service: SubscriptionService = Depends(get_subscription_service),
    storage_tracker: StorageTrackerService = Depends(get_storage_tracker_service),
    redis: Redis = Depends(get_redis),
    db: AsyncSession = Depends(get_db),
):
    """
    Get storage usage information for the current account.

    Returns:
        Dict with storage usage information including:
        - Storage used and limit
        - Breakdown by data type
        - Last measurement time
    """
    try:
        account_id = current_user.account_id

        # Get subscription for storage limit
        subscription = await subscription_service._subscription_repo.get_by_account(account_id)

        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active subscription found",
            )

        # Create Redis storage meter
        redis_meter = RedisStorageMeterService(redis, account_id)

        # Get storage usage from Redis
        redis_storage_gb = await redis_meter.get_storage_usage_gb()
        redis_storage_bytes = await redis_meter.get_storage_usage_bytes()
        storage_details = await redis_meter.get_storage_details()

        # If not in Redis, calculate using storage tracker
        if redis_storage_bytes == 0 and storage_details is None:
            log_info(f"Storage usage for account {account_id} not found in Redis, calculating directly")
            db_storage_bytes = await storage_tracker.get_current_storage_usage()

            # Format the response with DB data
            usage_info = {
                "storage": {
                    "used_bytes": db_storage_bytes,
                    "used_mb": round(db_storage_bytes / (1024 * 1024), 2),
                    "used_gb": round(db_storage_bytes / (1024 * 1024 * 1024), 4),
                    "limit_bytes": subscription.storage_limit,
                    "limit_mb": round(subscription.storage_limit / (1024 * 1024), 2),
                    "limit_gb": round(subscription.storage_limit / (1024 * 1024 * 1024), 4),
                    "percentage": round(
                        (db_storage_bytes / subscription.storage_limit) * 100
                        if subscription.storage_limit > 0
                        else 0,
                        2,
                    ),
                },
                "details": {
                    "source": "database_calculation",
                    "breakdown": {
                        "products": "Not available",
                        "end_users": "Not available",
                        "interactions": "Not available",
                        "artifacts": "Not available",
                    },
                    "last_measured": "Just now",
                }
            }
        else:
            # Format the response with Redis data
            usage_info = {
                "storage": {
                    "used_bytes": redis_storage_bytes,
                    "used_mb": round(redis_storage_bytes / (1024 * 1024), 2),
                    "used_gb": redis_storage_gb,
                    "limit_bytes": subscription.storage_limit,
                    "limit_mb": round(subscription.storage_limit / (1024 * 1024), 2),
                    "limit_gb": round(subscription.storage_limit / (1024 * 1024 * 1024), 4),
                    "percentage": round(
                        (redis_storage_bytes / subscription.storage_limit) * 100
                        if subscription.storage_limit > 0
                        else 0,
                        2,
                    ),
                },
                "details": {
                    "source": "redis_cache",
                    "breakdown": {
                        "products": f"{round(storage_details.get('products_bytes', 0) / (1024 * 1024), 2)} MB" if storage_details else "Not available",
                        "end_users": f"{round(storage_details.get('end_users_bytes', 0) / (1024 * 1024), 2)} MB" if storage_details else "Not available",
                        "interactions": f"{round(storage_details.get('interactions_bytes', 0) / (1024 * 1024), 2)} MB" if storage_details else "Not available",
                        "artifacts": f"{round(storage_details.get('artifacts_bytes', 0) / (1024 * 1024), 2)} MB" if storage_details else "Not available",
                    },
                    "last_measured": storage_details.get("measured_at", "Unknown") if storage_details else "Unknown",
                }
            }

        log_info(f"Retrieved storage usage for account {account_id}")
        return usage_info
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error retrieving storage usage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving storage usage",
        )


@router.post("/refresh", response_model=Dict[str, Any])
async def refresh_storage_usage(
    current_user: SystemUser = Depends(get_current_active_user),
    storage_tracker: StorageTrackerService = Depends(get_storage_tracker_service),
    redis: Redis = Depends(get_redis),
    db: AsyncSession = Depends(get_db),
):
    """
    Force a refresh of storage usage calculation for the current account.

    Returns:
        Dict with updated storage usage information
    """
    try:
        account_id = current_user.account_id

        # Calculate storage usage
        storage_details = await storage_tracker._calculate_account_storage()

        # Store in Redis with a TTL of 24 hours
        storage_gb = storage_details["total_gb"]
        redis_key = f"usage:{account_id}:storage_gb"
        await redis.setex(redis_key, 86400, str(storage_gb))

        # Store details
        details_key = f"usage:{account_id}:storage_details"
        await redis.setex(details_key, 86400, storage_details)

        # Update subscription
        await storage_tracker.update_subscription_storage_usage()

        log_info(f"Refreshed storage usage for account {account_id}")

        # Return the updated usage
        return {
            "message": "Storage usage refreshed successfully",
            "storage": {
                "used_bytes": storage_details["total_bytes"],
                "used_mb": round(storage_details["total_bytes"] / (1024 * 1024), 2),
                "used_gb": storage_details["total_gb"],
            },
            "details": {
                "breakdown": {
                    "products": f"{round(storage_details.get('products_bytes', 0) / (1024 * 1024), 2)} MB",
                    "end_users": f"{round(storage_details.get('end_users_bytes', 0) / (1024 * 1024), 2)} MB",
                    "interactions": f"{round(storage_details.get('interactions_bytes', 0) / (1024 * 1024), 2)} MB",
                    "artifacts": f"{round(storage_details.get('artifacts_bytes', 0) / (1024 * 1024), 2)} MB",
                },
                "last_measured": storage_details.get("measured_at", "Just now"),
            }
        }
    except Exception as e:
        log_error(f"Error refreshing storage usage: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error refreshing storage usage",
        )
