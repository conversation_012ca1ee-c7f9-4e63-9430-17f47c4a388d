"""
Integration tests for RLS functions security.

These tests verify that the SECURITY DEFINER functions in PostgreSQL
are properly secured against SQL injection and unauthorized access.
"""
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from datetime import datetime, timezone, timedelta
import json
import re
from typing import List, Dict, Any, Tuple

from src.db.models import Account, Product, Interaction, SystemUser
from src.db.session import get_db


@pytest_asyncio.fixture
@pytest.mark.asyncio
async def test_data(db_session: AsyncSession) -> Tuple[List[Account], List[Product], List[Interaction]]:
    """Create test data for RLS function tests."""
    # Create test accounts
    accounts = []
    for i in range(1, 3):
        account = Account(
            name=f"Test Account {i}",
            email=f"test{i}@example.com",
            status="active"
        )
        db_session.add(account)
    await db_session.flush()
    accounts = [account for account in accounts]
    
    # Create test products
    products = []
    for i, account in enumerate(accounts):
        for j in range(1, 4):
            product = Product(
                name=f"Product {j} for Account {i+1}",
                description=f"Test product {j}",
                price=10.0 * j,
                category="test",
                account_id=account.account_id,
                last_interaction_at=datetime.now(timezone.utc) - timedelta(days=200 if j == 3 else 10)
            )
            db_session.add(product)
    await db_session.flush()
    products = [product for product in products]
    
    # Create test interactions
    interactions = []
    for i, account in enumerate(accounts):
        for j in range(1, 6):
            interaction = Interaction(
                user_id=j,
                product_id=products[i*3].id,
                interaction_type="view",
                account_id=account.account_id,
                created_at=datetime.now(timezone.utc) - timedelta(days=200 if j > 3 else 10)
            )
            db_session.add(interaction)
    await db_session.flush()
    interactions = [interaction for interaction in interactions]
    
    # Create test system users
    for i, account in enumerate(accounts):
        user = SystemUser(
            email=f"user{i+1}@example.com",
            hashed_password="hashed_password",
            account_id=account.account_id,
            status="active",
            last_login_at=datetime.now(timezone.utc) - timedelta(days=200 if i == 1 else 10)
        )
        db_session.add(user)
    
    await db_session.commit()
    
    return accounts, products, interactions


class TestRLSFunctionsSecurity:
    """Tests for RLS functions security."""
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data_parameter_validation(self, db_session: AsyncSession):
        """Test that cleanup_old_data validates its parameters."""
        # Test with invalid account_id
        with pytest.raises(Exception) as excinfo:
            await db_session.execute(
                text("SELECT cleanup_old_data(:account_id, :days_old)"),
                {"account_id": -1, "days_old": 30}
            )
        assert "Invalid account_id" in str(excinfo.value)
        
        # Test with invalid days_old (too small)
        with pytest.raises(Exception) as excinfo:
            await db_session.execute(
                text("SELECT cleanup_old_data(:account_id, :days_old)"),
                {"account_id": 1, "days_old": 0}
            )
        assert "Invalid days_old" in str(excinfo.value)
        
        # Test with invalid days_old (too large)
        with pytest.raises(Exception) as excinfo:
            await db_session.execute(
                text("SELECT cleanup_old_data(:account_id, :days_old)"),
                {"account_id": 1, "days_old": 4000}
            )
        assert "Invalid days_old" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_bypass_rls_parameter_validation(self, db_session: AsyncSession):
        """Test that bypass_rls validates its parameters."""
        # Test with invalid account_id
        with pytest.raises(Exception) as excinfo:
            await db_session.execute(
                text("SELECT bypass_rls(:account_id)"),
                {"account_id": -1}
            )
        assert "Invalid account_id" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data_tenant_isolation(self, db_session: AsyncSession, test_data: Tuple):
        """Test that cleanup_old_data respects tenant isolation."""
        accounts, products, interactions = test_data
        
        # Get initial counts for each account
        account1_id = accounts[0].account_id
        account2_id = accounts[1].account_id
        
        # Count old interactions for account 1
        result = await db_session.execute(
            text("SELECT COUNT(*) FROM interactions WHERE account_id = :account_id AND created_at < NOW() - INTERVAL '100 days'"),
            {"account_id": account1_id}
        )
        account1_old_interactions = result.scalar_one()
        
        # Count old interactions for account 2
        result = await db_session.execute(
            text("SELECT COUNT(*) FROM interactions WHERE account_id = :account_id AND created_at < NOW() - INTERVAL '100 days'"),
            {"account_id": account2_id}
        )
        account2_old_interactions = result.scalar_one()
        
        # Verify we have old interactions for both accounts
        assert account1_old_interactions > 0
        assert account2_old_interactions > 0
        
        # Run cleanup for account 1 only
        result = await db_session.execute(
            text("SELECT cleanup_old_data(:account_id, :days_old)"),
            {"account_id": account1_id, "days_old": 100}
        )
        cleanup_result = result.scalar_one()
        
        # Parse the JSON result
        cleanup_data = json.loads(cleanup_result)
        assert cleanup_data["account_id"] == account1_id
        assert cleanup_data["deleted_interactions"] > 0
        
        # Verify account 1's old interactions are deleted
        result = await db_session.execute(
            text("SELECT COUNT(*) FROM interactions WHERE account_id = :account_id AND created_at < NOW() - INTERVAL '100 days'"),
            {"account_id": account1_id}
        )
        account1_old_interactions_after = result.scalar_one()
        assert account1_old_interactions_after == 0
        
        # Verify account 2's old interactions are still there
        result = await db_session.execute(
            text("SELECT COUNT(*) FROM interactions WHERE account_id = :account_id AND created_at < NOW() - INTERVAL '100 days'"),
            {"account_id": account2_id}
        )
        account2_old_interactions_after = result.scalar_one()
        assert account2_old_interactions_after == account2_old_interactions
    
    @pytest.mark.asyncio
    async def test_sql_injection_protection(self, db_session: AsyncSession, test_data: Tuple):
        """Test that functions are protected against SQL injection."""
        accounts, _, _ = test_data
        account_id = accounts[0].account_id
        
        # Attempt SQL injection in days_old parameter
        injection_attempts = [
            "30; DELETE FROM interactions; --",
            "30); DELETE FROM interactions; --",
            "30 OR 1=1",
            "30' OR '1'='1",
            "30 UNION SELECT 1"
        ]
        
        for injection in injection_attempts:
            # This should raise an exception due to parameter validation
            with pytest.raises(Exception):
                await db_session.execute(
                    text("SELECT cleanup_old_data(:account_id, :days_old)"),
                    {"account_id": account_id, "days_old": injection}
                )
    
    @pytest.mark.asyncio
    async def test_cleanup_old_data_functionality(self, db_session: AsyncSession, test_data: Tuple):
        """Test that cleanup_old_data correctly cleans up old data."""
        accounts, products, _ = test_data
        account_id = accounts[0].account_id
        
        # Run cleanup with 150 days threshold
        result = await db_session.execute(
            text("SELECT cleanup_old_data(:account_id, :days_old)"),
            {"account_id": account_id, "days_old": 150}
        )
        cleanup_result = result.scalar_one()
        cleanup_data = json.loads(cleanup_result)
        
        # Verify results
        assert cleanup_data["account_id"] == account_id
        assert cleanup_data["days_old"] == 150
        assert "deleted_interactions" in cleanup_data
        assert "archived_products" in cleanup_data
        assert "inactivated_users" in cleanup_data
        assert "timestamp" in cleanup_data
        
        # Verify old products are archived
        result = await db_session.execute(
            text("SELECT COUNT(*) FROM products WHERE account_id = :account_id AND status = 'archived'"),
            {"account_id": account_id}
        )
        archived_products = result.scalar_one()
        assert archived_products > 0
