from typing import Dict, Any, Optional
import asyncio
from datetime import datetime
import json
import joblib
import os
from google.cloud import storage
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, desc
from src.core.config import settings
from src.db import models
from src.utils.base_logger import log_info, log_error
from src.core.redis_utils import get_redis
from redis.asyncio import Redis


class ModelArtifactManager:
    """Gestor de artefactos de modelos"""

    def __init__(self):
        self.storage_client = storage.Client() if settings.ENV == "production" else None
        self.bucket_name = settings.GCS_BUCKET_NAME
        self._redis: Optional[Redis] = None
        self._cache_ttl = 3600  # 1 hora

    async def _get_redis(self) -> Redis:
        """Obtiene la conexión a Redis"""
        if not self._redis:
            self._redis = await get_redis()
        return self._redis

    def _get_cache_key(self, account_id: int, model_type: str, key_type: str) -> str:
        """Genera la clave de caché para un modelo"""
        return f"model:{account_id}:{model_type}:{key_type}"

    async def save_artifacts(
        self,
        db: AsyncSession,
        account_id: int,
        model_artifacts: Dict[str, Any],
        parameters: Dict[str, Any],
    ) -> str:
        """
        Guarda los artefactos del modelo y sus metadatos.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_artifacts: Diccionario con los artefactos del modelo
            parameters: Parámetros de entrenamiento

        Returns:
            Ruta donde se guardaron los artefactos
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            artifacts_path = f"models/account_{account_id}/{timestamp}"

            # Guardar artefactos
            if settings.ENV == "production":
                await self._save_to_gcs(artifacts_path, model_artifacts)
            else:
                await self._save_to_local(artifacts_path, model_artifacts)

            # Crear registro de metadatos
            model_metadata = models.ModelMetadata(
                account_id=account_id,
                artifact_name=f"hybrid_recommender_{datetime.now().strftime('%Y%m%d')}",
                artifact_version=f"1.0.{datetime.now().strftime('%H%M%S')}",
                description=f"Hybrid recommender model trained on {datetime.now().isoformat()}",
                parameters=parameters,
                performance_metrics=model_artifacts.get("performance_metrics", {}),
                artifacts_path=artifacts_path,
            )

            db.add(model_metadata)
            await db.commit()
            await db.refresh(model_metadata)

            # Cachear solo los factores y métricas, no el modelo completo
            await self._cache_model_factors(account_id, model_artifacts)

            log_info(f"Artefactos guardados en {artifacts_path}")
            return artifacts_path

        except Exception as e:
            log_error(f"Error guardando artefactos: {str(e)}")
            raise

    async def _cache_model_factors(
        self, account_id: int, model_artifacts: Dict[str, Any]
    ) -> None:
        """Cachea los factores del modelo y métricas en Redis"""
        try:
            redis = await self._get_redis()

            # Cachear factores colaborativos
            if "collaborative" in model_artifacts:
                collab_factors = {
                    "user_factors": model_artifacts["collaborative"].get(
                        "user_factors", {}
                    ),
                    "item_factors": model_artifacts["collaborative"].get(
                        "item_factors", {}
                    ),
                    "metrics": model_artifacts["collaborative"].get("metrics", {}),
                }
                cache_key = self._get_cache_key(account_id, "collaborative", "factors")
                await redis.setex(
                    cache_key, self._cache_ttl, json.dumps(collab_factors)
                )

            # Cachear factores de contenido
            if "content" in model_artifacts:
                content_factors = {
                    "item_embeddings": model_artifacts["content"].get(
                        "item_embeddings", {}
                    ),
                    "metrics": model_artifacts["content"].get("metrics", {}),
                }
                cache_key = self._get_cache_key(account_id, "content", "factors")
                await redis.setex(
                    cache_key, self._cache_ttl, json.dumps(content_factors)
                )

            log_info(f"Factores cacheados para account_id={account_id}")

        except Exception as e:
            log_error(f"Error cacheando factores: {str(e)}")
            # No propagamos el error para no interrumpir el flujo principal

    async def get_artifacts(
        self, db: AsyncSession, account_id: int, model_type: str
    ) -> Optional[Dict[str, Any]]:
        """
        Obtiene los artefactos de un modelo.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            model_type: Tipo de modelo ('collaborative' o 'content')

        Returns:
            Diccionario con los artefactos o None si no existen
        """
        try:
            # Intentar obtener factores de caché
            redis = await self._get_redis()
            cache_key = self._get_cache_key(account_id, model_type, "factors")
            cached_factors = await redis.get(cache_key)

            if cached_factors:
                return json.loads(cached_factors)

            # Si no está en caché, obtener de base de datos
            result = await db.execute(
                select(models.ModelMetadata)
                .filter(models.ModelMetadata.account_id == account_id)
                .order_by(desc(models.ModelMetadata.created_at))
            )

            metadata = result.scalar_one_or_none()
            if not metadata:
                return None

            # Cargar artefactos completos
            if settings.ENV == "production":
                artifacts = await self._load_from_gcs(metadata.artifacts_path)
            else:
                artifacts = await self._load_from_local(metadata.artifacts_path)

            # Extraer y cachear factores
            if model_type in artifacts:
                await self._cache_model_factors(account_id, artifacts)
                return artifacts[model_type]

            return None

        except Exception as e:
            log_error(f"Error obteniendo artefactos: {str(e)}")
            raise

    async def invalidate_cache(
        self, account_id: Optional[int] = None, model_type: Optional[str] = None
    ) -> None:
        """
        Invalida la caché de modelos.

        Args:
            account_id: ID de la cuenta (opcional)
            model_type: Tipo de modelo (opcional)
        """
        try:
            redis = await self._get_redis()

            if account_id is None:
                # Invalidar toda la caché
                pattern = "model:*"
                keys = await redis.keys(pattern)
                if keys:
                    await redis.delete(*keys)
            else:
                # Invalidar modelos de una cuenta
                if model_type:
                    # Invalidar un tipo específico
                    cache_key = self._get_cache_key(account_id, model_type, "factors")
                    await redis.delete(cache_key)
                else:
                    # Invalidar todos los tipos
                    pattern = f"model:{account_id}:*"
                    keys = await redis.keys(pattern)
                    if keys:
                        await redis.delete(*keys)

            log_info(
                f"Caché invalidada para account_id={account_id}, "
                f"model_type={model_type}"
            )

        except Exception as e:
            log_error(f"Error invalidando caché: {str(e)}")
            raise

    async def _save_to_gcs(self, path: str, artifacts: Dict[str, Any]):
        """Guarda artefactos en Google Cloud Storage"""
        try:
            bucket = self.storage_client.bucket(self.bucket_name)

            # Guardar modelo
            model_blob = bucket.blob(f"{path}/model.joblib")
            await asyncio.to_thread(
                model_blob.upload_from_string, joblib.dumps(artifacts)
            )

            # Guardar metadatos
            metadata_blob = bucket.blob(f"{path}/metadata.json")
            metadata = {
                "timestamp": datetime.now().isoformat(),
                "performance_metrics": artifacts.get("performance_metrics", {}),
                "data_stats": artifacts.get("data_stats", {}),
            }
            await asyncio.to_thread(
                metadata_blob.upload_from_string, json.dumps(metadata)
            )

        except Exception as e:
            log_error(f"Error guardando en GCS: {str(e)}")
            raise

    async def _save_to_local(self, path: str, artifacts: Dict[str, Any]):
        """Guarda artefactos en sistema de archivos local"""
        try:
            os.makedirs(path, exist_ok=True)

            # Guardar modelo
            model_path = os.path.join(path, "model.joblib")
            await asyncio.to_thread(joblib.dump, artifacts, model_path)

            # Guardar metadatos
            metadata_path = os.path.join(path, "metadata.json")
            metadata = {
                "timestamp": datetime.now().isoformat(),
                "performance_metrics": artifacts.get("performance_metrics", {}),
                "data_stats": artifacts.get("data_stats", {}),
            }
            await asyncio.to_thread(
                lambda: json.dump(metadata, open(metadata_path, "w"))
            )

        except Exception as e:
            log_error(f"Error guardando localmente: {str(e)}")
            raise

    async def _load_from_gcs(self, path: str) -> Dict[str, Any]:
        """Carga artefactos desde Google Cloud Storage"""
        try:
            bucket = self.storage_client.bucket(self.bucket_name)
            model_blob = bucket.blob(f"{path}/model.joblib")

            model_data = await asyncio.to_thread(model_blob.download_as_bytes)

            return joblib.loads(model_data)

        except Exception as e:
            log_error(f"Error cargando desde GCS: {str(e)}")
            raise

    async def _load_from_local(self, path: str) -> Dict[str, Any]:
        """Carga artefactos desde sistema de archivos local"""
        try:
            model_path = os.path.join(path, "model.joblib")

            return await asyncio.to_thread(joblib.load, model_path)

        except Exception as e:
            log_error(f"Error cargando localmente: {str(e)}")
            raise
