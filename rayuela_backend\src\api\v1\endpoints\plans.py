"""
Endpoints for retrieving plan information.
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Dict, Any, List
from pydantic import BaseModel

from src.db.enums import PLAN_LIMITS, SubscriptionPlan
from src.core.config import settings

router = APIRouter()


class PlanInfo(BaseModel):
    """Plan information model."""
    id: str
    name: str
    description: str
    price: str
    features: List[str]
    limits: Dict[str, Any]
    stripe_price_id: str = None
    contact_required: bool = False
    recommended: bool = False


@router.get("/", response_model=Dict[str, PlanInfo])
async def get_available_plans():
    """
    Get all available subscription plans with their details.
    
    Returns:
        Dict with plan information for all available plans.
    """
    try:
        # Helper function to format price from PLAN_LIMITS
        def format_price(plan_type: SubscriptionPlan) -> str:
            """Format price for display based on plan type and centralized configuration."""
            if plan_type == SubscriptionPlan.FREE:
                return "Gratis"
            elif plan_type == SubscriptionPlan.ENTERPRISE:
                return "Contactar"
            else:
                # Get price from PLAN_LIMITS (centralized source of truth)
                price_ars = PLAN_LIMITS.get(plan_type, {}).get("price_ars", 0.0)
                if price_ars > 0:
                    return f"${price_ars:,.0f} ARS/mes"
                else:
                    return "Contactar"

        # Plan display information (without hardcoded prices)
        plan_display_info = {
            SubscriptionPlan.FREE: {
                "name": "Free",
                "description": "Para probar la plataforma",
                "features": [
                    "1,000 llamadas API/mes",
                    "10 MB de almacenamiento",
                    "Soporte por email"
                ],
                "recommended": False,
                "contact_required": False,
                "stripe_price_id": None
            },
            SubscriptionPlan.STARTER: {
                "name": "Starter",
                "description": "Para startups y pequeños equipos",
                "features": [
                    "10,000 llamadas API/mes",
                    "100 MB de almacenamiento",
                    "Soporte por email",
                    "Acceso a todas las funciones básicas"
                ],
                "recommended": True,
                "contact_required": False,
                "stripe_price_id": settings.STRIPE_PRICE_IDS.get("STARTER")
            },
            SubscriptionPlan.PRO: {
                "name": "Pro",
                "description": "Para equipos profesionales",
                "features": [
                    "100,000 llamadas API/mes",
                    "1 GB de almacenamiento",
                    "Soporte prioritario",
                    "Acceso a todas las funciones avanzadas",
                    "Análisis detallado de datos"
                ],
                "recommended": False,
                "contact_required": False,
                "stripe_price_id": settings.STRIPE_PRICE_IDS.get("PRO")
            },
            SubscriptionPlan.ENTERPRISE: {
                "name": "Enterprise",
                "description": "Para grandes organizaciones",
                "features": [
                    "Llamadas API ilimitadas",
                    "Almacenamiento personalizado",
                    "Soporte dedicado 24/7",
                    "Implementación personalizada",
                    "SLA garantizado"
                ],
                "recommended": False,
                "contact_required": True,
                "stripe_price_id": settings.STRIPE_PRICE_IDS.get("ENTERPRISE")
            }
        }
        
        # Combine plan display info with limits from PLAN_LIMITS
        result = {}
        for plan_type, limits in PLAN_LIMITS.items():
            display_info = plan_display_info.get(plan_type, {})
            
            # Format storage limits for display
            storage_mb = limits.get("storage_limit", 0) / (1024 * 1024)
            storage_gb = limits.get("storage_limit", 0) / (1024 * 1024 * 1024)
            
            # Format training data limit for display
            training_data_limit = limits.get("training_data_limit", 0)
            training_data_limit_formatted = (
                f"{training_data_limit:,} interactions" 
                if training_data_limit > 0 
                else "Unlimited"
            )
            
            # Format max items and users for display
            max_items = limits.get("max_items", 0)
            max_items_formatted = f"{max_items:,}" if max_items > 0 else "Unlimited"
            
            max_users = limits.get("max_users", 0)
            max_users_formatted = f"{max_users:,}" if max_users > 0 else "Unlimited"
            
            # Create plan info object
            result[plan_type.value] = PlanInfo(
                id=plan_type.value,
                name=display_info.get("name", plan_type.value),
                description=display_info.get("description", ""),
                price=format_price(plan_type),  # Use centralized pricing
                features=display_info.get("features", []),
                limits={
                    "api_calls": limits.get("api_calls_limit", 0),
                    "storage_bytes": limits.get("storage_limit", 0),
                    "storage_mb": round(storage_mb, 2),
                    "storage_gb": round(storage_gb, 4),
                    "max_requests_per_minute": limits.get("max_requests_per_minute", 0),
                    "max_concurrent_requests": limits.get("max_concurrent_requests", 0),
                    "training_frequency": limits.get("training_frequency", "manual"),
                    "training_data_limit": training_data_limit,
                    "training_data_limit_formatted": training_data_limit_formatted,
                    "max_items": max_items,
                    "max_items_formatted": max_items_formatted,
                    "max_users": max_users,
                    "max_users_formatted": max_users_formatted,
                    "recommendation_cache_ttl": limits.get("recommendation_cache_ttl", 3600),
                    "available_models": limits.get("available_models", []),
                },
                stripe_price_id=display_info.get("stripe_price_id"),
                contact_required=display_info.get("contact_required", False),
                recommended=display_info.get("recommended", False)
            )
        
        return result
    
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving plan information: {str(e)}",
        )
