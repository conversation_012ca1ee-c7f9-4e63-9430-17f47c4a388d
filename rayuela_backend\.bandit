[bandit]
# Configuración de Bandit para Rayuela
# Security scanning estricto

# Tests a ejecutar (todos por defecto)
tests = B101,B102,B103,B104,B105,B106,B107,B108,B110,B112,B201,B301,B302,B303,B304,B305,B306,B307,B308,B309,B310,B311,B312,B313,B314,B315,B316,B317,B318,B319,B320,B321,B322,B323,B324,B325,B401,B402,B403,B404,B405,B406,B407,B408,B409,B410,B411,B412,B413,B501,B502,B503,B504,B505,B506,B507,B601,B602,B603,B604,B605,B606,B607,B608,B609,B610,B611,B701,B702,B703

# Directorios a incluir
include = src/

# Archivos y directorios a excluir
exclude_dirs = 
    tests,
    alembic/versions,
    .venv,
    venv,
    .pytest_cache,
    __pycache__

# Nivel mínimo de severidad (low, medium, high)
# Solo reportar issues de medium y high
severity = medium

# Nivel mínimo de confianza (low, medium, high)
# Solo reportar issues con medium/high confidence
confidence = medium

# Formato de output
format = json

# Tests específicos a ignorar (con justificación)
skips = 
    # B101: assert_used - Tests pueden usar assert
    # B603: subprocess_without_shell_equals_true - Permitido en casos específicos controlados

# Configuración para tests específicos
[bandit.B101]
# Skip assert statements in test files
skip_file = tests/*

[bandit.B603]
# subprocess calls - revisar manualmente
# Solo permitir en scripts de administración controlados

[bandit.B105]
# hardcoded_password_string
# Ignorar en archivos de configuración de ejemplo

[bandit.B106] 
# hardcoded_password_funcarg
# Ignorar en tests que usan passwords de prueba

[bandit.B108]
# hardcoded_tmp_directory
# Revisar uso de directorios temporales

[bandit.B201]
# flask_debug_true
# Asegurar que debug está deshabilitado en producción

[bandit.B608]
# hardcoded_sql_expressions
# CRÍTICO: Revisar todas las queries SQL para prevenir injection 