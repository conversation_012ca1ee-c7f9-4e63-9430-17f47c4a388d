"""
Tests unitarios y de integración para BaseRepository enfocados en multi-tenancy.
Verifica que _add_tenant_filter se aplica correctamente en todas las operaciones.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import Query
from sqlalchemy import Column, Integer, String

from src.db.repositories.base import BaseRepository
from src.db.models import Product, Account, EndUser
from src.db.base import Base


# Modelo de prueba sin tenant (para tests de repository global)
class GlobalModel(Base):
    __tablename__ = "global_test_table"

    id = Column(Integer, primary_key=True)
    name = Column(String(100))


# Modelo de prueba con tenant
class TenantModel(Base):
    __tablename__ = "tenant_test_table"

    id = Column(Integer, primary_key=True)
    account_id = Column(Integer)  # Campo que lo hace tenant-scoped
    name = Column(String(100))


class TestBaseRepositoryTenant:
    """Tests para validar el comportamiento de multi-tenancy en BaseRepository."""

    @pytest.fixture
    def mock_db_session(self):
        """Mock de sesión de base de datos."""
        session = AsyncMock(spec=AsyncSession)
        return session

    @pytest.fixture
    def tenant_repository(self, mock_db_session):
        """Repository para modelo tenant-scoped."""
        return BaseRepository(db=mock_db_session, account_id=123, model=TenantModel)

    @pytest.fixture
    def global_repository(self, mock_db_session):
        """Repository para modelo global (sin tenant)."""
        return BaseRepository(db=mock_db_session, account_id=None, model=GlobalModel)

    @pytest.fixture
    def product_repository(self, mock_db_session):
        """Repository para modelo Product real."""
        return BaseRepository(db=mock_db_session, account_id=456, model=Product)

    def test_repository_identifies_tenant_scoped_models(
        self, tenant_repository, global_repository
    ):
        """Test que el repository identifica correctamente modelos tenant-scoped."""
        # Modelo con account_id debería ser tenant-scoped
        assert tenant_repository.is_tenant_scoped is True

        # Modelo sin account_id no debería ser tenant-scoped
        assert global_repository.is_tenant_scoped is False

    def test_repository_real_models_tenant_detection(self, mock_db_session):
        """Test detección de tenant en modelos reales."""
        # Product tiene account_id -> tenant-scoped
        product_repo = BaseRepository(mock_db_session, account_id=123, model=Product)
        assert product_repo.is_tenant_scoped is True

        # Account no tiene account_id propio -> no tenant-scoped
        account_repo = BaseRepository(mock_db_session, account_id=None, model=Account)
        assert account_repo.is_tenant_scoped is False

        # EndUser tiene account_id -> tenant-scoped
        enduser_repo = BaseRepository(mock_db_session, account_id=123, model=EndUser)
        assert enduser_repo.is_tenant_scoped is True

    async def test_validate_tenant_access_with_account_id(self, tenant_repository):
        """Test validación de acceso con account_id válido."""
        # No debería lanzar excepción
        await tenant_repository._validate_tenant_access()

    async def test_validate_tenant_access_without_account_id_fails(
        self, mock_db_session
    ):
        """Test que falla la validación sin account_id para modelo tenant-scoped."""
        repo = BaseRepository(
            db=mock_db_session, account_id=None, model=TenantModel  # Sin account_id
        )

        with pytest.raises(ValueError, match="Account ID is required"):
            await repo._validate_tenant_access()

    async def test_validate_tenant_access_global_model_without_account_id(
        self, global_repository
    ):
        """Test que modelos globales no requieren account_id."""
        # No debería lanzar excepción aunque no tenga account_id
        await global_repository._validate_tenant_access()

    def test_add_tenant_filter_with_tenant_scoped_model(self, tenant_repository):
        """Test que _add_tenant_filter aplica filtro para modelos tenant-scoped."""
        # Mock query
        mock_query = Mock()
        mock_query.filter.return_value = mock_query

        # Aplicar filtro
        filtered_query = tenant_repository._add_tenant_filter(mock_query)

        # Verificar que se aplicó el filtro
        mock_query.filter.assert_called_once()
        assert filtered_query == mock_query

    def test_add_tenant_filter_with_global_model(self, global_repository):
        """Test que _add_tenant_filter no aplica filtro para modelos globales."""
        # Mock query
        mock_query = Mock()

        # Aplicar filtro
        filtered_query = global_repository._add_tenant_filter(mock_query)

        # Verificar que NO se aplicó filtro
        mock_query.filter.assert_not_called()
        assert filtered_query == mock_query

    def test_add_tenant_filter_without_account_id_raises_error(self, mock_db_session):
        """Test que _add_tenant_filter lanza error sin account_id para modelo tenant-scoped."""
        repo = BaseRepository(db=mock_db_session, account_id=None, model=TenantModel)

        mock_query = Mock()

        with pytest.raises(ValueError, match="Account ID es obligatorio"):
            repo._add_tenant_filter(mock_query)

    async def test_get_all_applies_tenant_filter(
        self, tenant_repository, mock_db_session
    ):
        """Test que get_all aplica filtro de tenant."""
        # Mock result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db_session.execute.return_value = mock_result

        # Ejecutar get_all
        await tenant_repository.get_all()

        # Verificar que se ejecutó una query con filtro
        mock_db_session.execute.assert_called_once()

        # Verificar que la query incluye filtro de tenant
        call_args = mock_db_session.execute.call_args[0][0]
        # La query debería tener filtros aplicados
        assert call_args is not None

    async def test_get_by_filters_applies_tenant_filter(
        self, tenant_repository, mock_db_session
    ):
        """Test que get_by_filters aplica filtro de tenant."""
        # Mock result
        mock_result = Mock()
        mock_result.scalars.return_value.all.return_value = []
        mock_db_session.execute.return_value = mock_result

        # Ejecutar get_by_filters
        filters = {"name": "test"}
        await tenant_repository.get_by_filters(filters)

        # Verificar que se ejecutó query
        mock_db_session.execute.assert_called_once()

    async def test_create_adds_account_id_to_tenant_model(
        self, tenant_repository, mock_db_session
    ):
        """Test que create añade account_id para modelos tenant-scoped."""
        # Mock flush y refresh
        mock_db_session.flush.return_value = None
        mock_db_session.refresh.return_value = None

        # Datos de entrada
        create_data = {"name": "test item"}

        # Ejecutar create
        result = await tenant_repository.create(create_data)

        # Verificar que se añadió el objeto a la sesión
        mock_db_session.add.assert_called_once()

        # Verificar que el objeto creado incluye account_id
        added_obj = mock_db_session.add.call_args[0][0]
        assert hasattr(added_obj, "account_id")
        assert added_obj.account_id == 123

    async def test_create_does_not_add_account_id_to_global_model(
        self, global_repository, mock_db_session
    ):
        """Test que create no añade account_id para modelos globales."""
        # Mock flush y refresh
        mock_db_session.flush.return_value = None
        mock_db_session.refresh.return_value = None

        # Datos de entrada
        create_data = {"name": "global item"}

        # Ejecutar create
        result = await global_repository.create(create_data)

        # Verificar que se añadió el objeto
        mock_db_session.add.assert_called_once()

        # Verificar que el objeto NO incluye account_id
        added_obj = mock_db_session.add.call_args[0][0]
        assert not hasattr(added_obj, "account_id") or added_obj.account_id is None

    async def test_bulk_create_adds_account_id_to_all_items(
        self, tenant_repository, mock_db_session
    ):
        """Test que bulk_create añade account_id a todos los items."""
        # Mock add_all, flush y refresh
        mock_db_session.add_all.return_value = None
        mock_db_session.flush.return_value = None
        mock_db_session.refresh.return_value = None

        # Datos de entrada
        bulk_data = [{"name": "item1"}, {"name": "item2"}, {"name": "item3"}]

        # Ejecutar bulk_create
        result = await tenant_repository.bulk_create(bulk_data)

        # Verificar que se añadieron objetos
        mock_db_session.add_all.assert_called_once()

        # Verificar que todos los objetos tienen account_id
        added_objects = mock_db_session.add_all.call_args[0][0]
        assert len(added_objects) == 3
        for obj in added_objects:
            assert hasattr(obj, "account_id")
            assert obj.account_id == 123

    async def test_get_by_id_uses_tenant_validation(
        self, tenant_repository, mock_db_session
    ):
        """Test que get_by_id valida tenant access."""
        # Mock get
        mock_db_session.get.return_value = None

        # Ejecutar get_by_id
        await tenant_repository.get_by_id(1)

        # Verificar que se usó get con validación
        mock_db_session.get.assert_called_once()

    async def test_update_validates_tenant_access(
        self, tenant_repository, mock_db_session
    ):
        """Test que update valida acceso de tenant."""
        # Mock objeto existente
        mock_obj = Mock()
        mock_obj.account_id = 123
        mock_obj.name = "old name"

        # Mock get_by_id para retornar el objeto
        with patch.object(tenant_repository, "get_by_id", return_value=mock_obj):
            # Mock flush y refresh
            mock_db_session.flush.return_value = None
            mock_db_session.refresh.return_value = None

            # Ejecutar update
            update_data = {"name": "new name"}
            result = await tenant_repository.update(1, update_data)

            # Verificar que se actualizó el objeto
            assert mock_obj.name == "new name"
            assert result == mock_obj

    async def test_soft_delete_validates_tenant_access(self, tenant_repository):
        """Test que soft_delete valida acceso de tenant."""
        # Mock prepare_soft_delete
        with patch.object(
            tenant_repository, "prepare_soft_delete", return_value=Mock()
        ) as mock_prepare:
            # Ejecutar soft_delete
            result = await tenant_repository.soft_delete(1)

            # Verificar que se llamó prepare_soft_delete
            mock_prepare.assert_called_once_with(1)
            assert result is True

    async def test_count_by_filters_applies_tenant_filter(
        self, tenant_repository, mock_db_session
    ):
        """Test que count_by_filters aplica filtro de tenant."""
        # Mock result para count
        mock_result = Mock()
        mock_result.scalar.return_value = 5
        mock_db_session.execute.return_value = mock_result

        # Ejecutar count_by_filters
        filters = {"name": "test"}
        count = await tenant_repository.count_by_filters(filters)

        # Verificar que se ejecutó query con filtro
        mock_db_session.execute.assert_called_once()
        assert count == 5

    def test_repository_initialization_validation(self, mock_db_session):
        """Test validación en inicialización del repository."""
        # Repository tenant-scoped debe aceptar account_id
        tenant_repo = BaseRepository(mock_db_session, account_id=123, model=TenantModel)
        assert tenant_repo.account_id == 123
        assert tenant_repo.is_tenant_scoped is True

        # Repository global puede no tener account_id
        global_repo = BaseRepository(
            mock_db_session, account_id=None, model=GlobalModel
        )
        assert global_repo.account_id is None
        assert global_repo.is_tenant_scoped is False

    async def test_error_handling_preserves_tenant_context(
        self, tenant_repository, mock_db_session
    ):
        """Test que el manejo de errores preserva el contexto de tenant."""
        # Simular error en la base de datos
        mock_db_session.execute.side_effect = Exception("DB Error")

        # Ejecutar operación que debería fallar
        with pytest.raises(Exception):
            await tenant_repository.get_all()

        # Verificar que el repository mantiene su configuración
        assert tenant_repository.account_id == 123
        assert tenant_repository.is_tenant_scoped is True

    async def test_repository_respects_model_inheritance(self, mock_db_session):
        """Test que el repository respeta la herencia de modelos."""
        # Usar modelo real Product que hereda mixins
        product_repo = BaseRepository(mock_db_session, account_id=789, model=Product)

        # Verificar que detecta correctamente que Product es tenant-scoped
        assert product_repo.is_tenant_scoped is True
        assert product_repo.account_id == 789

    async def test_repository_operations_with_different_account_ids(
        self, mock_db_session
    ):
        """Test que repositories con diferentes account_ids están aislados."""
        # Crear dos repositories con diferentes account_ids
        repo_1 = BaseRepository(mock_db_session, account_id=100, model=TenantModel)
        repo_2 = BaseRepository(mock_db_session, account_id=200, model=TenantModel)

        # Verificar que tienen configuraciones independientes
        assert repo_1.account_id == 100
        assert repo_2.account_id == 200
        assert repo_1.account_id != repo_2.account_id

        # Ambos deberían ser tenant-scoped
        assert repo_1.is_tenant_scoped is True
        assert repo_2.is_tenant_scoped is True

    async def test_patch_method_validates_tenant(
        self, tenant_repository, mock_db_session
    ):
        """Test que el método patch valida tenant access."""
        # Mock objeto existente
        mock_obj = Mock()
        mock_obj.account_id = 123
        mock_obj.name = "original"

        # Mock get_by_id
        with patch.object(tenant_repository, "get_by_id", return_value=mock_obj):
            # Mock flush y refresh
            mock_db_session.flush.return_value = None
            mock_db_session.refresh.return_value = None

            # Ejecutar patch
            patch_data = {"name": "patched"}
            result = await tenant_repository.patch(1, patch_data)

            # Verificar actualización
            assert mock_obj.name == "patched"
            assert result == mock_obj

    def test_security_logging_for_missing_account_id(self, mock_db_session):
        """Test que se registra alerta de seguridad cuando falta account_id."""
        repo = BaseRepository(mock_db_session, account_id=None, model=TenantModel)

        with patch("src.db.repositories.base.log_error") as mock_log_error:
            mock_query = Mock()

            with pytest.raises(ValueError):
                repo._add_tenant_filter(mock_query)

            # Verificar que se registró la alerta de seguridad
            mock_log_error.assert_called_once()
            error_message = mock_log_error.call_args[0][0]
            assert "ALERTA DE SEGURIDAD" in error_message
            assert "TenantModel" in error_message
