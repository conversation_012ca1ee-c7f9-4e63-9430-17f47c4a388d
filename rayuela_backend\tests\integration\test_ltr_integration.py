"""
Tests de integración para el modelo Learning-to-Rank (LTR) con el objetivo correcto.
"""
import pytest
import pytest_asyncio
import numpy as np
import pandas as pd
from unittest.mock import patch, MagicMock, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from src.ml_pipeline.training_pipeline import TrainingPipeline
from src.ml_pipeline.learning_to_rank import LearningToRankModel
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.ml_pipeline.evaluation import RecommendationEvaluator


class TestLTRIntegration:
    """Tests de integración para el modelo LTR con el pipeline de entrenamiento."""

    @pytest.fixture
    def mock_artifact_manager(self):
        """Fixture para mockear el gestor de artefactos."""
        mock = MagicMock(spec=ModelArtifactManager)
        mock.save_artifacts = AsyncMock(return_value="test/path")
        return mock

    @pytest.fixture
    def mock_metrics_tracker(self):
        """Fixture para mockear el tracker de métricas."""
        mock = MagicMock(spec=MetricsTracker)
        mock.register_metrics = AsyncMock()
        return mock

    @pytest.fixture
    def mock_evaluator(self):
        """Fixture para mockear el evaluador."""
        mock = MagicMock(spec=RecommendationEvaluator)
        mock.evaluate = MagicMock(return_value={"precision": 0.8, "recall": 0.7})
        return mock

    @pytest.fixture
    def training_pipeline(self, mock_artifact_manager, mock_metrics_tracker, mock_evaluator):
        """Fixture para crear el pipeline de entrenamiento."""
        return TrainingPipeline(
            artifact_manager=mock_artifact_manager,
            metrics_tracker=mock_metrics_tracker,
            evaluator=mock_evaluator
        )

    @pytest.mark.asyncio
    async def test_ltr_uses_real_relevance_in_pipeline(self, training_pipeline):
        """Test que verifica que el pipeline usa relevancia real para entrenar el modelo LTR."""
        # Mockear el método _generate_recommendations para devolver recomendaciones con target_relevance
        test_recommendations = [
            {
                "user_id": 1,
                "item_id": 101,
                "collab_score": 0.8,
                "content_score": 0.6,
                "score": 0.8,
                "rank": 1,
                "model_type": "collaborative",
                "is_hit": True,
                "target_relevance": 1.0
            },
            {
                "user_id": 1,
                "item_id": 102,
                "collab_score": 0.7,
                "content_score": 0.5,
                "score": 0.7,
                "rank": 2,
                "model_type": "collaborative",
                "is_hit": False,
                "target_relevance": 0.0
            }
        ]
        
        with patch.object(training_pipeline, '_generate_recommendations', return_value=AsyncMock(return_value=test_recommendations)):
            # Mockear LearningToRankModel para verificar que se llama con los target_scores correctos
            with patch('src.ml_pipeline.learning_to_rank.LearningToRankModel') as mock_ltr_class:
                # Configurar el mock del modelo LTR
                mock_ltr = MagicMock()
                mock_ltr.train = MagicMock(return_value={"ndcg": 0.85})
                mock_ltr.predict = MagicMock(return_value=test_recommendations)
                mock_ltr_class.return_value = mock_ltr
                
                # Crear datos de prueba
                collab_artifacts = {"account_id": 1, "model": MagicMock()}
                content_artifacts = {"model": MagicMock()}
                test_interactions = pd.DataFrame({
                    "user_id": [1, 1],
                    "item_id": [101, 103],
                    "value": [1.0, 1.0]
                })
                interactions_df = pd.DataFrame({
                    "user_id": [1, 1, 2],
                    "item_id": [101, 102, 103],
                    "value": [1.0, 1.0, 1.0]
                })
                products_df = pd.DataFrame({
                    "item_id": [101, 102, 103],
                    "category": ["A", "B", "A"]
                })
                
                # Llamar al método _evaluate_models
                await training_pipeline._evaluate_models(
                    collab_artifacts=collab_artifacts,
                    content_artifacts=content_artifacts,
                    test_interactions=test_interactions,
                    interactions_df=interactions_df,
                    products_df=products_df
                )
                
                # Verificar que se creó el modelo LTR con el account_id correcto
                mock_ltr_class.assert_called_once_with(account_id=1, model_type="gbdt")
                
                # Verificar que se llamó a train con los target_scores correctos
                mock_ltr.train.assert_called_once()
                args, kwargs = mock_ltr.train.call_args
                
                # Verificar que se pasaron los target_scores
                assert "target_scores" in kwargs
                assert kwargs["target_scores"] == [1.0, 0.0]  # Valores de target_relevance
                
                # Verificar que se aplicó el modelo a las recomendaciones
                mock_ltr.predict.assert_called_once()

    @pytest.mark.asyncio
    async def test_pipeline_handles_ltr_error_gracefully(self, training_pipeline):
        """Test que verifica que el pipeline maneja errores del modelo LTR correctamente."""
        # Mockear el método _generate_recommendations
        test_recommendations = [
            {
                "user_id": 1,
                "item_id": 101,
                "collab_score": 0.8,
                "content_score": 0.6,
                "score": 0.8,
                "rank": 1,
                "model_type": "collaborative",
                "is_hit": True,
                "target_relevance": 1.0
            }
        ]
        
        with patch.object(training_pipeline, '_generate_recommendations', AsyncMock(return_value=test_recommendations)):
            # Mockear LearningToRankModel para lanzar una excepción
            with patch('src.ml_pipeline.learning_to_rank.LearningToRankModel') as mock_ltr_class:
                mock_ltr = MagicMock()
                mock_ltr.train = MagicMock(side_effect=Exception("Error de entrenamiento"))
                mock_ltr_class.return_value = mock_ltr
                
                # Crear datos de prueba
                collab_artifacts = {"account_id": 1, "model": MagicMock()}
                content_artifacts = {"model": MagicMock()}
                test_interactions = pd.DataFrame({"user_id": [1], "item_id": [101], "value": [1.0]})
                interactions_df = pd.DataFrame({"user_id": [1], "item_id": [101], "value": [1.0]})
                products_df = pd.DataFrame({"item_id": [101], "category": ["A"]})
                
                # Mockear log_error para verificar que se registra el error
                with patch('src.ml_pipeline.training_pipeline.log_error') as mock_log_error:
                    # Llamar al método _evaluate_models
                    recommendations, metrics = await training_pipeline._evaluate_models(
                        collab_artifacts=collab_artifacts,
                        content_artifacts=content_artifacts,
                        test_interactions=test_interactions,
                        interactions_df=interactions_df,
                        products_df=products_df
                    )
                    
                    # Verificar que se registró el error
                    mock_log_error.assert_called_once()
                    assert "Error entrenando modelo LTR" in mock_log_error.call_args[0][0]
                    
                    # Verificar que el pipeline continuó a pesar del error
                    assert recommendations == test_recommendations
                    assert metrics == {"precision": 0.8, "recall": 0.7}
