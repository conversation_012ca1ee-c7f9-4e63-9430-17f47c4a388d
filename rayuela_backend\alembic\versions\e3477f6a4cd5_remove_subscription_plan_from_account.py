"""remove_subscription_plan_from_account

Revision ID: e3477f6a4cd5
Revises: 48196a49d36e
Create Date: 2025-04-17 03:05:51.055646

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'e3477f6a4cd5'
down_revision: Union[str, None] = '48196a49d36e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # Eliminar la columna subscription_plan de la tabla accounts
    op.drop_column('accounts', 'subscription_plan')


def downgrade() -> None:
    """Downgrade schema."""
    # Recrear la columna subscription_plan en la tabla accounts
    op.add_column('accounts',
                  sa.Column('subscription_plan',
                           sa.Enum('FREE', 'BASIC', 'PRO', 'ENTERPRISE', name='subscriptionplan'),
                           nullable=False,
                           server_default=sa.text("'FREE'::subscriptionplan")))
