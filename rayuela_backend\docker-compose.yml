services:
  api:
    build: .
    ports:
      - "${API_PORT:-8001}:${API_PORT:-8001}"
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - API_HOST=0.0.0.0
      - API_PORT=${API_PORT:-8001}
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 1g
    deploy:
      resources:
        limits:
          memory: 2g

  # Worker for training tasks (high memory, low concurrency)
  worker-training:
    build:
      context: .
      dockerfile: docker/worker.Dockerfile
    command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=1 --max-tasks-per-child=1 --queues=training --hostname=training@%h
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
      - api
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 4g
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 6g

  # Worker for batch processing tasks (medium memory, medium concurrency)
  worker-batch:
    build:
      context: .
      dockerfile: docker/worker.Dockerfile
    command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=5 --queues=batch_processing --hostname=batch@%h
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
      - api
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 3g
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4g

  # Worker for maintenance tasks (low memory, high concurrency)
  worker-maintenance:
    build:
      context: .
      dockerfile: docker/worker.Dockerfile
    command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --queues=maintenance --hostname=maintenance@%h
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
      - api
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 2g
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 3g

  # Worker for default tasks (low memory, medium concurrency)
  worker-default:
    build:
      context: .
      dockerfile: docker/worker.Dockerfile
    command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --queues=default --hostname=default@%h
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
      - api
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 2g
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 3g

  # Celery Beat scheduler for periodic tasks
  celery-beat:
    build:
      context: .
      dockerfile: docker/worker.Dockerfile
    command: celery -A src.workers.celery_app beat --loglevel=info
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
      - worker-maintenance
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 768m
    deploy:
      resources:
        limits:
          memory: 1.5g

  db:
    image: postgres:13
    ports:
      - "5432:5432"
    env_file:
      - .env.docker
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - rayuela-network

  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rayuela-network

volumes:
  postgres_data:
  redis_data:

networks:
  rayuela-network:
    driver: bridge
