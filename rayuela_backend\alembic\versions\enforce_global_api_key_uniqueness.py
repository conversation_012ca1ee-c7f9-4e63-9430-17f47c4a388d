"""enforce_global_api_key_uniqueness

Revision ID: enforce_global_api_key_uniqueness
Revises: af19ff6c9c1a
Create Date: 2024-12-19 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'enforce_global_api_key_uniqueness'
down_revision: Union[str, None] = 'af19ff6c9c1a'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to enforce global API key hash uniqueness.
    
    This migration addresses US-SEC-003: Asegurar Unicidad Global del API Key Hash
    
    Security Issue Fixed:
    - Previously: UniqueConstraint("account_id", "api_key_hash") allowed same hash across accounts
    - Now: UniqueConstraint("api_key_hash") enforces global uniqueness
    - Prevents potential cross-tenant access vulnerabilities
    """
    
    # Step 1: Check for existing duplicate API key hashes across accounts
    # This query will help identify any existing violations before applying the constraint
    connection = op.get_bind()
    
    # Find duplicate API key hashes across different accounts
    duplicate_check = connection.execute(sa.text("""
        SELECT api_key_hash, COUNT(*) as count, 
               array_agg(account_id) as account_ids
        FROM accounts 
        WHERE api_key_hash IS NOT NULL 
        GROUP BY api_key_hash 
        HAVING COUNT(*) > 1
    """))
    
    duplicates = duplicate_check.fetchall()
    
    if duplicates:
        print("⚠️  WARNING: Found duplicate API key hashes across accounts:")
        for row in duplicates:
            print(f"   Hash: {row.api_key_hash[:10]}... appears in accounts: {row.account_ids}")
        
        # For safety, we'll regenerate API keys for duplicate accounts
        # Keep the first account's API key, regenerate for others
        for row in duplicates:
            account_ids = row.account_ids
            # Keep the first account, regenerate for others
            for account_id in account_ids[1:]:
                print(f"   Regenerating API key for account {account_id}")
                # Set api_key_hash to NULL to force regeneration
                connection.execute(sa.text("""
                    UPDATE accounts 
                    SET api_key_hash = NULL,
                        api_key_prefix = NULL,
                        api_key_last_chars = NULL,
                        api_key_created_at = NULL,
                        api_key_revealed = FALSE
                    WHERE account_id = :account_id
                """), {"account_id": account_id})
    
    # Step 2: Drop the old constraint
    try:
        op.drop_constraint("uq_account_api_key_hash", "accounts", type_="unique")
        print("✅ Dropped old constraint: uq_account_api_key_hash")
    except Exception as e:
        print(f"⚠️  Old constraint not found (this is expected): {e}")
    
    # Step 3: Create the new global uniqueness constraint
    op.create_unique_constraint(
        "uq_api_key_hash_global", 
        "accounts", 
        ["api_key_hash"]
    )
    print("✅ Created new global uniqueness constraint: uq_api_key_hash_global")
    
    # Step 4: Add a comment to document the security improvement
    op.execute(sa.text("""
        COMMENT ON CONSTRAINT uq_api_key_hash_global ON accounts IS 
        'Global uniqueness constraint for API key hashes to prevent cross-tenant access vulnerabilities. 
         Addresses US-SEC-003: Asegurar Unicidad Global del API Key Hash.'
    """))
    
    print("🔒 Security improvement applied: API key hashes are now globally unique")


def downgrade() -> None:
    """Downgrade schema to restore account-scoped API key hash uniqueness.
    
    WARNING: This downgrade reduces security by allowing duplicate API key hashes
    across different accounts, which could lead to cross-tenant access vulnerabilities.
    """
    
    print("⚠️  WARNING: Downgrading will reduce security!")
    print("   This allows duplicate API key hashes across accounts.")
    
    # Step 1: Drop the global uniqueness constraint
    op.drop_constraint("uq_api_key_hash_global", "accounts", type_="unique")
    print("❌ Dropped global uniqueness constraint: uq_api_key_hash_global")
    
    # Step 2: Recreate the old account-scoped constraint
    op.create_unique_constraint(
        "uq_account_api_key_hash", 
        "accounts", 
        ["account_id", "api_key_hash"]
    )
    print("⚠️  Restored account-scoped constraint: uq_account_api_key_hash")
    
    print("⚠️  Security downgrade completed - API key hashes are no longer globally unique")
