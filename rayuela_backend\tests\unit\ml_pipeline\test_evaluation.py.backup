import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from src.ml_pipeline.evaluation import (
    calculate_metrics,
    cross_validate,
    evaluate_recommendations,
    calculate_diversity,
    calculate_novelty
)
from src.db.models import (
    Product,
    EndUser,
    Interaction,
    Recommendation
)

@pytest.fixture
async def sample_data(db_session: AsyncSession):
    """Fixture para crear datos de muestra."""
    # Crear usuarios
    users = []
    for i in range(10):
        user = EndUser(
            account_id=1,
            email=f"user{i}@example.com",
            name=f"User {i}"
        )
        db_session.add(user)
        users.append(user)
    
    # Crear productos
    products = []
    for i in range(20):
        product = Product(
            account_id=1,
            name=f"Product {i}",
            description=f"Description {i}",
            price=10.0,
            category=f"Category {i % 5}"  # 5 categorías diferentes
        )
        db_session.add(product)
        products.append(product)
    
    await db_session.commit()
    
    # Crear interacciones
    interactions = []
    for user in users:
        for product in products[:5]:  # Cada usuario interactúa con 5 productos
            interaction = Interaction(
                account_id=1,
                end_user_id=user.id,
                product_id=product.id,
                interaction_type="view",
                timestamp=datetime.utcnow() - timedelta(days=np.random.randint(0, 30))
            )
            db_session.add(interaction)
            interactions.append(interaction)
    
    await db_session.commit()
    return users, products, interactions

def test_calculate_metrics():
    """Test para calcular métricas de evaluación."""
    # Datos de prueba
    y_true = np.array([1, 0, 1, 1, 0, 1, 0, 1])
    y_pred = np.array([1, 0, 1, 0, 0, 1, 1, 1])
    
    # Calcular métricas
    metrics = calculate_metrics(y_true, y_pred)
    
    # Verificar métricas
    assert isinstance(metrics, dict)
    assert "accuracy" in metrics
    assert "precision" in metrics
    assert "recall" in metrics
    assert "f1" in metrics
    assert all(0 <= v <= 1 for v in metrics.values())
    
    # Verificar valores específicos
    assert metrics["accuracy"] == 0.75  # 6/8 correctos
    assert metrics["precision"] == 0.8  # 4/5 positivos correctos
    assert metrics["recall"] == 0.8  # 4/5 positivos reales detectados
    assert abs(metrics["f1"] - 0.8) < 0.01  # F1 score para precision=recall=0.8

def test_cross_validate():
    """Test para validación cruzada."""
    # Datos de prueba
    X = np.array([[1, 2], [3, 4], [5, 6], [7, 8], [9, 10]])
    y = np.array([1, 0, 1, 0, 1])
    
    # Función de modelo mock
    def mock_model(X_train, y_train, X_test):
        return np.array([1, 0, 1])
    
    # Realizar validación cruzada
    metrics = cross_validate(X, y, mock_model, n_splits=2)
    
    # Verificar resultados
    assert isinstance(metrics, dict)
    assert "accuracy" in metrics
    assert "precision" in metrics
    assert "recall" in metrics
    assert "f1" in metrics
    assert all(0 <= v <= 1 for v in metrics.values())

async def test_evaluate_recommendations(db_session: AsyncSession, sample_data):
    """Test para evaluar recomendaciones."""
    users, products, interactions = sample_data
    
    # Crear recomendaciones
    recommendations = []
    for user in users:
        for product in products[5:10]:  # Recomendar productos diferentes a los vistos
            recommendation = Recommendation(
                account_id=1,
                end_user_id=user.id,
                product_id=product.id,
                score=0.8,
                created_at=datetime.utcnow()
            )
            db_session.add(recommendation)
            recommendations.append(recommendation)
    
    await db_session.commit()
    
    # Evaluar recomendaciones
    metrics = await evaluate_recommendations(db_session, 1)
    
    # Verificar métricas
    assert isinstance(metrics, dict)
    assert "precision" in metrics
    assert "recall" in metrics
    assert "ndcg" in metrics
    assert all(0 <= v <= 1 for v in metrics.values())

async def test_calculate_diversity(db_session: AsyncSession, sample_data):
    """Test para calcular diversidad de recomendaciones."""
    users, products, interactions = sample_data
    
    # Crear recomendaciones
    recommendations = []
    for user in users:
        # Recomendar productos de diferentes categorías
        for category in range(5):
            product = next(p for p in products if p.category == f"Category {category}")
            recommendation = Recommendation(
                account_id=1,
                end_user_id=user.id,
                product_id=product.id,
                score=0.8,
                created_at=datetime.utcnow()
            )
            db_session.add(recommendation)
            recommendations.append(recommendation)
    
    await db_session.commit()
    
    # Calcular diversidad
    diversity = await calculate_diversity(db_session, 1)
    
    # Verificar diversidad
    assert isinstance(diversity, float)
    assert 0 <= diversity <= 1
    assert diversity > 0.5  # Debería ser alta ya que recomendamos de diferentes categorías

async def test_calculate_novelty(db_session: AsyncSession, sample_data):
    """Test para calcular novedad de recomendaciones."""
    users, products, interactions = sample_data
    
    # Crear recomendaciones
    recommendations = []
    for user in users:
        # Recomendar productos no vistos
        for product in products[5:10]:
            recommendation = Recommendation(
                account_id=1,
                end_user_id=user.id,
                product_id=product.id,
                score=0.8,
                created_at=datetime.utcnow()
            )
            db_session.add(recommendation)
            recommendations.append(recommendation)
    
    await db_session.commit()
    
    # Calcular novedad
    novelty = await calculate_novelty(db_session, 1)
    
    # Verificar novedad
    assert isinstance(novelty, float)
    assert 0 <= novelty <= 1
    assert novelty > 0.5  # Debería ser alta ya que recomendamos productos no vistos

def test_calculate_metrics_with_empty_data():
    """Test para calcular métricas con datos vacíos."""
    # Datos vacíos
    y_true = np.array([])
    y_pred = np.array([])
    
    # Calcular métricas
    metrics = calculate_metrics(y_true, y_pred)
    
    # Verificar métricas
    assert isinstance(metrics, dict)
    assert all(v == 0 for v in metrics.values())

def test_cross_validate_with_single_class():
    """Test para validación cruzada con una sola clase."""
    # Datos de una sola clase
    X = np.array([[1, 2], [3, 4], [5, 6]])
    y = np.array([1, 1, 1])
    
    # Función de modelo mock
    def mock_model(X_train, y_train, X_test):
        return np.array([1, 1])
    
    # Realizar validación cruzada
    metrics = cross_validate(X, y, mock_model, n_splits=2)
    
    # Verificar resultados
    assert isinstance(metrics, dict)
    assert metrics["precision"] == 1.0
    assert metrics["recall"] == 1.0
    assert metrics["f1"] == 1.0

async def test_evaluate_recommendations_with_no_data(db_session: AsyncSession):
    """Test para evaluar recomendaciones sin datos."""
    # Evaluar recomendaciones
    metrics = await evaluate_recommendations(db_session, 1)
    
    # Verificar métricas
    assert isinstance(metrics, dict)
    assert all(v == 0 for v in metrics.values()) 