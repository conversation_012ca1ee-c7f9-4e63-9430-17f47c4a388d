# Seguridad de API Keys

Este documento describe la implementación de seguridad para las API keys en el sistema.

## Problema

Anteriormente, las API keys se almacenaban en texto plano en la base de datos, lo que representaba un riesgo de seguridad significativo en caso de que la base de datos fuera comprometida.

## Solución

Hemos implementado un sistema de hashing para las API keys, similar a cómo se manejan las contraseñas:

1. Las API keys se generan de forma segura con alta entropía
2. Se muestran al usuario una sola vez al momento de creación
3. Solo se almacena el hash de la API key en la base de datos
4. Cuando se recibe una solicitud, se hashea la API key proporcionada y se compara con el hash almacenado

## Implementación

### Estructura de la API Key

Las API keys tienen el siguiente formato:

```
sk_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789
```

Donde:
- `sk` es un prefijo que indica "secret key"
- El resto es una cadena aleatoria de alta entropía codificada en base64

### Almacenamiento

En la base de datos, almacenamos:

- `api_key_hash`: Hash SHA-256 de la API key completa
- `api_key_prefix`: El prefijo de la API key (ej. "sk")
- `api_key_last_chars`: Los últimos 6 caracteres de la API key
- `api_key_created_at`: Cuándo se creó la API key

Esto permite mostrar información parcial de la API key en la interfaz de usuario (como "sk_...XyZ123") sin comprometer la seguridad.

### Verificación

Cuando un usuario hace una solicitud con una API key:

1. Se calcula el hash SHA-256 de la API key proporcionada
2. Se compara con el hash almacenado en la base de datos
3. Si coinciden, se autentica al usuario

## Seguridad Mejorada

### Unicidad Global de API Key Hashes

**Mejora de Seguridad (US-SEC-003)**: Los hashes de API keys ahora son globalmente únicos en todo el sistema.

- **Antes**: `UniqueConstraint("account_id", "api_key_hash")` - unicidad solo dentro de cada cuenta
- **Ahora**: `UniqueConstraint("api_key_hash")` - unicidad global en todo el sistema

**Beneficios de Seguridad**:
- Previene vulnerabilidades de acceso cruzado entre tenants
- Garantiza que cada API key identifica únicamente a una cuenta
- Elimina el riesgo de que `get_by_api_key()` devuelva la cuenta incorrecta

## Proceso de Migración

La migración de API keys en texto plano a hasheadas se realiza en tres pasos:

### 1. Migración de Base de Datos

```bash
alembic upgrade 6fdd0755dc87
```

Esta migración añade las nuevas columnas para almacenar el hash y la información relacionada.

### 2. Migración de Datos

```bash
python -m scripts.migrate_api_keys
```

Este script:
- Busca todas las cuentas con API keys en texto plano
- Calcula el hash para cada una
- Actualiza los registros con el hash y la información relacionada
- Mantiene temporalmente la API key en texto plano para compatibilidad

### 3. Eliminación de API Keys en Texto Plano

```bash
alembic upgrade 7fdd0755dc88
```

Esta migración elimina la columna `api_key` y sus índices/restricciones asociados.

## Regeneración de API Keys

Los usuarios pueden regenerar sus API keys a través del endpoint `/api/v1/api-keys/` (POST). Esto:

1. Invalida inmediatamente la API key anterior
2. Genera una nueva API key
3. Muestra la nueva API key al usuario una sola vez
4. Almacena solo el hash en la base de datos

### Endpoints de Gestión de API Keys

- **POST `/api/v1/api-keys/`** - Crear/regenerar API key
- **GET `/api/v1/api-keys/current`** - Obtener metadatos de la API key actual
- **DELETE `/api/v1/api-keys/`** - Revocar API key actual

## Consideraciones de Seguridad

- Las API keys nunca deben almacenarse en texto plano en logs o bases de datos
- Las API keys deben transmitirse solo a través de conexiones seguras (HTTPS)
- Las API keys deben rotarse periódicamente
- Se debe implementar rate limiting para prevenir ataques de fuerza bruta

## Referencias

- [OWASP API Security Top 10](https://owasp.org/www-project-api-security/)
- [NIST Guidelines for Access Tokens](https://nvlpubs.nist.gov/nistpubs/SpecialPublications/NIST.SP.800-63b.pdf)
