# Rayuela API - Guía de Inicio Rápido para Python

Esta guía te mostrará cómo integrar Rayuela en tu aplicación Python, desde el registro hasta obtener recomendaciones personalizadas.

## Requisitos

- Python 3.7 o superior
- Paquete `requests` (`pip install requests`)

## Instalación

```bash
pip install requests
```

## 1. Registro y Obtención de API Key

Primero, necesitas registrarte y obtener tu API Key:

```python
import requests

def register_account():
    url = "https://api.rayuela.com/v1/auth/register"
    payload = {
        "email": "<EMAIL>",
        "password": "Tu_Contraseña_Segura123",
        "account_name": "Mi Empresa"
    }
    headers = {"Content-Type": "application/json"}

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()  # Lanza excepción si hay error HTTP
        data = response.json()

        # Guarda tu API Key de forma segura
        api_key = data["api_key"]
        access_token = data["access_token"]

        print(f"Registro exitoso!")
        print(f"Tu API Key: {api_key}")
        print("IMPORTANTE: Guarda esta clave en un lugar seguro. Solo se muestra una vez.")

        return {
            "api_key": api_key,
            "access_token": access_token
        }
    except requests.exceptions.HTTPError as e:
        print(f"Error HTTP: {e}")
        print(f"Respuesta: {e.response.text}")
    except Exception as e:
        print(f"Error: {e}")

    return None

# Ejecutar el registro
credentials = register_account()
```

## 2. Configuración del Cliente

Crea un cliente reutilizable para interactuar con la API:

```python
class RayuelaClient:
    def __init__(self, api_key):
        self.api_key = api_key
        self.base_url = "https://api.rayuela.com/v1"
        self.headers = {
            "X-API-Key": api_key,
            "Content-Type": "application/json"
        }

    def check_auth(self):
        """Verifica que la API Key es válida"""
        url = f"{self.base_url}/health/auth"
        response = requests.get(url, headers=self.headers)
        return response.json()

    def ingest_batch_data(self, products=None, users=None, interactions=None):
        """Ingesta datos por lotes"""
        url = f"{self.base_url}/ingestion/batch"
        payload = {}

        if products:
            payload["products"] = products
        if users:
            payload["users"] = users
        if interactions:
            payload["interactions"] = interactions

        response = requests.post(url, json=payload, headers=self.headers)
        return response.json()

    def check_job_status(self, job_id):
        """Verifica el estado de un trabajo de ingesta"""
        url = f"{self.base_url}/ingestion/batch/{job_id}"
        response = requests.get(url, headers=self.headers)
        return response.json()

    def train_model(self, model_type="collaborative_filtering", hyperparameters=None):
        """Entrena un modelo de recomendación"""
        url = f"{self.base_url}/pipeline/train"
        payload = {
            "model_type": model_type,
            "hyperparameters": hyperparameters or {}
        }

        response = requests.post(url, json=payload, headers=self.headers)
        return response.json()

    def check_training_status(self, job_id):
        """Verifica el estado de un trabajo de entrenamiento"""
        url = f"{self.base_url}/pipeline/jobs/{job_id}/status"
        response = requests.get(url, headers=self.headers)
        return response.json()

    def get_recommendations(self, user_id, limit=10, filters=None):
        """Obtiene recomendaciones personalizadas"""
        url = f"{self.base_url}/recommendations/personalized/query"
        payload = {
            "user_id": user_id,
            "limit": limit
        }

        if filters:
            payload["filters"] = filters

        response = requests.post(url, json=payload, headers=self.headers)
        return response.json()

# Crear cliente con la API Key obtenida
client = RayuelaClient(credentials["api_key"])
```

## 3. Verificación de Autenticación

```python
def verify_authentication():
    try:
        result = client.check_auth()
        print(f"Autenticación exitosa: {result}")
        return True
    except Exception as e:
        print(f"Error de autenticación: {e}")
        return False

# Verificar autenticación
is_authenticated = verify_authentication()
```

## 4. Ingesta de Datos

```python
def ingest_sample_data():
    # Datos de ejemplo
    products = [
        {"product_id": "p1", "name": "Producto 1", "category": "Electrónica", "price": 99.99},
        {"product_id": "p2", "name": "Producto 2", "category": "Ropa", "price": 49.99},
        {"product_id": "p3", "name": "Producto 3", "category": "Hogar", "price": 29.99}
    ]

    users = [
        {"user_id": "u1", "age": 28, "gender": "M"},
        {"user_id": "u2", "age": 35, "gender": "F"}
    ]

    interactions = [
        {"user_id": "u1", "product_id": "p1", "interaction_type": "view", "timestamp": "2023-01-01T10:30:00Z"},
        {"user_id": "u1", "product_id": "p2", "interaction_type": "purchase", "timestamp": "2023-01-01T11:45:00Z"},
        {"user_id": "u2", "product_id": "p2", "interaction_type": "view", "timestamp": "2023-01-02T09:15:00Z"},
        {"user_id": "u2", "product_id": "p3", "interaction_type": "purchase", "timestamp": "2023-01-02T14:20:00Z"}
    ]

    try:
        result = client.ingest_batch_data(products, users, interactions)
        job_id = result["job_id"]
        print(f"Ingesta iniciada. Job ID: {job_id}")
        return job_id
    except Exception as e:
        print(f"Error en la ingesta: {e}")
        return None

# Ingestar datos de ejemplo
ingestion_job_id = ingest_sample_data()
```

## 5. Verificar Estado del Trabajo de Ingesta

```python
import time

def wait_for_job_completion(job_id, max_wait_seconds=300):
    """Espera hasta que el trabajo se complete o falle"""
    start_time = time.time()

    while time.time() - start_time < max_wait_seconds:
        try:
            status = client.check_job_status(job_id)
            print(f"Estado del trabajo: {status['status']}")

            if status["status"] in ["completed", "failed"]:
                return status

            time.sleep(5)  # Esperar 5 segundos antes de verificar nuevamente
        except Exception as e:
            print(f"Error al verificar estado: {e}")
            time.sleep(5)

    print("Tiempo de espera agotado")
    return None

# Esperar a que se complete la ingesta
if ingestion_job_id:
    ingestion_status = wait_for_job_completion(ingestion_job_id)
    print(f"Resultado final de ingesta: {ingestion_status}")
```

## 6. Entrenamiento del Modelo

```python
def train_recommendation_model():
    try:
        hyperparameters = {
            "factors": 20,
            "iterations": 20
        }

        result = client.train_model(
            model_type="collaborative_filtering",
            hyperparameters=hyperparameters
        )

        job_id = result["job_id"]
        print(f"Entrenamiento iniciado. Job ID: {job_id}")
        return job_id
    except Exception as e:
        print(f"Error al iniciar entrenamiento: {e}")
        return None

# Entrenar modelo
training_job_id = train_recommendation_model()
```

## 7. Verificar Estado del Entrenamiento

```python
# Esperar a que se complete el entrenamiento
if training_job_id:
    training_status = wait_for_job_completion(training_job_id, max_wait_seconds=600)
    print(f"Resultado final de entrenamiento: {training_status}")
```

## 8. Obtener Recomendaciones

```python
def get_personalized_recommendations(user_id="u1"):
    try:
        filters = {
            "categories": ["Electrónica", "Hogar"]
        }

        recommendations = client.get_recommendations(
            user_id=user_id,
            limit=5,
            filters=filters
        )

        print(f"Recomendaciones para usuario {user_id}:")
        for i, item in enumerate(recommendations["items"], 1):
            print(f"{i}. {item['name']} (ID: {item['product_id']}, Score: {item['score']})")

        return recommendations
    except Exception as e:
        print(f"Error al obtener recomendaciones: {e}")
        return None

# Obtener recomendaciones
recommendations = get_personalized_recommendations()
```

## Ejemplo Completo

Aquí tienes un script completo que puedes ejecutar:

```python
import requests
import time
import os
from dotenv import load_dotenv

# Cargar variables de entorno (opcional)
load_dotenv()

# Usar API Key existente o registrar una nueva cuenta
api_key = os.getenv("RAYUELA_API_KEY")

if not api_key:
    # Código de registro (ver sección 1)
    pass

# Crear cliente
client = RayuelaClient(api_key)

# Verificar autenticación
is_authenticated = verify_authentication()

if is_authenticated:
    # Ingestar datos
    ingestion_job_id = ingest_sample_data()

    # Esperar a que se complete la ingesta
    if ingestion_job_id:
        ingestion_status = wait_for_job_completion(ingestion_job_id)

        if ingestion_status and ingestion_status["status"] == "completed":
            # Entrenar modelo
            training_job_id = train_recommendation_model()

            # Esperar a que se complete el entrenamiento
            if training_job_id:
                training_status = wait_for_job_completion(training_job_id, max_wait_seconds=600)

                if training_status and training_status["status"] == "completed":
                    # Obtener recomendaciones
                    recommendations = get_personalized_recommendations()
```

## Documentación de Referencia de la API

Para obtener información detallada sobre todos los endpoints disponibles, parámetros y esquemas de datos, consulta la documentación interactiva de la API:

- **Swagger UI**: [/api/docs](/api/docs) - Interfaz interactiva para explorar y probar la API
- **OpenAPI JSON**: [/api/openapi.json](/api/openapi.json) - Especificación OpenAPI en formato JSON
- **ReDoc**: [/api/redoc](/api/redoc) - Documentación alternativa con un diseño más limpio

La documentación interactiva te permite:

- Explorar todos los endpoints disponibles
- Ver los parámetros requeridos y opcionales para cada endpoint
- Probar los endpoints directamente desde el navegador con la función "Try it out"
- Ver los esquemas de solicitud y respuesta para cada endpoint

## Recursos Adicionales

- [Guía General de Inicio Rápido](../../QUICKSTART.md)
- [Guías para Otros Lenguajes](../)
- [Repositorio de Ejemplos](https://github.com/rayuela-examples)
- [Soporte](mailto:<EMAIL>)
