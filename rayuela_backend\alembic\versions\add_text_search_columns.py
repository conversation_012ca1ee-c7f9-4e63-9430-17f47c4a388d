"""add text search columns

Revision ID: add_text_search_columns
Revises: 001_add_rls_policies
Create Date: 2024-03-21 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_text_search_columns'
down_revision = '001_add_rls_policies'  # Updated to point to the RLS policies migration
branch_labels = None
depends_on = None


def upgrade():
    # Habilitar la extensión pg_trgm si no está habilitada
    op.execute('CREATE EXTENSION IF NOT EXISTS pg_trgm')

    # Agregar columnas para búsqueda de texto
    # Usar tsvector en lugar de text para search_vector
    op.add_column('products', sa.Column('search_vector', postgresql.TSVECTOR(), nullable=True))
    op.add_column('products', sa.Column('name_trgm', sa.Text(), nullable=True))
    op.add_column('products', sa.Column('description_trgm', sa.Text(), nullable=True))

    # Crear índices GIN para búsqueda de texto
    # Para tsvector podemos usar GIN directamente
    op.create_index('idx_product_search_vector', 'products', ['search_vector'],
                    postgresql_using='gin')

    # Para text con pg_trgm necesitamos especificar el operador de clase
    op.execute("""
        CREATE INDEX idx_product_name_trgm ON products
        USING gin (name_trgm gin_trgm_ops)
    """)

    op.execute("""
        CREATE INDEX idx_product_description_trgm ON products
        USING gin (description_trgm gin_trgm_ops)
    """)

    # Actualizar los datos existentes
    op.execute("""
        UPDATE products
        SET search_vector = setweight(to_tsvector('spanish', coalesce(name, '')), 'A') ||
                           setweight(to_tsvector('spanish', coalesce(description, '')), 'B'),
            name_trgm = coalesce(name, ''),
            description_trgm = coalesce(description, '')
    """)


def downgrade():
    # Eliminar índices
    op.drop_index('idx_product_search_vector', table_name='products')
    op.drop_index('idx_product_name_trgm', table_name='products')
    op.drop_index('idx_product_description_trgm', table_name='products')

    # Eliminar columnas
    op.drop_column('products', 'search_vector')
    op.drop_column('products', 'name_trgm')
    op.drop_column('products', 'description_trgm')