{"users": [{"external_id": "viewer_001"}, {"external_id": "viewer_002"}, {"external_id": "viewer_003"}, {"external_id": "viewer_004"}, {"external_id": "viewer_005"}], "products": [{"name": "Película: Avengers Endgame", "description": "Épica conclusión de la saga del infinito. Los Vengadores se unen una vez más para enfrentar a Thanos en una batalla final por el destino del universo.", "price": 4.99, "category": "movies", "average_rating": 4.9, "num_ratings": 15420, "inventory_count": 999999}, {"name": "Serie: Stranger Things T4", "description": "Cuarta temporada de la popular serie de Netflix. Los adolescentes de Hawkins enfrentan nuevos horrores sobrenaturales mientras se separan por primera vez.", "price": 0.0, "category": "series", "average_rating": 4.6, "num_ratings": 8765, "inventory_count": 999999}, {"name": "Documental: Nuestro Planeta", "description": "Serie documental narrada por <PERSON> que explora la belleza natural de nuestro planeta y los desafíos del cambio climático.", "price": 0.0, "category": "documentaries", "average_rating": 4.8, "num_ratings": 3421, "inventory_count": 999999}, {"name": "Película: Dune", "description": "Adaptación épica de la novela de <PERSON>. <PERSON> debe navegar por la política peligrosa del planeta desértico A<PERSON>kis.", "price": 3.99, "category": "movies", "average_rating": 4.4, "num_ratings": 7890, "inventory_count": 999999}, {"name": "Serie: The Crown T5", "description": "Quinta temporada del drama histórico sobre la familia real británica, centrándose en los años 90 y los escándalos de la monarquía.", "price": 0.0, "category": "series", "average_rating": 4.3, "num_ratings": 5432, "inventory_count": 999999}, {"name": "Anime: Attack on Titan Final", "description": "Temporada final del popular anime. Eren y sus compañeros enfrentan la verdad sobre los titanes y el mundo más allá de las murallas.", "price": 0.0, "category": "anime", "average_rating": 4.9, "num_ratings": 12345, "inventory_count": 999999}, {"name": "Película: Top Gun Maverick", "description": "Secuela del clásico de 1986. <PERSON> 'Maverick' <PERSON> regresa como instructor de élite para una misión casi imposible.", "price": 5.99, "category": "movies", "average_rating": 4.7, "num_ratings": 9876, "inventory_count": 999999}, {"name": "Documental: Formula 1: Drive to Survive T4", "description": "Cuarta temporada de la serie documental que sigue a los pilotos y equipos de Fórmula 1 durante la temporada 2021.", "price": 0.0, "category": "documentaries", "average_rating": 4.5, "num_ratings": 4567, "inventory_count": 999999}, {"name": "Serie: Wednesday", "description": "Serie de misterio y comedia protagonizada por Jenna Ortega como Wednesday Addams en la Academia Nevermore.", "price": 0.0, "category": "series", "average_rating": 4.2, "num_ratings": 6789, "inventory_count": 999999}, {"name": "Película: Everything Everywhere All at Once", "description": "Una lavandera china-estadounidense se ve arrastrada a una aventura salvaje donde solo ella puede salvar el mundo explorando otros universos.", "price": 4.99, "category": "movies", "average_rating": 4.8, "num_ratings": 11234, "inventory_count": 999999}], "interactions": [{"user_id": 1, "product_id": 1, "interaction_type": "SEARCH", "value": 1.0}, {"user_id": 1, "product_id": 1, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 1, "product_id": 1, "interaction_type": "PURCHASE", "value": 1.0}, {"user_id": 1, "product_id": 1, "interaction_type": "RATING", "value": 5.0}, {"user_id": 1, "product_id": 2, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 1, "product_id": 2, "interaction_type": "LIKE", "value": 1.0}, {"user_id": 1, "product_id": 6, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 1, "product_id": 6, "interaction_type": "FAVORITE", "value": 1.0}, {"user_id": 2, "product_id": 3, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 2, "product_id": 3, "interaction_type": "RATING", "value": 4.5}, {"user_id": 2, "product_id": 8, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 2, "product_id": 8, "interaction_type": "LIKE", "value": 1.0}, {"user_id": 2, "product_id": 5, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 2, "product_id": 5, "interaction_type": "RATING", "value": 4.0}, {"user_id": 3, "product_id": 4, "interaction_type": "SEARCH", "value": 1.0}, {"user_id": 3, "product_id": 4, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 3, "product_id": 4, "interaction_type": "PURCHASE", "value": 1.0}, {"user_id": 3, "product_id": 4, "interaction_type": "RATING", "value": 4.0}, {"user_id": 3, "product_id": 7, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 3, "product_id": 7, "interaction_type": "WISHLIST", "value": 1.0}, {"user_id": 4, "product_id": 9, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 4, "product_id": 9, "interaction_type": "LIKE", "value": 1.0}, {"user_id": 4, "product_id": 2, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 4, "product_id": 2, "interaction_type": "RATING", "value": 5.0}, {"user_id": 4, "product_id": 6, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 4, "product_id": 6, "interaction_type": "FAVORITE", "value": 1.0}, {"user_id": 5, "product_id": 10, "interaction_type": "SEARCH", "value": 1.0}, {"user_id": 5, "product_id": 10, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 5, "product_id": 10, "interaction_type": "PURCHASE", "value": 1.0}, {"user_id": 5, "product_id": 10, "interaction_type": "RATING", "value": 5.0}, {"user_id": 5, "product_id": 1, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 5, "product_id": 1, "interaction_type": "WISHLIST", "value": 1.0}, {"user_id": 5, "product_id": 3, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 5, "product_id": 3, "interaction_type": "RATING", "value": 4.5}]}