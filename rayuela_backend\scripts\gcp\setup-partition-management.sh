#!/bin/bash
# Script para configurar la gestión de particiones en Google Cloud
# ⚠️ DEPRECATED: Este script usa :latest tags.
# Se recomienda usar el pipeline de CI/CD con tags inmutables.

# Variables (ajustar según el entorno)
PROJECT_ID="rayuela"
REGION="us-central1"
SERVICE_NAME="partition-manager"
# SECURITY WARNING: Using :latest tag - should use immutable tags in production
IMAGE_NAME="us-central1-docker.pkg.dev/${PROJECT_ID}/rayuela-repo/rayuela-backend:latest"
SERVICE_ACCOUNT="${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"
SCHEDULER_JOB_NAME="partition-management-job"
SCHEDULE="0 1 * * *"  # Diariamente a la 1:00 AM

echo "⚠️  WARNING: This script uses :latest image tags which is not recommended for production."
echo "   For production deployments, use the CI/CD pipeline with immutable tags."
echo "   See: cloudbuild-deploy-production.yaml"
echo ""

# Crear la cuenta de servicio si no existe
if ! gcloud iam service-accounts describe ${SERVICE_ACCOUNT} &>/dev/null; then
  echo "Creando cuenta de servicio ${SERVICE_ACCOUNT}..."
  gcloud iam service-accounts create ${SERVICE_NAME} \
    --display-name="Partition Management Service Account"
fi

# Otorgar permisos necesarios
echo "Otorgando permisos a la cuenta de servicio..."
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
  --member="serviceAccount:${SERVICE_ACCOUNT}" \
  --role="roles/cloudsql.client"

# Desplegar el servicio Cloud Run
echo "Desplegando servicio Cloud Run ${SERVICE_NAME}..."
gcloud run deploy ${SERVICE_NAME} \
  --image=${IMAGE_NAME} \
  --platform=managed \
  --region=${REGION} \
  --service-account=${SERVICE_ACCOUNT} \
  --no-allow-unauthenticated \
  --command="python" \
  --args="scripts/manage_partitions.py" \
  --set-env-vars="PYTHONPATH=." \
  --set-env-vars="PARTITION_SIZE=100000" \
  --set-env-vars="PARTITION_BUFFER_COUNT=5" \
  --set-cloudsql-instances="${PROJECT_ID}:${REGION}:rayuela-db" \
  --memory=512Mi \
  --cpu=1 \
  --timeout=600s

# Obtener la URL del servicio
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} \
  --platform=managed \
  --region=${REGION} \
  --format="value(status.url)")

# Crear o actualizar el trabajo de Cloud Scheduler
if gcloud scheduler jobs describe ${SCHEDULER_JOB_NAME} --location=${REGION} &>/dev/null; then
  echo "Actualizando trabajo de Cloud Scheduler ${SCHEDULER_JOB_NAME}..."
  gcloud scheduler jobs update http ${SCHEDULER_JOB_NAME} \
    --location=${REGION} \
    --schedule="${SCHEDULE}" \
    --uri="${SERVICE_URL}" \
    --http-method=GET \
    --oidc-service-account-email=${SERVICE_ACCOUNT} \
    --oidc-token-audience="${SERVICE_URL}"
else
  echo "Creando trabajo de Cloud Scheduler ${SCHEDULER_JOB_NAME}..."
  gcloud scheduler jobs create http ${SCHEDULER_JOB_NAME} \
    --location=${REGION} \
    --schedule="${SCHEDULE}" \
    --uri="${SERVICE_URL}" \
    --http-method=GET \
    --oidc-service-account-email=${SERVICE_ACCOUNT} \
    --oidc-token-audience="${SERVICE_URL}"
fi

echo "Configuración completada."
echo "El servicio ${SERVICE_NAME} se ejecutará según el cronograma: ${SCHEDULE}"
