"""
Repositorio para trabajos de ingesta masiva de datos.
"""

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
from typing import Optional, Dict, Any, List
from src.db import models, schemas
from .base import BaseRepository


class BatchIngestionJobRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.BatchIngestionJob)

    async def create(self, job_data: Dict[str, Any]) -> models.BatchIngestionJob:
        """Crear un nuevo trabajo de ingesta masiva."""
        try:
            job = models.BatchIngestionJob(
                **job_data, account_id=self.account_id
            )
            self.db.add(job)
            await self.db.flush()
            await self.db.refresh(job)
            return job
        except SQLAlchemyError as e:
            await self._handle_error("creating batch ingestion job", e)

    async def update_status(
        self, job_id: int, status: str, error_message: Optional[str] = None
    ) -> Optional[models.BatchIngestionJob]:
        """Actualizar el estado de un trabajo de ingesta masiva."""
        try:
            job = await self.get_by_id(job_id)
            if not job:
                return None

            job.status = status
            if error_message:
                job.error_message = error_message

            await self.db.flush()
            await self.db.refresh(job)
            return job
        except SQLAlchemyError as e:
            await self._handle_error("updating batch ingestion job status", e)

    async def get_recent_jobs(
        self, limit: int = 10, status: Optional[str] = None
    ) -> List[models.BatchIngestionJob]:
        """Obtener trabajos recientes de ingesta masiva."""
        try:
            query = self.db.query(models.BatchIngestionJob)
            query = self._add_tenant_filter(query)

            if status:
                query = query.filter(models.BatchIngestionJob.status == status)

            query = query.order_by(models.BatchIngestionJob.created_at.desc()).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching recent batch ingestion jobs", e)
            return []
