#!/bin/bash

# 🚀 Script de Despliegue Simplificado para Producción
# Utiliza la configuración optimizada cloudbuild-deploy-production.yaml

set -e

# Colores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Verificar prerrequisitos
print_status "🔍 Verificando prerrequisitos..."

if ! command -v gcloud &> /dev/null; then
    print_error "gcloud CLI no está instalado"
    exit 1
fi

PROJECT_ID=$(gcloud config get-value project)
if [ -z "$PROJECT_ID" ]; then
    print_error "No hay proyecto configurado. Ejecuta: gcloud config set project TU_PROJECT_ID"
    exit 1
fi

print_success "Proyecto: $PROJECT_ID"

# Verificar que el archivo de configuración existe
if [ ! -f "cloudbuild-deploy-production.yaml" ]; then
    print_error "Archivo cloudbuild-deploy-production.yaml no encontrado"
    exit 1
fi

print_status "✅ Archivo de configuración encontrado"

# Verificar que estamos en la rama correcta
CURRENT_BRANCH=$(git branch --show-current 2>/dev/null || echo "main")
print_status "Rama actual: $CURRENT_BRANCH"

# Hacer commit de cambios si es necesario
if ! git diff --quiet HEAD || ! git diff --cached --quiet; then
    print_warning "Hay cambios sin commitear"
    read -p "¿Hacer commit automático? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        git add .
        git commit -m "chore: prepare for production deployment $(date '+%Y-%m-%d %H:%M:%S')"
        git push origin $CURRENT_BRANCH
        print_success "Cambios commiteados y pusheados"
    else
        print_warning "Continuando sin commit. Asegúrate de que el código esté actualizado en el repositorio."
    fi
fi

# Ejecutar despliegue
print_status "🚀 Iniciando despliegue en producción..."

# Opción 1: Despliegue directo con Cloud Build
if [ "$1" = "--direct" ]; then
    print_status "Ejecutando Cloud Build directamente..."
    
    gcloud builds submit \
        --config=cloudbuild-deploy-production.yaml \
        --region=us-central1 \
        .
    
    if [ $? -eq 0 ]; then
        print_success "🎉 Despliegue completado exitosamente!"
    else
        print_error "❌ El despliegue falló"
        exit 1
    fi

# Opción 2: Usar trigger (requiere configuración previa)
else
    print_status "Verificando triggers disponibles..."
    
    # Listar triggers
    TRIGGERS=$(gcloud builds triggers list --filter="name~rayuela" --format="value(name)" --region=us-central1)
    
    if [ -z "$TRIGGERS" ]; then
        print_warning "No se encontraron triggers configurados"
        print_status "Creando trigger temporal..."
        
        # Crear trigger temporal
        gcloud builds triggers create github \
            --repo-name=rayuela \
            --repo-owner=marcelo-vera \
            --branch-pattern="^${CURRENT_BRANCH}$" \
            --build-config=cloudbuild-deploy-production.yaml \
            --name="rayuela-production-trigger" \
            --description="Trigger temporal para despliegue en producción" \
            --region=us-central1
        
        TRIGGER_NAME="rayuela-production-trigger"
    else
        TRIGGER_NAME=$(echo "$TRIGGERS" | head -n1)
        print_status "Usando trigger existente: $TRIGGER_NAME"
    fi
    
    # Ejecutar trigger
    print_status "Ejecutando trigger: $TRIGGER_NAME"
    
    BUILD_OUTPUT=$(gcloud builds triggers run $TRIGGER_NAME \
        --branch=$CURRENT_BRANCH \
        --region=us-central1 2>&1)
    
    BUILD_ID=$(echo "$BUILD_OUTPUT" | grep -o "id: [a-zA-Z0-9-]*" | cut -d' ' -f2)
    
    if [ -n "$BUILD_ID" ]; then
        print_success "Build iniciado con ID: $BUILD_ID"
        print_status "🔗 Seguir progreso: https://console.cloud.google.com/cloud-build/builds/$BUILD_ID?project=$PROJECT_ID"
        
        # Opción para seguir logs
        read -p "¿Seguir logs en tiempo real? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            gcloud builds log $BUILD_ID --stream --region=us-central1
        fi
        
        # Verificar estado final
        print_status "Verificando estado del build..."
        BUILD_STATUS=$(gcloud builds describe $BUILD_ID --format="value(status)" --region=us-central1)
        
        if [ "$BUILD_STATUS" = "SUCCESS" ]; then
            print_success "🎉 ¡Despliegue completado exitosamente!"
            
            # Mostrar URLs de servicios
            print_status "🌐 Servicios desplegados:"
            echo ""
            
            BACKEND_URL=$(gcloud run services describe rayuela-backend --region=us-central1 --format="value(status.url)" 2>/dev/null || echo "No disponible")
            FRONTEND_URL=$(gcloud run services describe rayuela-frontend --region=us-central1 --format="value(status.url)" 2>/dev/null || echo "No disponible")
            
            echo "🔧 Backend API: $BACKEND_URL"
            echo "🖥️  Frontend: $FRONTEND_URL"
            echo ""
            
            if [ "$BACKEND_URL" != "No disponible" ]; then
                print_status "🏥 Probando backend..."
                if curl -f "$BACKEND_URL/health" &>/dev/null; then
                    print_success "✅ Backend responde correctamente"
                else
                    print_warning "⚠️  Backend no responde en /health"
                fi
            fi
            
        else
            print_error "❌ Build falló con estado: $BUILD_STATUS"
            print_error "📋 Ver logs: gcloud builds log $BUILD_ID --region=us-central1"
            exit 1
        fi
        
    else
        print_error "No se pudo obtener el ID del build"
        exit 1
    fi
fi

# Mostrar siguiente pasos
echo ""
print_success "📋 SIGUIENTE PASOS RECOMENDADOS:"
echo "1. Configurar dominio personalizado (opcional)"
echo "2. Configurar SSL/TLS certificado"
echo "3. Configurar monitoreo y alertas"
echo "4. Configurar backups de base de datos"
echo "5. Revisar logs de aplicación"

print_success "🎉 ¡Despliegue completado!" 