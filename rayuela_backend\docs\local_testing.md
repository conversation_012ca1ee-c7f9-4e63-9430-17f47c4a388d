# Guía para Pruebas Locales de Rayuela

Esta guía detalla cómo configurar y ejecutar pruebas locales para el sistema de recomendación Rayuela, asegurando que todos los componentes funcionen correctamente antes de desplegar en GCP.

## Requisitos Previos

1. **Git** - Para control de versiones
2. **Python 3.9+** - Para ejecutar el backend
3. **Node.js 18+** - Para ejecutar el frontend
4. **Docker y Docker Compose** - Para ejecutar PostgreSQL y Redis

## Método Simplificado (Recomendado)

Hemos creado scripts simplificados que configuran los componentes esenciales para probar Rayuela localmente:

### Windows

```powershell
# Navegar al directorio del backend
cd rayuela_backend

# Ejecutar el script de prueba simplificado
.\scripts\test_local.ps1
```

### Linux/Mac

```bash
# Navegar al directorio del backend
cd rayuela_backend

# Ejecutar el script de prueba simplificado
python -m scripts.test_local
```

### Opciones de los Scripts Simplificados

Los scripts aceptan las siguientes opciones:

- En Windows (PowerShell):
  ```powershell
  .\scripts\test_local.ps1 -SkipDocker -SkipDbInit -SkipApi
  ```

- En Linux/Mac:
  ```bash
  python -m scripts.test_local --skip-docker --skip-db-init --skip-api
  ```

## Método Completo (Opcional)

Si necesitas probar todos los componentes, incluyendo los workers de Celery, puedes usar los scripts completos:

### Windows

```powershell
# Navegar al directorio del backend
cd rayuela_backend

# Ejecutar el script de prueba completo
.\scripts\run_local_test.ps1
```

### Linux/Mac

```bash
# Navegar al directorio del backend
cd rayuela_backend

# Dar permisos de ejecución al script
chmod +x scripts/run_local_test.sh

# Ejecutar el script de prueba completo
./scripts/run_local_test.sh
```

### Opciones del Script Completo

Ambos scripts completos aceptan las siguientes opciones:

- `--skip-docker`: Omitir inicio de servicios Docker
- `--skip-db-init`: Omitir inicialización de base de datos
- `--skip-api`: Omitir inicio de servidor API
- `--skip-workers`: Omitir inicio de workers de Celery
- `--skip-tests`: Omitir ejecución de pruebas

Ejemplo:
```bash
./scripts/run_local_test.sh --skip-docker --skip-db-init
```

## Método Manual Paso a Paso

Si prefieres ejecutar cada paso manualmente, sigue estas instrucciones:

### 1. Configurar Variables de Entorno

1. **Backend**: Crea o verifica el archivo `.env` en el directorio `rayuela_backend`:
   ```
   ENV=development
   API_HOST=0.0.0.0
   API_PORT=8001
   SECRET_KEY=test_secure_key_for_local_development_at_least_32_chars
   POSTGRES_USER=postgres
   POSTGRES_PASSWORD=postgres
   POSTGRES_SERVER=localhost
   POSTGRES_PORT=5432
   POSTGRES_DB=rayuela_dev
   REDIS_URL=redis://localhost:6379/0
   REDIS_HOST=localhost
   REDIS_PORT=6379
   FRONTEND_URL=http://localhost:3000
   ```

2. **Frontend**: Crea o verifica el archivo `.env.local` en el directorio `rayuela_frontend`:
   ```
   NEXT_PUBLIC_API_BASE_URL=http://localhost:8001
   ```

### 2. Iniciar Base de Datos y Redis

Para evitar conflictos con la configuración de Docker Compose, usamos un archivo simplificado:

```bash
# Navegar al directorio del backend
cd rayuela_backend

# Iniciar servicios con el archivo simplificado
docker-compose -f docker-compose.simple.yml up -d
```

### 3. Inicializar la Base de Datos

```bash
# Navegar al directorio del backend
cd rayuela_backend

# Inicializar la base de datos
python -m scripts.start init_db
```

### 4. Iniciar el Servidor API

```bash
# Navegar al directorio del backend
cd rayuela_backend

# Iniciar el servidor API
python main.py
```

### 5. Iniciar Workers de Celery (en terminales separadas)

```bash
# Terminal 1: Worker para tareas por defecto
cd rayuela_backend
celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --queues=default --hostname=default@%h

# Terminal 2: Worker para tareas de entrenamiento
cd rayuela_backend
celery -A src.workers.celery_app worker --loglevel=info --concurrency=1 --max-tasks-per-child=1 --queues=training --hostname=training@%h

# Terminal 3: Celery Beat para tareas periódicas
cd rayuela_backend
celery -A src.workers.celery_app beat --loglevel=info
```

### 6. Iniciar el Frontend

```bash
# Navegar al directorio del frontend
cd rayuela_frontend

# Instalar dependencias (si es necesario)
npm install

# Iniciar el servidor de desarrollo
npm run dev
```

### 7. Ejecutar Pruebas

```bash
# Navegar al directorio del backend
cd rayuela_backend

# Ejecutar todas las pruebas
python -m scripts.run_tests all

# O ejecutar pruebas específicas
python -m scripts.run_tests unit
python -m scripts.run_tests integration
```

## Prueba del Flujo Completo

Una vez que todos los componentes estén en ejecución, puedes probar el flujo completo:

1. Registrar una cuenta en `http://localhost:3000/register`
2. Iniciar sesión en `http://localhost:3000/login`
3. Cargar datos de productos e interacciones
4. Entrenar un modelo
5. Obtener recomendaciones

### Ejemplo de Comandos API para Pruebas

```bash
# Obtener API Key (guarda la respuesta)
curl -X POST http://localhost:8001/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email": "<EMAIL>", "password": "securepassword", "company_name": "Test Company"}'

# Cargar productos
curl -X POST http://localhost:8001/api/v1/data/products \
  -H "Authorization: Bearer TU_API_KEY" \
  -H "Content-Type: application/json" \
  -d '[{"product_id": "p1", "name": "Product 1", "category": "Category A"}, {"product_id": "p2", "name": "Product 2", "category": "Category B"}]'

# Cargar interacciones
curl -X POST http://localhost:8001/api/v1/data/interactions \
  -H "Authorization: Bearer TU_API_KEY" \
  -H "Content-Type: application/json" \
  -d '[{"user_id": "u1", "product_id": "p1", "interaction_type": "view"}, {"user_id": "u1", "product_id": "p2", "interaction_type": "purchase"}]'

# Iniciar entrenamiento
curl -X POST http://localhost:8001/api/v1/training/start \
  -H "Authorization: Bearer TU_API_KEY"

# Verificar estado del entrenamiento
curl -X GET http://localhost:8001/api/v1/training/status \
  -H "Authorization: Bearer TU_API_KEY"

# Obtener recomendaciones
curl -X GET http://localhost:8001/api/v1/recommendations/personalized?user_id=u1 \
  -H "Authorization: Bearer TU_API_KEY"
```

## Solución de Problemas

### Problemas con Docker Compose

Si encuentras errores relacionados con la configuración de memoria en Docker Compose:

```
services.worker-training: can't set distinct values on 'mem_limit' and 'deploy.resources.limits.memory'
```

Usa el archivo `docker-compose.simple.yml` que solo incluye los servicios esenciales:

```bash
docker-compose -f docker-compose.simple.yml up -d
```

### Problemas con las Migraciones de Alembic o Importaciones de SQLAlchemy

Si encuentras errores al ejecutar las migraciones de Alembic, como problemas de codificación:

```
UnicodeDecodeError: 'utf-8' codec can't decode byte 0xed in position 1964: invalid continuation byte
```

O errores de importación de SQLAlchemy:

```
ImportError: cannot import name 'postgresql_ops' from 'sqlalchemy'
```

Usa los scripts simplificados que hemos creado:

```bash
# Para inicializar la base de datos sin usar Alembic
python -m scripts.init_db_simple

# Para iniciar la API sin cargar todos los módulos del proyecto
python -m scripts.run_api_simple
```

El script `init_db_simple.py` crea la base de datos si no existe, crea las tablas básicas y carga datos de prueba, todo sin depender de Alembic.

El script `run_api_simple.py` crea una API FastAPI básica con los endpoints esenciales para probar la funcionalidad, sin cargar todos los módulos del proyecto.

### Puertos en Uso

Si el puerto 8001 ya está en uso:

```bash
# Cambiar el puerto en el archivo .env
API_PORT=8002
```

### Problemas de Conexión a la Base de Datos

Verifica que los contenedores estén en ejecución:

```bash
docker-compose ps
```

Verifica las credenciales en el archivo `.env`.

### Problemas con Celery

Asegúrate de que Redis esté en ejecución:

```bash
docker-compose ps redis
```

## Limpieza

Cuando hayas terminado de probar, detén todos los servicios:

```bash
# Detener procesos (Ctrl+C en cada terminal)

# Detener contenedores Docker
cd rayuela_backend
docker-compose -f docker-compose.simple.yml down
```
