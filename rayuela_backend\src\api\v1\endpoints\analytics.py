"""
API endpoints for analytics data.
"""

from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.deps import get_db, get_current_account, get_current_admin_user
from src.services import AnalyticsService
from src.core.exceptions import handle_exceptions
from src.db.models import SystemUser
from src.ml_pipeline.training_pipeline import TrainingPipeline
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.ml_pipeline.evaluation import RecommendationEvaluator

router = APIRouter()
artifact_manager = ModelArtifactManager()
metrics_tracker = MetricsTracker()
evaluator = RecommendationEvaluator()
training_pipeline = TrainingPipeline(
    artifact_manager=artifact_manager,
    metrics_tracker=metrics_tracker,
    evaluator=evaluator
)


@router.get("/analytics/account", response_model=Dict[str, Any])
@handle_exceptions
async def get_account_analytics(
    account_id: Optional[int] = None,
    current_account: int = Depends(get_current_account),
    current_user: SystemUser = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get analytics data for the current account.

    If account_id is provided and the user has admin privileges, returns data for that account.
    Otherwise, returns data for the current account.
    """
    # Create analytics service
    analytics_service = AnalyticsService(db)

    # Get account metrics using the service
    return await analytics_service.get_account_metrics(
        current_account_id=current_account,
        requested_account_id=account_id,
        current_user=current_user
    )


@router.get("/analytics/endpoints", response_model=List[Dict[str, Any]])
@handle_exceptions
async def get_endpoint_analytics(
    endpoint: Optional[str] = Query(None, description="Filter by specific endpoint"),
    method: Optional[str] = Query(None, description="Filter by HTTP method"),
    account_id: Optional[int] = None,
    current_account: int = Depends(get_current_account),
    current_user: SystemUser = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get analytics data for API endpoints.

    If account_id is provided and the user has admin privileges, returns data for that account.
    Otherwise, returns data for the current account.

    Can be filtered by endpoint path and HTTP method.
    """
    # Create analytics service
    analytics_service = AnalyticsService(db)

    # Get endpoint metrics using the service
    return await analytics_service.get_endpoint_metrics(
        current_account_id=current_account,
        requested_account_id=account_id,
        current_user=current_user,
        endpoint=endpoint,
        method=method
    )


@router.get("/analytics/recommendation_performance", response_model=Dict[str, Any])
@handle_exceptions
async def get_recommendation_performance(
    model_id: Optional[int] = Query(None, description="Filter by specific model ID"),
    metric_type: Optional[str] = Query(None, description="Filter by metric type (ndcg, map, catalog_coverage, etc.)"),
    account_id: Optional[int] = None,
    current_account: int = Depends(get_current_account),
    current_user: SystemUser = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get performance metrics for recommendation models.

    Returns metrics such as:
    - Precision/Recall/NDCG/MAP: Accuracy of recommendations
    - Coverage: Percentage of catalog being recommended
    - Diversity: Variety in recommendations
    - Novelty: Measure of how unpopular/unknown the recommended items are (based on inverse popularity)
    - Serendipity: Measure of how surprising but relevant the recommendations are
    - CTR (Click-Through Rate): Percentage of recommended items that receive clicks
    - CVR (Conversion Rate): Percentage of recommended items that result in conversions
    - Training time: Time taken to train models
    - Inference time: Time taken to generate recommendations
    - System metrics: CPU/Memory usage, latency percentiles

    If account_id is provided and the user has admin privileges, returns data for that account.
    Otherwise, returns data for the current account.
    """
    # Usar el servicio de métricas
    # Get recommendation metrics
    metrics = await metrics_tracker.get_metrics(
        db=db,
        account_id=account_id or current_account,
        metric_type=metric_type,
        model_id=model_id
    )

    # Add online metrics if available (CTR, etc.)
    analytics_service = AnalyticsService(db)
    online_metrics = await analytics_service.get_recommendation_metrics(
        current_account_id=current_account,
        requested_account_id=account_id,
        current_user=current_user
    )

    # Combine offline and online metrics
    combined_metrics = {
        "offline_metrics": metrics,
        "online_metrics": online_metrics,
        "model_id": model_id
    }

    return combined_metrics

@router.get("/analytics/models/compare", response_model=Dict[str, Any])
@handle_exceptions
async def compare_model_versions(
    model_ids: List[int] = Query(None, description="List of model IDs to compare"),
    limit: int = Query(5, description="Maximum number of models to compare if model_ids not provided"),
    account_id: Optional[int] = None,
    current_account: int = Depends(get_current_account),
    current_user: SystemUser = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Compara métricas entre diferentes versiones de modelos.
    
    Este endpoint permite comparar las métricas de rendimiento entre diferentes modelos
    para analizar mejoras o regresiones en el tiempo.
    
    Incluye métricas estándar de ML así como métricas proxy de negocio como:
    - Tasa de conversión en el conjunto de prueba
    - ROI estimado
    - Engagement estimado
    - Valor del cliente estimado
    
    Si se proporcionan model_ids, compara específicamente esos modelos.
    Si no, compara los últimos N modelos según el parámetro limit.
    
    Returns:
        Diccionario con comparación detallada de métricas entre versiones de modelos
    """
    # Determinar la cuenta objetivo
    target_account_id = account_id or current_account
    
    # Verificar si el usuario tiene permisos para acceder a la cuenta solicitada
    if account_id is not None and account_id != current_account:
        # Verificar si el usuario es administrador
        if not current_user.is_admin:
            from fastapi import HTTPException
            raise HTTPException(status_code=403, detail="No tiene permisos para acceder a esta cuenta")
    
    # Comparar modelos usando el servicio de métricas
    comparison = await metrics_tracker.compare_model_versions(
        db=db,
        account_id=target_account_id,
        model_ids=model_ids,
        limit=limit
    )
    
    return comparison

@router.get("/analytics/metrics/history", response_model=List[Dict[str, Any]])
@handle_exceptions
async def get_metrics_history(
    metric_name: str = Query(..., description="Name of the metric to get history for"),
    limit: int = Query(10, description="Maximum number of historical data points to return"),
    account_id: Optional[int] = None,
    current_account: int = Depends(get_current_account),
    current_user: SystemUser = Depends(get_current_admin_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Obtiene el historial de valores para una métrica específica.
    
    Este endpoint permite visualizar la evolución de una métrica a lo largo del tiempo
    para diferentes versiones de modelos.
    
    Útil para análisis de tendencias y para evaluar el impacto de cambios en el modelo.
    
    Args:
        metric_name: Nombre de la métrica a consultar
        limit: Número máximo de puntos de datos a devolver
        account_id: ID opcional de la cuenta
        
    Returns:
        Lista con valores históricos de la métrica
    """
    # Determinar la cuenta objetivo
    target_account_id = account_id or current_account
    
    # Verificar si el usuario tiene permisos para acceder a la cuenta solicitada
    if account_id is not None and account_id != current_account:
        # Verificar si el usuario es administrador
        if not current_user.is_admin:
            from fastapi import HTTPException
            raise HTTPException(status_code=403, detail="No tiene permisos para acceder a esta cuenta")
    
    # Obtener historial de la métrica
    history = await metrics_tracker.get_metrics_history(
        db=db,
        account_id=target_account_id,
        metric_name=metric_name,
        limit=limit
    )
    
    return history
