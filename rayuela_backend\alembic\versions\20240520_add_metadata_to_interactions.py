"""add metadata to interactions

Revision ID: 20240520_add_metadata
Revises:
Create Date: 2024-05-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20240520_add_metadata'
down_revision = None  # Reemplazar con la revisión anterior real
branch_labels = None
depends_on = None


def upgrade():
    # Añadir columna recommendation_metadata a la tabla interactions
    op.add_column('interactions', sa.Column('recommendation_metadata', sa.JSON(), nullable=True))


def downgrade():
    # Eliminar columna recommendation_metadata de la tabla interactions
    op.drop_column('interactions', 'recommendation_metadata')
