"""
Tests for the UsageMeterService.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from src.services.usage_meter_service import UsageMeterService
from src.core.exceptions import RateLimitExceededError, LimitExceededError


@pytest.fixture
def mock_db():
    """Mock database session."""
    db = AsyncMock()
    db.commit = AsyncMock()
    db.rollback = AsyncMock()
    db.execute = AsyncMock()
    return db


@pytest.fixture
def mock_redis():
    """Mock Redis connection."""
    redis = AsyncMock()
    redis.incr = AsyncMock(return_value=1)
    redis.get = AsyncMock(return_value=None)
    redis.set = AsyncMock()
    redis.expire = AsyncMock()
    redis.keys = AsyncMock(return_value=[b"monthly_counter:123:2023-01"])
    redis.delete = AsyncMock()
    return redis


@pytest.fixture
def usage_meter_service(mock_db, mock_redis):
    """Create a UsageMeterService with mocked dependencies."""
    service = UsageMeterService(mock_db, mock_redis)
    return service


@pytest.mark.asyncio
async def test_increment_api_call(usage_meter_service, mock_redis):
    """Test incrementing API call counter."""
    # Arrange
    account_id = "123"
    mock_redis.incr.return_value = 5

    # Act
    result = await usage_meter_service.increment_api_call(account_id)

    # Assert
    assert result == 5
    mock_redis.incr.assert_called_once()
    mock_redis.expire.assert_not_called()  # Should not be called for existing keys


@pytest.mark.asyncio
async def test_increment_api_call_new_key(usage_meter_service, mock_redis):
    """Test incrementing API call counter with a new key."""
    # Arrange
    account_id = "123"
    mock_redis.incr.return_value = 1  # First call

    # Act
    result = await usage_meter_service.increment_api_call(account_id)

    # Assert
    assert result == 1
    mock_redis.incr.assert_called_once()
    mock_redis.expire.assert_called_once()  # Should be called for new keys


@pytest.mark.asyncio
async def test_increment_api_call_redis_error(usage_meter_service, mock_redis, mock_db):
    """Test incrementing API call counter with Redis error."""
    # Arrange
    account_id = "123"
    mock_redis.incr.side_effect = Exception("Redis error")
    
    # Mock the database fallback
    mock_subscription = MagicMock()
    mock_subscription.monthly_api_calls_used = 10
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = mock_subscription
    mock_db.execute.return_value = mock_result

    # Act
    result = await usage_meter_service.increment_api_call(account_id)

    # Assert
    assert result == 11  # 10 + 1
    mock_db.execute.assert_called()
    mock_db.commit.assert_called_once()


@pytest.mark.asyncio
async def test_check_rate_limit_not_exceeded(usage_meter_service, mock_redis):
    """Test checking rate limit that is not exceeded."""
    # Arrange
    account_id = "123"
    max_requests_per_minute = 10
    mock_redis.incr.return_value = 5  # Below limit

    # Act
    result = await usage_meter_service.check_rate_limit(account_id, max_requests_per_minute)

    # Assert
    assert result is True
    mock_redis.incr.assert_called_once()


@pytest.mark.asyncio
async def test_check_rate_limit_exceeded(usage_meter_service, mock_redis):
    """Test checking rate limit that is exceeded."""
    # Arrange
    account_id = "123"
    max_requests_per_minute = 10
    mock_redis.incr.return_value = 11  # Above limit

    # Act & Assert
    with pytest.raises(RateLimitExceededError):
        await usage_meter_service.check_rate_limit(account_id, max_requests_per_minute)

    mock_redis.incr.assert_called_once()


@pytest.mark.asyncio
async def test_check_monthly_limit_not_exceeded(usage_meter_service, mock_redis):
    """Test checking monthly limit that is not exceeded."""
    # Arrange
    account_id = "123"
    max_requests_per_month = 1000
    mock_redis.get.return_value = b"500"  # Below limit

    # Act
    result = await usage_meter_service.check_monthly_limit(account_id, max_requests_per_month)

    # Assert
    assert result is True
    mock_redis.get.assert_called_once()


@pytest.mark.asyncio
async def test_check_monthly_limit_exceeded(usage_meter_service, mock_redis):
    """Test checking monthly limit that is exceeded."""
    # Arrange
    account_id = "123"
    max_requests_per_month = 1000
    mock_redis.get.return_value = b"1001"  # Above limit

    # Act & Assert
    with pytest.raises(LimitExceededError):
        await usage_meter_service.check_monthly_limit(account_id, max_requests_per_month)

    mock_redis.get.assert_called_once()


@pytest.mark.asyncio
async def test_sync_counters_to_db(usage_meter_service, mock_redis, mock_db):
    """Test syncing counters to database."""
    # Arrange
    mock_redis.keys.return_value = [b"monthly_counter:123:2023-01", b"monthly_counter:456:2023-01"]
    mock_redis.get.side_effect = [b"500", b"700"]
    
    # Mock the database update
    mock_subscription = MagicMock()
    mock_subscription.monthly_api_calls_used = 400  # Lower than Redis count
    mock_result = MagicMock()
    mock_result.scalars.return_value.first.return_value = mock_subscription
    mock_db.execute.return_value = mock_result

    # Act
    result = await usage_meter_service.sync_counters_to_db()

    # Assert
    assert "123" in result
    assert "456" in result
    assert result["123"] == 500
    assert result["456"] == 700
    assert mock_db.execute.call_count == 4  # 2 selects + 2 updates
    assert mock_db.commit.call_count == 2
