"""
Utilidades para consultas a la base de datos.
"""
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Any, List, TypeVar, Generic, Optional

T = TypeVar('T')


async def execute_paginated_query(db: AsyncSession, query, skip: int, limit: int) -> List[Any]:
    """
    Ejecuta una consulta paginada.
    
    Args:
        db: Sesión de base de datos
        query: Consulta SQLAlchemy
        skip: Número de elementos a saltar
        limit: Número máximo de elementos a devolver
        
    Returns:
        Lista de resultados paginados
    """
    paginated_query = query.offset(skip).limit(limit)
    result = await db.execute(paginated_query)
    return result.scalars().all()
