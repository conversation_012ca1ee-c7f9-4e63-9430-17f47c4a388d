"""Initial schema setup based on models

Revision ID: 4fdd0755dc85
Revises:
Create Date: 2025-04-09 14:25:43.102526

"""

from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "4fdd0755dc85"
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "accounts",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column(
            "subscription_plan",
            sa.Enum("FREE", "BASIC", "PRO", "ENTERPRISE", name="subscriptionplan"),
            nullable=False,
        ),
        sa.Column("api_key", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.PrimaryKeyConstraint("account_id"),
        sa.UniqueConstraint("account_id", "api_key", name="uq_account_api_key"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_account_api_key", "accounts", ["account_id", "api_key"], unique=False
    )
    op.create_table(
        "account_usage_metrics",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("api_calls_count", sa.Integer(), nullable=True),
        sa.Column("storage_used", sa.Integer(), nullable=True),
        sa.Column("last_reset", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_table(
        "artifact_metadata",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("artifact_name", sa.String(), nullable=True),
        sa.Column("artifact_version", sa.String(), nullable=True),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("training_date", sa.DateTime(), nullable=True),
        sa.Column(
            "performance_metrics",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
        ),
        sa.Column("parameters", postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column("artifacts_path", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        sa.UniqueConstraint(
            "account_id",
            "artifact_name",
            "artifact_version",
            name="uq_artifact_name_version",
        ),
        sa.UniqueConstraint("account_id", "id", name="uq_artifact_metadata_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_artifact_account_date",
        "artifact_metadata",
        ["account_id", "training_date"],
        unique=False,
    )
    op.create_index(
        "idx_artifact_account_name_version",
        "artifact_metadata",
        ["account_id", "artifact_name", "artifact_version"],
        unique=False,
    )
    op.create_index(
        op.f("ix_artifact_metadata_artifact_name"),
        "artifact_metadata",
        ["artifact_name"],
        unique=False,
    )
    op.create_index(
        op.f("ix_artifact_metadata_id"), "artifact_metadata", ["id"], unique=False
    )
    op.create_table(
        "audit_logs",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("action", sa.String(), nullable=False),
        sa.Column("entity_type", sa.String(), nullable=False),
        sa.Column("entity_id", sa.String(), nullable=False),
        sa.Column("changes", sa.JSON(), nullable=True),
        sa.Column("performed_by", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("details", sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        sa.UniqueConstraint("account_id", "id", name="uq_audit_log_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_audit_account_timestamp",
        "audit_logs",
        ["account_id", "created_at"],
        unique=False,
    )
    op.create_index(op.f("ix_audit_logs_id"), "audit_logs", ["id"], unique=False)
    op.create_table(
        "end_users",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("external_id", sa.String(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        sa.UniqueConstraint(
            "account_id", "external_id", name="uq_end_user_external_id"
        ),
        sa.UniqueConstraint("account_id", "id", name="uq_end_user_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_end_user_account_external_id",
        "end_users",
        ["account_id", "external_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_end_users_external_id"), "end_users", ["external_id"], unique=False
    )
    op.create_index(op.f("ix_end_users_id"), "end_users", ["id"], unique=False)
    op.create_table(
        "notifications",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column(
            "type",
            sa.Enum("INFO", "WARNING", "ERROR", "SUCCESS", name="notificationtype"),
            nullable=False,
        ),
        sa.Column("message", sa.Text(), nullable=False),
        sa.Column("is_read", sa.Boolean(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"], ["accounts.account_id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_table(
        "permissions",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.ForeignKeyConstraint(
            ["account_id"], ["accounts.account_id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id", "account_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_table(
        "products",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(length=255), nullable=False),
        sa.Column("description", sa.Text(), nullable=True),
        sa.Column("price", sa.Float(), nullable=False),
        sa.Column("category", sa.String(length=100), nullable=False),
        sa.Column("average_rating", sa.Float(), nullable=True),
        sa.Column("num_ratings", sa.Integer(), nullable=True),
        sa.Column("inventory_count", sa.Integer(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_product_account_category",
        "products",
        ["account_id", "category"],
        unique=False,
    )
    op.create_index(op.f("ix_products_id"), "products", ["id"], unique=False)
    op.create_table(
        "roles",
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("name", sa.String(), nullable=False),
        sa.Column("description", sa.String(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"], ["accounts.account_id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("id", "account_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_table(
        "subscriptions",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("plan_type", sa.String(), nullable=False),
        sa.Column("api_calls_limit", sa.Integer(), nullable=True),
        sa.Column("storage_limit", sa.Integer(), nullable=True),
        sa.Column("training_frequency", sa.Interval(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        sa.UniqueConstraint("account_id", "id", name="uq_subscription_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(op.f("ix_subscriptions_id"), "subscriptions", ["id"], unique=False)
    op.create_index(
        op.f("ix_subscriptions_plan_type"), "subscriptions", ["plan_type"], unique=False
    )
    op.create_table(
        "system_users",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("email", sa.String(), nullable=False),
        sa.Column("hashed_password", sa.String(), nullable=False),
        sa.Column("created_at", sa.DateTime(), nullable=True),
        sa.Column("updated_at", sa.DateTime(), nullable=True),
        sa.Column("is_active", sa.Boolean(), nullable=True),
        sa.Column("is_admin", sa.Boolean(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id"], ["accounts.account_id"], ondelete="CASCADE"
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        sa.UniqueConstraint(
            "account_id", "id", "email", name="uq_system_user_id_email"
        ),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_system_user_email", "system_users", ["account_id", "email"], unique=False
    )
    op.create_index(op.f("ix_system_users_id"), "system_users", ["id"], unique=False)
    op.create_table(
        "interactions",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("end_user_id", sa.Integer(), nullable=False),
        sa.Column("product_id", sa.Integer(), nullable=False),
        sa.Column(
            "interaction_type",
            sa.Enum(
                "VIEW",
                "LIKE",
                "PURCHASE",
                "CART",
                "RATING",
                "WISHLIST",
                "CLICK",
                "SEARCH",
                "FAVORITE",
                name="interactiontype",
            ),
            nullable=False,
        ),
        sa.Column("value", sa.Float(), nullable=True),
        sa.Column(
            "timestamp",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["account_id", "end_user_id"],
            ["end_users.account_id", "end_users.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["account_id", "product_id"],
            ["products.account_id", "products.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        sa.UniqueConstraint(
            "account_id",
            "end_user_id",
            "product_id",
            "interaction_type",
            name="uq_interaction",
        ),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_interaction_account_product",
        "interactions",
        ["account_id", "product_id"],
        unique=False,
    )
    op.create_index(
        "idx_interaction_account_timestamp",
        "interactions",
        ["account_id", "timestamp"],
        unique=False,
    )
    op.create_index(
        "idx_interaction_account_user",
        "interactions",
        ["account_id", "end_user_id"],
        unique=False,
    )
    op.create_index(op.f("ix_interactions_id"), "interactions", ["id"], unique=False)
    op.create_table(
        "role_permissions",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("role_id", sa.Integer(), nullable=False),
        sa.Column("permission_id", sa.Integer(), nullable=False),
        sa.ForeignKeyConstraint(
            ["account_id", "permission_id"],
            ["permissions.account_id", "permissions.id"],
            name="fk_role_permissions_permission",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["account_id", "role_id"],
            ["roles.account_id", "roles.id"],
            name="fk_role_permissions_role",
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
            name="fk_role_permissions_account",
            ondelete="CASCADE",
        ),
        sa.PrimaryKeyConstraint("account_id", "role_id", "permission_id"),
    )
    op.create_table(
        "searches",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("end_user_id", sa.Integer(), nullable=False),
        sa.Column("query", sa.Text(), nullable=True),
        sa.Column(
            "timestamp",
            sa.DateTime(timezone=True),
            server_default=sa.text("now()"),
            nullable=True,
        ),
        sa.ForeignKeyConstraint(
            ["account_id", "end_user_id"],
            ["end_users.account_id", "end_users.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_search_account_timestamp",
        "searches",
        ["account_id", "timestamp"],
        unique=False,
    )
    op.create_index(
        "idx_search_account_user",
        "searches",
        ["account_id", "end_user_id"],
        unique=False,
    )
    op.create_index(op.f("ix_searches_id"), "searches", ["id"], unique=False)
    op.create_table(
        "training_jobs",
        sa.Column("account_id", sa.Integer(), nullable=False),
        sa.Column("id", sa.Integer(), nullable=False),
        sa.Column("artifact_metadata_id", sa.Integer(), nullable=False),
        sa.Column(
            "status",
            sa.Enum(
                "PENDING", "RUNNING", "COMPLETED", "FAILED", name="trainingjobstatus"
            ),
            nullable=True,
        ),
        sa.Column(
            "started_at", sa.DateTime(), server_default=sa.text("now()"), nullable=True
        ),
        sa.Column("completed_at", sa.DateTime(), nullable=True),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.ForeignKeyConstraint(
            ["account_id", "artifact_metadata_id"],
            ["artifact_metadata.account_id", "artifact_metadata.id"],
            ondelete="CASCADE",
        ),
        sa.ForeignKeyConstraint(
            ["account_id"],
            ["accounts.account_id"],
        ),
        sa.PrimaryKeyConstraint("account_id", "id"),
        sa.UniqueConstraint("account_id", "id", name="uq_training_job_id"),
        postgresql_partition_by="RANGE (account_id)",
    )
    op.create_index(
        "idx_job_account_metadata",
        "training_jobs",
        ["account_id", "artifact_metadata_id"],
        unique=False,
    )
    op.create_index(
        "idx_job_account_status",
        "training_jobs",
        ["account_id", "status"],
        unique=False,
    )
    op.create_index(op.f("ix_training_jobs_id"), "training_jobs", ["id"], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_training_jobs_id"), table_name="training_jobs")
    op.drop_index("idx_job_account_status", table_name="training_jobs")
    op.drop_index("idx_job_account_metadata", table_name="training_jobs")
    op.drop_table("training_jobs")
    op.drop_index(op.f("ix_searches_id"), table_name="searches")
    op.drop_index("idx_search_account_user", table_name="searches")
    op.drop_index("idx_search_account_timestamp", table_name="searches")
    op.drop_table("searches")
    op.drop_table("role_permissions")
    op.drop_index(op.f("ix_interactions_id"), table_name="interactions")
    op.drop_index("idx_interaction_account_user", table_name="interactions")
    op.drop_index("idx_interaction_account_timestamp", table_name="interactions")
    op.drop_index("idx_interaction_account_product", table_name="interactions")
    op.drop_table("interactions")
    op.drop_index(op.f("ix_system_users_id"), table_name="system_users")
    op.drop_index("idx_system_user_email", table_name="system_users")
    op.drop_table("system_users")
    op.drop_index(op.f("ix_subscriptions_plan_type"), table_name="subscriptions")
    op.drop_index(op.f("ix_subscriptions_id"), table_name="subscriptions")
    op.drop_table("subscriptions")
    op.drop_table("roles")
    op.drop_index(op.f("ix_products_id"), table_name="products")
    op.drop_index("idx_product_account_category", table_name="products")
    op.drop_table("products")
    op.drop_table("permissions")
    op.drop_table("notifications")
    op.drop_index(op.f("ix_end_users_id"), table_name="end_users")
    op.drop_index(op.f("ix_end_users_external_id"), table_name="end_users")
    op.drop_index("idx_end_user_account_external_id", table_name="end_users")
    op.drop_table("end_users")
    op.drop_index(op.f("ix_audit_logs_id"), table_name="audit_logs")
    op.drop_index("idx_audit_account_timestamp", table_name="audit_logs")
    op.drop_table("audit_logs")
    op.drop_index(op.f("ix_artifact_metadata_id"), table_name="artifact_metadata")
    op.drop_index(
        op.f("ix_artifact_metadata_artifact_name"), table_name="artifact_metadata"
    )
    op.drop_index("idx_artifact_account_name_version", table_name="artifact_metadata")
    op.drop_index("idx_artifact_account_date", table_name="artifact_metadata")
    op.drop_table("artifact_metadata")
    op.drop_table("account_usage_metrics")
    op.drop_index("idx_account_api_key", table_name="accounts")
    op.drop_table("accounts")
    # ### end Alembic commands ###
