# Particionamiento de Tablas en PostgreSQL

Este documento explica la estrategia de particionamiento utilizada en el sistema y cómo se gestionan las particiones.

## Estrategia de Particionamiento

El sistema utiliza **particionamiento declarativo** de PostgreSQL basado en el campo `account_id`. Esto permite:

1. Mejor rendimiento para consultas que filtran por `account_id`
2. Mantenimiento más eficiente (se pueden eliminar particiones enteras cuando se elimina una cuenta)
3. Mejor escalabilidad para grandes volúmenes de datos

## Implementación

### 1. Definición en Modelos SQLAlchemy

Los modelos SQLAlchemy definen el particionamiento usando `postgresql_partition_by`:

```python
__table_args__ = (
    # Otras restricciones e índices...
    {"postgresql_partition_by": ACCOUNT_RANGE},
)
```

Donde `ACCOUNT_RANGE` es una constante definida como `"RANGE (account_id)"`.

### 2. Creación de Particiones

El particionamiento se implementa en dos pasos:

1. **Alembic crea la estructura de tabla particionada** durante las migraciones iniciales.
2. **El script `manage_partitions.py` crea las particiones individuales** para rangos específicos de `account_id`.

## Script `manage_partitions.py`

Este script se encarga de crear proactivamente las particiones necesarias para cada tabla particionada.

### Funcionamiento

1. Obtiene el `account_id` máximo actual en la base de datos
2. Para cada tabla particionada:
   - Verifica las particiones existentes
   - Crea la partición para el rango actual si no existe
   - Crea particiones adicionales según el buffer configurado

### Configuración

El script utiliza los siguientes parámetros de configuración:

- `PARTITION_SIZE`: Tamaño de cada partición (por ejemplo, 100,000 account_ids por partición)
- `PARTITION_BUFFER_COUNT`: Número de particiones adicionales a crear más allá del máximo actual

### Ejecución

El script debe ejecutarse periódicamente (por ejemplo, mediante un cron job) para asegurar que siempre haya suficientes particiones disponibles:

```bash
# Ejecución manual
python -m scripts.manage_partitions

# Ejemplo de configuración de cron (diariamente a las 2 AM)
0 2 * * * cd /path/to/app && python -m scripts.manage_partitions
```

## Tablas Particionadas

Las tablas particionadas se definen en `src/utils/partition_helper.py` en la constante `ALLOWED_TABLES`:

```python
ALLOWED_TABLES = {
    "accounts", "subscriptions", "system_users", "roles", "permissions",
    "role_permissions", "system_user_roles", "audit_logs", "end_users",
    "products", "interactions", "searches", "artifact_metadata", "training_jobs",
    "account_usage_metrics"
}
```

## Convención de Nombres

Las particiones siguen esta convención de nombres:

```
{table_name}_p_{start_range}_{end_range}
```

Por ejemplo: `interactions_p_0_100000` para interacciones con account_id de 0 a 99,999.

## Consideraciones Importantes

1. **Nunca insertar datos directamente** en las particiones individuales, siempre usar la tabla principal.
2. **No modificar manualmente las particiones** a menos que sea absolutamente necesario.
3. **Ejecutar regularmente el script `manage_partitions.py`** para asegurar que haya suficientes particiones.
4. **Monitorear el crecimiento** de las tablas y ajustar `PARTITION_SIZE` si es necesario.

## Solución de Problemas

Si encuentra errores relacionados con particiones faltantes, ejecute el script `manage_partitions.py` para crear las particiones necesarias.

Si necesita crear particiones para un rango específico, puede usar el script `start.py`:

```bash
python -m scripts.start manage_partitions --target-max-id 500000 --buffer-count 10
```
