"use client";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BillingPortalButton } from '@/components/dashboard/BillingPortalButton';
import { AccountInfo, AccountUsage, PlanInfo } from '@/lib/api';
import {
  getCurrentPlan,
  getPlanDisplayName,
  formatRenewalDate,
  formatNumber,
  formatBytes,
  getUsagePercentage,
  getUsageStatusColor
} from '@/lib/billing-utils';
import { 
  CreditCardIcon, 
  CalendarIcon, 
  TrendingUpIcon, 
  DatabaseIcon,
  ExternalLinkIcon 
} from 'lucide-react';

interface CurrentPlanCardProps {
  accountData?: AccountInfo | null;
  usageData?: AccountUsage | null;
  plans?: Record<string, PlanInfo> | null;
}

export function CurrentPlanCard({ accountData, usageData, plans }: CurrentPlanCardProps) {
  const currentPlanId = getCurrentPlan(accountData, usageData);
  const currentPlan = currentPlanId && plans ? plans[currentPlanId] : null;
  
  // Get usage data
  const apiCallsUsed = usageData?.api_calls?.used || 0;
  const apiCallsLimit = usageData?.api_calls?.limit || currentPlan?.limits?.api_calls || 0;
  const storageUsed = usageData?.storage?.used_bytes || 0;
  const storageLimit = usageData?.storage?.limit_bytes || currentPlan?.limits?.storage_bytes || 0;
  
  // Calculate percentages
  const apiCallsPercentage = getUsagePercentage(apiCallsUsed, apiCallsLimit);
  const storagePercentage = getUsagePercentage(storageUsed, storageLimit);
  
  // Get renewal date
  const renewalDate = accountData?.subscription?.expires_at || usageData?.subscription?.expires_at;

  if (!currentPlanId || !currentPlan) {
    return (
      <Card className="border-dashed">
        <CardHeader>
          <CardTitle className="flex items-center">
            <CreditCardIcon className="h-5 w-5 mr-2 text-muted-foreground" />
            Plan Actual
          </CardTitle>
          <CardDescription>
            No se pudo cargar la información del plan actual
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      {/* Plan Information Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <CreditCardIcon className="h-5 w-5 mr-2 text-blue-600" />
              Tu Plan Actual
            </div>
            {currentPlan.recommended && (
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                Recomendado
              </Badge>
            )}
          </CardTitle>
          <CardDescription>
            Información de tu suscripción y próxima renovación
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Plan Name and Price */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-2xl font-bold">{getPlanDisplayName(currentPlanId)}</h3>
              <div className="text-right">
                <div className="text-lg font-semibold">{currentPlan.price}</div>
                {currentPlan.price !== 'Gratis' && currentPlan.price !== 'Contactar' && (
                  <div className="text-sm text-muted-foreground">por mes</div>
                )}
              </div>
            </div>
            <p className="text-sm text-muted-foreground">{currentPlan.description}</p>
          </div>

          {/* Renewal Date */}
          <div className="flex items-center space-x-2 text-sm">
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">Renovación:</span>
            <span className="font-medium">{formatRenewalDate(renewalDate)}</span>
          </div>

          {/* Manage Subscription Button */}
          <div className="pt-4">
            <BillingPortalButton 
              className="w-full"
              variant="outline"
            >
              <ExternalLinkIcon className="mr-2 h-4 w-4" />
              Gestionar Suscripción
            </BillingPortalButton>
          </div>
        </CardContent>
      </Card>

      {/* Usage Summary Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUpIcon className="h-5 w-5 mr-2 text-green-600" />
            Resumen de Uso
          </CardTitle>
          <CardDescription>
            Tu consumo actual vs. los límites de tu plan
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* API Calls Usage */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUpIcon className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Llamadas API</span>
              </div>
              <span className={`text-sm font-medium ${getUsageStatusColor(apiCallsPercentage)}`}>
                {apiCallsPercentage.toFixed(1)}%
              </span>
            </div>
            <div className="space-y-2">
              <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className={`h-full transition-all duration-300 ${
                    apiCallsPercentage >= 90 ? 'bg-red-500' :
                    apiCallsPercentage >= 75 ? 'bg-yellow-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${Math.min(apiCallsPercentage, 100)}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{formatNumber(apiCallsUsed)} usadas</span>
                <span>
                  {apiCallsLimit === -1 ? 'Ilimitadas' : `${formatNumber(apiCallsLimit)} límite`}
                </span>
              </div>
            </div>
          </div>

          {/* Storage Usage */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <DatabaseIcon className="h-4 w-4 text-purple-600" />
                <span className="font-medium">Almacenamiento</span>
              </div>
              <span className={`text-sm font-medium ${getUsageStatusColor(storagePercentage)}`}>
                {storagePercentage.toFixed(1)}%
              </span>
            </div>
            <div className="space-y-2">
              <div className="relative h-2 w-full overflow-hidden rounded-full bg-gray-200 dark:bg-gray-700">
                <div
                  className={`h-full transition-all duration-300 ${
                    storagePercentage >= 90 ? 'bg-red-500' :
                    storagePercentage >= 75 ? 'bg-yellow-500' : 'bg-purple-500'
                  }`}
                  style={{ width: `${Math.min(storagePercentage, 100)}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{formatBytes(storageUsed)} usado</span>
                <span>
                  {storageLimit === -1 ? 'Ilimitado' : `${formatBytes(storageLimit)} límite`}
                </span>
              </div>
            </div>
          </div>

          {/* Usage Warning */}
          {(apiCallsPercentage >= 80 || storagePercentage >= 80) && (
            <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                <strong>Atención:</strong> Te estás acercando a los límites de tu plan. 
                Considera actualizar para evitar interrupciones en el servicio.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
