"""
Servicio para manejar la validación de límites del sistema.
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.core.exceptions import LimitExceededError
from src.db.models import Account, SystemUser, Subscription, AccountUsageMetrics
from src.db.enums import SubscriptionPlan, PLAN_LIMITS
from src.db.repositories import (
    AccountRepository,
    SubscriptionRepository,
    UsageMetricsRepository
)
from src.utils.base_logger import log_info, log_error

class LimitService:
    def __init__(self, db: AsyncSession, account_id: str):
        self.db = db
        self.account_id = account_id
        self.account_repo = AccountRepository(db)
        self.subscription_repo = SubscriptionRepository(db)
        self.usage_repo = UsageMetricsRepository(db)
        
    async def check_all_limits(
        self,
        user: SystemUser,
        additional_api_calls: int = 0,
        additional_storage_bytes: int = 0,
        additional_products: int = 0,
        additional_users: int = 0
    ) -> None:
        """
        Verifica todos los límites del sistema para una cuenta.
        """
        try:
            # Obtener suscripción activa
            subscription = await self._get_active_subscription()
            if not subscription:
                raise LimitExceededError("No active subscription found")
                
            # Obtener métricas de uso actuales
            usage_metrics = await self._get_current_usage()
            
            # Verificar cada tipo de límite
            await self.check_api_limits(usage_metrics, additional_api_calls)
            await self.check_storage_limits(usage_metrics, additional_storage_bytes)
            await self.check_product_limits(usage_metrics, additional_products)
            await self.check_user_limits(usage_metrics, additional_users)
            
        except Exception as e:
            log_error(f"Error checking limits: {str(e)}")
            raise
            
    async def check_api_limits(
        self,
        usage_metrics: AccountUsageMetrics,
        additional_calls: int = 0
    ) -> None:
        """
        Verifica los límites de llamadas API.
        """
        try:
            subscription = await self._get_active_subscription()
            if not subscription:
                raise LimitExceededError("No active subscription found")
                
            plan_limits = PLAN_LIMITS[subscription.plan_type]
            current_calls = usage_metrics.api_calls_count + additional_calls
            
            if current_calls > plan_limits['max_api_calls']:
                raise LimitExceededError(
                    f"API call limit exceeded. Current: {current_calls}, Limit: {plan_limits['max_api_calls']}"
                )
                
        except Exception as e:
            log_error(f"Error checking API limits: {str(e)}")
            raise
            
    async def check_storage_limits(
        self,
        usage_metrics: AccountUsageMetrics,
        additional_bytes: int = 0
    ) -> None:
        """
        Verifica los límites de almacenamiento.
        """
        try:
            subscription = await self._get_active_subscription()
            if not subscription:
                raise LimitExceededError("No active subscription found")
                
            plan_limits = PLAN_LIMITS[subscription.plan_type]
            current_storage = usage_metrics.storage_bytes + additional_bytes
            
            if current_storage > plan_limits['max_storage_bytes']:
                raise LimitExceededError(
                    f"Storage limit exceeded. Current: {current_storage}, Limit: {plan_limits['max_storage_bytes']}"
                )
                
        except Exception as e:
            log_error(f"Error checking storage limits: {str(e)}")
            raise
            
    async def check_product_limits(
        self,
        usage_metrics: AccountUsageMetrics,
        additional_products: int = 0
    ) -> None:
        """
        Verifica los límites de productos.
        """
        try:
            subscription = await self._get_active_subscription()
            if not subscription:
                raise LimitExceededError("No active subscription found")
                
            plan_limits = PLAN_LIMITS[subscription.plan_type]
            current_products = usage_metrics.products_count + additional_products
            
            if current_products > plan_limits['max_products']:
                raise LimitExceededError(
                    f"Product limit exceeded. Current: {current_products}, Limit: {plan_limits['max_products']}"
                )
                
        except Exception as e:
            log_error(f"Error checking product limits: {str(e)}")
            raise
            
    async def check_user_limits(
        self,
        usage_metrics: AccountUsageMetrics,
        additional_users: int = 0
    ) -> None:
        """
        Verifica los límites de usuarios.
        """
        try:
            subscription = await self._get_active_subscription()
            if not subscription:
                raise LimitExceededError("No active subscription found")
                
            plan_limits = PLAN_LIMITS[subscription.plan_type]
            current_users = usage_metrics.users_count + additional_users
            
            if current_users > plan_limits['max_users']:
                raise LimitExceededError(
                    f"User limit exceeded. Current: {current_users}, Limit: {plan_limits['max_users']}"
                )
                
        except Exception as e:
            log_error(f"Error checking user limits: {str(e)}")
            raise
            
    async def validate_training_data_limit(self, interaction_count: int) -> None:
        """
        Verifica los límites de datos de entrenamiento según el plan de suscripción.
        
        Args:
            interaction_count: Número de interacciones a utilizar para entrenamiento
            
        Raises:
            LimitExceededError: Si se excede el límite de datos de entrenamiento
        """
        try:
            subscription = await self._get_active_subscription()
            if not subscription:
                raise LimitExceededError("No active subscription found")
                
            plan_limits = PLAN_LIMITS[subscription.plan_type]
            
            # Si el plan es ENTERPRISE, no hay límite
            if subscription.plan_type == SubscriptionPlan.ENTERPRISE:
                return
                
            # Verificar límite de datos de entrenamiento
            if interaction_count > plan_limits.get('max_training_interactions', 10000):
                raise LimitExceededError(
                    f"Training data limit exceeded. Your plan ({subscription.plan_type.value}) allows up to "
                    f"{plan_limits.get('max_training_interactions', 10000)} interactions for training. "
                    f"Attempted to use {interaction_count} interactions."
                )
                
        except Exception as e:
            log_error(f"Error validating training data limit: {str(e)}")
            raise
            
    async def increment_api_calls(
        self,
        count: int = 1
    ) -> None:
        """
        Incrementa el contador de llamadas API.
        """
        try:
            async with self.db.begin():
                await self.usage_repo.increment_api_calls(self.account_id, count)
                
        except Exception as e:
            log_error(f"Error incrementing API calls: {str(e)}")
            raise
            
    async def increment_storage(
        self,
        bytes_count: int
    ) -> None:
        """
        Incrementa el contador de almacenamiento.
        """
        try:
            async with self.db.begin():
                await self.usage_repo.increment_storage(self.account_id, bytes_count)
                
        except Exception as e:
            log_error(f"Error incrementing storage: {str(e)}")
            raise
            
    async def _get_active_subscription(self) -> Optional[Subscription]:
        """Obtiene la suscripción activa de la cuenta."""
        return await self.subscription_repo.get_active_subscription(self.account_id)
        
    async def _get_current_usage(self) -> AccountUsageMetrics:
        """Obtiene las métricas de uso actuales de la cuenta."""
        return await self.usage_repo.get_current_metrics(self.account_id)
