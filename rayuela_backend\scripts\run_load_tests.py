#!/usr/bin/env python
"""
Script para ejecutar tests de carga en Rayuela.
Este script inicia la API simplificada y ejecuta los tests de carga con Locust.
"""

import os
import sys
import time
import asyncio
import subprocess
import threading
import click
import signal
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.core.config import settings
from src.utils.base_logger import logger


def start_api_server():
    """Iniciar el servidor API en un proceso separado"""
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    logger.info("Iniciando servidor API...")
    api_process = subprocess.Popen(
        ["python", "-m", "scripts.run_api_simple"],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        env=env
    )

    # Esperar a que el servidor esté listo
    time.sleep(5)

    return api_process


def run_locust_tests(host="localhost", port=8001, users=10, spawn_rate=1,
                     run_time=60, tags=None, headless=True):
    """Ejecutar tests de carga con Locust"""
    # Configurar el entorno para que locust pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")

    cmd = [
        "locust",
        "-f",
        "tests/load/locustfile.py",
        "--host",
        f"http://{host}:{port}",
    ]

    if headless:
        cmd.extend([
            "--headless",
            "-u", str(users),
            "-r", str(spawn_rate),
            "-t", f"{run_time}s",
            "--html", "load_test_report.html",
        ])

    if tags:
        cmd.extend(["--tags", tags])

    logger.info(f"Ejecutando tests de carga contra {host}:{port}...")
    result = subprocess.run(cmd, env=env)

    if headless:
        logger.info(f"Reporte generado en load_test_report.html")

    return result.returncode == 0


@click.command()
@click.option("--host", default="localhost", help="Host para el servidor")
@click.option("--port", default=8001, help="Puerto para el servidor")
@click.option("--users", default=10, help="Número de usuarios concurrentes")
@click.option("--spawn-rate", default=1, help="Tasa de creación de usuarios por segundo")
@click.option("--time", default=60, help="Tiempo de ejecución en segundos")
@click.option("--tags", default=None, help="Tags para filtrar tests (separados por comas)")
@click.option("--headless", is_flag=True, default=True, help="Ejecutar en modo headless (sin interfaz web)")
@click.option("--no-api", is_flag=True, help="No iniciar el servidor API (asume que ya está en ejecución)")
def main(host, port, users, spawn_rate, time, tags, headless, no_api):
    """Ejecutar tests de carga para Rayuela"""
    api_process = None

    try:
        # Iniciar servidor API si es necesario
        if not no_api:
            api_process = start_api_server()

        # Ejecutar tests de carga
        success = run_locust_tests(
            host=host,
            port=port,
            users=users,
            spawn_rate=spawn_rate,
            run_time=time,
            tags=tags,
            headless=headless
        )

        if success:
            logger.info("Tests de carga completados correctamente.")
            return 0
        else:
            logger.error("Tests de carga fallaron.")
            return 1

    finally:
        # Detener el servidor API si lo iniciamos
        if api_process:
            logger.info("Deteniendo servidor API...")
            api_process.terminate()
            api_process.wait()


if __name__ == "__main__":
    sys.exit(main())
