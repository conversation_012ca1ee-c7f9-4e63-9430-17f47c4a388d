#!/usr/bin/env python
"""
Script para monitorear el crecimiento de tablas de alto volumen y proporcionar
recomendaciones sobre la creación o modificación de índices.

Uso:
    python -m scripts.monitor_table_growth --table interactions
    python -m scripts.monitor_table_growth --table audit_logs
    python -m scripts.monitor_table_growth --table products
    python -m scripts.monitor_table_growth --all
"""

import os
import sys
import asyncio
import argparse
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from tabulate import tabulate
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.db.session import get_db
from src.utils.base_logger import logger
from src.core.config import settings


# Tablas de alto volumen a monitorear
HIGH_VOLUME_TABLES = ["interactions", "audit_logs", "products"]


async def get_table_stats(db: AsyncSession, table_name: str) -> Dict[str, Any]:
    """Obtiene estadísticas de una tabla específica."""
    try:
        # Obtener tamaño de la tabla
        size_query = """
            SELECT
                pg_size_pretty(pg_total_relation_size(:table_name)) as total_size,
                pg_size_pretty(pg_relation_size(:table_name)) as table_size,
                pg_size_pretty(pg_total_relation_size(:table_name) - pg_relation_size(:table_name)) as index_size,
                pg_total_relation_size(:table_name) as total_bytes,
                pg_relation_size(:table_name) as table_bytes,
                (pg_total_relation_size(:table_name) - pg_relation_size(:table_name)) as index_bytes
            FROM pg_catalog.pg_tables
            WHERE tablename = :table_name
        """
        size_result = await db.execute(text(size_query), {"table_name": table_name})
        size_row = size_result.fetchone()
        
        if not size_row:
            return {"error": f"Table {table_name} not found"}
        
        # Obtener conteo de filas
        count_query = f"SELECT COUNT(*) FROM {table_name}"
        count_result = await db.execute(text(count_query))
        row_count = count_result.scalar()
        
        # Obtener conteo por account_id
        account_count_query = f"""
            SELECT account_id, COUNT(*) as row_count
            FROM {table_name}
            GROUP BY account_id
            ORDER BY row_count DESC
            LIMIT 10
        """
        account_result = await db.execute(text(account_count_query))
        account_counts = [{"account_id": row[0], "row_count": row[1]} for row in account_result]
        
        # Obtener información de índices
        index_query = """
            SELECT
                indexname,
                indexdef,
                pg_size_pretty(pg_relation_size(quote_ident(indexname)::text)) as index_size,
                pg_relation_size(quote_ident(indexname)::text) as index_bytes
            FROM pg_indexes
            WHERE tablename = :table_name
            ORDER BY pg_relation_size(quote_ident(indexname)::text) DESC
        """
        index_result = await db.execute(text(index_query), {"table_name": table_name})
        indexes = [
            {
                "name": row[0],
                "definition": row[1],
                "size": row[2],
                "bytes": row[3]
            }
            for row in index_result
        ]
        
        # Obtener estadísticas de uso de índices
        index_usage_query = """
            SELECT
                relname as table_name,
                indexrelname as index_name,
                idx_scan as index_scans,
                idx_tup_read as tuples_read,
                idx_tup_fetch as tuples_fetched
            FROM pg_stat_user_indexes
            WHERE relname = :table_name
            ORDER BY idx_scan DESC
        """
        index_usage_result = await db.execute(text(index_usage_query), {"table_name": table_name})
        index_usage = [
            {
                "table_name": row[0],
                "index_name": row[1],
                "index_scans": row[2],
                "tuples_read": row[3],
                "tuples_fetched": row[4]
            }
            for row in index_usage_result
        ]
        
        # Obtener tasa de crecimiento (si hay datos históricos)
        growth_data = await get_growth_data(db, table_name)
        
        return {
            "table_name": table_name,
            "total_size": size_row[0],
            "table_size": size_row[1],
            "index_size": size_row[2],
            "total_bytes": size_row[3],
            "table_bytes": size_row[4],
            "index_bytes": size_row[5],
            "row_count": row_count,
            "account_counts": account_counts,
            "indexes": indexes,
            "index_usage": index_usage,
            "growth_data": growth_data
        }
    except Exception as e:
        logger.error(f"Error getting stats for table {table_name}: {e}")
        return {"error": str(e)}


async def get_growth_data(db: AsyncSession, table_name: str) -> Dict[str, Any]:
    """Obtiene datos de crecimiento histórico de una tabla."""
    try:
        # Verificar si existe la tabla de estadísticas
        check_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'table_growth_stats'
            )
        """
        check_result = await db.execute(text(check_query))
        table_exists = check_result.scalar()
        
        if not table_exists:
            # Crear la tabla si no existe
            create_query = """
                CREATE TABLE IF NOT EXISTS table_growth_stats (
                    id SERIAL PRIMARY KEY,
                    table_name VARCHAR(100) NOT NULL,
                    row_count BIGINT NOT NULL,
                    total_bytes BIGINT NOT NULL,
                    table_bytes BIGINT NOT NULL,
                    index_bytes BIGINT NOT NULL,
                    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                )
            """
            await db.execute(text(create_query))
            await db.commit()
            return {"history": [], "growth_rate": None}
        
        # Obtener datos históricos
        history_query = """
            SELECT 
                row_count, 
                total_bytes, 
                table_bytes, 
                index_bytes, 
                timestamp
            FROM table_growth_stats
            WHERE table_name = :table_name
            ORDER BY timestamp ASC
        """
        history_result = await db.execute(text(history_query), {"table_name": table_name})
        history = [
            {
                "row_count": row[0],
                "total_bytes": row[1],
                "table_bytes": row[2],
                "index_bytes": row[3],
                "timestamp": row[4]
            }
            for row in history_result
        ]
        
        # Calcular tasa de crecimiento si hay suficientes datos
        growth_rate = None
        if len(history) >= 2:
            first = history[0]
            last = history[-1]
            days = (last["timestamp"] - first["timestamp"]).days
            if days > 0:
                row_growth = (last["row_count"] - first["row_count"]) / days
                size_growth = (last["total_bytes"] - first["total_bytes"]) / days
                growth_rate = {
                    "rows_per_day": row_growth,
                    "bytes_per_day": size_growth,
                    "mb_per_day": size_growth / (1024 * 1024),
                    "days_measured": days
                }
        
        return {"history": history, "growth_rate": growth_rate}
    except Exception as e:
        logger.error(f"Error getting growth data for table {table_name}: {e}")
        return {"history": [], "growth_rate": None, "error": str(e)}


async def save_current_stats(db: AsyncSession, table_name: str, stats: Dict[str, Any]):
    """Guarda las estadísticas actuales en la tabla de historial."""
    try:
        if "error" in stats:
            return
        
        insert_query = """
            INSERT INTO table_growth_stats 
            (table_name, row_count, total_bytes, table_bytes, index_bytes)
            VALUES (:table_name, :row_count, :total_bytes, :table_bytes, :index_bytes)
        """
        await db.execute(
            text(insert_query),
            {
                "table_name": table_name,
                "row_count": stats["row_count"],
                "total_bytes": stats["total_bytes"],
                "table_bytes": stats["table_bytes"],
                "index_bytes": stats["index_bytes"]
            }
        )
        await db.commit()
        logger.info(f"Saved current stats for table {table_name}")
    except Exception as e:
        logger.error(f"Error saving stats for table {table_name}: {e}")


def generate_growth_chart(table_name: str, growth_data: Dict[str, Any]):
    """Genera un gráfico de crecimiento para una tabla."""
    history = growth_data.get("history", [])
    if not history:
        logger.warning(f"No growth history available for table {table_name}")
        return
    
    # Crear directorio para gráficos si no existe
    charts_dir = Path(root_dir) / "reports" / "charts"
    charts_dir.mkdir(parents=True, exist_ok=True)
    
    # Preparar datos para el gráfico
    timestamps = [item["timestamp"] for item in history]
    row_counts = [item["row_count"] for item in history]
    total_sizes = [item["total_bytes"] / (1024 * 1024) for item in history]  # Convertir a MB
    
    # Crear figura con dos subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8), sharex=True)
    
    # Gráfico de conteo de filas
    ax1.plot(timestamps, row_counts, 'b-', marker='o')
    ax1.set_ylabel('Número de filas')
    ax1.set_title(f'Crecimiento de la tabla {table_name}')
    ax1.grid(True)
    
    # Gráfico de tamaño total
    ax2.plot(timestamps, total_sizes, 'r-', marker='o')
    ax2.set_ylabel('Tamaño total (MB)')
    ax2.set_xlabel('Fecha')
    ax2.grid(True)
    
    # Formatear eje x para mostrar fechas
    fig.autofmt_xdate()
    
    # Ajustar espaciado
    plt.tight_layout()
    
    # Guardar gráfico
    chart_path = charts_dir / f"{table_name}_growth.png"
    plt.savefig(chart_path)
    logger.info(f"Growth chart saved to {chart_path}")
    
    # Cerrar figura para liberar memoria
    plt.close(fig)


def analyze_index_usage(stats: Dict[str, Any]) -> List[str]:
    """Analiza el uso de índices y proporciona recomendaciones."""
    recommendations = []
    
    if "error" in stats:
        return [f"Error al analizar índices: {stats['error']}"]
    
    # Verificar índices no utilizados
    unused_indexes = []
    for usage in stats.get("index_usage", []):
        if usage["index_scans"] == 0:
            unused_indexes.append(usage["index_name"])
    
    if unused_indexes:
        recommendations.append(f"Los siguientes índices no se están utilizando y podrían eliminarse: {', '.join(unused_indexes)}")
    
    # Verificar índices compuestos con account_id
    has_account_id_first = False
    for index in stats.get("indexes", []):
        if "account_id" in index["definition"] and "(" in index["definition"]:
            # Verificar si account_id es la primera columna en el índice
            columns_start = index["definition"].find("(") + 1
            columns_end = index["definition"].find(")")
            if columns_end > columns_start:
                columns = index["definition"][columns_start:columns_end].split(",")
                first_column = columns[0].strip()
                if first_column == "account_id":
                    has_account_id_first = True
                else:
                    recommendations.append(
                        f"El índice {index['name']} no tiene account_id como primera columna. "
                        f"Considere reordenar las columnas para mejorar el rendimiento."
                    )
    
    if not has_account_id_first:
        recommendations.append(
            "No se encontraron índices compuestos con account_id como primera columna. "
            "Considere crear índices que utilicen account_id como primera columna para mejorar el rendimiento."
        )
    
    # Analizar distribución por account_id
    account_counts = stats.get("account_counts", [])
    if account_counts:
        max_count = account_counts[0]["row_count"] if account_counts else 0
        total_rows = stats.get("row_count", 0)
        if max_count > 0 and total_rows > 0:
            percentage = (max_count / total_rows) * 100
            if percentage > 50:
                recommendations.append(
                    f"La cuenta {account_counts[0]['account_id']} contiene el {percentage:.2f}% de las filas. "
                    f"Considere implementar particionamiento adicional o estrategias de archivado para esta cuenta."
                )
    
    # Analizar ratio de tamaño de índices vs tabla
    table_bytes = stats.get("table_bytes", 0)
    index_bytes = stats.get("index_bytes", 0)
    if table_bytes > 0:
        index_ratio = (index_bytes / table_bytes) * 100
        if index_ratio > 100:
            recommendations.append(
                f"Los índices ocupan {index_ratio:.2f}% del tamaño de la tabla. "
                f"Considere revisar y posiblemente eliminar índices redundantes o no utilizados."
            )
    
    # Analizar tasa de crecimiento
    growth_rate = stats.get("growth_data", {}).get("growth_rate")
    if growth_rate:
        rows_per_day = growth_rate.get("rows_per_day", 0)
        mb_per_day = growth_rate.get("mb_per_day", 0)
        if rows_per_day > 10000 or mb_per_day > 10:
            recommendations.append(
                f"La tabla está creciendo a un ritmo de {rows_per_day:.2f} filas y {mb_per_day:.2f} MB por día. "
                f"Considere implementar estrategias de particionamiento y archivado."
            )
    
    # Si no hay recomendaciones, agregar una nota positiva
    if not recommendations:
        recommendations.append("No se encontraron problemas con los índices. La configuración actual parece adecuada.")
    
    return recommendations


def print_table_stats(stats: Dict[str, Any]):
    """Imprime las estadísticas de una tabla en formato legible."""
    if "error" in stats:
        print(f"Error: {stats['error']}")
        return
    
    print(f"\n{'='*80}")
    print(f"Estadísticas para la tabla: {stats['table_name']}")
    print(f"{'='*80}")
    
    print(f"Tamaño total: {stats['total_size']}")
    print(f"Tamaño de la tabla: {stats['table_size']}")
    print(f"Tamaño de los índices: {stats['index_size']}")
    print(f"Número de filas: {stats['row_count']}")
    
    # Mostrar distribución por account_id
    print("\nDistribución por account_id (Top 10):")
    account_data = []
    for item in stats.get("account_counts", []):
        percentage = (item["row_count"] / stats["row_count"]) * 100 if stats["row_count"] > 0 else 0
        account_data.append([item["account_id"], item["row_count"], f"{percentage:.2f}%"])
    
    if account_data:
        print(tabulate(account_data, headers=["Account ID", "Filas", "Porcentaje"], tablefmt="grid"))
    else:
        print("No hay datos de distribución por account_id.")
    
    # Mostrar información de índices
    print("\nÍndices:")
    index_data = []
    for index in stats.get("indexes", []):
        index_data.append([index["name"], index["size"], index["definition"]])
    
    if index_data:
        print(tabulate(index_data, headers=["Nombre", "Tamaño", "Definición"], tablefmt="grid"))
    else:
        print("No hay índices definidos.")
    
    # Mostrar uso de índices
    print("\nUso de índices:")
    usage_data = []
    for usage in stats.get("index_usage", []):
        usage_data.append([
            usage["index_name"],
            usage["index_scans"],
            usage["tuples_read"],
            usage["tuples_fetched"]
        ])
    
    if usage_data:
        print(tabulate(usage_data, headers=["Nombre", "Escaneos", "Tuplas leídas", "Tuplas obtenidas"], tablefmt="grid"))
    else:
        print("No hay datos de uso de índices.")
    
    # Mostrar tasa de crecimiento
    growth_rate = stats.get("growth_data", {}).get("growth_rate")
    if growth_rate:
        print("\nTasa de crecimiento:")
        print(f"Filas por día: {growth_rate['rows_per_day']:.2f}")
        print(f"MB por día: {growth_rate['mb_per_day']:.2f}")
        print(f"Período de medición: {growth_rate['days_measured']} días")
    else:
        print("\nNo hay datos suficientes para calcular la tasa de crecimiento.")
    
    # Mostrar recomendaciones
    recommendations = analyze_index_usage(stats)
    print("\nRecomendaciones:")
    for i, rec in enumerate(recommendations, 1):
        print(f"{i}. {rec}")
    
    print(f"{'='*80}\n")


async def main():
    parser = argparse.ArgumentParser(description="Monitor growth of high-volume tables")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--table", choices=HIGH_VOLUME_TABLES, help="Table to monitor")
    group.add_argument("--all", action="store_true", help="Monitor all high-volume tables")
    parser.add_argument("--save", action="store_true", help="Save current stats to history")
    parser.add_argument("--chart", action="store_true", help="Generate growth charts")
    
    args = parser.parse_args()
    
    tables_to_monitor = HIGH_VOLUME_TABLES if args.all else [args.table]
    
    async for db in get_db():
        for table in tables_to_monitor:
            logger.info(f"Monitoring table: {table}")
            stats = await get_table_stats(db, table)
            
            if args.save and "error" not in stats:
                await save_current_stats(db, table, stats)
            
            if args.chart and "error" not in stats:
                generate_growth_chart(table, stats.get("growth_data", {}))
            
            print_table_stats(stats)
        
        # Solo necesitamos una iteración
        break


if __name__ == "__main__":
    asyncio.run(main())
