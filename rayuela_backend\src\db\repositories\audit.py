"""
Repositorios para la gestión de logs de auditoría y notificaciones.
"""

from sqlalchemy import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from datetime import datetime
from src.db import models, schemas
from src.db.enums import NotificationType
from .base import BaseRepository


class AuditLogRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: Optional[int] = None):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        # Si account_id es None, se obtendrán registros de todas las cuentas
        super().__init__(db, account_id=account_id, model=models.AuditLog)

    async def create(self, audit_log_create: schemas.AuditLogCreate) -> models.AuditLog:
        try:
            audit_log = models.AuditLog(
                **audit_log_create.model_dump(), account_id=self.account_id
            )
            self.db.add(audit_log)
            await self.db.refresh(audit_log)
            return audit_log
        except SQLAlchemyError as e:
            await self._handle_error("creating audit log", e)

    async def get_logs_by_user(
        self, user_id: int, limit: int = 100
    ) -> List[models.AuditLog]:
        """Obtener logs de auditoría por usuario."""
        try:
            query = (
                select(models.AuditLog)
                .filter(
                    models.AuditLog.user_id == user_id,
                    models.AuditLog.account_id == self.account_id,
                )
                .order_by(models.AuditLog.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching audit logs by user", e)
            return []

    async def get_logs_by_action(
        self, action: str, limit: int = 100
    ) -> List[models.AuditLog]:
        """Obtener logs de auditoría por acción."""
        try:
            query = (
                select(models.AuditLog)
                .filter(
                    models.AuditLog.action == action,
                    models.AuditLog.account_id == self.account_id,
                )
                .order_by(models.AuditLog.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching audit logs by action", e)
            return []

    async def get_logs_by_date_range(
        self, start_date: datetime, end_date: datetime, limit: int = 100
    ) -> List[models.AuditLog]:
        """Obtener logs de auditoría en un rango de fechas."""
        try:
            query = (
                select(models.AuditLog)
                .filter(
                    models.AuditLog.account_id == self.account_id,
                    models.AuditLog.created_at >= start_date,
                    models.AuditLog.created_at <= end_date,
                )
                .order_by(models.AuditLog.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching audit logs by date range", e)
            return []

    async def get_logs_by_resource(
        self, resource_type: str, resource_id: Optional[int] = None, limit: int = 100
    ) -> List[models.AuditLog]:
        """Obtener logs de auditoría por tipo de recurso y opcionalmente ID de recurso."""
        try:
            query = select(models.AuditLog).filter(
                models.AuditLog.resource_type == resource_type,
                models.AuditLog.account_id == self.account_id,
            )

            if resource_id is not None:
                query = query.filter(models.AuditLog.resource_id == resource_id)

            query = query.order_by(models.AuditLog.created_at.desc()).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching audit logs by resource", e)
            return []

    async def get_filtered_logs(
        self,
        user_id: Optional[int] = None,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[models.AuditLog]:
        """Obtener logs de auditoría con múltiples filtros."""
        try:
            query = select(models.AuditLog).filter(
                models.AuditLog.account_id == self.account_id,
            )

            if user_id is not None:
                query = query.filter(models.AuditLog.user_id == user_id)

            if action is not None:
                query = query.filter(models.AuditLog.action == action)

            if resource_type is not None:
                query = query.filter(models.AuditLog.resource_type == resource_type)

            if resource_id is not None:
                query = query.filter(models.AuditLog.resource_id == resource_id)

            if start_date is not None:
                query = query.filter(models.AuditLog.created_at >= start_date)

            if end_date is not None:
                query = query.filter(models.AuditLog.created_at <= end_date)

            query = query.order_by(models.AuditLog.created_at.desc()).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching filtered audit logs", e)
            return []


class NotificationRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: Optional[int] = None):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        # Si account_id es None, se obtendrán registros de todas las cuentas
        super().__init__(db, account_id=account_id, model=models.Notification)

    async def create(
        self, notification_create: schemas.NotificationCreate
    ) -> models.Notification:
        try:
            notification = models.Notification(
                **notification_create.model_dump(), account_id=self.account_id
            )
            self.db.add(notification)
            await self.db.refresh(notification)
            return notification
        except SQLAlchemyError as e:
            await self._handle_error("creating notification", e)

    async def get_pending_notifications(
        self, user_id: int, limit: int = 10
    ) -> List[models.Notification]:
        try:
            query = (
                select(models.Notification)
                .filter(
                    models.Notification.user_id == user_id,
                    models.Notification.read_at.is_(None),
                    models.Notification.account_id == self.account_id,
                )
                .order_by(models.Notification.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching pending notifications", e)
            return []

    async def mark_as_read(self, notification_id: int) -> Optional[models.Notification]:
        try:
            notification = await self.get_by_id(notification_id)
            if not notification:
                return None

            notification.read_at = datetime.utcnow()
            await self.db.refresh(notification)
            return notification
        except SQLAlchemyError as e:
            await self._handle_error("marking notification as read", e)
            return None

    async def mark_all_as_read(self, user_id: int) -> int:
        """Marcar todas las notificaciones de un usuario como leídas."""
        try:
            query = select(models.Notification).filter(
                models.Notification.user_id == user_id,
                models.Notification.read_at.is_(None),
                models.Notification.account_id == self.account_id,
            )

            result = await self.db.execute(query)
            notifications = result.scalars().all()

            count = 0
            for notification in notifications:
                notification.read_at = datetime.utcnow()
                count += 1

            return count
        except SQLAlchemyError as e:
            await self._handle_error("marking all notifications as read", e)
            return 0

    async def get_notifications_by_type(
        self, user_id: int, notification_type: NotificationType, limit: int = 10
    ) -> List[models.Notification]:
        """Obtener notificaciones por tipo."""
        try:
            query = (
                select(models.Notification)
                .filter(
                    models.Notification.user_id == user_id,
                    models.Notification.notification_type == notification_type,
                    models.Notification.account_id == self.account_id,
                )
                .order_by(models.Notification.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching notifications by type", e)
            return []

    async def delete_old_notifications(self, days: int = 30) -> int:
        """Eliminar notificaciones antiguas."""
        try:
            cutoff_date = datetime.utcnow() - datetime.timedelta(days=days)

            query = select(models.Notification).filter(
                models.Notification.created_at < cutoff_date,
                models.Notification.account_id == self.account_id,
            )

            result = await self.db.execute(query)
            notifications = result.scalars().all()

            count = 0
            for notification in notifications:
                await self.db.delete(notification)
                count += 1

            return count
        except SQLAlchemyError as e:
            await self._handle_error("deleting old notifications", e)
            return 0
