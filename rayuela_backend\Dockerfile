# Build stage
FROM python:3.12-slim AS builder

WORKDIR /app

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
	build-essential \
	gcc \
	g++ \
	gfortran \
	libgomp1 \
	libopenblas-dev \
	&& rm -rf /var/lib/apt/lists/*

# Copy requirements and add gunicorn
COPY requirements.txt .
RUN echo "gunicorn==21.2.0" >> requirements.txt

# Build wheels for all dependencies
RUN pip wheel --no-cache-dir --no-deps --wheel-dir /app/wheels -r requirements.txt

# Final stage - optimized for production
FROM python:3.12-slim

# Install only runtime dependencies - no build tools
RUN apt-get update && apt-get install -y --no-install-recommends \
	libgomp1 \
	libopenblas0 \
	&& apt-get clean \
	&& rm -rf /var/lib/apt/lists/*

# Create non-root user
RUN useradd -m -u 1000 appuser

WORKDIR /app

# Copy wheels and requirements from builder stage
COPY --from=builder /app/wheels /wheels
COPY --from=builder /app/requirements.txt .

# Install dependencies from pre-built wheels and clean up
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir /wheels/* && \
    pip install --no-cache-dir asgiref==3.8.1 && \
    pip install --no-cache-dir itsdangerous && \
    rm -rf /wheels /root/.cache/pip/*

# Copy only necessary application code
COPY ./src ./src
COPY ./alembic ./alembic
COPY alembic.ini .
COPY main.py .
COPY gunicorn_conf.py .

# Change ownership of files
RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Environment variables for production
ENV ENV=production \
	PYTHONUNBUFFERED=1 \
	PYTHONDONTWRITEBYTECODE=1 \
	API_HOST=0.0.0.0

# Cloud Run will set PORT environment variable
# Default to 8080 for local development
ENV PORT=8080

# Expose application port
EXPOSE ${PORT}

# Health check
HEALTHCHECK CMD curl --fail http://localhost:${PORT}/health || exit 1

# Use gunicorn with uvicorn workers for production
CMD ["gunicorn", "main:app", "-c", "gunicorn_conf.py"]