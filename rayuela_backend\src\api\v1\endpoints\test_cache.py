from fastapi import APIRouter
from fastapi_cache.decorator import cache
import redis.asyncio as aioredis
import time
from src.core.config import settings

router = APIRouter()


# Añade esto a tu archivo de rutas
@router.get("/test-cache")
@cache(expire=30)  # Cache por 30 segundos
async def test_cache():
    # Simulamos una operación costosa
    time.sleep(2)  # En producción usar asyncio.sleep(2)
    return {
        "timestamp": time.time(),
        "message": "Si ves el mismo timestamp en múltiples llamadas en menos de 30 segundos, el caché está funcionando",
    }


# Endpoint para verificar el estado de Redis
@router.get("/redis-health")
async def check_redis():
    try:
        redis = aioredis.from_url(
            settings.REDIS_URL, encoding="utf8", decode_responses=True
        )
        await redis.ping()
        keys = await redis.keys("*")
        await redis.close()
        return {"status": "connected", "cached_keys": len(keys), "keys": keys}
    except Exception as e:
        return {"status": "error", "error": str(e)}
