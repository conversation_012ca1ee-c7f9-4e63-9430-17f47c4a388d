"""
Servicio para manejar la lógica de suscripciones.
"""
from datetime import datetime, timezone
from typing import Optional, Dict, Any
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from src.core.exceptions import LimitExceededError
from src.db.models import Account, Subscription, AccountUsageMetrics
from src.db.enums import SubscriptionPlan, PLAN_LIMITS
from src.db.repositories import (
    AccountRepository,
    SubscriptionRepository,
    UsageMetricsRepository
)
from src.utils.base_logger import log_info, log_error, log_warning
from src.core.config import settings

class SubscriptionService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.account_repo = AccountRepository(db)
        self.subscription_repo = SubscriptionRepository(db)
        self.usage_repo = UsageMetricsRepository(db)

    async def get_active_subscription(
        self,
        account_id: int
    ) -> Optional[Subscription]:
        """
        Obtiene la suscripción activa de una cuenta.
        
        Args:
            account_id: ID de la cuenta
            
        Returns:
            La suscripción activa o None si no existe
        """
        try:
            # Usamos el repositorio para obtener la suscripción activa
            query = select(Subscription).where(
                Subscription.account_id == account_id,
                Subscription.is_active == True
            )
            result = await self.db.execute(query)
            return result.scalars().first()
        except Exception as e:
            log_error(f"Error getting active subscription: {str(e)}")
            return None

    async def create_subscription(
        self,
        account_id: str,
        plan_type: SubscriptionPlan,
        stripe_customer_id: str,
        stripe_subscription_id: str
    ) -> Subscription:
        """
        Crea una nueva suscripción.
        """
        try:
            async with self.db.begin():
                # Crear suscripción
                subscription = Subscription(
                    account_id=account_id,
                    plan_type=plan_type,
                    stripe_customer_id=stripe_customer_id,
                    stripe_subscription_id=stripe_subscription_id,
                    is_active=True,
                    start_date=datetime.now(timezone.utc)
                )

                # Guardar suscripción
                await self.subscription_repo.create(subscription)

                return subscription

        except Exception as e:
            log_error(f"Error creating subscription: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating subscription"
            )

    async def update_subscription(
        self,
        subscription: Subscription,
        update_data: Dict[str, Any]
    ) -> Subscription:
        """
        Actualiza una suscripción existente.
        """
        try:
            async with self.db.begin():
                # Actualizar suscripción
                updated_subscription = await self.subscription_repo.update(
                    subscription,
                    update_data
                )

                return updated_subscription

        except Exception as e:
            log_error(f"Error updating subscription: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating subscription"
            )

    async def cancel_subscription(
        self,
        subscription: Subscription
    ) -> Subscription:
        """
        Cancela una suscripción.
        """
        try:
            async with self.db.begin():
                # Actualizar suscripción
                update_data = {
                    'is_active': False,
                    'end_date': datetime.now(timezone.utc)
                }

                updated_subscription = await self.subscription_repo.update(
                    subscription,
                    update_data
                )

                return updated_subscription

        except Exception as e:
            log_error(f"Error canceling subscription: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error canceling subscription"
            )

    async def handle_stripe_event(
        self,
        event_type: str,
        event_data: Dict[str, Any]
    ) -> None:
        """
        Maneja eventos de Stripe.
        """
        try:
            if event_type == 'customer.subscription.created':
                await self._handle_subscription_created(event_data)
            elif event_type == 'customer.subscription.updated':
                await self._handle_subscription_updated(event_data)
            elif event_type == 'customer.subscription.deleted':
                await self._handle_subscription_deleted(event_data)
            else:
                log_info(f"Unhandled Stripe event type: {event_type}")

        except Exception as e:
            log_error(f"Error handling Stripe event: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error handling Stripe event"
            )

    async def _handle_subscription_created(
        self,
        event_data: Dict[str, Any]
    ) -> None:
        """Maneja el evento de creación de suscripción."""
        try:
            subscription_data = event_data['data']['object']

            # Obtener datos necesarios
            account_id = subscription_data['metadata']['account_id']
            plan_type = SubscriptionPlan[subscription_data['plan']['nickname'].upper()]
            stripe_customer_id = subscription_data['customer']
            stripe_subscription_id = subscription_data['id']

            # Crear suscripción
            await self.create_subscription(
                account_id=account_id,
                plan_type=plan_type,
                stripe_customer_id=stripe_customer_id,
                stripe_subscription_id=stripe_subscription_id
            )

        except Exception as e:
            log_error(f"Error handling subscription created event: {str(e)}")
            raise

    async def _handle_subscription_updated(
        self,
        event_data: Dict[str, Any]
    ) -> None:
        """Maneja el evento de actualización de suscripción."""
        try:
            subscription_data = event_data['data']['object']
            stripe_subscription_id = subscription_data['id']

            # Obtener suscripción existente
            subscription = await self.subscription_repo.get_by_stripe_id(
                stripe_subscription_id
            )
            if not subscription:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Subscription not found"
                )

            # Preparar datos de actualización
            update_data = {
                'plan_type': SubscriptionPlan[subscription_data['plan']['nickname'].upper()],
                'is_active': subscription_data['status'] == 'active'
            }

            # Actualizar suscripción
            await self.update_subscription(subscription, update_data)

        except Exception as e:
            log_error(f"Error handling subscription updated event: {str(e)}")
            raise

    async def _handle_subscription_deleted(
        self,
        event_data: Dict[str, Any]
    ) -> None:
        """Maneja el evento de eliminación de suscripción."""
        try:
            subscription_data = event_data['data']['object']
            stripe_subscription_id = subscription_data['id']

            # Obtener suscripción existente
            subscription = await self.subscription_repo.get_by_stripe_id(
                stripe_subscription_id
            )
            if not subscription:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Subscription not found"
                )

            # Cancelar suscripción
            await self.cancel_subscription(subscription)

        except Exception as e:
            log_error(f"Error handling subscription deleted event: {str(e)}")
            raise

    # Métodos para manejar eventos de Mercado Pago

    async def handle_mercadopago_payment_approved(
        self,
        payment: Dict[str, Any]
    ) -> None:
        """
        Maneja un pago aprobado de Mercado Pago.

        Args:
            payment: Datos del pago de Mercado Pago
        """
        try:
            # Obtener datos del pago
            metadata = payment.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago payment metadata")
                return

            # Obtener la suscripción asociada
            subscription_id = payment.get("subscription_id")
            if subscription_id:
                # Es un pago de suscripción recurrente
                await self._update_subscription_from_mercadopago(subscription_id, account_id)
            else:
                # Es un pago único, verificar si hay un plan asociado
                price_id = metadata.get("price_id")
                if price_id:
                    # Crear o actualizar la suscripción
                    plan_type = self._get_plan_from_price_id(price_id)

                    # Preparar datos de la suscripción
                    subscription_data = {
                        "plan_type": plan_type,
                        "api_calls_limit": PLAN_LIMITS.get(plan_type, {}).get("api_calls_limit", 0),
                        "storage_limit": PLAN_LIMITS.get(plan_type, {}).get("storage_limit", 0),
                        "mercadopago_subscription_id": subscription_id,
                        "mercadopago_price_id": price_id,
                        "payment_gateway": "mercadopago",
                        "is_active": True,
                    }

                    # Crear o actualizar la suscripción
                    subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))
                    from src.db.schemas.subscription import SubscriptionCreate
                    subscription_create = SubscriptionCreate(**subscription_data)
                    await subscription_repo.create_or_update(subscription_create)

                    log_info(f"Subscription created/updated for account {account_id} from Mercado Pago payment")

        except Exception as e:
            log_error(f"Error handling Mercado Pago payment approved: {str(e)}")
            raise

    async def handle_mercadopago_payment_failed(
        self,
        payment: Dict[str, Any]
    ) -> None:
        """
        Maneja un pago fallido de Mercado Pago.

        Args:
            payment: Datos del pago de Mercado Pago
        """
        try:
            # Obtener datos del pago
            metadata = payment.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago payment metadata")
                return

            # Registrar el evento de pago fallido
            log_warning(f"Payment failed for account {account_id} in Mercado Pago")

            # No modificamos la suscripción todavía, solo registramos el evento
            # Mercado Pago intentará el cobro nuevamente

        except Exception as e:
            log_error(f"Error handling Mercado Pago payment failed: {str(e)}")
            raise

    async def handle_mercadopago_subscription_created(
        self,
        subscription: Dict[str, Any]
    ) -> None:
        """
        Maneja una suscripción creada en Mercado Pago.

        Args:
            subscription: Datos de la suscripción de Mercado Pago
        """
        try:
            # Obtener datos de la suscripción
            metadata = subscription.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago subscription metadata")
                return

            # Obtener el plan
            price_id = metadata.get("price_id")
            if not price_id:
                log_warning("No price_id found in Mercado Pago subscription metadata")
                return

            # Determinar el plan
            plan_type = self._get_plan_from_price_id(price_id)

            # Preparar datos de la suscripción
            subscription_data = {
                "plan_type": plan_type,
                "api_calls_limit": PLAN_LIMITS.get(plan_type, {}).get("api_calls_limit", 0),
                "storage_limit": PLAN_LIMITS.get(plan_type, {}).get("storage_limit", 0),
                "mercadopago_subscription_id": subscription["id"],
                "mercadopago_price_id": price_id,
                "payment_gateway": "mercadopago",
                "is_active": True,
            }

            # Crear o actualizar la suscripción
            subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))
            from src.db.schemas.subscription import SubscriptionCreate
            subscription_create = SubscriptionCreate(**subscription_data)
            await subscription_repo.create_or_update(subscription_create)

            log_info(f"Subscription created for account {account_id} from Mercado Pago")

        except Exception as e:
            log_error(f"Error handling Mercado Pago subscription created: {str(e)}")
            raise

    async def handle_mercadopago_subscription_paused(
        self,
        subscription: Dict[str, Any]
    ) -> None:
        """
        Maneja una suscripción pausada en Mercado Pago.

        Args:
            subscription: Datos de la suscripción de Mercado Pago
        """
        try:
            # Obtener datos de la suscripción
            metadata = subscription.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago subscription metadata")
                return

            # Buscar la suscripción en la base de datos
            subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))
            db_subscription = await subscription_repo.get_by_mercadopago_id(subscription["id"])

            if not db_subscription:
                log_warning(f"No subscription found for Mercado Pago subscription {subscription['id']}")
                return

            # Actualizar la suscripción
            update_data = {
                "is_active": False,
            }

            await subscription_repo.update(db_subscription.id, update_data)

            log_info(f"Subscription paused for account {account_id} in Mercado Pago")

        except Exception as e:
            log_error(f"Error handling Mercado Pago subscription paused: {str(e)}")
            raise

    async def handle_mercadopago_subscription_cancelled(
        self,
        subscription: Dict[str, Any]
    ) -> None:
        """
        Maneja una suscripción cancelada en Mercado Pago.

        Args:
            subscription: Datos de la suscripción de Mercado Pago
        """
        try:
            # Obtener datos de la suscripción
            metadata = subscription.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago subscription metadata")
                return

            # Buscar la suscripción en la base de datos
            subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))
            db_subscription = await subscription_repo.get_by_mercadopago_id(subscription["id"])

            if not db_subscription:
                log_warning(f"No subscription found for Mercado Pago subscription {subscription['id']}")
                return

            # Actualizar la suscripción al plan gratuito
            update_data = {
                "plan_type": SubscriptionPlan.FREE,
                "api_calls_limit": PLAN_LIMITS.get(SubscriptionPlan.FREE, {}).get("api_calls_limit", 0),
                "storage_limit": PLAN_LIMITS.get(SubscriptionPlan.FREE, {}).get("storage_limit", 0),
                "is_active": False,
            }

            await subscription_repo.update(db_subscription.id, update_data)

            log_info(f"Subscription cancelled for account {account_id} in Mercado Pago")

        except Exception as e:
            log_error(f"Error handling Mercado Pago subscription cancelled: {str(e)}")
            raise

    async def _update_subscription_from_mercadopago(
        self,
        subscription_id: str,
        account_id: str
    ) -> None:
        """
        Actualiza la suscripción en la base de datos a partir de los datos de Mercado Pago.

        Args:
            subscription_id: ID de la suscripción de Mercado Pago
            account_id: ID de la cuenta
        """
        try:
            # Importar el servicio de Mercado Pago
            from src.services.mercadopago_service import MercadoPagoService
            mercadopago_service = MercadoPagoService()

            # Obtener la suscripción de Mercado Pago
            subscription_data = mercadopago_service.sdk.preapproval().get(subscription_id)
            subscription = subscription_data.get("response", {})

            # Verificar que la suscripción existe
            if not subscription:
                log_warning(f"No subscription found in Mercado Pago with ID {subscription_id}")
                return

            # Obtener el plan
            price_id = subscription.get("metadata", {}).get("price_id")
            if not price_id:
                log_warning("No price_id found in Mercado Pago subscription metadata")
                return

            # Determinar el plan
            plan_type = self._get_plan_from_price_id(price_id)

            # Verificar si la suscripción está activa
            is_active = subscription.get("status") == "authorized"

            # Preparar datos de la suscripción
            subscription_data = {
                "plan_type": plan_type,
                "api_calls_limit": PLAN_LIMITS.get(plan_type, {}).get("api_calls_limit", 0),
                "storage_limit": PLAN_LIMITS.get(plan_type, {}).get("storage_limit", 0),
                "mercadopago_subscription_id": subscription_id,
                "mercadopago_price_id": price_id,
                "payment_gateway": "mercadopago",
                "is_active": is_active,
            }

            # Crear o actualizar la suscripción
            subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))
            from src.db.schemas.subscription import SubscriptionCreate
            subscription_create = SubscriptionCreate(**subscription_data)
            await subscription_repo.create_or_update(subscription_create)

            log_info(f"Subscription updated for account {account_id} from Mercado Pago")

        except Exception as e:
            log_error(f"Error updating subscription from Mercado Pago: {str(e)}")
            raise

    def _get_plan_from_price_id(self, price_id: str) -> SubscriptionPlan:
        """
        Determina el plan basado en el price_id.

        Args:
            price_id: ID del precio (Stripe o Mercado Pago)

        Returns:
            Plan de suscripción
        """
        # Verificar si es un price_id de Mercado Pago
        if settings.PAYMENT_GATEWAY == "mercadopago":
            price_map = {
                settings.MERCADOPAGO_PRICE_STARTER: SubscriptionPlan.STARTER,
                settings.MERCADOPAGO_PRICE_PRO: SubscriptionPlan.PRO,
                settings.MERCADOPAGO_PRICE_ENTERPRISE: SubscriptionPlan.ENTERPRISE
            }
        else:
            # Usar los price_id de Stripe
            price_map = {
                settings.STRIPE_PRICE_STARTER: SubscriptionPlan.STARTER,
                settings.STRIPE_PRICE_PRO: SubscriptionPlan.PRO,
                settings.STRIPE_PRICE_ENTERPRISE: SubscriptionPlan.ENTERPRISE
            }

        return price_map.get(price_id, SubscriptionPlan.FREE)
