import pytest
from fastapi import HTTPException
from src.core.exceptions import (
    NotFoundError,
    ValidationError,
    PermissionDeniedError,
    ConflictError,
    RateLimitExceededError,
    InternalServerError
)
from src.core.error_handlers import (
    handle_not_found,
    handle_validation_error,
    handle_permission_denied,
    handle_conflict,
    handle_rate_limit,
    handle_internal_error
)

class TestErrorHandling:
    """Tests de integración para el manejo de errores."""
    
    async def test_not_found_error_handling(self):
        """Test para manejo de error 404."""
        error = NotFoundError("Resource not found")
        response = await handle_not_found(error)
        
        assert response.status_code == 404
        assert response.json()["detail"] == "Resource not found"
    
    async def test_validation_error_handling(self):
        """Test para manejo de error 422."""
        error = ValidationError("Invalid input data")
        response = await handle_validation_error(error)
        
        assert response.status_code == 422
        assert "validation_error" in response.json()
    
    async def test_permission_denied_error_handling(self):
        """Test para manejo de error 403."""
        error = PermissionDeniedError("Access denied")
        response = await handle_permission_denied(error)
        
        assert response.status_code == 403
        assert response.json()["detail"] == "Access denied"
    
    async def test_conflict_error_handling(self):
        """Test para manejo de error 409."""
        error = ConflictError("Resource already exists")
        response = await handle_conflict(error)
        
        assert response.status_code == 409
        assert response.json()["detail"] == "Resource already exists"
    
    async def test_rate_limit_error_handling(self):
        """Test para manejo de error 429."""
        error = RateLimitExceededError("Too many requests")
        response = await handle_rate_limit(error)
        
        assert response.status_code == 429
        assert response.json()["detail"] == "Too many requests"
    
    async def test_internal_error_handling(self):
        """Test para manejo de error 500."""
        error = InternalServerError("Internal server error")
        response = await handle_internal_error(error)
        
        assert response.status_code == 500
        assert "internal_error" in response.json()
    
    async def test_error_handling_in_api(
        self,
        api_client,
        test_accounts,
        test_tokens
    ):
        """Test para manejo de errores en endpoints de la API."""
        account = test_accounts[0]
        api_key = test_tokens[account.id]["regular"]["api_key"]
        
        # Test 404 - Recurso no encontrado
        response = await api_client.get(
            "/products/999999",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 404
        
        # Test 422 - Datos inválidos
        response = await api_client.post(
            "/products",
            json={"name": "", "price": -10},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 422
        
        # Test 403 - Permiso denegado
        response = await api_client.post(
            "/admin/users",
            json={"email": "<EMAIL>"},
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 403
        
        # Test 409 - Conflicto
        response = await api_client.post(
            "/accounts/register",
            json={
                "email": account.email,  # Email ya existente
                "password": "test123",
                "name": "Test"
            }
        )
        assert response.status_code == 409
        
        # Test 429 - Límite de tasa excedido
        for _ in range(100):  # Exceder límite de requests
            await api_client.get(
                "/products",
                headers={"X-API-Key": api_key}
            )
        response = await api_client.get(
            "/products",
            headers={"X-API-Key": api_key}
        )
        assert response.status_code == 429
    
    async def test_error_handling_in_services(
        self,
        product_service,
        user_service,
        test_accounts
    ):
        """Test para manejo de errores en servicios."""
        account = test_accounts[0]
        
        # Test NotFoundError
        with pytest.raises(NotFoundError):
            await product_service.get_product(999999)
        
        # Test ValidationError
        with pytest.raises(ValidationError):
            await product_service.create_product(
                account_id=account.id,
                data={"name": "", "price": -10}
            )
        
        # Test PermissionDeniedError
        with pytest.raises(PermissionDeniedError):
            await user_service.create_admin_user(
                account_id=account.id,
                data={"email": "<EMAIL>"}
            )
        
        # Test ConflictError
        with pytest.raises(ConflictError):
            await user_service.create_user(
                account_id=account.id,
                data={"email": account.email}  # Email duplicado
            )
    
    async def test_error_handling_in_repositories(
        self,
        product_repository,
        test_accounts
    ):
        """Test para manejo de errores en repositorios."""
        account = test_accounts[0]
        
        # Test NotFoundError en get
        with pytest.raises(NotFoundError):
            await product_repository.get(999999)
        
        # Test ValidationError en create
        with pytest.raises(ValidationError):
            await product_repository.create(
                account_id=account.id,
                data={"name": "", "price": -10}
            )
        
        # Test ConflictError en create
        with pytest.raises(ConflictError):
            await product_repository.create(
                account_id=account.id,
                data={"name": "Test", "price": 10, "id": 1}  # ID duplicado
            ) 