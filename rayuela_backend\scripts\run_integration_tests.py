#!/usr/bin/env python
"""
Script para ejecutar tests de integración.
Este script configura la base de datos de prueba y ejecuta los tests de integración.
"""

import os
import sys
import argparse
import asyncio
import subprocess
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

from src.utils.base_logger import logger


def setup_test_database():
    """
    Configurar la base de datos de prueba.
    
    Returns:
        True si la configuración fue exitosa, False en caso contrario
    """
    # Configurar el entorno para que el script pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")
    
    try:
        # Usar el script simplificado para crear la base de datos
        logger.info("Configurando base de datos de prueba...")
        result = subprocess.run(
            ["python", "-m", "scripts.init_db_simple", "--db-name", "rayuela_test"],
            check=True,
            env=env
        )
        
        logger.info("Base de datos de prueba configurada correctamente.")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error al configurar la base de datos de prueba: {e}")
        return False


def run_integration_test(test_path, verbose=False):
    """
    Ejecutar un test de integración específico.
    
    Args:
        test_path: Ruta al archivo de test
        verbose: Mostrar salida detallada
    
    Returns:
        Código de salida (0 si todos los tests pasan, 1 si hay errores)
    """
    # Verificar que el archivo existe
    test_file = Path(test_path)
    if not test_file.exists():
        logger.error(f"El archivo {test_path} no existe.")
        return 1
    
    # Configurar el entorno para que pytest pueda encontrar los módulos
    env = os.environ.copy()
    env["PYTHONPATH"] = root_dir + os.pathsep + env.get("PYTHONPATH", "")
    
    # Construir el comando
    cmd = ["python", "-m", "pytest", test_path, "-xvs", "--no-header", "--no-summary"]
    
    if verbose:
        cmd.append("-v")
    
    # Ejecutar el comando
    logger.info(f"Ejecutando test de integración: {' '.join(cmd)}")
    result = subprocess.run(cmd, env=env)
    
    return result.returncode


def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Ejecutar tests de integración")
    parser.add_argument("--path", type=str, default=None, help="Ruta al archivo de test")
    parser.add_argument("--verbose", "-v", action="store_true", help="Mostrar salida detallada")
    parser.add_argument("--skip-setup", action="store_true", help="Omitir la configuración de la base de datos")
    
    args = parser.parse_args()
    
    # Configurar la base de datos de prueba
    if not args.skip_setup:
        if not setup_test_database():
            return 1
    
    # Ejecutar el test de integración
    if args.path:
        return run_integration_test(args.path, args.verbose)
    else:
        logger.error("Debe especificar la ruta al archivo de test.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
