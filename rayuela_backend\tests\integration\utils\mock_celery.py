"""
Utilities for mocking Celery tasks in integration tests.
"""
import asyncio
from typing import Dict, Any, Callable, Optional, List
from unittest.mock import patch
import pytest
import pytest_asyncio
from sqlalchemy.ext.asyncio import AsyncSession

from src.workers.celery_tasks import (
    _train_model_async,
    _train_model_for_job_async,
    _process_batch_data_async
)
from src.services.batch_data_storage_service import BatchDataStorageService


class MockCeleryTask:
    """Mock for a Celery task that can be executed synchronously in tests."""
    
    def __init__(self, task_id: str = "mock-task-id"):
        self.task_id = task_id
        self.args = []
        self.kwargs = {}
        self.queue = None
        self.result = None
        self.status = "PENDING"
        self.exception = None
    
    def apply_async(self, args=None, kwargs=None, queue=None, **options):
        """Mock for apply_async that stores the arguments and returns self."""
        self.args = args or []
        self.kwargs = kwargs or {}
        self.queue = queue
        return self
    
    def delay(self, *args, **kwargs):
        """Mock for delay that stores the arguments and returns self."""
        self.args = args
        self.kwargs = kwargs
        return self
    
    async def execute(self, db: AsyncSession):
        """Execute the task synchronously."""
        try:
            if self.task_id.startswith("train_model"):
                if len(self.args) == 2:
                    # train_model
                    account_id, data = self.args
                    self.result = await _train_model_async(db, account_id, data)
                else:
                    # train_model_for_job
                    account_id, job_id = self.args
                    self.result = await _train_model_for_job_async(db, account_id, job_id)
            elif self.task_id.startswith("process_batch_data"):
                account_id, job_id, file_path = self.args
                storage_service = BatchDataStorageService()
                self.result = await _process_batch_data_async(db, account_id, job_id, file_path, storage_service)
            
            self.status = "SUCCESS"
            return self.result
        except Exception as e:
            self.status = "FAILURE"
            self.exception = e
            raise


class MockCeleryTaskRegistry:
    """Registry for mock Celery tasks."""
    
    def __init__(self):
        self.tasks: Dict[str, MockCeleryTask] = {}
        self.executed_tasks: List[MockCeleryTask] = []
    
    def register_task(self, task_name: str) -> MockCeleryTask:
        """Register a new mock task."""
        task = MockCeleryTask(task_id=task_name)
        self.tasks[task_name] = task
        return task
    
    def get_task(self, task_name: str) -> MockCeleryTask:
        """Get a registered mock task."""
        if task_name not in self.tasks:
            self.register_task(task_name)
        return self.tasks[task_name]
    
    async def execute_task(self, task_name: str, db: AsyncSession) -> Any:
        """Execute a registered task."""
        task = self.get_task(task_name)
        result = await task.execute(db)
        self.executed_tasks.append(task)
        return result
    
    def clear(self):
        """Clear all registered tasks."""
        self.tasks.clear()
        self.executed_tasks.clear()


@pytest.fixture(scope="function")
def mock_celery_registry():
    """Fixture for mock Celery task registry."""
    registry = MockCeleryTaskRegistry()
    yield registry
    registry.clear()


@pytest_asyncio.fixture(scope="function")
async def mock_celery_tasks(mock_celery_registry):
    """
    Fixture that patches Celery tasks with mocks.
    
    This fixture patches the Celery tasks with mocks that can be executed
    synchronously in tests. It yields a registry of mock tasks that can be
    used to execute the tasks and check their results.
    """
    # Create mock tasks
    train_model_task = mock_celery_registry.register_task("train_model")
    train_model_for_job_task = mock_celery_registry.register_task("train_model_for_job")
    process_batch_data_task = mock_celery_registry.register_task("process_batch_data")
    
    # Patch the Celery tasks
    with patch("src.workers.celery_tasks.train_model", return_value=train_model_task), \
         patch("src.workers.celery_tasks.train_model_for_job", return_value=train_model_for_job_task), \
         patch("src.workers.celery_tasks.process_batch_data", return_value=process_batch_data_task), \
         patch("src.api.v1.endpoints.pipeline.train_model", return_value=train_model_task), \
         patch("src.api.v1.endpoints.pipeline.train_model_for_job", return_value=train_model_for_job_task), \
         patch("src.services.data_ingestion_service.process_batch_data", return_value=process_batch_data_task):
        
        yield mock_celery_registry


async def execute_mock_tasks(registry: MockCeleryTaskRegistry, db: AsyncSession):
    """
    Execute all registered tasks in the registry.
    
    Args:
        registry: The mock Celery task registry
        db: The database session
    
    Returns:
        A dictionary mapping task names to their results
    """
    results = {}
    for task_name, task in registry.tasks.items():
        if task not in registry.executed_tasks:
            try:
                results[task_name] = await task.execute(db)
            except Exception as e:
                results[task_name] = e
    
    return results
