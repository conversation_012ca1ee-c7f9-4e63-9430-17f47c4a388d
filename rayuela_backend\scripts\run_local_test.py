#!/usr/bin/env python
"""
Script para ejecutar pruebas locales de Rayuela.
Este script configura y ejecuta todos los componentes necesarios para probar Rayuela localmente.
"""

import os
import sys
import subprocess
import time
import argparse
import signal
import atexit

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

# Lista para almacenar procesos que deben cerrarse al salir
processes = []

def cleanup():
    """Limpia los procesos al salir."""
    print("\nLimpiando procesos...")
    for p in processes:
        if p.poll() is None:  # Si el proceso sigue en ejecución
            try:
                p.terminate()
                time.sleep(0.5)
                if p.poll() is None:
                    p.kill()
            except Exception as e:
                print(f"Error al cerrar proceso: {e}")
    print("Limpieza completada.")

# Registrar la función de limpieza para ejecutarse al salir
atexit.register(cleanup)

def run_command(cmd, cwd=None, env=None, wait=True):
    """Ejecuta un comando y devuelve el proceso."""
    print(f"Ejecutando: {' '.join(cmd)}")

    # Usar el entorno actual si no se proporciona uno
    if env is None:
        env = os.environ.copy()

    # Ejecutar el comando
    process = subprocess.Popen(
        cmd,
        cwd=cwd or root_dir,
        env=env,
        stdout=subprocess.PIPE if wait else None,
        stderr=subprocess.PIPE if wait else None,
        text=True,
        shell=isinstance(cmd, str)
    )

    # Si wait es True, esperar a que termine el proceso
    if wait:
        stdout, stderr = process.communicate()
        if process.returncode != 0:
            print(f"Error al ejecutar {cmd[0]}:")
            print(f"STDOUT: {stdout}")
            print(f"STDERR: {stderr}")
            return None
        return stdout
    else:
        # Si wait es False, agregar el proceso a la lista para limpieza
        processes.append(process)
        return process

def start_docker_services():
    """Inicia los servicios de Docker."""
    print("Iniciando servicios de Docker...")

    # Verificar si docker-compose.simple.yml existe
    if os.path.exists(os.path.join(root_dir, "docker-compose.simple.yml")):
        cmd = ["docker-compose", "-f", "docker-compose.simple.yml", "up", "-d"]
    else:
        cmd = ["docker-compose", "up", "-d", "db", "redis"]

    result = run_command(cmd)
    if result is None:
        print("Error al iniciar servicios de Docker.")
        sys.exit(1)

    print("Servicios de Docker iniciados correctamente.")
    time.sleep(5)  # Esperar a que los servicios estén listos

def init_database():
    """Inicializa la base de datos."""
    print("Inicializando base de datos...")

    # Primero, crear la base de datos si no existe
    cmd_create = ["python", "-m", "scripts.start", "create-db"]
    result_create = run_command(cmd_create)
    if result_create is None:
        print("Error al crear la base de datos.")
        sys.exit(1)

    # Luego, aplicar migraciones
    cmd_migrate = ["python", "-m", "scripts.start", "migrate-db"]
    result_migrate = run_command(cmd_migrate)
    if result_migrate is None:
        print("Error al aplicar migraciones.")
        sys.exit(1)

    # Finalmente, insertar datos iniciales
    cmd_seed = ["python", "-m", "scripts.start", "seed-db"]
    result_seed = run_command(cmd_seed)
    if result_seed is None:
        print("Error al insertar datos iniciales.")
        # No salimos aquí porque los datos iniciales podrían ser opcionales

    print("Base de datos inicializada correctamente.")

def start_api_server():
    """Inicia el servidor API."""
    print("Iniciando servidor API...")
    cmd = ["python", "main.py"]
    process = run_command(cmd, wait=False)
    if process is None:
        print("Error al iniciar el servidor API.")
        sys.exit(1)
    print("Servidor API iniciado correctamente.")
    time.sleep(3)  # Esperar a que el servidor esté listo

def start_celery_workers():
    """Inicia los workers de Celery."""
    print("Iniciando workers de Celery...")

    # Worker para tareas por defecto
    cmd_default = ["celery", "-A", "src.workers.celery_app", "worker",
                  "--loglevel=info", "--concurrency=2", "--max-tasks-per-child=10",
                  "--queues=default", "--hostname=default@%h"]
    process_default = run_command(cmd_default, wait=False)

    # Worker para tareas de entrenamiento
    cmd_training = ["celery", "-A", "src.workers.celery_app", "worker",
                   "--loglevel=info", "--concurrency=1", "--max-tasks-per-child=1",
                   "--queues=training", "--hostname=training@%h"]
    process_training = run_command(cmd_training, wait=False)

    # Celery Beat para tareas periódicas
    cmd_beat = ["celery", "-A", "src.workers.celery_app", "beat", "--loglevel=info"]
    process_beat = run_command(cmd_beat, wait=False)

    if None in [process_default, process_training, process_beat]:
        print("Error al iniciar los workers de Celery.")
        sys.exit(1)

    print("Workers de Celery iniciados correctamente.")
    time.sleep(3)  # Esperar a que los workers estén listos

def run_tests():
    """Ejecuta las pruebas."""
    print("Ejecutando pruebas...")
    cmd = ["python", "-m", "scripts.run_tests", "all"]
    result = run_command(cmd)
    if result is None:
        print("Error al ejecutar las pruebas.")
        sys.exit(1)
    print("Pruebas ejecutadas correctamente.")

def main():
    """Función principal."""
    parser = argparse.ArgumentParser(description="Ejecutar pruebas locales de Rayuela")
    parser.add_argument("--skip-docker", action="store_true", help="Omitir inicio de servicios Docker")
    parser.add_argument("--skip-db-init", action="store_true", help="Omitir inicialización de base de datos")
    parser.add_argument("--skip-api", action="store_true", help="Omitir inicio de servidor API")
    parser.add_argument("--skip-workers", action="store_true", help="Omitir inicio de workers de Celery")
    parser.add_argument("--skip-tests", action="store_true", help="Omitir ejecución de pruebas")

    args = parser.parse_args()

    try:
        # Iniciar servicios de Docker
        if not args.skip_docker:
            start_docker_services()

        # Inicializar base de datos
        if not args.skip_db_init:
            init_database()

        # Iniciar servidor API
        if not args.skip_api:
            start_api_server()

        # Iniciar workers de Celery
        if not args.skip_workers:
            start_celery_workers()

        # Ejecutar pruebas
        if not args.skip_tests:
            run_tests()

        print("\nTodo el proceso se completó correctamente.")
        print("Presiona Ctrl+C para detener todos los servicios.")

        # Mantener el script en ejecución hasta que el usuario lo detenga
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\nDetención solicitada por el usuario.")
    finally:
        cleanup()

if __name__ == "__main__":
    main()
