#!/usr/bin/env python3
"""
Script de prueba para validar el aislamiento de tenants usando contextvars.

Este script prueba que:
1. Las operaciones concurrentes entre diferentes tenants están completamente aisladas
2. No hay race conditions entre contextos de diferentes tenants
3. Los repositorios obtienen el account_id correcto desde el contextvar
4. El middleware establece y limpia correctamente el contexto

IMPORTANTE: Ejecutar este script DESPUÉS de la refactorización para validar
que el aislamiento de tenants funciona correctamente.
"""
import asyncio
import random
import sys
import os
from typing import List

# Agregar el path del proyecto
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.tenant_context import set_current_tenant_id, get_current_tenant_id
from src.db.session import get_current_account_id, set_current_account_id
from src.utils.base_logger import log_info, log_error, log_warning


async def simulate_tenant_request(
    tenant_id: int, operation_id: str, duration_ms: int = 100
):
    """
    Simula una solicitud de un tenant específico.

    Args:
        tenant_id: ID del tenant
        operation_id: Identificador único de la operación
        duration_ms: Duración simulada de la operación en milisegundos
    """
    try:
        # Simular que el middleware establece el tenant
        set_current_tenant_id(tenant_id)
        set_current_account_id(tenant_id)  # Para compatibilidad con session.py

        log_info(f"[{operation_id}] Iniciando operación para tenant {tenant_id}")

        # Verificar que el contexto está correctamente establecido
        ctx_tenant_id = get_current_tenant_id()
        session_tenant_id = get_current_account_id()

        if ctx_tenant_id != tenant_id:
            log_error(
                f"[{operation_id}] ERROR: Contexto incorrecto! Esperado: {tenant_id}, Obtenido: {ctx_tenant_id}"
            )
            return False

        if session_tenant_id != tenant_id:
            log_error(
                f"[{operation_id}] ERROR: Session incorrecto! Esperado: {tenant_id}, Obtenido: {session_tenant_id}"
            )
            return False

        # Simular trabajo asíncrono
        await asyncio.sleep(duration_ms / 1000.0)

        # Verificar nuevamente que el contexto sigue siendo correcto
        final_ctx_tenant_id = get_current_tenant_id()
        final_session_tenant_id = get_current_account_id()

        if final_ctx_tenant_id != tenant_id:
            log_error(
                f"[{operation_id}] ERROR: Contexto corrompido! Esperado: {tenant_id}, Obtenido: {final_ctx_tenant_id}"
            )
            return False

        if final_session_tenant_id != tenant_id:
            log_error(
                f"[{operation_id}] ERROR: Session corrompido! Esperado: {tenant_id}, Obtenido: {final_session_tenant_id}"
            )
            return False

        log_info(
            f"[{operation_id}] Operación completada exitosamente para tenant {tenant_id}"
        )
        return True

    except Exception as e:
        log_error(
            f"[{operation_id}] Error en simulación de tenant {tenant_id}: {str(e)}"
        )
        return False
    finally:
        # Simular limpieza del middleware
        set_current_tenant_id(None)
        set_current_account_id(None)


async def test_concurrent_tenant_isolation():
    """
    Prueba el aislamiento entre tenants con operaciones concurrentes.
    """
    print("🧪 Iniciando prueba de aislamiento concurrente de tenants...")

    # Crear múltiples operaciones concurrentes para diferentes tenants
    tasks = []
    num_tenants = 5
    operations_per_tenant = 10

    for tenant_id in range(1, num_tenants + 1):
        for op_num in range(operations_per_tenant):
            operation_id = f"T{tenant_id}-OP{op_num}"
            # Duración aleatoria para aumentar la probabilidad de race conditions
            duration = random.randint(50, 200)
            task = simulate_tenant_request(tenant_id, operation_id, duration)
            tasks.append(task)

    # Mezclar las tareas para aumentar la concurrencia
    random.shuffle(tasks)

    print(
        f"🚀 Ejecutando {len(tasks)} operaciones concurrentes para {num_tenants} tenants..."
    )

    # Ejecutar todas las operaciones concurrentemente
    results = await asyncio.gather(*tasks, return_exceptions=True)

    # Analizar resultados
    successful = sum(1 for r in results if r is True)
    failed = sum(1 for r in results if r is False or isinstance(r, Exception))

    print(f"\n📊 Resultados de la prueba:")
    print(f"   ✅ Operaciones exitosas: {successful}")
    print(f"   ❌ Operaciones fallidas: {failed}")
    print(f"   📈 Tasa de éxito: {(successful / len(results)) * 100:.1f}%")

    if failed == 0:
        print("\n🎉 ¡ÉXITO! Aislamiento de tenants funcionando correctamente")
        print("   ✅ No se detectaron race conditions")
        print("   ✅ Todos los contextos se mantuvieron aislados")
        return True
    else:
        print(f"\n⚠️  FALLO: Se detectaron {failed} problemas de aislamiento")
        print("   ❌ Posibles race conditions o contextos corrompidos")
        return False


async def test_context_inheritance():
    """
    Prueba que los contextos se hereden correctamente en tareas anidadas.
    """
    print("\n🔍 Probando herencia de contexto en tareas anidadas...")

    async def nested_operation(level: int, expected_tenant: int):
        actual_tenant = get_current_tenant_id()
        if actual_tenant != expected_tenant:
            log_error(
                f"Nivel {level}: Contexto incorrecto! Esperado: {expected_tenant}, Obtenido: {actual_tenant}"
            )
            return False

        if level > 0:
            # Llamada recursiva para probar anidamiento profundo
            return await nested_operation(level - 1, expected_tenant)

        return True

    # Probar con diferentes tenants
    for tenant_id in [1, 2, 3]:
        set_current_tenant_id(tenant_id)
        set_current_account_id(tenant_id)

        try:
            result = await nested_operation(5, tenant_id)
            if not result:
                print(f"❌ Fallo en herencia de contexto para tenant {tenant_id}")
                return False
        finally:
            set_current_tenant_id(None)
            set_current_account_id(None)

    print("✅ Herencia de contexto funcionando correctamente")
    return True


async def test_context_cleanup():
    """
    Prueba que los contextos se limpien correctamente.
    """
    print("\n🧹 Probando limpieza de contexto...")

    # Establecer un contexto
    set_current_tenant_id(999)
    set_current_account_id(999)

    # Verificar que está establecido
    if get_current_tenant_id() != 999 or get_current_account_id() != 999:
        print("❌ Error estableciendo contexto inicial")
        return False

    # Limpiar contexto
    set_current_tenant_id(None)
    set_current_account_id(None)

    # Verificar que está limpio
    if get_current_tenant_id() is not None or get_current_account_id() is not None:
        print("❌ Error limpiando contexto")
        return False

    print("✅ Limpieza de contexto funcionando correctamente")
    return True


async def main():
    """
    Función principal que ejecuta todas las pruebas.
    """
    print("🛡️  VALIDACIÓN DE SEGURIDAD: AISLAMIENTO DE TENANTS")
    print("=" * 60)
    print("Probando que la refactorización a contextvars eliminó")
    print("las vulnerabilidades de race conditions en multi-tenancy")
    print("=" * 60)

    all_tests_passed = True

    # Ejecutar todas las pruebas
    tests = [
        test_context_cleanup,
        test_context_inheritance,
        test_concurrent_tenant_isolation,
    ]

    for test in tests:
        try:
            result = await test()
            all_tests_passed = all_tests_passed and result
        except Exception as e:
            print(f"❌ Error ejecutando {test.__name__}: {str(e)}")
            all_tests_passed = False

    print("\n" + "=" * 60)
    if all_tests_passed:
        print("🎉 TODAS LAS PRUEBAS PASARON!")
        print("✅ El aislamiento de tenants está funcionando correctamente")
        print("✅ No se detectaron vulnerabilidades de race conditions")
        print("✅ La refactorización fue exitosa")
    else:
        print("⚠️  ALGUNAS PRUEBAS FALLARON!")
        print("❌ Revisar la implementación de contextvars")
        print("❌ Posibles vulnerabilidades de seguridad")
    print("=" * 60)

    return all_tests_passed


if __name__ == "__main__":
    # Ejecutar las pruebas
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
