import pytest
import pytest_asyncio
import numpy as np
from datetime import datetime, timezone
from sqlalchemy.ext.asyncio import AsyncSession

from src.ml_pipeline.serving_engine import ServingEngine, HybridRecommender
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.evaluation import RecommendationEvaluator
from src.db import models
from src.core.config import settings


class TestHybridRecommenderIntegration:
    """Tests de integración para el servicio de recomendaciones híbridas"""

    @pytest.fixture
    async def serving_engine(self):
        """Fixture para crear una instancia del motor de recomendaciones"""
        artifact_manager = ModelArtifactManager()
        engine = ServingEngine(artifact_manager)
        yield engine

    @pytest.fixture
    async def test_data(self, async_db_session: AsyncSession):
        """Fixture para crear datos de prueba"""
        # Crear cuenta de prueba
        from src.core.security.api_key import hash_api_key

        test_api_key = "test_api_key"
        account = models.Account(
            id=1,
            name="Test Account",
            subscription_plan="BASIC",
            api_key_hash=hash_api_key(test_api_key),
            api_key_prefix="test",
            api_key_last_chars="pi_key",
            api_key_created_at=datetime.now(timezone.utc),
            api_key_revealed=True,
            is_active=True,
        )
        async_db_session.add(account)

        # Crear usuarios finales
        users = []
        for i in range(1, 11):
            user = models.EndUser(
                account_id=1, id=i, external_id=f"user{i}", is_active=True
            )
            users.append(user)
            async_db_session.add(user)

        # Crear productos
        products = []
        categories = ["Electronics", "Books", "Clothing", "Food"]
        for i in range(1, 51):
            product = models.Product(
                account_id=1,
                id=i,
                name=f"Product {i}",
                description=f"Description for product {i}",
                category=categories[i % len(categories)],
                is_active=True,
            )
            products.append(product)
            async_db_session.add(product)

        # Crear interacciones
        interactions = []
        interaction_types = ["VIEW", "LIKE", "PURCHASE"]
        for user in users:
            for product in products[:10]:  # Cada usuario interactúa con 10 productos
                interaction = models.Interaction(
                    account_id=1,
                    end_user_id=user.id,
                    product_id=product.id,
                    interaction_type=interaction_types[
                        hash(f"{user.id}{product.id}") % len(interaction_types)
                    ],
                    value=float(hash(f"{user.id}{product.id}") % 5) + 1.0,
                    timestamp=datetime.now(timezone.utc),
                )
                interactions.append(interaction)
                async_db_session.add(interaction)

        await async_db_session.commit()

        return {
            "account": account,
            "users": users,
            "products": products,
            "interactions": interactions,
        }

    @pytest.mark.asyncio
    async def test_get_recommendations(
        self,
        serving_engine: ServingEngine,
        async_db_session: AsyncSession,
        test_data: dict,
    ):
        """Test de integración que verifica la obtención de recomendaciones"""
        # Obtener recomendaciones para un usuario
        user_id = test_data["users"][0].id
        account_id = test_data["account"].id
        
        recommendations = await serving_engine.get_candidate_recommendations(
            db=async_db_session, 
            account_id=account_id, 
            user_id=user_id,
            n_recommendations=10
        )

        # Verificar estructura de recomendaciones
        assert len(recommendations) > 0
        assert all("item_id" in item for item in recommendations)
        assert all("score" in item for item in recommendations)

        # Verificar que los scores están normalizados
        scores = [item["score"] for item in recommendations]
        assert all(0 <= score <= 1 for score in scores)

        # Verificar que no hay duplicados
        ids = [item["item_id"] for item in recommendations]
        assert len(ids) == len(set(ids))

    @pytest.mark.asyncio
    async def test_recommendations_with_different_strategies(
        self,
        serving_engine: ServingEngine,
        async_db_session: AsyncSession,
        test_data: dict,
    ):
        """Test que verifica diferentes estrategias de recomendación"""
        # Obtener recomendaciones con diferentes estrategias
        user_id = test_data["users"][0].id
        account_id = test_data["account"].id
        
        # Probar diferentes estrategias
        strategies = ["balanced", "maximize_engagement", "promote_new_arrivals", "discover_diverse"]
        
        for strategy in strategies:
            recommendations = await serving_engine.get_candidate_recommendations(
                db=async_db_session, 
                account_id=account_id, 
                user_id=user_id,
                n_recommendations=10,
                strategy=strategy
            )
            
            # Verificar estructura básica
            assert len(recommendations) > 0
            assert all("item_id" in item for item in recommendations)
            assert all("score" in item for item in recommendations)

    @pytest.mark.asyncio
    async def test_hybrid_recommender_combine(
        self,
        serving_engine: ServingEngine,
        async_db_session: AsyncSession,
        test_data: dict,
    ):
        """Test que verifica la combinación de recomendaciones"""
        # Crear datos de prueba para combinar
        collab_recs = [
            {"item_id": 1, "score": 0.8},
            {"item_id": 2, "score": 0.6},
            {"item_id": 3, "score": 0.4}
        ]
        
        content_recs = [
            {"item_id": 2, "score": 0.7},
            {"item_id": 3, "score": 0.5},
            {"item_id": 4, "score": 0.9}
        ]
        
        # Combinar recomendaciones usando HybridRecommender
        hybrid_recommender = serving_engine.hybrid_recommender
        combined_recs = hybrid_recommender.combine_recommendations(
            collab_recs, 
            content_recs, 
            n_recommendations=5
        )
        
        # Verificar estructura y propiedades
        assert len(combined_recs) <= 5
        assert all("item_id" in item for item in combined_recs)
        assert all("score" in item for item in combined_recs)
        assert all(0 <= item["score"] <= 1 for item in combined_recs)
        
        # Verificar que están ordenados por score
        scores = [item["score"] for item in combined_recs]
        assert all(scores[i] >= scores[i+1] for i in range(len(scores)-1))
        
        # Verificar que todos los items son únicos
        item_ids = [item["item_id"] for item in combined_recs]
        assert len(item_ids) == len(set(item_ids))

    @pytest.mark.asyncio
    async def test_different_recommendation_types(
        self,
        serving_engine: ServingEngine,
        async_db_session: AsyncSession,
        test_data: dict,
    ):
        """Test que verifica diferentes tipos de recomendación"""
        # Obtener recomendaciones con diferentes tipos
        user_id = test_data["users"][0].id
        account_id = test_data["account"].id
        
        # Probar diferentes tipos de recomendación
        rec_types = ["user", "item", "hybrid"]
        
        for rec_type in rec_types:
            recommendations = await serving_engine.get_candidate_recommendations(
                db=async_db_session, 
                account_id=account_id, 
                user_id=user_id,
                n_recommendations=10,
                recommendation_type=rec_type
            )
            
            # Verificar estructura básica
            assert len(recommendations) >= 0  # Podría ser vacío si no hay artefactos
            if recommendations:
                assert all("item_id" in item for item in recommendations)
                assert all("score" in item for item in recommendations)
