"""
Servicio de ejemplo para demostrar el patrón correcto de manejo de transacciones.
"""
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status

from src.db import models, schemas
from src.db.repositories import ProductRepository, EndUserRepository
from src.utils.base_logger import log_info, log_error


class TransactionExampleService:
    """
    Servicio de ejemplo para demostrar el patrón correcto de manejo de transacciones.
    
    Este servicio muestra cómo manejar transacciones en la capa de servicios para
    operaciones que involucran múltiples repositorios y deben ser atómicas.
    """
    
    def __init__(self, db: AsyncSession, account_id: int):
        """
        Inicializa el servicio de ejemplo.
        
        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
        """
        self.db = db
        self.account_id = account_id
        self.product_repo = ProductRepository(db, account_id=account_id)
        self.user_repo = EndUserRepository(db, account_id=account_id)
    
    async def create_product_with_initial_user(
        self, 
        product_data: schemas.ProductCreate,
        user_data: schemas.EndUserCreate
    ) -> Dict[str, Any]:
        """
        Crea un producto y un usuario inicial en una sola transacción.
        
        Esta operación es atómica: o se crean ambos objetos o ninguno.
        
        Args:
            product_data: Datos del producto
            user_data: Datos del usuario
            
        Returns:
            Diccionario con el producto y usuario creados
            
        Raises:
            HTTPException: Si ocurre un error durante la creación
        """
        try:
            # Iniciar una transacción explícita para garantizar atomicidad
            async with self.db.begin():
                # Crear el producto
                product = await self.product_repo.create(product_data)
                
                # Crear el usuario
                user = await self.user_repo.create(user_data)
                
                # Ambas operaciones se realizan en la misma transacción
                # Si cualquiera falla, se hará rollback automáticamente
                
                # No es necesario hacer flush explícito, se hará automáticamente
                # al salir del bloque with
            
            # La transacción se ha completado exitosamente en este punto
            
            log_info(
                f"Product {product.id} and user {user.id} created successfully "
                f"for account {self.account_id}"
            )
            
            return {
                "product": product,
                "user": user,
                "message": "Product and user created successfully"
            }
            
        except Exception as e:
            # No es necesario hacer rollback explícito, se hará automáticamente
            log_error(f"Error creating product and user: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating product and user: {str(e)}"
            )
    
    async def update_product_and_user(
        self,
        product_id: int,
        user_id: int,
        product_data: schemas.ProductUpdate,
        user_data: schemas.EndUserUpdate
    ) -> Dict[str, Any]:
        """
        Actualiza un producto y un usuario en una sola transacción.
        
        Esta operación es atómica: o se actualizan ambos objetos o ninguno.
        
        Args:
            product_id: ID del producto
            user_id: ID del usuario
            product_data: Datos actualizados del producto
            user_data: Datos actualizados del usuario
            
        Returns:
            Diccionario con el producto y usuario actualizados
            
        Raises:
            HTTPException: Si ocurre un error durante la actualización
        """
        try:
            # Iniciar una transacción explícita para garantizar atomicidad
            async with self.db.begin():
                # Verificar que el producto existe
                product = await self.product_repo.get_by_id(product_id)
                if not product:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Product {product_id} not found"
                    )
                
                # Verificar que el usuario existe
                user = await self.user_repo.get_by_id(user_id)
                if not user:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"User {user_id} not found"
                    )
                
                # Actualizar el producto
                updated_product = await self.product_repo.update(product_id, product_data)
                
                # Actualizar el usuario
                updated_user = await self.user_repo.update(user_id, user_data)
                
                # Ambas operaciones se realizan en la misma transacción
                # Si cualquiera falla, se hará rollback automáticamente
            
            # La transacción se ha completado exitosamente en este punto
            
            log_info(
                f"Product {product_id} and user {user_id} updated successfully "
                f"for account {self.account_id}"
            )
            
            return {
                "product": updated_product,
                "user": updated_user,
                "message": "Product and user updated successfully"
            }
            
        except HTTPException:
            # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
            raise
        except Exception as e:
            # No es necesario hacer rollback explícito, se hará automáticamente
            log_error(f"Error updating product and user: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error updating product and user: {str(e)}"
            )
    
    async def delete_product_and_related_data(self, product_id: int) -> Dict[str, Any]:
        """
        Elimina un producto y todos sus datos relacionados en una sola transacción.
        
        Esta operación es atómica: o se elimina todo o nada.
        
        Args:
            product_id: ID del producto
            
        Returns:
            Mensaje de confirmación
            
        Raises:
            HTTPException: Si ocurre un error durante la eliminación
        """
        try:
            # Iniciar una transacción explícita para garantizar atomicidad
            async with self.db.begin():
                # Verificar que el producto existe
                product = await self.product_repo.get_by_id(product_id)
                if not product:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Product {product_id} not found"
                    )
                
                # En una implementación real, aquí eliminaríamos todos los datos relacionados
                # Por ejemplo:
                # await self.interaction_repo.delete_by_product(product_id)
                # await self.review_repo.delete_by_product(product_id)
                # etc.
                
                # Finalmente, eliminar el producto
                await self.product_repo.delete(product_id)
                
                # Todas las operaciones se realizan en la misma transacción
                # Si cualquiera falla, se hará rollback automáticamente
            
            # La transacción se ha completado exitosamente en este punto
            
            log_info(
                f"Product {product_id} and related data deleted successfully "
                f"for account {self.account_id}"
            )
            
            return {
                "message": f"Product {product_id} and related data deleted successfully"
            }
            
        except HTTPException:
            # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
            raise
        except Exception as e:
            # No es necesario hacer rollback explícito, se hará automáticamente
            log_error(f"Error deleting product and related data: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting product and related data: {str(e)}"
            )
