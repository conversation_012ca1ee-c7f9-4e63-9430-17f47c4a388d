"""Extend RLS policies to api_keys, orders, and order_items tables

Revision ID: extend_rls_missing_001
Revises: add_api_keys_multi_001
Create Date: 2024-12-19 16:00:00.000000

"""

from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
import os
import logging

# revision identifiers, used by Alembic.
revision: str = "extend_rls_missing_001"
down_revision: Union[str, None] = "add_api_keys_multi_001"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None

# Configurar logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Tablas que necesitan RLS extendido
MISSING_RLS_TABLES = [
    'api_keys',
    'orders', 
    'order_items'
]


def upgrade() -> None:
    """Extend RLS policies to missing tenant-specific tables."""
    logger.info("Extending RLS policies to missing tables...")
    
    # Verificar que las tablas existen antes de aplicar RLS
    for table in MISSING_RLS_TABLES:
        if _table_exists(table):
            logger.info(f"Applying RLS to table: {table}")
            _enable_rls(table)
            _create_rls_policies(table)
            _grant_permissions(table)
        else:
            logger.warning(f"Table {table} does not exist, skipping RLS setup")
    
    logger.info("RLS extension completed successfully")


def downgrade() -> None:
    """Remove RLS policies from the extended tables."""
    logger.info("Removing RLS policies from extended tables...")
    
    for table in MISSING_RLS_TABLES:
        if _table_exists(table):
            logger.info(f"Removing RLS from table: {table}")
            _disable_rls(table)
            _drop_rls_policies(table)
        else:
            logger.warning(f"Table {table} does not exist, skipping RLS removal")
    
    logger.info("RLS removal completed successfully")


def _table_exists(table_name: str) -> bool:
    """Check if a table exists in the database."""
    try:
        result = op.get_bind().execute(
            sa.text("""
                SELECT EXISTS (
                    SELECT 1 FROM information_schema.tables 
                    WHERE table_schema = 'public' 
                    AND table_name = :table_name
                )
            """),
            {"table_name": table_name}
        )
        return result.scalar()
    except Exception as e:
        logger.error(f"Error checking if table {table_name} exists: {e}")
        return False


def _enable_rls(table: str) -> None:
    """Enable RLS on a table."""
    try:
        op.execute(f"ALTER TABLE {table} ENABLE ROW LEVEL SECURITY")
        logger.info(f"RLS enabled for table: {table}")
    except Exception as e:
        logger.error(f"Error enabling RLS for table {table}: {e}")
        raise


def _disable_rls(table: str) -> None:
    """Disable RLS on a table."""
    try:
        op.execute(f"ALTER TABLE {table} DISABLE ROW LEVEL SECURITY")
        logger.info(f"RLS disabled for table: {table}")
    except Exception as e:
        logger.error(f"Error disabling RLS for table {table}: {e}")
        raise


def _create_rls_policies(table: str) -> None:
    """Create RLS policies for a table."""
    try:
        # Política para SELECT
        op.execute(f"""
            CREATE POLICY {table}_select_policy ON {table}
            FOR SELECT
            USING (account_id = current_setting('app.tenant_id')::integer)
        """)

        # Política para INSERT
        op.execute(f"""
            CREATE POLICY {table}_insert_policy ON {table}
            FOR INSERT
            WITH CHECK (account_id = current_setting('app.tenant_id')::integer)
        """)

        # Política para UPDATE
        op.execute(f"""
            CREATE POLICY {table}_update_policy ON {table}
            FOR UPDATE
            USING (account_id = current_setting('app.tenant_id')::integer)
            WITH CHECK (account_id = current_setting('app.tenant_id')::integer)
        """)

        # Política para DELETE
        op.execute(f"""
            CREATE POLICY {table}_delete_policy ON {table}
            FOR DELETE
            USING (account_id = current_setting('app.tenant_id')::integer)
        """)
        
        logger.info(f"RLS policies created for table: {table}")
    except Exception as e:
        logger.error(f"Error creating RLS policies for table {table}: {e}")
        raise


def _drop_rls_policies(table: str) -> None:
    """Drop RLS policies for a table."""
    try:
        # Drop all policies for the table
        policies = ['select_policy', 'insert_policy', 'update_policy', 'delete_policy']
        for policy in policies:
            op.execute(f"DROP POLICY IF EXISTS {table}_{policy} ON {table}")
        
        logger.info(f"RLS policies dropped for table: {table}")
    except Exception as e:
        logger.error(f"Error dropping RLS policies for table {table}: {e}")
        raise


def _grant_permissions(table: str) -> None:
    """Grant permissions to existing roles for the table."""
    try:
        # Check if roles exist before granting permissions
        if _role_exists('app_role'):
            op.execute(f"GRANT SELECT, INSERT, UPDATE, DELETE ON {table} TO app_role")
            logger.info(f"Permissions granted to app_role for table: {table}")
        
        if _role_exists('maintenance_role'):
            op.execute(f"GRANT ALL ON {table} TO maintenance_role")
            logger.info(f"Permissions granted to maintenance_role for table: {table}")
            
    except Exception as e:
        logger.error(f"Error granting permissions for table {table}: {e}")
        raise


def _role_exists(role_name: str) -> bool:
    """Check if a role exists in the database."""
    try:
        result = op.get_bind().execute(
            sa.text("""
                SELECT EXISTS (
                    SELECT 1 FROM pg_roles 
                    WHERE rolname = :role_name
                )
            """),
            {"role_name": role_name}
        )
        return result.scalar()
    except Exception as e:
        logger.error(f"Error checking if role {role_name} exists: {e}")
        return False
