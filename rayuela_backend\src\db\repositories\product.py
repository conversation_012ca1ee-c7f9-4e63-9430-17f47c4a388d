"""
Repositorios para la gestión de productos.
"""

from sqlalchemy import select, func, text, or_, and_
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
from fastapi import HTTPException
from src.db import models, schemas
from .base import BaseRepository


class ProductRepository(BaseRepository):
    def __init__(self, db: AsyncSession, account_id: int):
        # Pasamos el modelo y el account_id para filtrar automáticamente
        super().__init__(db, account_id=account_id, model=models.Product)

    async def create(self, product_create: schemas.ProductCreate) -> models.Product:
        """Crear un nuevo producto."""
        try:
            # Preparar datos para búsqueda
            product_data = product_create.model_dump()
            
            # Generar vectores de búsqueda
            search_vector = self._generate_search_vector(
                product_data["name"], 
                product_data.get("description", "")
            )
            
            # Generar trigrams
            name_trgm = self._generate_trigram(product_data["name"])
            description_trgm = self._generate_trigram(product_data.get("description", ""))
            
            # Agregar campos de búsqueda
            product_data.update({
                "search_vector": search_vector,
                "name_trgm": name_trgm,
                "description_trgm": description_trgm,
                "account_id": self.account_id
            })
            
            product = models.Product(**product_data)
            self.db.add(product)
            await self.db.flush()
            await self.db.refresh(product)
            return product
        except SQLAlchemyError as e:
            await self._handle_error("creating product", e)

    def _generate_search_vector(self, name: str, description: str) -> str:
        """Genera el vector de búsqueda para Full-Text Search."""
        # Combinar nombre y descripción con diferentes pesos
        return f"setweight(to_tsvector('spanish', coalesce(:name, '')), 'A') || " \
               f"setweight(to_tsvector('spanish', coalesce(:description, '')), 'B')"

    def _generate_trigram(self, text: str) -> str:
        """Genera el trigram para búsqueda fuzzy."""
        return f"to_tsvector('spanish', coalesce(:text, ''))"

    async def search_products(
        self,
        search_term: str,
        category: Optional[str] = None,
        skip: int = 0,
        limit: int = 100,
        search_type: str = "combined"  # 'trigram', 'fulltext', o 'combined'
    ) -> List[models.Product]:
        """Buscar productos usando diferentes estrategias de búsqueda."""
        try:
            # Construir consulta base
            query = select(models.Product)
            query = self._add_tenant_filter(query)

            # Preparar término de búsqueda
            search_term = search_term.strip()
            if not search_term:
                return []

            # Construir filtros de búsqueda según el tipo
            search_filters = []
            
            if search_type in ['trigram', 'combined']:
                # Búsqueda usando trigrams (fuzzy search)
                trigram_filter = or_(
                    func.similarity(models.Product.name_trgm, search_term) > 0.3,
                    func.similarity(models.Product.description_trgm, search_term) > 0.3
                )
                search_filters.append(trigram_filter)
            
            if search_type in ['fulltext', 'combined']:
                # Búsqueda usando Full-Text Search
                ts_query = func.to_tsquery('spanish', search_term.replace(' ', ' & '))
                fulltext_filter = models.Product.search_vector.op('@@')(ts_query)
                search_filters.append(fulltext_filter)

            # Combinar filtros de búsqueda
            if search_filters:
                query = query.filter(or_(*search_filters))

            # Filtrar por categoría si se proporciona
            if category:
                query = query.filter(models.Product.category == category)

            # Ordenar resultados por relevancia
            if search_type == 'trigram':
                query = query.order_by(
                    func.greatest(
                        func.similarity(models.Product.name_trgm, search_term),
                        func.similarity(models.Product.description_trgm, search_term)
                    ).desc()
                )
            elif search_type == 'fulltext':
                query = query.order_by(
                    func.ts_rank_cd(models.Product.search_vector, ts_query).desc()
                )
            else:  # combined
                query = query.order_by(
                    func.greatest(
                        func.similarity(models.Product.name_trgm, search_term),
                        func.similarity(models.Product.description_trgm, search_term)
                    ).desc(),
                    func.ts_rank_cd(models.Product.search_vector, ts_query).desc()
                )

            # Aplicar paginación
            query = query.offset(skip).limit(limit)

            # Ejecutar consulta
            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("searching products", e)
            return []

    async def get_product_with_interactions(
        self, product_id: int, limit: int = 10
    ) -> Dict[str, Any]:
        """Obtener un producto con sus interacciones recientes."""
        try:
            # Obtener el producto
            product = await self.get_by_id(product_id)
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")

            # Obtener interacciones recientes
            query = (
                select(models.Interaction)
                .filter(
                    models.Interaction.product_id == product_id,
                    models.Interaction.account_id == self.account_id,
                )
                .order_by(models.Interaction.created_at.desc())
                .limit(limit)
            )

            result = await self.db.execute(query)
            interactions = result.scalars().all()

            return {
                "product": product,
                "recent_interactions": interactions,
            }
        except SQLAlchemyError as e:
            await self._handle_error("fetching product with interactions", e)

    async def update_inventory(self, product_id: int, quantity: int) -> models.Product:
        """Actualizar el inventario de un producto."""
        try:
            product = await self.get_by_id(product_id)
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")

            product.inventory_count = quantity
            await self.db.flush()
            await self.db.refresh(product)
            return product
        except SQLAlchemyError as e:
            await self._handle_error("updating product inventory", e)

    async def update_rating(self, product_id: int, new_rating: float) -> models.Product:
        """Actualizar la calificación de un producto."""
        try:
            product = await self.get_by_id(product_id)
            if not product:
                raise HTTPException(status_code=404, detail="Product not found")

            # Calcular nueva calificación promedio
            total_rating = product.average_rating * product.num_ratings
            total_rating += new_rating
            product.num_ratings += 1
            product.average_rating = total_rating / product.num_ratings

            await self.db.flush()
            await self.db.refresh(product)
            return product
        except SQLAlchemyError as e:
            await self._handle_error("updating product rating", e)

    async def get_categories(self) -> List[str]:
        """Obtener todas las categorías únicas de productos."""
        try:
            query = select(models.Product.category).distinct()
            query = self._add_tenant_filter(query)
            result = await self.db.execute(query)
            categories = result.scalars().all()
            return categories
        except SQLAlchemyError as e:
            await self._handle_error("fetching product categories", e)
            return []

    async def get_products_by_ids(self, product_ids: List[int]) -> List[models.Product]:
        """Obtener productos por sus IDs."""
        if not product_ids:
            return []

        try:
            query = select(models.Product).where(
                models.Product.account_id == self.account_id,
                models.Product.id.in_(product_ids)
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching products by ids", e)
            return []

    async def get_top_products(
        self, limit: int = 10, category: Optional[str] = None
    ) -> List[models.Product]:
        """Obtener los productos mejor calificados."""
        try:
            query = select(models.Product)
            query = self._add_tenant_filter(query)

            if category:
                query = query.filter(models.Product.category == category)

            query = query.order_by(
                models.Product.average_rating.desc(),
                models.Product.num_ratings.desc(),
            ).limit(limit)

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching top products", e)
            return []

    async def get_low_inventory_products(
        self, threshold: int = 10
    ) -> List[models.Product]:
        """Obtener productos con inventario bajo."""
        try:
            query = (
                select(models.Product)
                .filter(
                    models.Product.inventory_count <= threshold,
                    models.Product.account_id == self.account_id,
                )
                .order_by(models.Product.inventory_count.asc())
            )

            result = await self.db.execute(query)
            return result.scalars().all()
        except SQLAlchemyError as e:
            await self._handle_error("fetching low inventory products", e)
            return []
