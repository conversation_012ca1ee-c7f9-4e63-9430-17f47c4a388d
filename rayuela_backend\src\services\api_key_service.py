"""
Servicio para manejar la lógica de API Keys (Multi-API Key Support).
"""
from datetime import datetime, timezone
from typing import Optional, Tu<PERSON>, Dict, Any, List
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.security.api_key import generate_api_key, hash_api_key, verify_api_key
from src.db.models import Account, Api<PERSON>ey
from src.db.repositories import AccountRepository
from src.db.repositories.api_key import ApiKeyRepository
from src.utils.base_logger import log_info, log_error

class ApiKeyService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.account_repo = AccountRepository(db)
        self.api_key_repo = ApiKeyRepository(db)

    async def create_api_key(
        self,
        account_id: int,
        name: Optional[str] = None
    ) -> <PERSON><PERSON>[str, <PERSON><PERSON><PERSON><PERSON>]:
        """
        Creates a new API key for an account.

        Args:
            account_id: The account ID to create the API key for
            name: Optional descriptive name for the API key

        Returns:
            Tuple of (api_key_string, api_key_model)
        """
        try:
            # Generate API key and hash
            api_key, api_key_hash = generate_api_key()
            api_key_parts = api_key.split("_")

            # Create the API key in the database
            api_key_model = await self.api_key_repo.create_api_key(
                account_id=account_id,
                name=name or f"API Key {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M')}",
                api_key_hash=api_key_hash,
                api_key_prefix=api_key_parts[0] if len(api_key_parts) > 0 else "",
                api_key_last_chars=api_key[-6:] if len(api_key) >= 6 else api_key
            )

            log_info(f"Created new API key for account {account_id}: {api_key_model.id}")
            return api_key, api_key_model

        except Exception as e:
            log_error(f"Error creating API key: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error creating API key"
            )
            
    async def list_api_keys(self, account_id: int) -> List[ApiKey]:
        """
        Lists all active API keys for an account.

        Args:
            account_id: The account ID to list API keys for

        Returns:
            List of active API keys
        """
        try:
            return await self.api_key_repo.get_by_account_id(account_id, include_inactive=False)
        except Exception as e:
            log_error(f"Error listing API keys: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error listing API keys"
            )

    async def get_api_key_by_id(self, api_key_id: int, account_id: int) -> Optional[ApiKey]:
        """
        Gets a specific API key by ID, ensuring it belongs to the account.

        Args:
            api_key_id: The API key ID
            account_id: The account ID for security

        Returns:
            The API key if found and belongs to account, None otherwise
        """
        try:
            api_key = await self.api_key_repo.get_by_id(api_key_id)
            if api_key and api_key.account_id == account_id:
                return api_key
            return None
        except Exception as e:
            log_error(f"Error getting API key: {str(e)}")
            return None

    async def update_api_key(
        self,
        api_key_id: int,
        account_id: int,
        name: Optional[str] = None
    ) -> Optional[ApiKey]:
        """
        Updates an API key's metadata.

        Args:
            api_key_id: The API key ID to update
            account_id: The account ID for security
            name: New name for the API key

        Returns:
            The updated API key if successful, None otherwise
        """
        try:
            update_data = {}
            if name is not None:
                update_data['name'] = name

            if not update_data:
                # Nothing to update
                return await self.get_api_key_by_id(api_key_id, account_id)

            return await self.api_key_repo.update_api_key(api_key_id, account_id, **update_data)
        except Exception as e:
            log_error(f"Error updating API key: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error updating API key"
            )

    async def revoke_api_key(self, api_key_id: int, account_id: int) -> bool:
        """
        Revokes (deactivates) an API key.

        Args:
            api_key_id: The API key ID to revoke
            account_id: The account ID for security

        Returns:
            True if the API key was revoked, False if not found
        """
        try:
            return await self.api_key_repo.revoke_api_key(api_key_id, account_id)
        except Exception as e:
            log_error(f"Error revoking API key: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Error revoking API key"
            )

    async def validate_api_key(self, api_key: str) -> Optional[ApiKey]:
        """
        Validates an API key and returns the corresponding API key model.

        Args:
            api_key: The API key string to validate

        Returns:
            The API key model if valid, None otherwise
        """
        try:
            # Hash the provided API key
            api_key_hash = hash_api_key(api_key)

            # Find the API key in the database
            api_key_model = await self.api_key_repo.get_by_api_key_hash(api_key_hash)

            if api_key_model:
                # Update last_used timestamp (fire and forget)
                try:
                    await self.api_key_repo.update_last_used(api_key_model.id)
                except Exception:
                    # Don't fail validation if we can't update last_used
                    pass

            return api_key_model

        except Exception as e:
            log_error(f"Error validating API key: {str(e)}")
            return None

    # Legacy methods for backward compatibility
    async def generate_first_api_key(self, account: Account) -> Tuple[str, Dict[str, Any]]:
        """
        Legacy method for backward compatibility.
        Creates the first API key for an account and returns legacy format.
        """
        api_key, api_key_model = await self.create_api_key(
            account_id=account.account_id,
            name="Default API Key"
        )

        # Return in legacy format
        update_data = {
            'api_key_hash': api_key_model.api_key_hash,
            'api_key_prefix': api_key_model.api_key_prefix,
            'api_key_last_chars': api_key_model.api_key_last_chars,
            'api_key_created_at': api_key_model.created_at,
            'api_key_revealed': True
        }

        return api_key, update_data

    async def rotate_api_key(self, account: Account) -> Tuple[str, Dict[str, Any]]:
        """
        Legacy method for backward compatibility.
        Creates a new API key and returns legacy format.
        """
        return await self.generate_first_api_key(account)