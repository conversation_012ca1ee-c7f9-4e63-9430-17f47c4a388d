"""
Tests para las utilidades de Mercado Pago.
"""

import pytest
import hmac
import hashlib
from unittest.mock import <PERSON><PERSON><PERSON>, patch
from fastapi import HTTPEx<PERSON>, Request
from src.utils.mercadopago_utils import verify_mercadopago_signature, extract_signature_parts


def test_extract_signature_parts_valid():
    """Test para extract_signature_parts con un header válido."""
    # Arrange
    x_signature = "ts=1704908010,v1=618c85345248dd820d5fd456117c2ab2ef8eda45a0282ff693eac24131a5e839"
    
    # Act
    ts, signature = extract_signature_parts(x_signature)
    
    # Assert
    assert ts == "1704908010"
    assert signature == "618c85345248dd820d5fd456117c2ab2ef8eda45a0282ff693eac24131a5e839"


def test_extract_signature_parts_invalid():
    """Test para extract_signature_parts con un header inválido."""
    # Arrange
    x_signature = "invalid_format"
    
    # Act
    ts, signature = extract_signature_parts(x_signature)
    
    # Assert
    assert ts is None
    assert signature is None


def test_extract_signature_parts_missing_parts():
    """Test para extract_signature_parts con partes faltantes."""
    # Arrange
    x_signature = "ts=1704908010"  # Falta la parte v1
    
    # Act
    ts, signature = extract_signature_parts(x_signature)
    
    # Assert
    assert ts == "1704908010"
    assert signature is None


@pytest.mark.asyncio
@patch("src.utils.mercadopago_utils.settings")
@pytest.mark.asyncio
async def test_verify_mercadopago_signature_valid(mock_settings):
    """Test para verify_mercadopago_signature con una firma válida."""
    # Arrange
    mock_settings.MERCADOPAGO_WEBHOOK_SECRET = "test_secret"
    
    # Crear un request mock
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {
        "x-signature": "ts=1704908010,v1=618c85345248dd820d5fd456117c2ab2ef8eda45a0282ff693eac24131a5e839",
        "x-request-id": "test_request_id"
    }
    mock_request.query_params = {"data.id": "test_data_id"}
    
    # Calcular la firma esperada
    template = f"id:test_data_id;request-id:test_request_id;ts:1704908010;"
    expected_signature = hmac.new(
        "test_secret".encode(),
        template.encode(),
        hashlib.sha256
    ).hexdigest()
    
    # Actualizar el mock con la firma calculada
    mock_request.headers["x-signature"] = f"ts=1704908010,v1={expected_signature}"
    
    # Act
    result = await verify_mercadopago_signature(mock_request)
    
    # Assert
    assert result


@pytest.mark.asyncio
@patch("src.utils.mercadopago_utils.settings")
async def test_verify_mercadopago_signature_invalid(mock_settings):
    """Test para verify_mercadopago_signature con una firma inválida."""
    # Arrange
    mock_settings.MERCADOPAGO_WEBHOOK_SECRET = "test_secret"
    
    # Crear un request mock
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {
        "x-signature": "ts=1704908010,v1=invalid_signature",
        "x-request-id": "test_request_id"
    }
    mock_request.query_params = {"data.id": "test_data_id"}
    
    # Act & Assert
    with pytest.raises(HTTPException) as excinfo:
        await verify_mercadopago_signature(mock_request)
    
    assert excinfo.value.status_code == 401
    assert "Invalid signature" in excinfo.value.detail


@pytest.mark.asyncio
async def test_verify_mercadopago_signature_missing_header():
    """Test para verify_mercadopago_signature con header faltante."""
    # Arrange
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {}  # Sin headers
    
    # Act & Assert
    with pytest.raises(HTTPException) as excinfo:
        await verify_mercadopago_signature(mock_request)
    
    assert excinfo.value.status_code == 401
    assert "Missing x-signature header" in excinfo.value.detail


@pytest.mark.asyncio
async def test_verify_mercadopago_signature_missing_request_id():
    """Test para verify_mercadopago_signature con request-id faltante."""
    # Arrange
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {
        "x-signature": "ts=1704908010,v1=some_signature"
    }  # Sin x-request-id
    
    # Act & Assert
    with pytest.raises(HTTPException) as excinfo:
        await verify_mercadopago_signature(mock_request)
    
    assert excinfo.value.status_code == 401
    assert "Missing x-request-id header" in excinfo.value.detail


@pytest.mark.asyncio
async def test_verify_mercadopago_signature_missing_data_id():
    """Test para verify_mercadopago_signature con data.id faltante."""
    # Arrange
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {
        "x-signature": "ts=1704908010,v1=some_signature",
        "x-request-id": "test_request_id"
    }
    mock_request.query_params = {}  # Sin data.id
    
    # Act & Assert
    with pytest.raises(HTTPException) as excinfo:
        await verify_mercadopago_signature(mock_request)
    
    assert excinfo.value.status_code == 400
    assert "Missing data.id parameter" in excinfo.value.detail


@pytest.mark.asyncio
@patch("src.utils.mercadopago_utils.settings")
async def test_verify_mercadopago_signature_missing_secret(mock_settings):
    """Test para verify_mercadopago_signature con secreto faltante."""
    # Arrange
    mock_settings.MERCADOPAGO_WEBHOOK_SECRET = None  # Sin secreto configurado
    
    # Crear un request mock
    mock_request = MagicMock(spec=Request)
    mock_request.headers = {
        "x-signature": "ts=1704908010,v1=some_signature",
        "x-request-id": "test_request_id"
    }
    mock_request.query_params = {"data.id": "test_data_id"}
    
    # Act & Assert
    with pytest.raises(HTTPException) as excinfo:
        await verify_mercadopago_signature(mock_request)
    
    assert excinfo.value.status_code == 500
    assert "Webhook secret not configured" in excinfo.value.detail
