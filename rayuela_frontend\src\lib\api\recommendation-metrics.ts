// src/lib/api/recommendation-metrics.ts
import { fetchApi } from '../api';

// Interfaces para las métricas de recomendación
export interface RecommendationPerformanceMetrics {
  models: ModelMetrics[];
  resource_metrics: ResourceMetrics;
  summary: MetricsSummary;
}

export interface ModelMetrics {
  model_id: number;
  model_type: string;
  version: string;
  created_at: string;
  metrics: {
    precision: number;
    recall: number;
    ndcg: number;
    map: number;
    catalog_coverage: number;
    diversity: number;
    novelty: number;
    serendipity: number;
  };
}

export interface ResourceMetrics {
  memory_usage_mb: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  cpu_percent: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  evaluation_time_seconds: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  collab_training_time: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
  content_training_time: {
    avg_value: number;
    min_value: number;
    max_value: number;
  };
}

export interface MetricsSummary {
  precision: number;
  recall: number;
  ndcg: number;
  map: number;
  catalog_coverage: number;
  diversity: number;
  novelty: number;
  serendipity: number;
}

// Interfaces para las métricas de confianza
export interface ConfidenceMetrics {
  confidence_distribution: {
    collaborative: ConfidenceDistribution;
    content: ConfidenceDistribution;
    hybrid: ConfidenceDistribution;
  };
  category_confidence: Record<string, number>;
  confidence_factors: {
    user_history_size: number;
    item_popularity: number;
    category_strength: number;
    model_type: number;
  };
  confidence_trends: {
    last_7_days: ConfidenceTrendPoint[];
    last_30_days: ConfidenceTrendPoint[];
  };
  total_interactions_analyzed: number;
  last_updated: string;
}

export interface ConfidenceDistribution {
  low: number;
  medium: number;
  high: number;
  avg: number;
}

export interface ConfidenceTrendPoint {
  date: string;
  avg_confidence: number;
  count: number;
}

/**
 * Obtiene las métricas de rendimiento de recomendaciones
 * @param token Token JWT del usuario
 * @param apiKey API Key de la cuenta
 * @param modelId ID del modelo específico (opcional)
 * @param metricType Tipo de métrica a filtrar (opcional)
 * @returns Métricas de rendimiento de recomendaciones
 */
export async function getRecommendationPerformance(
  token: string,
  apiKey: string,
  modelId?: number,
  metricType?: string
): Promise<RecommendationPerformanceMetrics> {
  let endpoint = '/analytics/recommendation_performance';
  
  // Añadir parámetros de consulta si se proporcionan
  const params = new URLSearchParams();
  if (modelId) params.append('model_id', modelId.toString());
  if (metricType) params.append('metric_type', metricType);
  
  const queryString = params.toString();
  if (queryString) endpoint += `?${queryString}`;
  
  return fetchApi<RecommendationPerformanceMetrics>(endpoint, { token, apiKey });
}

/**
 * Obtiene las métricas de confianza para las recomendaciones
 * @param token Token JWT del usuario
 * @param apiKey API Key de la cuenta
 * @returns Métricas de confianza para las recomendaciones
 */
export async function getConfidenceMetrics(
  token: string,
  apiKey: string
): Promise<ConfidenceMetrics> {
  return fetchApi<ConfidenceMetrics>('/recommendations/confidence-metrics', { token, apiKey });
}
