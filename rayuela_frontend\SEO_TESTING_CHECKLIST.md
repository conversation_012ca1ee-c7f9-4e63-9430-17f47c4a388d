# SEO Implementation Testing Checklist

## 🚀 Quick Start Testing

### 1. Development Server
```bash
cd rayuela_frontend
npm install
npm run dev
```

### 2. Basic Functionality Tests

#### ✅ Robots.txt
- [ ] Visit: `http://localhost:3000/robots.txt`
- [ ] Verify it shows proper Allow/Disallow directives
- [ ] Check sitemap reference is present

#### ✅ Sitemap
- [ ] Visit: `http://localhost:3000/sitemap.xml`
- [ ] Verify it contains all public pages
- [ ] Check documentation pages are included
- [ ] Verify proper lastModified dates

#### ✅ Homepage SEO
- [ ] Visit: `http://localhost:3000/`
- [ ] Should redirect to `/home`
- [ ] View page source and check:
  - [ ] `<title>` tag is present and descriptive
  - [ ] `<meta name="description">` is present
  - [ ] `<link rel="canonical">` is present
  - [ ] Open Graph tags (`og:title`, `og:description`, `og:image`)
  - [ ] Twitter Card tags
  - [ ] JSON-LD structured data scripts

#### ✅ Marketing Pages
- [ ] **Features**: `http://localhost:3000/features`
  - [ ] Unique title and description
  - [ ] Structured data for software application
  - [ ] Proper canonical URL
  
- [ ] **Pricing**: `http://localhost:3000/pricing`
  - [ ] Unique title and description
  - [ ] Offer schema for pricing plans
  - [ ] Badge component renders correctly
  
- [ ] **Documentation**: `http://localhost:3000/docs`
  - [ ] API reference schema
  - [ ] Links to documentation sections work
  - [ ] Proper navigation structure

#### ✅ Documentation Pages
- [ ] **Python Quickstart**: `http://localhost:3000/docs/quickstart/python`
  - [ ] Article schema markup
  - [ ] Breadcrumb navigation
  - [ ] Code examples render properly
  - [ ] Internal links work

#### ✅ Legal Pages
- [ ] **Privacy**: `http://localhost:3000/legal/privacy`
- [ ] **Terms**: `http://localhost:3000/legal/terms`
- [ ] **Notice**: `http://localhost:3000/legal/notice`
- [ ] **Cookies**: `http://localhost:3000/legal/cookies`

For each legal page, verify:
- [ ] Unique title and description
- [ ] Proper canonical URL
- [ ] Back to home link works
- [ ] Content renders properly

## 🔍 Advanced SEO Testing

### 1. Structured Data Validation
Use Google's Rich Results Test: https://search.google.com/test/rich-results

Test these URLs:
- [ ] Homepage (Organization + SoftwareApplication schema)
- [ ] Features page (SoftwareApplication schema)
- [ ] Pricing page (Offer schema)
- [ ] Documentation pages (APIReference schema)
- [ ] Python quickstart (Article schema)

### 2. Meta Tags Validation
Use a meta tag checker or browser dev tools:

For each page, verify:
- [ ] Title tag (unique, descriptive, under 60 characters)
- [ ] Meta description (unique, compelling, under 160 characters)
- [ ] Canonical URL (correct and absolute)
- [ ] Open Graph tags (title, description, image, URL)
- [ ] Twitter Card tags

### 3. Performance Testing
Use Google PageSpeed Insights: https://pagespeed.web.dev/

Test these pages:
- [ ] Homepage
- [ ] Features page
- [ ] Pricing page
- [ ] Documentation page

Check for:
- [ ] Good Core Web Vitals scores
- [ ] SEO score above 90
- [ ] Accessibility score above 90

### 4. Mobile Responsiveness
Use Google's Mobile-Friendly Test: https://search.google.com/test/mobile-friendly

- [ ] Homepage is mobile-friendly
- [ ] Features page is mobile-friendly
- [ ] Pricing page is mobile-friendly
- [ ] Documentation pages are mobile-friendly

## 🛠️ Build Testing

### 1. Production Build
```bash
cd rayuela_frontend
npm run build
npm start
```

### 2. Build Verification
- [ ] Build completes without errors
- [ ] All pages render correctly in production mode
- [ ] Static files are properly generated
- [ ] Sitemap is accessible in production build

## 🌐 Production Deployment Testing

### 1. Environment Setup
Ensure these environment variables are set:
```env
NEXT_PUBLIC_SITE_URL=https://rayuela.ai
NEXT_PUBLIC_API_URL=https://api.rayuela.ai
```

### 2. Live Site Testing
After deployment:
- [ ] `https://rayuela.ai/robots.txt` is accessible
- [ ] `https://rayuela.ai/sitemap.xml` is accessible
- [ ] All canonical URLs point to production domain
- [ ] OG images load correctly
- [ ] Social media previews work (test with Facebook Debugger, Twitter Card Validator)

### 3. Search Console Setup
- [ ] Verify domain in Google Search Console
- [ ] Submit sitemap: `https://rayuela.ai/sitemap.xml`
- [ ] Monitor for crawl errors
- [ ] Check indexing status

## 🐛 Common Issues & Troubleshooting

### Issue: Sitemap not generating documentation pages
**Solution**: Ensure the backend docs folder path is correct in `src/app/sitemap.ts`

### Issue: OG images not loading
**Solutions**:
1. Generate PNG version of OG image: `node src/scripts/generate-og-image.js`
2. Convert HTML template to PNG (1200x630px)
3. Save as `public/og-image.png`

### Issue: Canonical URLs pointing to localhost
**Solution**: Set `NEXT_PUBLIC_SITE_URL` environment variable

### Issue: Structured data validation errors
**Solutions**:
1. Check JSON-LD syntax in browser console
2. Verify schema.org properties are correct
3. Use Google's Structured Data Testing Tool

### Issue: Pages showing as client components
**Solution**: Remove `"use client"` directive from pages that don't need client-side features

## ✅ Success Criteria

Your SEO implementation is successful when:

- [ ] All pages have unique, descriptive titles and meta descriptions
- [ ] Robots.txt properly allows/disallows appropriate pages
- [ ] Sitemap includes all public pages and documentation
- [ ] Structured data validates without errors
- [ ] Google PageSpeed Insights shows good SEO scores
- [ ] Social media previews display correctly
- [ ] All internal links work properly
- [ ] Pages load quickly and are mobile-friendly

## 📞 Support

If you encounter issues:
1. Check the browser console for JavaScript errors
2. Validate HTML markup
3. Test with different browsers
4. Use SEO testing tools to identify specific issues
5. Review the implementation summary for configuration details
