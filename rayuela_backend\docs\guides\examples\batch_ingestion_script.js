#!/usr/bin/env node
/**
 * Script de ejemplo para la ingesta masiva de datos en Rayuela.
 * 
 * Este script lee datos de archivos CSV y los envía al endpoint de ingesta masiva.
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');
const csv = require('csv-parser');
const yargs = require('yargs/yargs');
const { hideBin } = require('yargs/helpers');

/**
 * Lee un archivo CSV y devuelve una promesa que resuelve a un array de objetos.
 * 
 * @param {string} filePath - Ruta al archivo CSV
 * @returns {Promise<Array<Object>>} - Promesa que resuelve a un array de objetos
 */
function readCsvFile(filePath) {
  return new Promise((resolve, reject) => {
    const results = [];
    
    fs.createReadStream(filePath)
      .pipe(csv())
      .on('data', (data) => {
        // Convertir valores numéricos
        Object.keys(data).forEach(key => {
          if (['price', 'average_rating', 'value'].includes(key)) {
            const num = parseFloat(data[key]);
            if (!isNaN(num)) {
              data[key] = num;
            }
          } else if (['num_ratings', 'inventory_count'].includes(key)) {
            const num = parseInt(data[key], 10);
            if (!isNaN(num)) {
              data[key] = num;
            }
          }
        });
        
        results.push(data);
      })
      .on('end', () => {
        resolve(results);
      })
      .on('error', (error) => {
        reject(error);
      });
  });
}

/**
 * Prepara el payload para la ingesta masiva.
 * 
 * @param {string} usersFile - Ruta al archivo CSV de usuarios
 * @param {string} productsFile - Ruta al archivo CSV de productos
 * @param {string} interactionsFile - Ruta al archivo CSV de interacciones
 * @returns {Object} Payload para la ingesta masiva
 */
async function preparePayload(usersFile, productsFile, interactionsFile) {
  const payload = {};
  
  // Cargar usuarios
  if (usersFile) {
    const users = await readCsvFile(usersFile);
    payload.users = users;
  }
  
  // Cargar productos
  if (productsFile) {
    const products = await readCsvFile(productsFile);
    payload.products = products;
  }
  
  // Cargar interacciones
  if (interactionsFile) {
    const interactions = await readCsvFile(interactionsFile);
    
    // Convertir external_id a id para interacciones
    // Nota: En un caso real, necesitarías mapear los external_id a los id reales
    // Este es solo un ejemplo simplificado
    interactions.forEach(interaction => {
      if (interaction.end_user_external_id) {
        interaction.end_user_id = interaction.end_user_external_id;
        delete interaction.end_user_external_id;
      }
      if (interaction.product_external_id) {
        interaction.product_id = interaction.product_external_id;
        delete interaction.product_external_id;
      }
    });
    
    payload.interactions = interactions;
  }
  
  return payload;
}

/**
 * Envía una solicitud de ingesta masiva a la API.
 * 
 * @param {string} apiUrl - URL base de la API
 * @param {string} apiKey - API Key para autenticación
 * @param {Object} payload - Payload para la ingesta masiva
 * @returns {Promise<Object>} - Promesa que resuelve a la respuesta de la API
 */
async function sendBatchRequest(apiUrl, apiKey, payload) {
  try {
    const response = await axios.post(
      `${apiUrl}/ingestion/batch`,
      payload,
      {
        headers: {
          'X-API-Key': apiKey,
          'Content-Type': 'application/json'
        }
      }
    );
    
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`Error en la solicitud: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
    } else {
      throw error;
    }
  }
}

/**
 * Verifica el estado de un trabajo de ingesta masiva.
 * 
 * @param {string} apiUrl - URL base de la API
 * @param {string} apiKey - API Key para autenticación
 * @param {number} jobId - ID del trabajo de ingesta
 * @returns {Promise<Object>} - Promesa que resuelve al estado del trabajo
 */
async function checkJobStatus(apiUrl, apiKey, jobId) {
  try {
    const response = await axios.get(
      `${apiUrl}/ingestion/batch/${jobId}`,
      {
        headers: {
          'X-API-Key': apiKey
        }
      }
    );
    
    return response.data;
  } catch (error) {
    if (error.response) {
      throw new Error(`Error al verificar el estado: ${error.response.status} - ${JSON.stringify(error.response.data)}`);
    } else {
      throw error;
    }
  }
}

/**
 * Monitorea el estado de un trabajo de ingesta masiva hasta que se complete o falle.
 * 
 * @param {string} apiUrl - URL base de la API
 * @param {string} apiKey - API Key para autenticación
 * @param {number} jobId - ID del trabajo de ingesta
 * @param {number} checkInterval - Intervalo entre verificaciones (en segundos)
 * @param {number} maxChecks - Número máximo de verificaciones
 */
async function monitorJob(apiUrl, apiKey, jobId, checkInterval = 5, maxChecks = 20) {
  let checks = 0;
  let completed = false;
  
  console.log(`Monitoreando trabajo de ingesta (ID: ${jobId})...`);
  
  while (checks < maxChecks && !completed) {
    try {
      const status = await checkJobStatus(apiUrl, apiKey, jobId);
      
      const jobStatus = status.status || '';
      const progress = status.progress_percentage || 0;
      
      console.log(`Estado: ${jobStatus} - Progreso: ${progress.toFixed(1)}%`);
      
      if (['completed', 'failed'].includes(jobStatus)) {
        completed = true;
        
        if (jobStatus === 'completed') {
          const processed = status.processed_count || {};
          console.log("\n¡Ingesta completada exitosamente!");
          console.log(`Usuarios procesados: ${processed.users || 0}`);
          console.log(`Productos procesados: ${processed.products || 0}`);
          console.log(`Interacciones procesadas: ${processed.interactions || 0}`);
          console.log(`Total procesado: ${processed.total || 0}`);
          console.log(`Errores: ${processed.errors || 0}`);
        } else {
          const errorMsg = status.error_message || 'Error desconocido';
          console.log(`\n¡Ingesta fallida! Error: ${errorMsg}`);
        }
      }
      
      if (!completed) {
        await new Promise(resolve => setTimeout(resolve, checkInterval * 1000));
        checks++;
      }
    } catch (error) {
      console.log(`Error al monitorear el trabajo: ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, checkInterval * 1000));
      checks++;
    }
  }
  
  if (!completed) {
    console.log("\nSe alcanzó el límite de verificaciones. El trabajo puede seguir en progreso.");
    console.log(`Verifique manualmente el estado con el ID: ${jobId}`);
  }
}

/**
 * Función principal del script.
 */
async function main() {
  // Configurar argumentos de línea de comandos
  const argv = yargs(hideBin(process.argv))
    .option('api-url', {
      describe: 'URL base de la API de Rayuela',
      demandOption: true,
      type: 'string'
    })
    .option('api-key', {
      describe: 'API Key para autenticación',
      demandOption: true,
      type: 'string'
    })
    .option('users', {
      describe: 'Ruta al archivo CSV de usuarios',
      type: 'string'
    })
    .option('products', {
      describe: 'Ruta al archivo CSV de productos',
      type: 'string'
    })
    .option('interactions', {
      describe: 'Ruta al archivo CSV de interacciones',
      type: 'string'
    })
    .option('monitor', {
      describe: 'Monitorear el estado del trabajo',
      type: 'boolean',
      default: false
    })
    .check(argv => {
      if (!argv.users && !argv.products && !argv.interactions) {
        throw new Error('Debe proporcionar al menos un archivo de datos (usuarios, productos o interacciones)');
      }
      return true;
    })
    .argv;
  
  try {
    // Preparar payload
    console.log("Preparando datos para la ingesta...");
    const payload = await preparePayload(argv.users, argv.products, argv.interactions);
    
    // Mostrar resumen
    console.log("\nResumen de datos a ingestar:");
    if (payload.users) {
      console.log(`- Usuarios: ${payload.users.length}`);
    }
    if (payload.products) {
      console.log(`- Productos: ${payload.products.length}`);
    }
    if (payload.interactions) {
      console.log(`- Interacciones: ${payload.interactions.length}`);
    }
    
    // Enviar solicitud
    console.log("\nEnviando solicitud de ingesta masiva...");
    const response = await sendBatchRequest(argv.apiUrl, argv.apiKey, payload);
    
    // Mostrar respuesta
    const jobId = response.job_id;
    console.log(`\nSolicitud enviada exitosamente. Job ID: ${jobId}`);
    console.log(`Estado inicial: ${response.status}`);
    console.log(`Usuarios a procesar: ${response.total_users || 0}`);
    console.log(`Productos a procesar: ${response.total_products || 0}`);
    console.log(`Interacciones a procesar: ${response.total_interactions || 0}`);
    
    // Monitorear estado si se solicita
    if (argv.monitor && jobId) {
      console.log("\n");
      await monitorJob(argv.apiUrl, argv.apiKey, jobId);
    }
  } catch (error) {
    console.error(`\nError: ${error.message}`);
    process.exit(1);
  }
}

// Ejecutar la función principal
main();
