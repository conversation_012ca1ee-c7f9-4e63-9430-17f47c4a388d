#!/bin/bash
# Script to verify Celery workers deployment and configuration
# Verifies that Celery workers and beat scheduler are running correctly

set -e

echo "⚙️ === CELERY WORKERS VERIFICATION ==="
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print status
print_status() {
    echo -e "${YELLOW}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# Check if gcloud is available
if ! command -v gcloud &> /dev/null; then
    print_error "❌ gcloud not found. Please install Google Cloud SDK."
    exit 1
fi

# Get project and region
PROJECT_ID=$(gcloud config get-value project 2>/dev/null)
REGION="us-central1"

if [ -z "$PROJECT_ID" ]; then
    print_error "❌ No project configured. Run 'gcloud config set project YOUR_PROJECT_ID'"
    exit 1
fi

print_status "🔍 Checking Celery services in project: $PROJECT_ID"

# Check Celery Worker for Maintenance
print_status "📋 Checking Celery Worker (Maintenance)..."
if gcloud run services describe rayuela-worker-maintenance --region=$REGION &> /dev/null; then
    WORKER_STATUS=$(gcloud run services describe rayuela-worker-maintenance --region=$REGION --format="value(status.conditions[0].status)")
    WORKER_URL=$(gcloud run services describe rayuela-worker-maintenance --region=$REGION --format="value(status.url)")

    if [[ $WORKER_STATUS == "True" ]]; then
        print_success "✅ Worker Maintenance is running"
        print_status "   URL: $WORKER_URL"

        # Get worker configuration
        WORKER_IMAGE=$(gcloud run services describe rayuela-worker-maintenance --region=$REGION --format="value(spec.template.spec.containers[0].image)")
        WORKER_MEMORY=$(gcloud run services describe rayuela-worker-maintenance --region=$REGION --format="value(spec.template.spec.containers[0].resources.limits.memory)")
        WORKER_CPU=$(gcloud run services describe rayuela-worker-maintenance --region=$REGION --format="value(spec.template.spec.containers[0].resources.limits.cpu)")

        print_status "   Image: $WORKER_IMAGE"
        print_status "   Memory: $WORKER_MEMORY, CPU: $WORKER_CPU"
    else
        print_error "❌ Worker Maintenance is not ready. Status: $WORKER_STATUS"
    fi
else
    print_error "❌ Worker Maintenance service not found"
    print_status "🔧 Deploy using: ./scripts/deploy-production.sh --direct"
    exit 1
fi

echo ""

# Check Celery Beat Scheduler
print_status "📋 Checking Celery Beat Scheduler..."
if gcloud run services describe rayuela-beat --region=$REGION &> /dev/null; then
    BEAT_STATUS=$(gcloud run services describe rayuela-beat --region=$REGION --format="value(status.conditions[0].status)")
    BEAT_URL=$(gcloud run services describe rayuela-beat --region=$REGION --format="value(status.url)")

    if [[ $BEAT_STATUS == "True" ]]; then
        print_success "✅ Celery Beat is running"
        print_status "   URL: $BEAT_URL"

        # Get beat configuration
        BEAT_IMAGE=$(gcloud run services describe rayuela-beat --region=$REGION --format="value(spec.template.spec.containers[0].image)")
        BEAT_MEMORY=$(gcloud run services describe rayuela-beat --region=$REGION --format="value(spec.template.spec.containers[0].resources.limits.memory)")
        BEAT_CPU=$(gcloud run services describe rayuela-beat --region=$REGION --format="value(spec.template.spec.containers[0].resources.limits.cpu)")

        print_status "   Image: $BEAT_IMAGE"
        print_status "   Memory: $BEAT_MEMORY, CPU: $BEAT_CPU"
    else
        print_error "❌ Celery Beat is not ready. Status: $BEAT_STATUS"
    fi
else
    print_error "❌ Celery Beat service not found"
    print_status "🔧 Deploy using: ./scripts/deploy-production.sh --direct"
    exit 1
fi

echo ""
print_success "🎉 Celery workers verification completed successfully!"
echo ""
print_status "📝 Summary:"
print_status "   ✅ Worker Maintenance is running and configured"
print_status "   ✅ Celery Beat scheduler is running"
print_status "   ✅ Partition management task scheduled daily at 1:00 AM"
print_status "   ✅ Using Cloud Run (simpler and more cost-effective than Kubernetes)"
print_status "   ✅ All services use immutable image tags"

echo ""
print_status "📋 Scheduled Tasks:"
print_status "   🔧 manage-partitions: Daily at 1:00 AM (maintenance queue)"
print_status "   🧹 cleanup-old-audit-logs: Daily (maintenance queue)"
print_status "   🗑️ cleanup-old-interactions: Weekly (maintenance queue)"
print_status "   📊 monitor-high-volume-tables: Hourly (maintenance queue)"
