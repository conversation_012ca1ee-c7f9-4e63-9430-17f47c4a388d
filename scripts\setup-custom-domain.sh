#!/bin/bash

set -e

# Configuración
DOMAIN="rayuela.ai"
SUBDOMAIN="api.rayuela.ai"
PROJECT_ID="lateral-insight-461112-g9"
REGION="us-central1"
DNS_ZONE="rayuela-ai-zone"

echo "🌐 === CONFIGURACIÓN DE DOMINIO PERSONALIZADO RAYUELA ==="
echo ""

# Verificar si el dominio está verificado
echo "🔍 Verificando estado del dominio..."
VERIFIED_DOMAINS=$(gcloud domains list-user-verified --format="value(domain)" 2>/dev/null || echo "")

if [[ ! "$VERIFIED_DOMAINS" =~ "$DOMAIN" ]]; then
    echo "❌ El dominio $DOMAIN no está verificado."
    echo "📋 Por favor, verifica el dominio en Google Search Console:"
    echo "   https://search.google.com/search-console/welcome"
    echo "   Después ejecuta: gcloud domains verify $DOMAIN"
    exit 1
fi

echo "✅ Dominio $DOMAIN verificado correctamente"

# Crear mappings de dominio
echo ""
echo "🔗 Creando mappings de dominio..."

echo "📍 Configurando $DOMAIN -> rayuela-frontend"
gcloud beta run domain-mappings create \
    --service=rayuela-frontend \
    --domain=$DOMAIN \
    --region=$REGION || echo "⚠️ Mapping principal ya existe o falló"

echo "📍 Configurando $SUBDOMAIN -> rayuela-backend"
gcloud beta run domain-mappings create \
    --service=rayuela-backend \
    --domain=$SUBDOMAIN \
    --region=$REGION || echo "⚠️ Mapping de API ya existe o falló"

# Obtener información de los mappings
echo ""
echo "📋 Obteniendo información de configuración DNS..."

MAIN_MAPPING=$(gcloud beta run domain-mappings describe $DOMAIN --region=$REGION --format="value(status.resourceRecords[0].rrdata)" 2>/dev/null || echo "")
API_MAPPING=$(gcloud beta run domain-mappings describe $SUBDOMAIN --region=$REGION --format="value(status.resourceRecords[0].rrdata)" 2>/dev/null || echo "")

if [ -n "$MAIN_MAPPING" ]; then
    echo "✅ Mapping principal creado: $DOMAIN -> $MAIN_MAPPING"
fi

if [ -n "$API_MAPPING" ]; then
    echo "✅ Mapping API creado: $SUBDOMAIN -> $API_MAPPING"
fi

# Crear registros DNS
echo ""
echo "🌐 Configurando registros DNS..."

if [ -n "$MAIN_MAPPING" ]; then
    echo "📝 Creando registro CNAME para $DOMAIN"
    gcloud dns record-sets create $DOMAIN. \
        --zone=$DNS_ZONE \
        --type=CNAME \
        --ttl=300 \
        --rrdatas=$MAIN_MAPPING || echo "⚠️ Registro principal ya existe"
fi

if [ -n "$API_MAPPING" ]; then
    echo "📝 Creando registro CNAME para $SUBDOMAIN"
    gcloud dns record-sets create $SUBDOMAIN. \
        --zone=$DNS_ZONE \
        --type=CNAME \
        --ttl=300 \
        --rrdatas=$API_MAPPING || echo "⚠️ Registro API ya existe"
fi

# Mostrar información final
echo ""
echo "🎉 === CONFIGURACIÓN COMPLETADA ==="
echo ""
echo "📊 SERVIDORES DNS (configurar en tu registrador):"
gcloud dns managed-zones describe $DNS_ZONE --format="table(nameServers[].join('\n'):label=NAME_SERVERS)"

echo ""
echo "🌐 URLs CONFIGURADAS:"
echo "   Frontend: https://$DOMAIN"
echo "   Backend API: https://$SUBDOMAIN"
echo ""
echo "⏰ Nota: Los cambios DNS pueden tardar hasta 48 horas en propagarse"
echo "   Puedes verificar la propagación en: https://dnschecker.org"
echo ""
echo "🔍 Para verificar el estado:"
echo "   gcloud beta run domain-mappings list --region=$REGION" 