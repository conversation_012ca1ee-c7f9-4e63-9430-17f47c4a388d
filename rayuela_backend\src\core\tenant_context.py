"""
Contexto de tenant usando contextvars para aislamiento seguro entre solicitudes.

Este módulo centraliza la gestión del contexto de tenant para evitar importaciones circulares
y garantizar que el aislamiento multi-tenant funcione correctamente.
"""

import contextvars
from typing import Optional

# Variable contextual para almacenar el account_id (thread-safe y async-safe)
current_tenant_ctx = contextvars.ContextVar("current_tenant", default=None)


def get_current_tenant_id() -> Optional[int]:
    """
    Obtiene el ID del tenant actual desde el contexto.
    Esta función se puede usar en componentes que no tienen acceso al request,
    como servicios, repositorios o utilidades.

    Returns:
        Optional[int]: El ID del tenant o None si no hay tenant
    """
    return current_tenant_ctx.get()


def set_current_tenant_id(account_id: Optional[int]):
    """
    Establece el ID del tenant actual en el contexto.
    ADVERTENCIA: Esta función solo debe ser usada por componentes internos
    como el middleware de tenant o tareas de Celery. El middleware es el responsable principal.

    Args:
        account_id: ID de la cuenta (tenant) o None para limpiar
    """
    current_tenant_ctx.set(account_id)
