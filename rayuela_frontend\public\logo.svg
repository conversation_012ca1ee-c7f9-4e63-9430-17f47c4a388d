<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="logo-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1E40AF;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="90" fill="url(#logo-gradient)"/>
  
  <!-- Inner design representing connections/recommendations -->
  <g transform="translate(100, 100)">
    <!-- Central node -->
    <circle cx="0" cy="0" r="15" fill="white"/>
    
    <!-- Surrounding nodes -->
    <circle cx="30" cy="-20" r="8" fill="rgba(255,255,255,0.8)"/>
    <circle cx="35" cy="15" r="8" fill="rgba(255,255,255,0.8)"/>
    <circle cx="-25" cy="25" r="8" fill="rgba(255,255,255,0.8)"/>
    <circle cx="-35" cy="-10" r="8" fill="rgba(255,255,255,0.8)"/>
    <circle cx="5" cy="-35" r="8" fill="rgba(255,255,255,0.8)"/>
    
    <!-- Connection lines -->
    <line x1="0" y1="0" x2="30" y2="-20" stroke="white" stroke-width="2" opacity="0.6"/>
    <line x1="0" y1="0" x2="35" y2="15" stroke="white" stroke-width="2" opacity="0.6"/>
    <line x1="0" y1="0" x2="-25" y2="25" stroke="white" stroke-width="2" opacity="0.6"/>
    <line x1="0" y1="0" x2="-35" y2="-10" stroke="white" stroke-width="2" opacity="0.6"/>
    <line x1="0" y1="0" x2="5" y2="-35" stroke="white" stroke-width="2" opacity="0.6"/>
  </g>
  
  <!-- Text -->
  <text x="100" y="170" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="white" text-anchor="middle">
    Rayuela
  </text>
</svg>
