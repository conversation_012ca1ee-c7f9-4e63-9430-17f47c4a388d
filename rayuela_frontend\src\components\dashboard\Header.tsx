// Ruta EXACTA: rayuela-frontend/src/components/dashboard/Header.tsx
"use client";
// Asegúrate de haber añadido 'button' con shadcn: npx shadcn-ui@latest add button
import { Button } from '@/components/ui/button';
import { Logo } from '@/components/ui/logo';
import { useAuth } from '@/lib/auth'; // Importamos el hook de autenticación

export default function Header() {
  // Obtenemos la función logout del contexto de autenticación
  const { logout, user } = useAuth(); // También podrías obtener 'user' si quieres mostrar el email, etc.

  return (
    <header className="bg-white dark:bg-gray-800 shadow p-4 flex justify-between items-center h-16 shrink-0"> {/* Añadido h-16 y shrink-0 */}
      {/* Puedes añadir un título o breadcrumbs aquí si quieres */}
      <div className="flex items-center">
         {/* Icono de Menú para móvil (opcional) */}
         {/* <button className="md:hidden mr-4">...</button> */}
         <div className="flex items-center">
            <Logo className="text-gray-800 dark:text-white mr-2 hidden md:block" />
            <span className="text-xl font-semibold text-gray-800 dark:text-white">Dashboard</span>
         </div>
      </div>

      <div className="flex items-center space-x-4">
         {/* Aquí podrías añadir notificaciones, selector de tema, etc. */}
         {user && ( // Muestra el botón de logout solo si hay un usuario
           <Button onClick={logout} variant="outline" size="sm">
             Logout
           </Button>
         )}
      </div>
    </header>
  );
}