from typing import Dict, Any, List, Tuple, Optional, Callable, Set
import numpy as np
from src.db import schemas
from src.ml_pipeline.learning_to_rank import LearningToRankModel
from src.utils.base_logger import log_info, log_warning
import time


class ContextualFilter:
    """
    Clase para filtrar recomendaciones basadas en el contexto.
    """
    
    def apply_filters(
        self,
        recommendations: List[Dict[str, Any]],
        context: schemas.RecommendationContext
    ) -> List[Dict[str, Any]]:
        """
        Aplica filtros basados en el contexto explícito.
        
        Args:
            recommendations: Lista de recomendaciones a filtrar
            context: Contexto explícito con información adicional
            
        Returns:
            Lista filtrada de recomendaciones
        """
        if not recommendations:
            return []
            
        filtered_recommendations = recommendations.copy()
        
        # Filtrar por source_item_id (excluir el producto actual en página de detalle)
        if context.page_type == schemas.PageType.PRODUCT_DETAIL and context.source_item_id is not None:
            filtered_recommendations = [
                r for r in filtered_recommendations
                if r.get("item_id") != context.source_item_id
            ]
            
        # Filtrar por categoría si estamos en una página de categoría
        if context.page_type == schemas.PageType.CATEGORY and context.category_id is not None:
            filtered_recommendations = [
                r for r in filtered_recommendations
                if r.get("category_id") == context.category_id
            ]
            
        # Excluir productos que ya están en el carrito
        if context.cart_item_ids:
            filtered_recommendations = [
                r for r in filtered_recommendations
                if r.get("item_id") not in context.cart_item_ids
            ]
            
        # Aplicar filtros personalizados desde custom_attributes
        if context.custom_attributes:
            for attr_name, attr_value in context.custom_attributes.items():
                if attr_name.startswith("filter_"):
                    field_name = attr_name[7:]  # Quitar el prefijo "filter_"
                    filtered_recommendations = [
                        r for r in filtered_recommendations
                        if str(r.get(field_name, "")) == str(attr_value)
                    ]
                    
        log_info(f"Filtrado por contexto: {len(recommendations)} -> {len(filtered_recommendations)} recomendaciones")
        return filtered_recommendations


class ContextualReranker:
    """
    Clase para reordenar recomendaciones basadas en el contexto.
    """
    
    def apply_reranking(
        self,
        recommendations: List[Dict[str, Any]],
        context: schemas.RecommendationContext,
        user_id: int
    ) -> List[Dict[str, Any]]:
        """
        Re-rankea las recomendaciones basadas en el contexto explícito.
        
        Args:
            recommendations: Lista de recomendaciones a re-rankear
            context: Contexto explícito con información adicional
            user_id: ID del usuario
            
        Returns:
            Lista re-rankeada de recomendaciones
        """
        if not recommendations:
            return []
            
        # Crear una copia para no modificar la original
        reranked_recommendations = recommendations.copy()
        
        # Aplicar boost a productos similares al producto actual en página de detalle
        if context.page_type == schemas.PageType.PRODUCT_DETAIL and context.source_item_id is not None:
            source_category = None
            source_price = None
            
            # Buscar la categoría y precio del producto de origen
            for r in recommendations:
                if r.get("item_id") == context.source_item_id:
                    source_category = r.get("category")
                    source_price = r.get("price")
                    break
                    
            # Boost a productos de la misma categoría y rango de precio similar
            if source_category or source_price:
                for r in reranked_recommendations:
                    boost_factor = 1.0
                    
                    # Boost por categoría
                    if source_category and r.get("category") == source_category:
                        boost_factor *= 1.2
                        
                    # Boost por rango de precio similar (±20%)
                    if source_price and r.get("price"):
                        price_ratio = r.get("price") / source_price
                        if 0.8 <= price_ratio <= 1.2:
                            boost_factor *= 1.1
                            
                    # Aplicar boost
                    if boost_factor > 1.0:
                        r["score"] = r.get("score", 0) * boost_factor
                        r["context_boosted"] = True
                        
        # Boost a productos basados en el dispositivo
        if context.device:
            for r in reranked_recommendations:
                # En móvil, priorizar productos con buen rating y precio más bajo
                if context.device == schemas.DeviceType.MOBILE:
                    if r.get("average_rating", 0) >= 4.0:
                        r["score"] = r.get("score", 0) * 1.1
                    if r.get("price", 0) > 0 and r.get("price", 0) < 50:
                        r["score"] = r.get("score", 0) * 1.05
                        
                # En desktop, mostrar más variedad y productos premium
                elif context.device == schemas.DeviceType.DESKTOP:
                    if r.get("price", 0) > 100:
                        r["score"] = r.get("score", 0) * 1.05
                        
        # Boost a productos vistos recientemente (pero no los primeros)
        if context.recently_viewed_ids:
            for r in reranked_recommendations:
                if r.get("item_id") in context.recently_viewed_ids:
                    # Pequeño boost para que aparezcan pero no al principio
                    r["score"] = r.get("score", 0) * 1.02
                    
        # Re-ordenar por score
        reranked_recommendations.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        log_info(f"Re-ranking por contexto aplicado a {len(reranked_recommendations)} recomendaciones")
        return reranked_recommendations


class DiversificationEngine:
    """
    Clase para diversificar recomendaciones utilizando MMR (Maximal Marginal Relevance).
    """
    
    def diversify_recommendations(
        self,
        candidates: List[Dict[str, Any]],
        n: int,
        lambda_param: float = 0.5,
        content_artifacts: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Diversifica las recomendaciones utilizando MMR.
        
        Args:
            candidates: Lista de candidatos a diversificar
            n: Número de recomendaciones a devolver
            lambda_param: Parámetro de balance entre relevancia y diversidad (0-1)
                          0 = solo diversidad, 1 = solo relevancia
            content_artifacts: Artefactos del modelo de contenido para similitud
            
        Returns:
            Lista diversificada de recomendaciones
        """
        if not candidates:
            return []
            
        # Si hay menos candidatos que n, devolver todos
        if len(candidates) <= n:
            return candidates
            
        # Determinar método de similitud
        similarity_method = self._determine_similarity_method(content_artifacts)
        
        # Implementación de MMR
        S = []  # Conjunto seleccionado
        R = candidates.copy()  # Conjunto restante
        
        # Ordenar candidatos iniciales por score
        R.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        # Seleccionar el primer elemento (máxima relevancia)
        S.append(R.pop(0))
        
        # Seleccionar el resto usando MMR
        while len(S) < n and R:
            max_mmr = -float("inf")
            max_item = None
            max_idx = -1
            
            for i, item in enumerate(R):
                # Calcular primer término: relevancia
                rel = item.get("score", 0)
                
                # Calcular segundo término: similitud máxima con elementos ya seleccionados
                max_sim = 0
                for s_item in S:
                    sim = similarity_method(item, s_item)
                    max_sim = max(max_sim, sim)
                    
                # Calcular MMR
                mmr = lambda_param * rel - (1 - lambda_param) * max_sim
                
                if mmr > max_mmr:
                    max_mmr = mmr
                    max_item = item
                    max_idx = i
                    
            if max_item:
                S.append(max_item)
                R.pop(max_idx)
            else:
                break
                
        log_info(f"Diversificación aplicada: {len(candidates)} candidatos -> {len(S)} seleccionados")
        return S
        
    def _determine_similarity_method(self, content_artifacts: Optional[Dict[str, Any]]) -> Callable:
        """
        Determina el método de similitud a utilizar.
        
        Args:
            content_artifacts: Artefactos del modelo de contenido
            
        Returns:
            Función de similitud
        """
        # Si tenemos artefactos y vectores de ítems, usamos similitud vectorial
        if content_artifacts and "model" in content_artifacts:
            model = content_artifacts["model"]
            if hasattr(model, "item_vectors"):
                def vector_similarity(item1: Dict[str, Any], item2: Dict[str, Any]) -> float:
                    item1_id = item1.get("item_id")
                    item2_id = item2.get("item_id")
                    
                    # Si alguno de los IDs no existe, devolver similitud mínima
                    if item1_id not in model.item_vectors or item2_id not in model.item_vectors:
                        return 0.0
                        
                    # Calcular similitud del coseno
                    vec1 = model.item_vectors[item1_id]
                    vec2 = model.item_vectors[item2_id]
                    
                    from sklearn.metrics.pairwise import cosine_similarity
                    
                    sim = cosine_similarity(
                        np.array(vec1).reshape(1, -1),
                        np.array(vec2).reshape(1, -1)
                    )[0][0]
                    
                    return float(sim)
                    
                return vector_similarity
                
        # Fallback: similitud por categoría
        def category_similarity(item1: Dict[str, Any], item2: Dict[str, Any]) -> float:
            # Si los items tienen la misma categoría, similitud 1.0, si no 0.0
            cat1 = item1.get("category", "")
            cat2 = item2.get("category", "")
            return 1.0 if cat1 and cat1 == cat2 else 0.0
            
        return category_similarity


class RecencyBooster:
    """
    Clase para aplicar boost por actualidad/novedad a las recomendaciones.
    """
    
    def apply_recency_boost(
        self,
        candidates: List[Dict[str, Any]],
        recency_boost: float = 0.2
    ) -> List[Dict[str, Any]]:
        """
        Aplica un boost a los productos más recientes.
        
        Args:
            candidates: Lista de candidatos
            recency_boost: Factor de boost (0-1)
            
        Returns:
            Lista con boost aplicado
        """
        if not candidates or recency_boost <= 0:
            return candidates
            
        # Crear una copia para no modificar la original
        boosted_candidates = candidates.copy()
        
        # Verificar si hay información de fecha de creación
        has_created_at = any("created_at" in item for item in boosted_candidates)
        
        if has_created_at:
            # Encontrar la fecha más reciente y más antigua
            dates = [item.get("created_at") for item in boosted_candidates if "created_at" in item]
            if not dates:
                return boosted_candidates
                
            import datetime
            
            # Convertir strings a datetime si es necesario
            parsed_dates = []
            for d in dates:
                if isinstance(d, str):
                    try:
                        parsed_dates.append(datetime.datetime.fromisoformat(d.replace("Z", "+00:00")))
                    except ValueError:
                        continue
                elif isinstance(d, datetime.datetime):
                    parsed_dates.append(d)
                    
            if not parsed_dates:
                return boosted_candidates
                
            newest_date = max(parsed_dates)
            oldest_date = min(parsed_dates)
            
            # Calcular el rango de fechas en días
            date_range = (newest_date - oldest_date).total_seconds() / (24 * 3600)
            
            # Si el rango es muy pequeño, no aplicar boost
            if date_range < 1:
                return boosted_candidates
                
            # Aplicar boost basado en la antigüedad relativa
            for item in boosted_candidates:
                if "created_at" in item:
                    created_at = item["created_at"]
                    
                    # Convertir a datetime si es string
                    if isinstance(created_at, str):
                        try:
                            created_at = datetime.datetime.fromisoformat(created_at.replace("Z", "+00:00"))
                        except ValueError:
                            continue
                            
                    # Calcular antigüedad relativa (0 = más viejo, 1 = más nuevo)
                    age_seconds = (newest_date - created_at).total_seconds()
                    relative_newness = 1.0 - (age_seconds / ((newest_date - oldest_date).total_seconds()))
                    
                    # Aplicar boost
                    boost_factor = 1.0 + (recency_boost * relative_newness)
                    item["score"] = item.get("score", 0) * boost_factor
                    item["recency_boosted"] = True
                    
        else:
            # Si no hay fecha, usar el campo "is_new" si existe
            for item in boosted_candidates:
                if item.get("is_new", False):
                    item["score"] = item.get("score", 0) * (1.0 + recency_boost)
                    item["recency_boosted"] = True
                    
        # Re-ordenar por score
        boosted_candidates.sort(key=lambda x: x.get("score", 0), reverse=True)
        
        log_info(f"Boost por novedad aplicado a {len(boosted_candidates)} recomendaciones")
        return boosted_candidates


class LearningToRankApplier:
    """
    Clase para aplicar Learning to Rank a las recomendaciones.
    """
    
    def apply_learning_to_rank(
        self,
        candidates: List[Dict[str, Any]],
        ltr_model: Optional[LearningToRankModel] = None,
        user_id: Optional[int] = None,
        user_history: Optional[Dict[int, Dict[str, Any]]] = None,
        content_artifacts: Optional[Dict[str, Any]] = None,
        timestamp: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Aplica Learning to Rank para reordenar los candidatos.
        
        Args:
            candidates: Lista de candidatos
            ltr_model: Modelo de Learning to Rank
            user_id: ID del usuario
            user_history: Historial del usuario
            content_artifacts: Artefactos del modelo de contenido
            timestamp: Timestamp actual para calcular características temporales
            
        Returns:
            Lista reordenada de candidatos
        """
        if not candidates or not ltr_model:
            return candidates
            
        # Crear una copia para no modificar la original
        ranked_candidates = candidates.copy()
        
        # Extraer características enriquecidas para reranking
        try:
            # Obtener timestamp actual si no se proporciona
            current_timestamp = timestamp if timestamp is not None else int(time.time())
            
            # Usar la nueva versión de predict que acepta historial y timestamp
            ranked_candidates = ltr_model.predict(
                recommendations=ranked_candidates,
                user_id=user_id,
                user_history=user_history,
                timestamp=current_timestamp
            )
            
            log_info(f"Learning to Rank aplicado a {len(ranked_candidates)} candidatos")
            
            # Logging de métricas para monitoreo
            self._log_ltr_metrics(ranked_candidates, candidates)
            
        except Exception as e:
            log_warning(f"Error al aplicar Learning to Rank: {str(e)}")
                
        return ranked_candidates
        
    def _log_ltr_metrics(
        self, ranked_candidates: List[Dict[str, Any]], original_candidates: List[Dict[str, Any]]
    ) -> None:
        """Registra métricas sobre el reordenamiento de LTR."""
        # Calcular cambio promedio en el ranking
        if not ranked_candidates or not original_candidates:
            return
            
        # Crear mapeo de item_id a posición original
        original_positions = {item["item_id"]: i for i, item in enumerate(original_candidates)}
        
        # Calcular cambios
        position_changes = []
        for i, item in enumerate(ranked_candidates):
            original_pos = original_positions.get(item["item_id"])
            if original_pos is not None:
                position_changes.append(original_pos - i)
                
        if position_changes:
            avg_position_change = sum(map(abs, position_changes)) / len(position_changes)
            max_position_change = max(map(abs, position_changes))
            
            log_info(f"LTR metrics - Avg position change: {avg_position_change:.2f}, Max: {max_position_change}")


class PostProcessingService:
    """
    Servicio para aplicar post-procesamiento a las recomendaciones.
    """
    
    def __init__(self, ltr_model: Optional[LearningToRankModel] = None):
        self.contextual_filter = ContextualFilter()
        self.contextual_reranker = ContextualReranker()
        self.diversification_engine = DiversificationEngine()
        self.recency_booster = RecencyBooster()
        self.ltr_applier = LearningToRankApplier()
        self.ltr_model = ltr_model
        
    def apply_post_processing(
        self,
        recommendations: List[Dict[str, Any]],
        context: Optional[schemas.RecommendationContext] = None,
        user_id: Optional[int] = None,
        account_id: Optional[int] = None,
        user_history: Optional[Dict[int, float]] = None,
        content_artifacts: Optional[Dict[str, Any]] = None,
        diversify: bool = True,
        apply_ltr: bool = True,
        boost_recency: bool = True,
        n_recommendations: int = 10,
        mmr_lambda: float = 0.5,
        recency_boost: float = 0.2
    ) -> List[Dict[str, Any]]:
        """
        Aplica post-procesamiento a las recomendaciones.
        
        Args:
            recommendations: Lista de recomendaciones candidatas
            context: Contexto explícito para filtrado y reranking
            user_id: ID del usuario
            account_id: ID de la cuenta
            user_history: Historial de interacciones del usuario
            content_artifacts: Artefactos del modelo de contenido
            diversify: Si se debe aplicar diversificación
            apply_ltr: Si se debe aplicar Learning to Rank
            boost_recency: Si se debe aplicar boost por novedad
            n_recommendations: Número de recomendaciones a devolver
            mmr_lambda: Parámetro lambda para MMR (0-1)
            recency_boost: Factor de boost por novedad (0-1)
            
        Returns:
            Lista procesada de recomendaciones
        """
        if not recommendations:
            return []
            
        processed_recs = recommendations.copy()
        
        # 1. Aplicar filtros contextuales
        if context:
            processed_recs = self.contextual_filter.apply_filters(
                recommendations=processed_recs,
                context=context
            )
            
            # 2. Aplicar reranking contextual
            if user_id is not None:
                processed_recs = self.contextual_reranker.apply_reranking(
                    recommendations=processed_recs,
                    context=context,
                    user_id=user_id
                )
                
        # 3. Aplicar boost por novedad/actualidad
        if boost_recency and recency_boost > 0:
            processed_recs = self.recency_booster.apply_recency_boost(
                candidates=processed_recs,
                recency_boost=recency_boost
            )
            
        # 4. Aplicar Learning to Rank si está disponible
        if apply_ltr and self.ltr_model:
            processed_recs = self.ltr_applier.apply_learning_to_rank(
                candidates=processed_recs,
                ltr_model=self.ltr_model,
                user_id=user_id,
                user_history=user_history,
                content_artifacts=content_artifacts
            )
            
        # 5. Aplicar diversificación (MMR)
        if diversify and len(processed_recs) > n_recommendations:
            processed_recs = self.diversification_engine.diversify_recommendations(
                candidates=processed_recs,
                n=n_recommendations,
                lambda_param=mmr_lambda,
                content_artifacts=content_artifacts
            )
        elif len(processed_recs) > n_recommendations:
            # Si no diversificamos, simplemente tomamos los top-n
            processed_recs = processed_recs[:n_recommendations]
            
        # Actualizar ranks
        for i, rec in enumerate(processed_recs):
            rec["rank"] = i + 1
            
        log_info(f"Post-procesamiento completado: {len(processed_recs)} recomendaciones finales")
        
        return processed_recs