"""add_api_key_revealed_field

Revision ID: add_api_key_revealed_field
Revises: d6d03121d24c
Create Date: 2025-04-18 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'add_api_key_revealed_field'
down_revision: Union[str, None] = 'd6d03121d24c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to add api_key_revealed field to accounts table."""
    op.add_column('accounts', sa.Column('api_key_revealed', sa.<PERSON>(), server_default=sa.text('false'), nullable=False, comment='Indica si la API Key inicial ya fue mostrada al usuario'))
    # Asegurarse que las columnas de API key sean nullable inicialmente
    op.alter_column('accounts', 'api_key_hash', existing_type=sa.String(length=64), nullable=True)
    op.alter_column('accounts', 'api_key_prefix', existing_type=sa.String(length=10), nullable=True)
    op.alter_column('accounts', 'api_key_last_chars', existing_type=sa.String(length=6), nullable=True)
    op.alter_column('accounts', 'api_key_created_at', existing_type=sa.DateTime(), nullable=True)


def downgrade() -> None:
    """Downgrade schema to remove api_key_revealed field from accounts table."""
    op.drop_column('accounts', 'api_key_revealed')
    # Revertir nullability si es necesario (depende del estado previo)
    # op.alter_column('accounts', 'api_key_hash', existing_type=sa.String(length=64), nullable=False)
    # op.alter_column('accounts', 'api_key_prefix', existing_type=sa.String(length=10), nullable=False)
    # op.alter_column('accounts', 'api_key_last_chars', existing_type=sa.String(length=6), nullable=False)
    # op.alter_column('accounts', 'api_key_created_at', existing_type=sa.DateTime(), nullable=False)
