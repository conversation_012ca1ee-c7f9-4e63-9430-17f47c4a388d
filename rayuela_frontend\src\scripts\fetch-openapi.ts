#!/usr/bin/env node
import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import fetch from 'node-fetch';

const writeFile = promisify(fs.writeFile);
const mkdir = promisify(fs.mkdir);

// URL del documento OpenAPI
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';
const OPENAPI_URL = `${API_BASE_URL}/api/openapi.json`;
const OUTPUT_DIR = path.resolve(process.cwd(), 'src/lib/openapi');
const OUTPUT_FILE = path.join(OUTPUT_DIR, 'openapi.json');

// Flags para controlar el comportamiento
const FORCE_FETCH = process.argv.includes('--force');
const SKIP_VALIDATION = process.argv.includes('--skip-validation');
const VERBOSE = process.argv.includes('--verbose');

/**
 * Valida que la especificación OpenAPI tenga contenido útil
 */
function validateOpenAPISpec(data: any): { isValid: boolean; issues: string[] } {
  const issues: string[] = [];

  if (!data.openapi) {
    issues.push('Missing openapi version');
  }

  if (!data.info || !data.info.title) {
    issues.push('Missing API info/title');
  }

  if (!data.paths || Object.keys(data.paths).length === 0) {
    issues.push('No API paths defined - this will generate an empty client');
  }

  if (VERBOSE) {
    console.log(`📊 OpenAPI Validation Results:`);
    console.log(`   - OpenAPI Version: ${data.openapi || 'Missing'}`);
    console.log(`   - API Title: ${data.info?.title || 'Missing'}`);
    console.log(`   - API Version: ${data.info?.version || 'Missing'}`);
    console.log(`   - Number of paths: ${Object.keys(data.paths || {}).length}`);
    console.log(`   - Number of components: ${Object.keys(data.components?.schemas || {}).length}`);
  }

  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * Verifica si el servidor backend está disponible
 */
async function checkServerHealth(): Promise<boolean> {
  try {
    const healthUrl = `${API_BASE_URL}/health`;
    if (VERBOSE) {
      console.log(`🔍 Checking server health at: ${healthUrl}`);
    }

    const response = await fetch(healthUrl, {
      timeout: 5000,
      headers: { 'Accept': 'application/json' }
    });

    if (response.ok) {
      const health = await response.json();
      if (VERBOSE) {
        console.log(`✅ Server health check passed:`, health);
      }
      return true;
    }

    if (VERBOSE) {
      console.log(`⚠️ Server health check failed: ${response.status} ${response.statusText}`);
    }
    return false;
  } catch (error) {
    if (VERBOSE) {
      console.log(`❌ Server health check error:`, error);
    }
    return false;
  }
}

async function fetchOpenAPI() {
  try {
    console.log(`🔄 Fetching OpenAPI specification from: ${OPENAPI_URL}`);

    // Check if server is available first
    const serverAvailable = await checkServerHealth();
    if (!serverAvailable && !FORCE_FETCH) {
      throw new Error('BACKEND_UNAVAILABLE');
    }

    const response = await fetch(OPENAPI_URL, {
      timeout: 10000,
      headers: { 'Accept': 'application/json' }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();

    // Validate the OpenAPI specification
    if (!SKIP_VALIDATION) {
      const validation = validateOpenAPISpec(data);

      if (!validation.isValid) {
        console.warn(`⚠️ OpenAPI specification has issues:`);
        validation.issues.forEach(issue => console.warn(`   - ${issue}`));

        if (!FORCE_FETCH) {
          throw new Error(`INVALID_OPENAPI: ${validation.issues.join(', ')}`);
        } else {
          console.warn(`⚠️ Proceeding anyway due to --force flag`);
        }
      } else {
        console.log(`✅ OpenAPI specification validation passed`);
      }
    }

    // Asegurarse de que el directorio existe
    await mkdir(OUTPUT_DIR, { recursive: true });

    // Guardar el archivo JSON
    await writeFile(OUTPUT_FILE, JSON.stringify(data, null, 2));

    console.log(`✅ OpenAPI specification saved to: ${OUTPUT_FILE}`);
    console.log(`📊 Summary: ${Object.keys(data.paths || {}).length} endpoints, ${Object.keys(data.components?.schemas || {}).length} schemas`);

    return true;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    console.error(`❌ Error fetching OpenAPI specification:`, errorMessage);

    // Handle specific error cases
    if (errorMessage.includes('BACKEND_UNAVAILABLE')) {
      console.error(`\n🚨 CRITICAL: Backend server is not available at ${API_BASE_URL}`);
      console.error(`\n💡 To fix this issue:`);
      console.error(`   1. Start the backend server: cd rayuela_backend && python main.py`);
      console.error(`   2. Verify the server is running: curl ${API_BASE_URL}/health`);
      console.error(`   3. Then run: npm run fetch-openapi --verbose`);
      console.error(`   4. Or use --force to skip server check: npm run fetch-openapi -- --force`);
    } else if (errorMessage.includes('INVALID_OPENAPI')) {
      console.error(`\n🚨 CRITICAL: OpenAPI specification is invalid or empty`);
      console.error(`\n💡 This usually means:`);
      console.error(`   1. Backend server is running but has no API endpoints defined`);
      console.error(`   2. FastAPI router configuration is incorrect`);
      console.error(`   3. OpenAPI generation is disabled in FastAPI`);
      console.error(`\n🔧 Check the backend configuration in main.py and api/v1/api.py`);
    }

    // Durante el build, si no podemos conectar al servidor, usar un archivo de fallback
    const isConnectionError = (error as any).code === 'ECONNREFUSED' ||
                             (error as any).errno === 'ECONNREFUSED' ||
                             errorMessage.includes('BACKEND_UNAVAILABLE');

    if (isConnectionError && process.env.NODE_ENV === 'production') {
      console.log(`\n⚠️ Server unavailable during production build. Checking for existing OpenAPI file...`);

      // Verificar si ya existe un archivo OpenAPI válido
      if (fs.existsSync(OUTPUT_FILE)) {
        try {
          const existingData = JSON.parse(fs.readFileSync(OUTPUT_FILE, 'utf8'));
          const validation = validateOpenAPISpec(existingData);

          if (validation.isValid) {
            console.log(`✅ Using existing valid OpenAPI file: ${OUTPUT_FILE}`);
            return true;
          } else {
            console.warn(`⚠️ Existing OpenAPI file is invalid: ${validation.issues.join(', ')}`);
          }
        } catch (parseError) {
          console.warn(`⚠️ Could not parse existing OpenAPI file: ${parseError}`);
        }
      }

      console.error(`\n❌ CRITICAL: No valid OpenAPI specification available for production build`);
      console.error(`\n💡 For production builds, you must:`);
      console.error(`   1. Generate a valid OpenAPI spec in development first`);
      console.error(`   2. Commit the generated openapi.json file to version control`);
      console.error(`   3. Or ensure the backend is available during build`);

      process.exit(1);
    }

    // For development, provide helpful guidance
    if (!FORCE_FETCH) {
      console.error(`\n💡 Development workflow:`);
      console.error(`   1. Start backend: cd rayuela_backend && python main.py`);
      console.error(`   2. Generate API: npm run fetch-openapi --verbose`);
      console.error(`   3. Generate client: npm run generate-api`);
      console.error(`\n🔧 Or use --force to create minimal spec: npm run fetch-openapi -- --force`);

      process.exit(1);
    }

    // Create minimal OpenAPI for forced generation
    console.log(`\n⚠️ Creating minimal OpenAPI spec due to --force flag...`);
    const minimalOpenAPI = {
      openapi: "3.0.0",
      info: {
        title: "Rayuela API",
        version: "1.0.0",
        description: "Minimal OpenAPI spec created due to server unavailability"
      },
      paths: {},
      components: {
        schemas: {}
      }
    };

    await mkdir(OUTPUT_DIR, { recursive: true });
    await writeFile(OUTPUT_FILE, JSON.stringify(minimalOpenAPI, null, 2));
    console.log(`⚠️ Minimal OpenAPI file created: ${OUTPUT_FILE}`);
    console.warn(`\n🚨 WARNING: This will generate an empty API client!`);
    console.warn(`   You must fetch the real OpenAPI spec when the backend is available.`);

    return false;
  }
}

// Help text
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🔄 OpenAPI Specification Fetcher

Usage: npm run fetch-openapi [options]

Options:
  --force           Force fetch even if server is unavailable (creates minimal spec)
  --skip-validation Skip OpenAPI specification validation
  --verbose         Show detailed output and validation results
  --help, -h        Show this help message

Examples:
  npm run fetch-openapi                    # Normal fetch with validation
  npm run fetch-openapi -- --verbose      # Detailed output
  npm run fetch-openapi -- --force        # Force minimal spec creation

Environment Variables:
  NEXT_PUBLIC_API_URL    Backend server URL (default: http://localhost:8000)

Workflow:
  1. Start backend server: cd rayuela_backend && python main.py
  2. Fetch OpenAPI spec: npm run fetch-openapi --verbose
  3. Generate API client: npm run generate-api
  4. Use generated client in your frontend code

Troubleshooting:
  - If server is unavailable: Start the backend first
  - If OpenAPI is empty: Check FastAPI router configuration
  - For production builds: Commit a valid openapi.json to version control
`);
  process.exit(0);
}

// Main execution
async function main() {
  console.log(`\n🚀 Rayuela OpenAPI Fetcher`);
  console.log(`📡 Target: ${OPENAPI_URL}`);
  console.log(`📁 Output: ${OUTPUT_FILE}`);

  if (FORCE_FETCH) {
    console.log(`⚠️ Force mode enabled - will create minimal spec if server unavailable`);
  }

  if (SKIP_VALIDATION) {
    console.log(`⚠️ Validation skipped`);
  }

  if (VERBOSE) {
    console.log(`📝 Verbose mode enabled`);
  }

  console.log(``);

  const success = await fetchOpenAPI();

  if (success) {
    console.log(`\n🎉 OpenAPI specification fetched successfully!`);
    console.log(`\n📋 Next steps:`);
    console.log(`   1. Generate API client: npm run generate-api`);
    console.log(`   2. Import and use the generated client in your code`);
  } else {
    console.log(`\n⚠️ OpenAPI fetch completed with warnings`);
    console.log(`\n📋 Recommended actions:`);
    console.log(`   1. Start the backend server`);
    console.log(`   2. Re-run: npm run fetch-openapi --verbose`);
    console.log(`   3. Then generate client: npm run generate-api`);
  }
}

main().catch(error => {
  console.error(`\n💥 Unexpected error:`, error);
  process.exit(1);
});