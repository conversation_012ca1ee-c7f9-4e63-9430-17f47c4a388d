"""
Test para verificar el flujo de autenticación refactorizado.

Este test verifica que:
1. El endpoint /auth/register devuelve JWT + API Key
2. El endpoint /auth/token solo devuelve JWT (sin API Key)
3. El endpoint /api-keys/ permite generar nuevas API Keys para usuarios autenticados
"""

import pytest
import requests
import json
from typing import Dict, Any


class TestAuthFlowRefactored:
    """Test del flujo de autenticación refactorizado."""

    BASE_URL = "http://localhost:8000/api/v1"

    def test_register_returns_jwt_and_api_key(self):
        """
        Test: El endpoint /auth/register debe devolver JWT + API Key.
        """
        # Datos de registro únicos
        register_data = {
            "account_name": "Test Company Auth Flow",
            "email": f"test-auth-flow-{pytest.current_timestamp}@example.com",
            "password": "testpassword123",
        }

        # Llamar al endpoint de registro
        response = requests.post(
            f"{self.BASE_URL}/auth/register",
            json=register_data,
            headers={"Content-Type": "application/json"},
        )

        # Verificar respuesta exitosa
        assert (
            response.status_code == 200
        ), f"Expected 200, got {response.status_code}: {response.text}"

        data = response.json()

        # Verificar que contiene JWT
        assert "access_token" in data, "Response should contain access_token"
        assert "token_type" in data, "Response should contain token_type"
        assert data["token_type"] == "bearer", "Token type should be bearer"

        # Verificar que contiene API Key
        assert "api_key" in data, "Response should contain api_key"
        assert data["api_key"].startswith("ray_"), "API Key should start with 'ray_'"

        # Verificar que NO contiene initial_api_key (campo obsoleto)
        assert (
            "initial_api_key" not in data
        ), "Response should NOT contain initial_api_key field"

        return data

    def test_login_returns_only_jwt(self):
        """
        Test: El endpoint /auth/token debe devolver solo JWT (sin API Key).
        """
        # Primero registrar un usuario
        register_data = self.test_register_returns_jwt_and_api_key()

        # Extraer credenciales para login
        email = f"test-auth-flow-{pytest.current_timestamp}@example.com"
        password = "testpassword123"

        # Preparar datos de login
        login_data = {"username": email, "password": password}

        # Llamar al endpoint de login
        response = requests.post(
            f"{self.BASE_URL}/auth/token",
            data=login_data,  # Form data, no JSON
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        # Verificar respuesta exitosa
        assert (
            response.status_code == 200
        ), f"Expected 200, got {response.status_code}: {response.text}"

        data = response.json()

        # Verificar que contiene JWT
        assert "access_token" in data, "Response should contain access_token"
        assert "token_type" in data, "Response should contain token_type"
        assert data["token_type"] == "bearer", "Token type should be bearer"

        # Verificar que NO contiene API Key
        assert "api_key" not in data, "Login response should NOT contain api_key"
        assert (
            "initial_api_key" not in data
        ), "Login response should NOT contain initial_api_key"

        return data

    def test_api_keys_endpoint_generates_new_key(self):
        """
        Test: El endpoint /api-keys/ debe permitir generar nuevas API Keys para usuarios autenticados.
        """
        # Primero registrar un usuario
        register_data = self.test_register_returns_jwt_and_api_key()
        jwt_token = register_data["access_token"]
        original_api_key = register_data["api_key"]

        # Llamar al endpoint para generar nueva API Key
        response = requests.post(
            f"{self.BASE_URL}/api-keys/",
            headers={
                "Authorization": f"Bearer {jwt_token}",
                "Content-Type": "application/json",
            },
        )

        # Verificar respuesta exitosa
        assert (
            response.status_code == 201
        ), f"Expected 201, got {response.status_code}: {response.text}"

        data = response.json()

        # Verificar estructura de respuesta
        assert "api_key" in data, "Response should contain api_key"
        assert "prefix" in data, "Response should contain prefix"
        assert "created_at" in data, "Response should contain created_at"
        assert "message" in data, "Response should contain message"

        # Verificar que la nueva API Key es diferente
        new_api_key = data["api_key"]
        assert (
            new_api_key != original_api_key
        ), "New API Key should be different from original"
        assert new_api_key.startswith("ray_"), "New API Key should start with 'ray_'"

        return data

    def test_api_keys_endpoint_requires_authentication(self):
        """
        Test: El endpoint /api-keys/ debe requerir autenticación JWT.
        """
        # Intentar generar API Key sin autenticación
        response = requests.post(
            f"{self.BASE_URL}/api-keys/", headers={"Content-Type": "application/json"}
        )

        # Verificar que falla con 401
        assert (
            response.status_code == 401
        ), f"Expected 401, got {response.status_code}: {response.text}"

    def test_complete_auth_flow(self):
        """
        Test: Flujo completo de autenticación refactorizado.
        """
        # 1. Registro - debe devolver JWT + API Key
        register_data = {
            "account_name": "Complete Flow Test",
            "email": f"complete-flow-{pytest.current_timestamp}@example.com",
            "password": "testpassword123",
        }

        register_response = requests.post(
            f"{self.BASE_URL}/auth/register", json=register_data
        )

        assert register_response.status_code == 200
        register_result = register_response.json()

        jwt_token = register_result["access_token"]
        api_key = register_result["api_key"]

        # 2. Login - debe devolver solo JWT
        login_data = {
            "username": register_data["email"],
            "password": register_data["password"],
        }

        login_response = requests.post(
            f"{self.BASE_URL}/auth/token",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
        )

        assert login_response.status_code == 200
        login_result = login_response.json()

        # Verificar que login solo devuelve JWT
        assert "access_token" in login_result
        assert "api_key" not in login_result
        assert "initial_api_key" not in login_result

        # 3. Generar nueva API Key usando JWT
        new_key_response = requests.post(
            f"{self.BASE_URL}/api-keys/",
            headers={"Authorization": f"Bearer {jwt_token}"},
        )

        assert new_key_response.status_code == 201
        new_key_result = new_key_response.json()

        # Verificar que se generó nueva API Key
        assert "api_key" in new_key_result
        assert new_key_result["api_key"] != api_key

        # 4. Verificar que la API Key original ya no funciona (opcional)
        # Este test dependería de la implementación específica de invalidación

        print("✅ Flujo de autenticación refactorizado funciona correctamente")
        print(f"   - Registro: JWT + API Key ✓")
        print(f"   - Login: Solo JWT ✓")
        print(f"   - Generación de nueva API Key: Requiere JWT ✓")


if __name__ == "__main__":
    # Configurar timestamp único para tests
    import time

    pytest.current_timestamp = int(time.time())

    # Ejecutar tests
    test_instance = TestAuthFlowRefactored()

    try:
        print("🧪 Ejecutando tests del flujo de autenticación refactorizado...\n")

        print("1. Test: Registro devuelve JWT + API Key")
        test_instance.test_register_returns_jwt_and_api_key()
        print("   ✅ PASSED\n")

        print("2. Test: Login devuelve solo JWT")
        test_instance.test_login_returns_only_jwt()
        print("   ✅ PASSED\n")

        print("3. Test: Endpoint /api-keys/ genera nueva API Key")
        test_instance.test_api_keys_endpoint_generates_new_key()
        print("   ✅ PASSED\n")

        print("4. Test: Endpoint /api-keys/ requiere autenticación")
        test_instance.test_api_keys_endpoint_requires_authentication()
        print("   ✅ PASSED\n")

        print("5. Test: Flujo completo")
        test_instance.test_complete_auth_flow()
        print("   ✅ PASSED\n")

        print("🎉 Todos los tests pasaron exitosamente!")
        print("\n📋 Resumen de la refactorización:")
        print(
            "   ✅ /auth/register: Devuelve JWT + API Key (única fuente de API Key inicial)"
        )
        print(
            "   ✅ /auth/token: Devuelve solo JWT (sin confusión de responsabilidades)"
        )
        print(
            "   ✅ /api-keys/: Permite generar nuevas API Keys para usuarios autenticados"
        )
        print(
            "   ✅ Separación clara entre autenticación de usuario (JWT) y de aplicación (API Key)"
        )

    except Exception as e:
        print(f"❌ Test falló: {e}")
        raise
