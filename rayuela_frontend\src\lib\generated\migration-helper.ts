/**
 * Este archivo proporciona funciones de ayuda para migrar del sistema actual de llamadas API
 * al nuevo sistema generado por Orval.
 */
import { CustomRequestConfig } from './api-client';

/**
 * Convierte las opciones de autenticación del formato antiguo al nuevo formato de Axios
 */
export function convertAuthOptions(
  token?: string | null,
  apiKey?: string | null,
): CustomRequestConfig {
  return {
    token,
    apiKey,
  };
}

/**
 * Guía de migración para las funciones de API actuales
 *
 * Ejemplo de migración:
 *
 * Código antiguo:
 * ```typescript
 * // src/lib/api.ts
 * export const getMe = (token: string, apiKey?: string | null): Promise<UserInfo> =>
 *   fetchApi<UserInfo>('/system-users/me', { token, apiKey });
 * ```
 *
 * Código nuevo:
 * ```typescript
 * // En cualquier archivo donde necesites hacer la llamada
 * import { getSystemUsersMe } from '@/lib/generated/system-users';
 * import { convertAuthOptions } from '@/lib/generated/migration-helper';
 *
 * const getUserInfo = async (token: string, apiKey?: string | null) => {
 *   return await getSystemUsersMe({}, convertAuthOptions(token, apiKey));
 * }
 * ```
 */

/**
 * Pasos para una migración gradual:
 *
 * 1. Mantén las funciones existentes en lib/api.ts
 * 2. Refactoriza internamente cada función para usar las nuevas funciones generadas
 * 3. Los componentes seguirán usando las funciones de lib/api.ts sin cambios
 * 4. Cuando todo funcione correctamente, puedes comenzar a migrar los componentes gradualmente
 *
 * Ejemplo de migración gradual:
 *
 * ```typescript
 * // src/lib/api.ts (versión actualizada)
 * import { getSystemUsersMe } from './generated/system-users';
 * import { convertAuthOptions } from './generated/migration-helper';
 *
 * // Mantiene la misma firma para compatibilidad
 * export const getMe = (token: string, apiKey?: string | null): Promise<UserInfo> =>
 *   getSystemUsersMe({}, convertAuthOptions(token, apiKey)).then(res => res.data);
 * ```
 */
