# Configuración del Servidor

Este documento describe cómo configurar el servidor de la aplicación Rayuela.

## Variables de Entorno

La aplicación utiliza las siguientes variables de entorno para la configuración del servidor:

| Variable | Descripción | Valor por defecto | Rango válido |
|----------|-------------|-------------------|--------------|
| `API_HOST` | Dirección IP en la que escucha el servidor | `0.0.0.0` | Cualquier dirección IP válida |
| `API_PORT` | Puerto en el que escucha el servidor | `8001` | 1024-65535 |

## Configuración en Desarrollo

Para configurar el servidor en un entorno de desarrollo, puedes:

1. Crear un archivo `.env.development` en la raíz del proyecto con las variables de entorno deseadas:

```
API_HOST=0.0.0.0
API_PORT=8001
```

2. O establecer las variables de entorno directamente antes de ejecutar la aplicación:

```bash
# En Linux/Mac
export API_PORT=8002
python main.py

# En Windows (PowerShell)
$env:API_PORT=8002; python main.py

# En Windows (CMD)
set API_PORT=8002
python main.py
```

## Configuración en Producción

En un entorno de producción, se recomienda:

1. Configurar las variables de entorno a través del sistema de gestión de configuración de tu plataforma (por ejemplo, variables de entorno en Kubernetes, AWS ECS, etc.).

2. Utilizar un puerto no estándar para evitar conflictos y mejorar la seguridad.

3. Colocar la aplicación detrás de un proxy inverso como Nginx o Traefik, que puede manejar SSL/TLS, balanceo de carga y otras funcionalidades avanzadas.

## Manejo de Puertos Ocupados

La aplicación incluye un mecanismo para manejar puertos ocupados:

1. Si el puerto configurado está ocupado, la aplicación intentará encontrar un puerto disponible automáticamente.

2. La búsqueda comenzará desde el puerto configurado + 1 y continuará hasta encontrar un puerto disponible o alcanzar el número máximo de intentos (10 por defecto).

3. Si se encuentra un puerto disponible, la aplicación se iniciará en ese puerto y mostrará un mensaje informativo.

4. Si no se encuentra un puerto disponible, la aplicación mostrará un error y se detendrá.

## Configuración en Contenedores

Cuando se ejecuta la aplicación en un contenedor Docker, se recomienda:

1. Exponer el puerto configurado en el Dockerfile:

```dockerfile
EXPOSE ${API_PORT:-8001}
```

2. Mapear el puerto del contenedor al puerto del host al ejecutar el contenedor:

```bash
docker run -p 8001:8001 -e API_PORT=8001 rayuela
```

3. O utilizar docker-compose:

```yaml
services:
  api:
    image: rayuela
    environment:
      - API_PORT=8001
    ports:
      - "8001:8001"
```

## Validación de Puertos

La aplicación valida que el puerto configurado esté en un rango válido (1024-65535). Los puertos por debajo de 1024 requieren privilegios de administrador y no se recomiendan para aplicaciones en producción.

Si se configura un puerto fuera de este rango, la aplicación mostrará un error al iniciar.
