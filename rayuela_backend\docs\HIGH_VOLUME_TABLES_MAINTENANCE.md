# Mantenimiento de Tablas de Alto Volumen

Este documento describe las mejoras implementadas para el mantenimiento de tablas de alto volumen en el sistema, específicamente `interactions` y `audit_logs`.

## Problema Identificado

Las tablas `interactions` y `audit_logs` crecerán indefinidamente debido a la naturaleza de los datos que almacenan. Aunque el particionado ayuda a mejorar el rendimiento, es crucial implementar estrategias de limpieza o archivado a largo plazo para evitar problemas de rendimiento y almacenamiento.

## Solución Implementada

Se han robustecido las tareas de limpieza para hacerlas más resilientes, con mejor manejo de errores y logging más detallado. Las mejoras incluyen:

### 1. Eliminación por Lotes con Tamaño Adaptativo

- **Implementación**: Las tareas ahora eliminan registros en lotes pequeños (por defecto 10,000 registros) para evitar bloqueos prolongados en la base de datos.
- **Adaptabilidad**: Si ocurre un error durante la eliminación de un lote, el sistema reduce automáticamente el tamaño del lote y reintenta la operación.
- **Recuperación**: Después de varios lotes exitosos, el sistema intenta aumentar gradualmente el tamaño del lote para optimizar el rendimiento.

### 2. Manejo Robusto de Errores

- **Reintentos a Nivel de Lote**: Cada lote puede reintentarse varias veces antes de pasar al siguiente.
- **Reintentos a Nivel de Tarea**: Las tareas Celery ahora utilizan el mecanismo de reintentos con backoff exponencial.
- **Aislamiento de Errores**: Un error en un lote no detiene todo el proceso; el sistema continúa con los siguientes lotes.
- **Reducción Automática de Carga**: El sistema reduce el tamaño del lote o pausa brevemente si detecta demasiados errores consecutivos.

### 3. Logging Detallado

- **Métricas de Rendimiento**: Se registran métricas como duración total, registros procesados, número de lotes, reintentos, etc.
- **Progreso en Tiempo Real**: Se registra el progreso durante la ejecución (porcentaje completado, tiempo transcurrido).
- **Identificación de Tareas**: Cada entrada de log incluye el ID de la tarea Celery para facilitar el seguimiento.
- **Información de Errores**: Se registran detalles específicos sobre los errores, incluyendo el número de intento y la acción tomada.

### 4. Idempotencia

- **Verificación Previa**: Las tareas verifican primero cuántos registros deben eliminarse antes de iniciar el proceso.
- **Sin Efectos Secundarios**: Las tareas pueden ejecutarse múltiples veces sin efectos secundarios no deseados.
- **Transacciones Atómicas**: Cada lote se ejecuta en su propia transacción para garantizar la consistencia.

## Implementación Técnica

### Mejoras en `cleanup_old_audit_logs`

```python
async def cleanup_old_audit_logs(days_to_keep: int = 90, batch_size: int = 10000) -> Dict[str, Any]:
    """Elimina logs de auditoría más antiguos que el período especificado.
    
    Implementa eliminación por lotes para evitar bloqueos prolongados y es idempotente
    (puede ejecutarse múltiples veces sin efectos secundarios).
    """
    # Implementación mejorada con:
    # - Eliminación por lotes
    # - Manejo de errores por lote
    # - Reducción automática del tamaño del lote en caso de error
    # - Métricas detalladas de rendimiento
    # - Logging exhaustivo
```

### Mejoras en `cleanup_old_interactions`

```python
async def cleanup_old_interactions(
    days_to_keep: int = 180,
    account_id: Optional[int] = None,
    batch_size: int = 10000,
    max_retries: int = 3,
    retry_delay: int = 5
) -> Dict[str, Any]:
    """
    Elimina interacciones más antiguas que el período especificado.
    
    Es idempotente y tiene capacidad de recuperación ante errores.
    """
    # Implementación mejorada con:
    # - Parámetros configurables para reintentos
    # - Manejo de errores más sofisticado
    # - Pausa automática si hay demasiados errores consecutivos
    # - Ajuste dinámico del tamaño del lote
```

### Mejoras en las Tareas Celery

```python
@celery_app.task(name="cleanup_old_audit_logs", bind=True, max_retries=3, default_retry_delay=300)
def cleanup_old_audit_logs(self, days_to_keep: int = 90, batch_size: int = 10000) -> Dict[str, Any]:
    """
    Celery task to clean up old audit logs.
    
    This task is idempotent and can be safely retried.
    """
    # Implementación mejorada con:
    # - Binding a la tarea para acceder a self.request
    # - Reintentos con backoff exponencial
    # - Mejor logging con ID de tarea
```

## Beneficios

1. **Mayor Robustez**: Las tareas ahora son más resistentes a fallos y pueden recuperarse automáticamente de errores transitorios.

2. **Mejor Rendimiento**: La eliminación por lotes evita bloqueos prolongados en la base de datos y permite que otras operaciones continúen sin problemas.

3. **Observabilidad Mejorada**: El logging detallado facilita el diagnóstico de problemas y el monitoreo del rendimiento.

4. **Escalabilidad**: El sistema puede manejar eficientemente grandes volúmenes de datos, adaptándose dinámicamente a las condiciones de carga.

5. **Mantenimiento Proactivo**: Las tareas pueden programarse regularmente sin preocupación por efectos secundarios o fallos.

## Consideraciones Adicionales

- **Archivado**: Para datos históricos valiosos, considerar implementar un mecanismo de archivado antes de la eliminación.
- **Particionado**: Mantener una estrategia de particionado efectiva para complementar la limpieza.
- **Monitoreo**: Implementar alertas basadas en el tamaño de las tablas o en fallos repetidos de las tareas de limpieza.
- **Políticas de Retención**: Revisar periódicamente las políticas de retención de datos (días_to_keep) según las necesidades del negocio y requisitos legales.

## Conclusión

Las mejoras implementadas en las tareas de limpieza de tablas de alto volumen proporcionan un sistema más robusto y eficiente para el mantenimiento a largo plazo de la base de datos. Estas mejoras son cruciales para garantizar el rendimiento y la estabilidad del sistema a medida que crece el volumen de datos.
