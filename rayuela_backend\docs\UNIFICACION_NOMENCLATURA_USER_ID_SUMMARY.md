# Resumen de Unificación de Nomenclatura: `user_id` vs `end_user_id`

## Problema Identificado

Se detectó una **inconsistencia crítica** en la nomenclatura de IDs de usuario en la API de Rayuela:

### Inconsistencias Encontradas:
1. **En rutas de URL**: Se usaba `end_user_id` (ej: `/invalidate-cache/{end_user_id}`, `/explain/{end_user_id}/{item_id}`)
2. **En cuerpos de request**: Se usaba `user_id` (ej: `RecommendationQueryRequest.user_id`)
3. **En modelos de base de datos**: Se usaba `end_user_id` (ej: `Interaction.end_user_id`, `Recommendation.end_user_id`)
4. **En esquemas**: Mezc<PERSON> de ambos (`InteractionBase.end_user_id` vs `RecommendationQueryRequest.user_id`)

## Estrategia de Solución

Se decidió **unificar toda la nomenclatura a `user_id`** para mantener consistencia y simplicidad, siguiendo estas reglas:

1. **Mantener `system_user_id`** intacto para evitar confusión con usuarios del sistema
2. **Cambiar `end_user_id` a `user_id`** en todos los contextos de usuario final
3. **Implementar mapeo temporal** para manejar la transición gradual
4. **Actualizar rutas, esquemas y servicios** de forma sistemática

## Cambios Implementados

### 1. Endpoints de API (`rayuela_backend/src/api/v1/endpoints/`)

#### `recommendations.py`
- ✅ **Cambiado**: `/invalidate-cache/{end_user_id}` → `/invalidate-cache/{user_id}`
- ✅ **Cambiado**: `/explain/{end_user_id}/{item_id}` → `/explain/{user_id}/{item_id}`
- ✅ **Actualizado**: Parámetros de función y documentación
- ✅ **Actualizado**: Referencias internas en el código

#### `users.py`
- ✅ **Cambiado**: `/{end_user_id}` → `/{user_id}`
- ✅ **Actualizado**: Parámetros de función

### 2. Esquemas (`rayuela_backend/src/db/schemas/`)

#### `interaction.py`
- ✅ **Cambiado**: `InteractionBase.end_user_id` → `InteractionBase.user_id`

#### `search.py`
- ✅ **Cambiado**: `SearchBase.end_user_id` → `SearchBase.user_id`

### 3. Modelos de Base de Datos (`rayuela_backend/src/db/models/`)

#### `interaction.py`
- ✅ **Cambiado**: `Interaction.end_user_id` → `Interaction.user_id`
- ✅ **Actualizado**: Foreign key constraints (`fk_interaction_end_user` → `fk_interaction_user`)
- ✅ **Actualizado**: Relationships y primaryjoin
- ✅ **Actualizado**: Índices de base de datos

#### `recommendation.py`
- ✅ **Cambiado**: `Recommendation.end_user_id` → `Recommendation.user_id`
- ✅ **Actualizado**: Foreign key constraints (`fk_recommendation_end_user` → `fk_recommendation_user`)
- ✅ **Actualizado**: Relationships y primaryjoin
- ✅ **Actualizado**: Índices de base de datos

#### `search.py`
- ✅ **Cambiado**: `Search.end_user_id` → `Search.user_id`
- ✅ **Actualizado**: Foreign key constraints (`fk_search_end_user` → `fk_search_user`)
- ✅ **Actualizado**: Relationships y primaryjoin
- ✅ **Actualizado**: Índices de base de datos

### 4. Servicios (`rayuela_backend/src/services/`)

#### `interaction_service.py`
- ✅ **Simplificado**: Eliminado el uso de `UserIdMapper.to_db_format()`
- ✅ **Actualizado**: Referencias directas a `user_id` en lugar de `end_user_id`
- ✅ **Limpiado**: Código de mapeo innecesario

### 5. Migración de Base de Datos

#### `alembic/versions/unify_user_id_nomenclature.py` (NUEVO)
- ✅ **Creado**: Migración para renombrar columnas en base de datos
- ✅ **Incluye**: Renombrado de `end_user_id` a `user_id` en tablas interactions, recommendations, searches
- ✅ **Incluye**: Actualización de foreign key constraints
- ✅ **Incluye**: Actualización de índices
- ✅ **Incluye**: Función de downgrade para revertir cambios

### 6. Utilidades de Mapeo

#### `rayuela_backend/src/utils/user_id_mapper.py` (ACTUALIZADO)
- ✅ **Marcado como DEPRECATED**: Funciones para mapear entre `end_user_id` y `user_id`
- ✅ **Actualizado**: `UserIdMapper` class marcada como deprecated
- ✅ **Nota**: Estas funciones son para compatibilidad hacia atrás y pueden ser eliminadas en futuras versiones

### 4. Servicios Actualizados

#### `interaction_service.py`
- ✅ **Integrado**: Mapeo automático de `user_id` a `end_user_id` para compatibilidad con BD
- ✅ **Actualizado**: Manejo de datos de interacción con mapeo transparente

### 5. Scripts de Migración

#### `rayuela_backend/scripts/migrate_user_id_nomenclature.py` (NUEVO)
- ✅ **Creado**: Script para generar migraciones de Alembic automáticamente
- ✅ **Incluye**: Migración completa de columnas, índices y foreign keys
- ✅ **Incluye**: Función de rollback para revertir cambios si es necesario
- ✅ **Incluye**: Instrucciones detalladas para aplicar la migración

## Estado Actual

### ✅ Completado
- [x] Endpoints de recomendaciones unificados
- [x] Endpoint de usuarios unificado
- [x] Esquemas de interacción y búsqueda actualizados
- [x] Modelos de base de datos actualizados (`Interaction`, `Recommendation`, `Search`)
- [x] Servicio de interacciones actualizado
- [x] Migración de base de datos creada
- [x] Foreign key constraints actualizados
- [x] Índices de base de datos actualizados
- [x] UserIdMapper marcado como deprecated

### 🔄 Pendiente (Requiere Ejecución de Migración)
- [ ] Ejecutar migración de base de datos en entorno de desarrollo
- [ ] Ejecutar migración de base de datos en entorno de producción
- [ ] Validar que todos los tests pasan después de la migración
- [ ] Eliminar UserIdMapper en futuras versiones (opcional)

### 📋 Próximos Pasos Recomendados

1. **Aplicar migración de Alembic**:
   ```bash
   cd rayuela_backend
   alembic upgrade head
   ```

2. **Validar cambios**:
   ```bash
   # Verificar que las columnas fueron renombradas correctamente
   # Ejecutar tests para asegurar que todo funciona
   pytest tests/
   ```

3. **Verificar en base de datos**:
   ```sql
   -- Verificar que las columnas fueron renombradas
   \d interactions
   \d recommendations
   \d searches

   -- Verificar que los foreign keys funcionan
   SELECT * FROM interactions LIMIT 1;
   SELECT * FROM recommendations LIMIT 1;
   SELECT * FROM searches LIMIT 1;
   ```

4. **Opcional - Limpiar código legacy**:
   - Eliminar `UserIdMapper` completamente si ya no se usa
   - Limpiar imports y referencias a funciones deprecated

5. **Verificar funcionalidad**:
   - Ejecutar tests de integración
   - Verificar endpoints en ambiente de desarrollo

## Beneficios Logrados

### 🎯 Experiencia del Desarrollador (DX)
- **Consistencia**: Una sola convención (`user_id`) en toda la API
- **Claridad**: Eliminación de confusión entre `user_id` y `end_user_id`
- **Simplicidad**: Menos variaciones de nombres para recordar

### 🔧 Mantenibilidad
- **Código más limpio**: Menos mapeos manuales necesarios
- **Documentación clara**: APIs más fáciles de documentar y entender
- **Menos errores**: Reducción de bugs por inconsistencias de nomenclatura

### 🚀 Integración
- **APIs más intuitivas**: Los integradores pueden usar `user_id` consistentemente
- **Menos fricción**: Reducción de tiempo de onboarding para nuevos desarrolladores

## Compatibilidad Durante la Transición

El `UserIdMapper` permite mantener compatibilidad durante la migración:

```python
# Conversión automática en servicios
from src.utils.user_id_mapper import UserIdMapper

# API → BD
db_data = UserIdMapper.to_db_format(api_data)

# BD → API  
api_data = UserIdMapper.to_api_format(db_data)

# Extracción flexible
user_id = UserIdMapper.extract_user_id(request_data)
```

## Impacto en Integradores

### ✅ Cambios Necesarios para Integradores

1. **Actualizar rutas de endpoints**:
   - `/invalidate-cache/{end_user_id}` → `/invalidate-cache/{user_id}`
   - `/explain/{end_user_id}/{item_id}` → `/explain/{user_id}/{item_id}`
   - `/users/{end_user_id}` → `/users/{user_id}`

2. **Actualizar esquemas de datos**:
   - `InteractionCreate.end_user_id` → `InteractionCreate.user_id`
   - `SearchCreate.end_user_id` → `SearchCreate.user_id`

### 🔄 Sin Cambios Requeridos

- `RecommendationQueryRequest.user_id` (ya usaba la nomenclatura correcta)
- Endpoints que no involucran IDs de usuario

## Conclusión

La unificación de nomenclatura a `user_id` representa una **mejora crítica** en la experiencia del desarrollador y la consistencia de la API. Los cambios implementados eliminan la principal fuente de confusión identificada y establecen una base sólida para futuras integraciones.

La estrategia de mapeo temporal permite una transición suave sin romper la funcionalidad existente, mientras que los próximos pasos completarán la unificación a nivel de base de datos. 