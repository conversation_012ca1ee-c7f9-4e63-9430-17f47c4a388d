from sqlalchemy import Column, Integer, String, Index, PrimaryKeyConstraint, Enum as SQLAEnum, Identity
from sqlalchemy.orm import relationship
from src.db.base import Base
from src.db.enums import PermissionType
from .mixins import TenantMixin, ACCOUNT_RANGE


class Permission(Base, TenantMixin):
    __tablename__ = "permissions"

    # La otra parte de la PK compuesta
    id = Column(Integer, Identity(), nullable=False)
    name = Column(SQLAEnum(PermissionType), nullable=False)

    # Definición explícita de la PK compuesta e índices
    __table_args__ = (
        PrimaryKeyConstraint("account_id", "id"),
        Index("idx_permission_name", "name"),
        Index("idx_permission_account", "account_id"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    ) + getattr(TenantMixin, "__table_args__", ())

    # Relaciones
    roles = relationship(
        "Role", secondary="role_permissions", back_populates="permissions"
    )
