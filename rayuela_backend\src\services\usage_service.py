"""
Service for handling usage history business logic.
"""
from datetime import datetime, timedelta
from typing import Dict, List, Any
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.repositories.usage_repository import UsageRepository
from src.utils.base_logger import log_info, log_error

class UsageService:
    def __init__(self, db: AsyncSession):
        self.db = db
        self.usage_repository = UsageRepository(db)
        
    async def get_usage_history(
        self,
        account_id: int,
        start_date: datetime = None,
        end_date: datetime = None
    ) -> List[Dict[str, Any]]:
        """
        Get historical usage data for an account.
        
        Args:
            account_id: The account ID to query
            start_date: Optional start date for filtering data
            end_date: Optional end date for filtering data
            
        Returns:
            List of daily usage data points
        """
        try:
            # Default to last 30 days if no dates provided
            if not start_date:
                start_date = datetime.now() - timedelta(days=30)
            if not end_date:
                end_date = datetime.now()
                
            # Get API calls data from repository
            api_calls_data = await self.usage_repository.get_daily_api_calls(
                account_id=account_id,
                start_date=start_date,
                end_date=end_date
            )
            
            # Generate a complete date range
            date_range = []
            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')
                date_range.append(date_str)
                current_date += timedelta(days=1)
            
            # Create the response with all dates
            result = []
            for date_str in date_range:
                result.append({
                    "date": date_str,
                    "api_calls": api_calls_data.get(date_str, 0),
                    # For storage, we'll need to implement a way to track historical storage
                    # For now, we'll return a placeholder value
                    "storage": 0  # This will be updated in a future implementation
                })
            
            log_info(f"Retrieved usage history for account {account_id}")
            return result
            
        except Exception as e:
            log_error(f"Error retrieving usage history: {str(e)}")
            raise 