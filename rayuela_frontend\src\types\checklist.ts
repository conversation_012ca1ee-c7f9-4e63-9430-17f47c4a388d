import { ReactNode } from 'react';

/**
 * Interface for checklist items used in the GettingStartedChecklist component
 */
export interface ChecklistItem {
  id: string;
  label: string;
  description: string;
  icon: ReactNode;
  link: string;
  docsLink?: string;  // Link to specific documentation section
  docsTitle?: string; // Title for the documentation link
  isExternal?: boolean;
  completed: boolean;
  autoDetect?: boolean; // Indicates if this item is automatically detected
  tooltipContent?: string; // Additional content for the tooltip
  category?: 'setup' | 'data' | 'model'; // Category for grouping checklist items
}
