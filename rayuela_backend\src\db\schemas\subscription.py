from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
from src.db.enums import SubscriptionPlan


class SubscriptionBase(BaseModel):
    plan_type: SubscriptionPlan
    api_calls_limit: int = Field(..., gt=0)
    storage_limit: int = Field(..., gt=0)

    # Campos de Stripe eliminados

    # Campos de Mercado Pago
    mercadopago_subscription_id: Optional[str] = None
    mercadopago_price_id: Optional[str] = None
    payment_gateway: Optional[str] = "mercadopago"  # "stripe" o "mercadopago"

    is_active: bool = True
    expires_at: Optional[datetime] = None

    # Campos de seguimiento de uso
    monthly_api_calls_used: int = 0
    storage_used: int = 0
    last_reset_date: Optional[datetime] = None
    available_models: Optional[List[str]] = None

    # Campo para seguimiento de frecuencia de entrenamiento
    last_successful_training_at: Optional[datetime] = None


class SubscriptionCreate(SubscriptionBase):
    pass


class SubscriptionUpdate(BaseModel):
    plan_type: Optional[SubscriptionPlan] = None
    api_calls_limit: Optional[int] = Field(None, gt=0)
    storage_limit: Optional[int] = Field(None, gt=0)

    # Campos de Stripe eliminados

    # Campos de Mercado Pago
    mercadopago_subscription_id: Optional[str] = None
    mercadopago_price_id: Optional[str] = None
    payment_gateway: Optional[str] = None

    is_active: Optional[bool] = None
    expires_at: Optional[datetime] = None

    # Campos de seguimiento de uso
    monthly_api_calls_used: Optional[int] = None
    storage_used: Optional[int] = None
    last_reset_date: Optional[datetime] = None
    available_models: Optional[List[str]] = None

    # Campo para seguimiento de frecuencia de entrenamiento
    last_successful_training_at: Optional[datetime] = None


class Subscription(SubscriptionBase):
    account_id: int
    created_at: datetime
    updated_at: datetime

    class ConfigDict:
        from_attributes = True
