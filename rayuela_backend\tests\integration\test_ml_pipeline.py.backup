import pytest
import pandas as pd
import numpy as np
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime
from src.ml_pipeline.training_pipeline import Training<PERSON><PERSON>eline
from src.ml_pipeline.serving_engine import ServingEngine
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.ml_pipeline.evaluation import RecommendationEvaluator
from src.core.exceptions import Model<PERSON>otFoundError, TrainingError
from src.db.models import ModelMetadata, TrainingJob

class TestMLPipeline:
    """Tests de integración para el pipeline de ML."""

    @pytest.fixture
    def mock_trainer(self):
        """Mock del trainer para pruebas."""
        mock = MagicMock()
        mock.train = AsyncMock(return_value={"accuracy": 0.95})
        mock.evaluate = AsyncMock(return_value={"precision": 0.9, "recall": 0.85})
        mock.predict = AsyncMock(return_value=[1, 2, 3])
        return mock

    @pytest.fixture
    def mock_storage(self):
        """Mock del storage para pruebas."""
        mock = MagicMock()
        mock.upload = AsyncMock()
        mock.download = AsyncMock(return_value=b"model_data")
        mock.delete = AsyncMock()
        return mock

    @pytest.fixture
    def artifact_manager(self, mock_storage):
        """Crear instancia del manejador de artefactos."""
        return ModelArtifactManager(mock_storage)

    @pytest.fixture
    def metrics_tracker(self, db_session):
        """Crear instancia del tracker de métricas."""
        return MetricsTracker(db_session)

    @pytest.fixture
    def evaluator(self):
        """Crear instancia del evaluador."""
        return RecommendationEvaluator()

    @pytest.fixture
    def training_pipeline(self, db_session, mock_trainer, artifact_manager, metrics_tracker, evaluator):
        """Crear instancia del pipeline de entrenamiento."""
        return TrainingPipeline(
            db_session,
            artifact_manager=artifact_manager,
            metrics_tracker=metrics_tracker,
            evaluator=evaluator
        )

    @pytest.fixture
    def serving_engine(self, artifact_manager):
        """Crear instancia del motor de servicio."""
        return ServingEngine(artifact_manager)

    async def test_training_flow(
        self,
        training_pipeline,
        db_session,
        test_accounts,
        mock_trainer,
        metrics_tracker
    ):
        """Test para el flujo completo de entrenamiento."""
        account = test_accounts[0]

        # Mockear el método _evaluate_models para verificar que se usa el LTR con relevancia real
        with patch.object(training_pipeline, '_evaluate_models') as mock_evaluate:
            # Configurar el mock para devolver recomendaciones y métricas
            mock_evaluate.return_value = (
                [{"item_id": 1, "score": 0.9, "target_relevance": 1.0}],  # Recomendaciones con target_relevance
                {"precision": 0.8, "recall": 0.7}  # Métricas
            )

            # Iniciar entrenamiento
            result = await training_pipeline.train(
                db=db_session,
                account_id=account.id,
                data={"epochs": 10}
            )

            # Verificar que el job se creó y fue exitoso
            assert result["success"] is True
            assert "model_id" in result

            # Verificar que el modelo tiene métricas
            model_metrics = await metrics_tracker.get_metrics(
                db=db_session,
                account_id=account.id,
                model_id=result["model_id"]
            )

            assert "metrics" in model_metrics

            # Verificar que se llamó a _evaluate_models
            mock_evaluate.assert_called_once()

    async def test_ltr_uses_real_relevance(
        self,
        training_pipeline,
        db_session,
        test_accounts
    ):
        """Test que verifica que el modelo LTR usa relevancia real para entrenamiento."""
        account = test_accounts[0]

        # Crear datos de prueba
        test_recommendations = [
            {
                "user_id": 1,
                "item_id": 101,
                "collab_score": 0.8,
                "content_score": 0.6,
                "score": 0.8,
                "rank": 1,
                "model_type": "collaborative",
                "is_hit": True,
                "target_relevance": 1.0  # Usuario interactuó con este ítem
            },
            {
                "user_id": 1,
                "item_id": 102,
                "collab_score": 0.7,
                "content_score": 0.5,
                "score": 0.7,
                "rank": 2,
                "model_type": "collaborative",
                "is_hit": False,
                "target_relevance": 0.0  # Usuario no interactuó con este ítem
            }
        ]

        # Mockear _generate_recommendations para devolver nuestros datos de prueba
        with patch.object(training_pipeline, '_generate_recommendations', AsyncMock(return_value=test_recommendations)):
            # Mockear LearningToRankModel para verificar que se llama con los target_scores correctos
            with patch('src.ml_pipeline.learning_to_rank.LearningToRankModel') as mock_ltr_class:
                # Configurar el mock del modelo LTR
                mock_ltr = MagicMock()
                mock_ltr.train = MagicMock(return_value={"ndcg": 0.85})
                mock_ltr.predict = MagicMock(return_value=test_recommendations)
                mock_ltr_class.return_value = mock_ltr

                # Crear datos de prueba para _evaluate_models
                collab_artifacts = {"account_id": account.id, "model": MagicMock()}
                content_artifacts = {"model": MagicMock()}
                test_interactions = pd.DataFrame({
                    "user_id": [1, 1],
                    "item_id": [101, 103],
                    "value": [1.0, 1.0]
                })
                interactions_df = pd.DataFrame({
                    "user_id": [1, 1, 2],
                    "item_id": [101, 102, 103],
                    "value": [1.0, 1.0, 1.0]
                })
                products_df = pd.DataFrame({
                    "item_id": [101, 102, 103],
                    "category": ["A", "B", "A"]
                })

                # Llamar al método _evaluate_models
                await training_pipeline._evaluate_models(
                    collab_artifacts=collab_artifacts,
                    content_artifacts=content_artifacts,
                    test_interactions=test_interactions,
                    interactions_df=interactions_df,
                    products_df=products_df
                )

                # Verificar que se creó el modelo LTR con el account_id correcto
                mock_ltr_class.assert_called_once_with(account_id=account.id, model_type="gbdt")

                # Verificar que se llamó a train con los target_scores correctos
                mock_ltr.train.assert_called_once()
                args, kwargs = mock_ltr.train.call_args

                # Verificar que se pasaron los target_scores
                assert "target_scores" in kwargs
                assert kwargs["target_scores"] == [1.0, 0.0]  # Valores de target_relevance

                # Verificar que se aplicó el modelo a las recomendaciones
                mock_ltr.predict.assert_called_once()

    async def test_model_artifact_management(
        self,
        artifact_manager,
        db_session,
        test_accounts,
        mock_storage
    ):
        """Test para el manejo de artefactos del modelo."""
        account = test_accounts[0]

        # Crear modelo
        model = ModelMetadata(
            account_id=account.id,
            artifact_version="1.0",
            metrics={"accuracy": 0.95}
        )
        db_session.add(model)
        await db_session.commit()

        # Guardar artefactos
        artifacts = {"model": "test_model_data", "metadata": {"accuracy": 0.95}}
        artifact_path = await artifact_manager.save_artifacts(
            account_id=account.id,
            artifacts=artifacts,
            version="1.0"
        )

        # Verificar que se subió al storage
        mock_storage.upload.assert_called_once()

        # Cargar artefactos
        loaded_artifacts = await artifact_manager.get_artifacts(
            db=db_session,
            account_id=account.id,
            model_type="collaborative"
        )

        # Asegurarse de que se solicitó la descarga
        mock_storage.download.assert_called_once()

        # Eliminar artefactos
        await artifact_manager.delete_artifacts(
            account_id=account.id,
            version="1.0"
        )
        mock_storage.delete.assert_called_once()

    async def test_recommendation_generation(
        self,
        serving_engine,
        db_session,
        test_accounts,
        test_end_users,
        mock_trainer
    ):
        """Test para la generación de recomendaciones."""
        account = test_accounts[0]
        user = test_end_users[account.id][0]

        # Mock para el método get_candidate_recommendations
        original_method = serving_engine.get_candidate_recommendations
        serving_engine.get_candidate_recommendations = AsyncMock(return_value=[
            {"item_id": 1, "score": 0.9},
            {"item_id": 2, "score": 0.8},
            {"item_id": 3, "score": 0.7}
        ])

        try:
            # Generar recomendaciones
            recommendations = await serving_engine.get_candidate_recommendations(
                db=db_session,
                account_id=account.id,
                user_id=user.id,
                n_recommendations=5
            )

            # Verificar que se generaron recomendaciones
            assert len(recommendations) == 3
            assert recommendations[0]["item_id"] == 1
            assert recommendations[0]["score"] == 0.9
        finally:
            # Restaurar el método original
            serving_engine.get_candidate_recommendations = original_method

    async def test_metrics_tracking(
        self,
        metrics_tracker,
        db_session,
        test_accounts
    ):
        """Test para el tracking de métricas."""
        account = test_accounts[0]

        # Crear modelo
        model = ModelMetadata(
            account_id=account.id,
            artifact_version="1.0"
        )
        db_session.add(model)
        await db_session.commit()

        # Registrar métricas
        await metrics_tracker.register_metrics(
            db=db_session,
            model_id=model.id,
            account_id=account.id,
            metrics={
                "accuracy": 0.95,
                "precision": 0.9,
                "recall": 0.85
            },
            timestamp=datetime.now()
        )

        # Obtener métricas
        metrics = await metrics_tracker.get_metrics(
            db=db_session,
            account_id=account.id,
            model_id=model.id
        )

        assert metrics["metrics"]["accuracy"] == 0.95
        assert metrics["metrics"]["precision"] == 0.9
        assert metrics["metrics"]["recall"] == 0.85

    async def test_model_versioning(
        self,
        serving_engine,
        db_session,
        test_accounts
    ):
        """Test para el versionado de modelos."""
        account = test_accounts[0]

        # Crear modelos con diferentes versiones
        model_v1 = ModelMetadata(
            account_id=account.id,
            artifact_version="1.0",
            is_active=True
        )
        model_v2 = ModelMetadata(
            account_id=account.id,
            artifact_version="2.0",
            is_active=False
        )
        db_session.add_all([model_v1, model_v2])
        await db_session.commit()

        # Activar nueva versión
        await serving_engine.activate_model(
            account_id=account.id,
            model_id=model_v2.id
        )

        # Verificar que se actualizó el estado
        saved_v1 = await db_session.get(ModelMetadata, model_v1.id)
        saved_v2 = await db_session.get(ModelMetadata, model_v2.id)
        assert not saved_v1.is_active
        assert saved_v2.is_active

    async def test_training_error_handling(
        self,
        training_pipeline,
        db_session,
        test_accounts,
        mock_trainer
    ):
        """Test para el manejo de errores en entrenamiento."""
        account = test_accounts[0]

        # Configurar mock para lanzar error
        mock_trainer.train.side_effect = Exception("Training failed")

        # Iniciar entrenamiento
        result = await training_pipeline.train(
            db=db_session,
            account_id=account.id,
            data={"epochs": 10}
        )

        # Verificar que el job se marcó como fallido
        assert result["success"] is False
        assert "Training failed" in result["error"]

    async def test_tenant_isolation(
        self,
        serving_engine,
        db_session,
        test_accounts
    ):
        """Test para verificar aislamiento entre tenants en el pipeline."""
        account_a = test_accounts[0]
        account_b = test_accounts[1]

        # Crear modelos para cada tenant
        model_a = ModelMetadata(
            account_id=account_a.id,
            artifact_version="1.0",
            is_active=True
        )
        model_b = ModelMetadata(
            account_id=account_b.id,
            artifact_version="1.0",
            is_active=True
        )
        db_session.add_all([model_a, model_b])
        await db_session.commit()

        # Intentar acceder a modelo de otro tenant
        with pytest.raises(ModelNotFoundError):
            await serving_engine.get_candidate_recommendations(
                db=db_session,
                account_id=account_b.id,
                user_id=model_b.id,
                n_recommendations=5
            )

        # Verificar que cada tenant solo ve sus propios modelos
        models_a = await serving_engine.list_models(account_id=account_a.id)
        assert len(models_a) == 1
        assert models_a[0].id == model_a.id

        models_b = await serving_engine.list_models(account_id=account_b.id)
        assert len(models_b) == 1
        assert models_b[0].id == model_b.id