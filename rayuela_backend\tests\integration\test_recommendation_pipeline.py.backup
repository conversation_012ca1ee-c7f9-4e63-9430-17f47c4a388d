"""Integration tests for the recommendation pipeline."""
import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import FastAPI
from fastapi.testclient import TestClient
from httpx import AsyncClient
import json
from datetime import datetime, timedelta

from main import app
from src.db.models import TrainingJ<PERSON>, Account, SystemUser, Product, EndUser, Interaction
from src.db.enums import TrainingJobStatus
from src.core.deps import get_db, get_current_account, get_limit_service
from src.services import LimitService
from src.ml_pipeline.serving_engine import ServingEngine
from src.ml_pipeline.post_processing_service import PostProcessingService
from src.ml_pipeline.fallback_handler import Fallback<PERSON>andler
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager

from tests.integration.utils.mock_celery import mock_celery_tasks, execute_mock_tasks
from tests.integration.utils.mock_external_services import mock_gcs
from tests.integration.utils.test_db import (
    db_session,
    override_get_db,
    test_account,
    test_admin_user,
    test_products,
    test_end_users,
    test_interactions,
    test_training_job
)


@pytest.fixture(scope="function")
def client(override_get_db):
    """Create a test client with overridden dependencies."""
    app.dependency_overrides[get_db] = override_get_db
    with TestClient(app) as client:
        yield client
    app.dependency_overrides = {}


@pytest_asyncio.fixture(scope="function")
async def async_client(override_get_db, test_account, test_admin_user):
    """Create an async test client with overridden dependencies."""
    # Override dependencies
    async def override_get_current_account():
        return {"account_id": test_account.id, "name": test_account.name}
    
    async def override_get_limit_service(db=None):
        if db is None:
            db = override_get_db()
        return LimitService(db=db, account_id=test_account.id)
    
    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_current_account] = override_get_current_account
    app.dependency_overrides[get_limit_service] = override_get_limit_service
    
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
    
    app.dependency_overrides = {}


@pytest_asyncio.fixture(scope="function")
async def completed_training_job(db_session, test_account, mock_gcs):
    """Create a completed training job with model artifacts."""
    # Create a completed training job
    training_job = TrainingJob(
        account_id=test_account.id,
        status=TrainingJobStatus.COMPLETED,
        started_at=datetime.now() - timedelta(hours=1),
        completed_at=datetime.now(),
        model_type="collaborative",
        parameters={
            "factors": 10,
            "iterations": 20,
            "regularization": 0.1
        },
        metrics={
            "rmse": 0.85,
            "mae": 0.65,
            "precision": 0.75,
            "recall": 0.80
        }
    )
    
    db_session.add(training_job)
    await db_session.commit()
    await db_session.refresh(training_job)
    
    # Create mock model artifacts in GCS
    bucket = mock_gcs.bucket("default-bucket")
    
    # Create model file
    model_blob = bucket.blob(f"models/{test_account.id}/collaborative/model.pkl")
    model_blob.upload_from_string("mock model data", content_type="application/octet-stream")
    
    # Create metadata file
    metadata_blob = bucket.blob(f"models/{test_account.id}/collaborative/metadata.json")
    metadata_json = json.dumps({
        "model_type": "collaborative",
        "training_job_id": training_job.id,
        "created_at": datetime.now().isoformat(),
        "parameters": training_job.parameters,
        "metrics": training_job.metrics
    })
    metadata_blob.upload_from_string(metadata_json, content_type="application/json")
    
    return training_job


@pytest.mark.asyncio
async def test_get_recommendations_endpoint(
    async_client,
    db_session,
    test_account,
    test_products,
    test_end_users,
    test_interactions,
    completed_training_job,
    mock_gcs
):
    """Test the get_recommendations endpoint."""
    # Mock the recommendation service to return predictable recommendations
    with patch("src.api.v1.endpoints.recommendations.get_recommendation_service") as mock_get_service:
        mock_service = AsyncMock()
        mock_service.get_recommendations.return_value = [
            {"product_id": test_products[0].id, "score": 0.95},
            {"product_id": test_products[1].id, "score": 0.85},
            {"product_id": test_products[2].id, "score": 0.75},
            {"product_id": test_products[3].id, "score": 0.65},
            {"product_id": test_products[4].id, "score": 0.55}
        ]
        mock_get_service.return_value = mock_service
        
        # Call the endpoint
        response = await async_client.get(
            f"/api/v1/recommendations/user/{test_end_users[0].external_id}?limit=5"
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 5
        assert data[0]["product_id"] == test_products[0].id
        assert data[0]["score"] == 0.95
        assert "product" in data[0]
        assert data[0]["product"]["name"] == test_products[0].name
        
        # Verify the service was called with correct parameters
        mock_service.get_recommendations.assert_called_once_with(
            user_external_id=test_end_users[0].external_id,
            limit=5,
            exclude_viewed=False,
            category=None,
            min_score=None
        )


@pytest.mark.asyncio
async def test_get_recommendations_with_filters(
    async_client,
    db_session,
    test_account,
    test_products,
    test_end_users,
    test_interactions,
    completed_training_job,
    mock_gcs
):
    """Test the get_recommendations endpoint with filters."""
    # Mock the recommendation service to return predictable recommendations
    with patch("src.api.v1.endpoints.recommendations.get_recommendation_service") as mock_get_service:
        mock_service = AsyncMock()
        mock_service.get_recommendations.return_value = [
            {"product_id": test_products[0].id, "score": 0.95},
            {"product_id": test_products[3].id, "score": 0.65}
        ]
        mock_get_service.return_value = mock_service
        
        # Call the endpoint with filters
        response = await async_client.get(
            f"/api/v1/recommendations/user/{test_end_users[0].external_id}?limit=5&exclude_viewed=true&category=Category%201&min_score=0.6"
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["product_id"] == test_products[0].id
        assert data[0]["score"] == 0.95
        assert data[1]["product_id"] == test_products[3].id
        assert data[1]["score"] == 0.65
        
        # Verify the service was called with correct parameters
        mock_service.get_recommendations.assert_called_once_with(
            user_external_id=test_end_users[0].external_id,
            limit=5,
            exclude_viewed=True,
            category="Category 1",
            min_score=0.6
        )


@pytest.mark.asyncio
async def test_get_recommendations_user_not_found(
    async_client,
    db_session,
    test_account,
    test_products,
    completed_training_job,
    mock_gcs
):
    """Test the get_recommendations endpoint with a non-existent user."""
    # Call the endpoint with a non-existent user
    response = await async_client.get(
        "/api/v1/recommendations/user/non_existent_user?limit=5"
    )
    
    # Check response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "user not found" in data["detail"].lower()


@pytest.mark.asyncio
async def test_get_recommendations_no_model(
    async_client,
    db_session,
    test_account,
    test_products,
    test_end_users,
    mock_gcs
):
    """Test the get_recommendations endpoint when no model is available."""
    # Call the endpoint
    response = await async_client.get(
        f"/api/v1/recommendations/user/{test_end_users[0].external_id}?limit=5"
    )
    
    # Check response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "no trained model" in data["detail"].lower()


@pytest.mark.asyncio
async def test_get_similar_products_endpoint(
    async_client,
    db_session,
    test_account,
    test_products,
    completed_training_job,
    mock_gcs
):
    """Test the get_similar_products endpoint."""
    # Mock the recommendation service to return predictable similar products
    with patch("src.api.v1.endpoints.recommendations.get_recommendation_service") as mock_get_service:
        mock_service = AsyncMock()
        mock_service.get_similar_products.return_value = [
            {"product_id": test_products[1].id, "score": 0.95},
            {"product_id": test_products[2].id, "score": 0.85},
            {"product_id": test_products[3].id, "score": 0.75},
            {"product_id": test_products[4].id, "score": 0.65}
        ]
        mock_get_service.return_value = mock_service
        
        # Call the endpoint
        response = await async_client.get(
            f"/api/v1/recommendations/similar/{test_products[0].external_id}?limit=4"
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 4
        assert data[0]["product_id"] == test_products[1].id
        assert data[0]["score"] == 0.95
        assert "product" in data[0]
        assert data[0]["product"]["name"] == test_products[1].name
        
        # Verify the service was called with correct parameters
        mock_service.get_similar_products.assert_called_once_with(
            product_external_id=test_products[0].external_id,
            limit=4,
            category=None,
            min_score=None
        )


@pytest.mark.asyncio
async def test_get_similar_products_with_filters(
    async_client,
    db_session,
    test_account,
    test_products,
    completed_training_job,
    mock_gcs
):
    """Test the get_similar_products endpoint with filters."""
    # Mock the recommendation service to return predictable similar products
    with patch("src.api.v1.endpoints.recommendations.get_recommendation_service") as mock_get_service:
        mock_service = AsyncMock()
        mock_service.get_similar_products.return_value = [
            {"product_id": test_products[1].id, "score": 0.95},
            {"product_id": test_products[4].id, "score": 0.65}
        ]
        mock_get_service.return_value = mock_service
        
        # Call the endpoint with filters
        response = await async_client.get(
            f"/api/v1/recommendations/similar/{test_products[0].external_id}?limit=4&category=Category%202&min_score=0.6"
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["product_id"] == test_products[1].id
        assert data[0]["score"] == 0.95
        assert data[1]["product_id"] == test_products[4].id
        assert data[1]["score"] == 0.65
        
        # Verify the service was called with correct parameters
        mock_service.get_similar_products.assert_called_once_with(
            product_external_id=test_products[0].external_id,
            limit=4,
            category="Category 2",
            min_score=0.6
        )


@pytest.mark.asyncio
async def test_get_similar_products_product_not_found(
    async_client,
    db_session,
    test_account,
    completed_training_job,
    mock_gcs
):
    """Test the get_similar_products endpoint with a non-existent product."""
    # Call the endpoint with a non-existent product
    response = await async_client.get(
        "/api/v1/recommendations/similar/non_existent_product?limit=4"
    )
    
    # Check response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "product not found" in data["detail"].lower()


@pytest.mark.asyncio
async def test_get_similar_products_no_model(
    async_client,
    db_session,
    test_account,
    test_products,
    mock_gcs
):
    """Test the get_similar_products endpoint when no model is available."""
    # Call the endpoint
    response = await async_client.get(
        f"/api/v1/recommendations/similar/{test_products[0].external_id}?limit=4"
    )
    
    # Check response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "no trained model" in data["detail"].lower()


@pytest.mark.asyncio
async def test_end_to_end_recommendation_flow(
    async_client,
    db_session,
    test_account,
    test_products,
    test_end_users,
    test_interactions,
    mock_celery_tasks,
    mock_gcs
):
    """Test the end-to-end recommendation flow from training to recommendations."""
    # 1. Train a model
    training_data = {
        "model_type": "collaborative",
        "parameters": {
            "factors": 10,
            "iterations": 5,
            "regularization": 0.1
        }
    }
    
    # Call the training endpoint
    response = await async_client.post(
        "/api/v1/pipeline/train",
        json=training_data
    )
    
    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"
    assert "job_id" in data
    
    # Execute the mock Celery task
    job_id = data["job_id"]
    await execute_mock_tasks(mock_celery_tasks, db_session)
    
    # 2. Check the training job status
    response = await async_client.get(f"/api/v1/pipeline/jobs/{job_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"
    
    # 3. Get recommendations for a user
    # Mock the recommendation service to return predictable recommendations
    with patch("src.api.v1.endpoints.recommendations.get_recommendation_service") as mock_get_service:
        mock_service = AsyncMock()
        mock_service.get_recommendations.return_value = [
            {"product_id": test_products[0].id, "score": 0.95},
            {"product_id": test_products[1].id, "score": 0.85},
            {"product_id": test_products[2].id, "score": 0.75}
        ]
        mock_get_service.return_value = mock_service
        
        # Call the recommendations endpoint
        response = await async_client.get(
            f"/api/v1/recommendations/user/{test_end_users[0].external_id}?limit=3"
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 3
        assert data[0]["product_id"] == test_products[0].id
        assert data[0]["score"] == 0.95
    
    # 4. Get similar products
    # Mock the recommendation service to return predictable similar products
    with patch("src.api.v1.endpoints.recommendations.get_recommendation_service") as mock_get_service:
        mock_service = AsyncMock()
        mock_service.get_similar_products.return_value = [
            {"product_id": test_products[1].id, "score": 0.95},
            {"product_id": test_products[2].id, "score": 0.85}
        ]
        mock_get_service.return_value = mock_service
        
        # Call the similar products endpoint
        response = await async_client.get(
            f"/api/v1/recommendations/similar/{test_products[0].external_id}?limit=2"
        )
        
        # Check response
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]["product_id"] == test_products[1].id
        assert data[0]["score"] == 0.95
