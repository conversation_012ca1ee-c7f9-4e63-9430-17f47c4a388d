"""Fix composite foreign keys for multi-tenant tables

Revision ID: fix_composite_foreign_keys
Revises: add_mercadopago_fields
Create Date: 2025-05-01 10:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'fix_composite_foreign_keys'
down_revision: Union[str, None] = 'add_mercadopago_fields'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to fix composite foreign keys for multi-tenant tables."""

    # Fix Product.created_by foreign key
    # First check if the column exists
    from sqlalchemy import inspect
    from sqlalchemy.engine import reflection

    # Get inspector
    conn = op.get_bind()
    insp = inspect(conn)

    # Check if created_by column exists in products table
    columns = insp.get_columns('products', schema='public')
    has_created_by = any(col['name'] == 'created_by' for col in columns)

    if has_created_by:
        # Check if the constraint exists before trying to drop it
        constraints = insp.get_foreign_keys('products', schema='public')
        has_fk = any(constraint['name'] == 'products_created_by_fkey' for constraint in constraints)

        if has_fk:
            # Drop the existing simple FK if it exists
            op.drop_constraint('products_created_by_fkey', 'products', type_='foreignkey', schema='public')

        # Add the composite FK
        op.create_foreign_key(
            'fk_product_created_by',
            'products',
            'system_users',
            ['account_id', 'created_by'],
            ['account_id', 'id'],
            ondelete='SET NULL'
        )

    # Fix Recommendation foreign keys
    # Check if the recommendations table exists
    tables = insp.get_table_names(schema='public')
    if 'recommendations' in tables and 'end_users' in tables and 'products' in tables:
        # Check if end_user_id and product_id columns exist in recommendations table
        recommendations_columns = insp.get_columns('recommendations', schema='public')
        has_end_user_id = any(col['name'] == 'end_user_id' for col in recommendations_columns)
        has_product_id = any(col['name'] == 'product_id' for col in recommendations_columns)

        if has_end_user_id and has_product_id:
            # Check if the constraints exist before trying to drop them
            constraints = insp.get_foreign_keys('recommendations', schema='public')
            constraint_names = [constraint['name'] for constraint in constraints]

            if 'recommendations_end_user_id_fkey' in constraint_names:
                op.drop_constraint('recommendations_end_user_id_fkey', 'recommendations', type_='foreignkey', schema='public')

            if 'recommendations_product_id_fkey' in constraint_names:
                op.drop_constraint('recommendations_product_id_fkey', 'recommendations', type_='foreignkey', schema='public')

            # Check if id column exists in end_users table
            end_users_columns = insp.get_columns('end_users', schema='public')
            has_end_users_id = any(col['name'] == 'id' for col in end_users_columns)

            # Check if id column exists in products table
            products_columns = insp.get_columns('products', schema='public')
            has_products_id = any(col['name'] == 'id' for col in products_columns)

            # Add the composite FKs
            if has_end_users_id:
                op.create_foreign_key(
                    'fk_recommendation_end_user',
                    'recommendations',
                    'end_users',
                    ['account_id', 'end_user_id'],
                    ['account_id', 'id'],
                    ondelete='CASCADE'
                )

            if has_products_id:
                op.create_foreign_key(
                    'fk_recommendation_product',
                    'recommendations',
                    'products',
                    ['account_id', 'product_id'],
                    ['account_id', 'id'],
                    ondelete='CASCADE'
                )

    # Fix TrainingMetrics foreign key
    # Check if the training_metrics table exists
    if 'training_metrics' in tables:
        # Check if the constraint exists before trying to drop it
        constraints = insp.get_foreign_keys('training_metrics', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'training_metrics_model_id_fkey' in constraint_names:
            op.drop_constraint('training_metrics_model_id_fkey', 'training_metrics', type_='foreignkey', schema='public')

        # Add the composite FK
        op.create_foreign_key(
            'fk_training_metrics_model',
            'training_metrics',
            'artifact_metadata',
            ['account_id', 'model_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

        # Add missing indexes
        # Check if indexes already exist
        indexes = insp.get_indexes('training_metrics', schema='public')
        index_names = [index['name'] for index in indexes]

        if 'idx_training_metrics_model' not in index_names:
            op.create_index('idx_training_metrics_model', 'training_metrics', ['account_id', 'model_id'])

        if 'idx_training_metrics_timestamp' not in index_names:
            op.create_index('idx_training_metrics_timestamp', 'training_metrics', ['timestamp'])

    # Rename existing FKs to be more consistent
    # Interaction FKs
    if 'interactions' in tables and 'end_users' in tables and 'products' in tables:
        # Check if end_user_id column exists in interactions table
        interactions_columns = insp.get_columns('interactions', schema='public')
        has_end_user_id = any(col['name'] == 'end_user_id' for col in interactions_columns)
        has_product_id = any(col['name'] == 'product_id' for col in interactions_columns)

        if has_end_user_id and has_product_id:
            constraints = insp.get_foreign_keys('interactions', schema='public')
            constraint_names = [constraint['name'] for constraint in constraints]

            if 'interactions_account_id_end_user_id_fkey' in constraint_names:
                op.drop_constraint('interactions_account_id_end_user_id_fkey', 'interactions', type_='foreignkey', schema='public')

            if 'interactions_account_id_product_id_fkey' in constraint_names:
                op.drop_constraint('interactions_account_id_product_id_fkey', 'interactions', type_='foreignkey', schema='public')

            # Check if id column exists in end_users table
            end_users_columns = insp.get_columns('end_users', schema='public')
            has_end_users_id = any(col['name'] == 'id' for col in end_users_columns)

            # Check if id column exists in products table
            products_columns = insp.get_columns('products', schema='public')
            has_products_id = any(col['name'] == 'id' for col in products_columns)

            if has_end_users_id:
                op.create_foreign_key(
                    'fk_interaction_end_user',
                    'interactions',
                    'end_users',
                    ['account_id', 'end_user_id'],
                    ['account_id', 'id'],
                    ondelete='CASCADE'
                )

            if has_products_id:
                op.create_foreign_key(
                    'fk_interaction_product',
                    'interactions',
                    'products',
                    ['account_id', 'product_id'],
                    ['account_id', 'id'],
                    ondelete='CASCADE'
                )

    # Search FK
    if 'searches' in tables and 'end_users' in tables:
        # Check if end_user_id column exists in searches table
        searches_columns = insp.get_columns('searches', schema='public')
        has_end_user_id = any(col['name'] == 'end_user_id' for col in searches_columns)

        if has_end_user_id:
            constraints = insp.get_foreign_keys('searches', schema='public')
            constraint_names = [constraint['name'] for constraint in constraints]

            if 'searches_account_id_end_user_id_fkey' in constraint_names:
                op.drop_constraint('searches_account_id_end_user_id_fkey', 'searches', type_='foreignkey', schema='public')

            # Check if id column exists in end_users table
            end_users_columns = insp.get_columns('end_users', schema='public')
            has_end_users_id = any(col['name'] == 'id' for col in end_users_columns)

            if has_end_users_id:
                op.create_foreign_key(
                    'fk_search_end_user',
                    'searches',
                    'end_users',
                    ['account_id', 'end_user_id'],
                    ['account_id', 'id'],
                    ondelete='CASCADE'
                )

    # TrainingJob FK
    if 'training_jobs' in tables:
        constraints = insp.get_foreign_keys('training_jobs', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'training_jobs_account_id_artifact_metadata_id_fkey' in constraint_names:
            op.drop_constraint('training_jobs_account_id_artifact_metadata_id_fkey', 'training_jobs', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'fk_training_job_artifact_metadata',
            'training_jobs',
            'artifact_metadata',
            ['account_id', 'artifact_metadata_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

    # ModelMetric FK
    if 'model_metrics' in tables:
        constraints = insp.get_foreign_keys('model_metrics', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'model_metrics_account_id_model_metadata_id_fkey' in constraint_names:
            op.drop_constraint('model_metrics_account_id_model_metadata_id_fkey', 'model_metrics', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'fk_model_metric_model_metadata',
            'model_metrics',
            'artifact_metadata',
            ['account_id', 'model_metadata_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

    # SystemUserRole FKs
    if 'system_user_roles' in tables:
        constraints = insp.get_foreign_keys('system_user_roles', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'system_user_roles_system_user_id_fkey' in constraint_names:
            op.drop_constraint('system_user_roles_system_user_id_fkey', 'system_user_roles', type_='foreignkey', schema='public')

        if 'system_user_roles_role_id_fkey' in constraint_names:
            op.drop_constraint('system_user_roles_role_id_fkey', 'system_user_roles', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'fk_system_user_role_system_user',
            'system_user_roles',
            'system_users',
            ['account_id', 'system_user_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )
        op.create_foreign_key(
            'fk_system_user_role_role',
            'system_user_roles',
            'roles',
            ['account_id', 'role_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

        # Add indexes for SystemUserRole
        indexes = insp.get_indexes('system_user_roles', schema='public')
        index_names = [index['name'] for index in indexes]

        if 'idx_system_user_role_user' not in index_names:
            op.create_index('idx_system_user_role_user', 'system_user_roles', ['account_id', 'system_user_id'])

        if 'idx_system_user_role_role' not in index_names:
            op.create_index('idx_system_user_role_role', 'system_user_roles', ['account_id', 'role_id'])


def downgrade() -> None:
    """Downgrade schema to revert composite foreign key fixes."""

    # Get inspector
    from sqlalchemy import inspect
    conn = op.get_bind()
    insp = inspect(conn)

    # Get tables
    tables = insp.get_table_names(schema='public')

    # SystemUserRole
    if 'system_user_roles' in tables:
        # Remove SystemUserRole indexes
        indexes = insp.get_indexes('system_user_roles', schema='public')
        index_names = [index['name'] for index in indexes]

        if 'idx_system_user_role_role' in index_names:
            op.drop_index('idx_system_user_role_role', table_name='system_user_roles', schema='public')

        if 'idx_system_user_role_user' in index_names:
            op.drop_index('idx_system_user_role_user', table_name='system_user_roles', schema='public')

        # Revert SystemUserRole FKs
        constraints = insp.get_foreign_keys('system_user_roles', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'fk_system_user_role_role' in constraint_names:
            op.drop_constraint('fk_system_user_role_role', 'system_user_roles', type_='foreignkey', schema='public')

        if 'fk_system_user_role_system_user' in constraint_names:
            op.drop_constraint('fk_system_user_role_system_user', 'system_user_roles', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'system_user_roles_role_id_fkey',
            'system_user_roles',
            'roles',
            ['role_id'],
            ['id'],
            ondelete='CASCADE'
        )
        op.create_foreign_key(
            'system_user_roles_system_user_id_fkey',
            'system_user_roles',
            'system_users',
            ['system_user_id'],
            ['id'],
            ondelete='CASCADE'
        )

    # Revert ModelMetric FK
    if 'model_metrics' in tables:
        constraints = insp.get_foreign_keys('model_metrics', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'fk_model_metric_model_metadata' in constraint_names:
            op.drop_constraint('fk_model_metric_model_metadata', 'model_metrics', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'model_metrics_account_id_model_metadata_id_fkey',
            'model_metrics',
            'artifact_metadata',
            ['account_id', 'model_metadata_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

    # Revert TrainingJob FK
    if 'training_jobs' in tables:
        constraints = insp.get_foreign_keys('training_jobs', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'fk_training_job_artifact_metadata' in constraint_names:
            op.drop_constraint('fk_training_job_artifact_metadata', 'training_jobs', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'training_jobs_account_id_artifact_metadata_id_fkey',
            'training_jobs',
            'artifact_metadata',
            ['account_id', 'artifact_metadata_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

    # Revert Search FK
    if 'searches' in tables:
        constraints = insp.get_foreign_keys('searches', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'fk_search_end_user' in constraint_names:
            op.drop_constraint('fk_search_end_user', 'searches', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'searches_account_id_end_user_id_fkey',
            'searches',
            'end_users',
            ['account_id', 'end_user_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

    # Revert Interaction FKs
    if 'interactions' in tables:
        constraints = insp.get_foreign_keys('interactions', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'fk_interaction_product' in constraint_names:
            op.drop_constraint('fk_interaction_product', 'interactions', type_='foreignkey', schema='public')

        if 'fk_interaction_end_user' in constraint_names:
            op.drop_constraint('fk_interaction_end_user', 'interactions', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'interactions_account_id_product_id_fkey',
            'interactions',
            'products',
            ['account_id', 'product_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )
        op.create_foreign_key(
            'interactions_account_id_end_user_id_fkey',
            'interactions',
            'end_users',
            ['account_id', 'end_user_id'],
            ['account_id', 'id'],
            ondelete='CASCADE'
        )

    # Remove added indexes
    if 'training_metrics' in tables:
        indexes = insp.get_indexes('training_metrics', schema='public')
        index_names = [index['name'] for index in indexes]

        if 'idx_training_metrics_timestamp' in index_names:
            op.drop_index('idx_training_metrics_timestamp', table_name='training_metrics', schema='public')

        if 'idx_training_metrics_model' in index_names:
            op.drop_index('idx_training_metrics_model', table_name='training_metrics', schema='public')

        # Revert TrainingMetrics FK
        constraints = insp.get_foreign_keys('training_metrics', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'fk_training_metrics_model' in constraint_names:
            op.drop_constraint('fk_training_metrics_model', 'training_metrics', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'training_metrics_model_id_fkey',
            'training_metrics',
            'model_metadata',
            ['model_id'],
            ['id']
        )

    # Revert Recommendation FKs
    if 'recommendations' in tables:
        constraints = insp.get_foreign_keys('recommendations', schema='public')
        constraint_names = [constraint['name'] for constraint in constraints]

        if 'fk_recommendation_product' in constraint_names:
            op.drop_constraint('fk_recommendation_product', 'recommendations', type_='foreignkey', schema='public')

        if 'fk_recommendation_end_user' in constraint_names:
            op.drop_constraint('fk_recommendation_end_user', 'recommendations', type_='foreignkey', schema='public')

        op.create_foreign_key(
            'recommendations_product_id_fkey',
            'recommendations',
            'products',
            ['product_id'],
            ['id'],
            ondelete='CASCADE'
        )
        op.create_foreign_key(
            'recommendations_end_user_id_fkey',
            'recommendations',
            'end_users',
            ['end_user_id'],
            ['id'],
            ondelete='CASCADE'
        )

    # Revert Product.created_by FK
    if 'products' in tables:
        columns = insp.get_columns('products', schema='public')
        has_created_by = any(col['name'] == 'created_by' for col in columns)

        if has_created_by:
            constraints = insp.get_foreign_keys('products', schema='public')
            constraint_names = [constraint['name'] for constraint in constraints]

            if 'fk_product_created_by' in constraint_names:
                op.drop_constraint('fk_product_created_by', 'products', type_='foreignkey', schema='public')

            op.create_foreign_key(
                'products_created_by_fkey',
                'products',
                'system_users',
                ['created_by'],
                ['id']
            )
