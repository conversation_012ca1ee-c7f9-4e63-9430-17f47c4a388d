"""
Esquemas para trabajos de ingesta masiva de datos.
"""

from pydantic import BaseModel, Field, ConfigDict
from typing import Dict, Any, Optional
from datetime import datetime


class BatchIngestionJobBase(BaseModel):
    """Esquema base para trabajos de ingesta masiva."""
    status: str = Field(default="pending")
    task_id: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None


class BatchIngestionJobCreate(BatchIngestionJobBase):
    """Esquema para crear trabajos de ingesta masiva."""
    pass


class BatchIngestionJob(BatchIngestionJobBase):
    """Esquema para trabajos de ingesta masiva."""
    account_id: int
    id: int
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    processed_count: Optional[Dict[str, Any]] = None
    data_file_path: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class BatchIngestionJobResponse(BatchIngestionJob):
    """Esquema para respuestas de trabajos de ingesta masiva."""
    pass


class BatchIngestionJobStatus(BaseModel):
    """Esquema para consultar el estado de un trabajo de ingesta masiva."""
    job_id: int
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    task_id: Optional[str] = None
    processed_count: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None

    # Campos calculados para facilitar la interpretación del estado
    progress_percentage: Optional[float] = None
    estimated_remaining_time: Optional[float] = None  # en segundos
    success_rate: Optional[float] = None  # porcentaje de éxito (registros procesados correctamente / total)
    error_count: Optional[int] = None  # número de errores encontrados
    error_details: Optional[Dict[str, Any]] = None  # detalles de errores por tipo

    model_config = ConfigDict(from_attributes=True)
