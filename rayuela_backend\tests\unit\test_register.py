import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession
from src.db.enums import SubscriptionPlan, RoleType
from src.api.v1.endpoints.auth import register
from src.db.schemas.auth import RegisterRequest


@pytest.mark.asyncio
async def test_register_endpoint():
    """Test the register endpoint function directly."""
    # Mock dependencies
    mock_db = AsyncMock(spec=AsyncSession)
    
    # Mock repositories and their methods
    mock_account_repo = AsyncMock()
    mock_account = MagicMock()
    mock_account.account_id = 1
    mock_account_repo.create.return_value = mock_account
    
    mock_role_repo = AsyncMock()
    mock_role = MagicMock()
    mock_role.id = 1
    mock_role_repo.create.return_value = mock_role
    
    mock_user_role_repo = AsyncMock()
    
    # Create test data
    register_data = RegisterRequest(
        account_name="Test Account",
        subscription_plan=SubscriptionPlan.FREE,
        email="<EMAIL>",
        password="securepassword123",
        first_name="Admin",
        last_name="User"
    )
    
    # Patch the necessary dependencies
    with patch('src.api.v1.endpoints.auth.AccountRepository', return_value=mock_account_repo), \
         patch('src.api.v1.endpoints.auth.RoleRepository', return_value=mock_role_repo), \
         patch('src.api.v1.endpoints.auth.SystemUserRoleRepository', return_value=mock_user_role_repo), \
         patch('src.api.v1.endpoints.auth.generate_api_key', return_value=("test_api_key", "test_api_key_hash")), \
         patch('src.api.v1.endpoints.auth.get_password_hash', return_value="hashed_password"), \
         patch('src.api.v1.endpoints.auth.SystemUser') as mock_system_user_class:
        
        # Mock the SystemUser instance
        mock_user = MagicMock()
        mock_user.id = 1
        mock_user.email = register_data.email
        mock_system_user_class.return_value = mock_user
        
        # Call the register function
        response = await register(register_data, mock_db)
        
        # Assertions
        assert response is not None
        assert mock_account_repo.create.called
        
        # Check that the account was created with the correct data
        account_create_call = mock_account_repo.create.call_args[0][0]
        assert account_create_call.name == register_data.account_name
        assert account_create_call.subscription_plan == register_data.subscription_plan
        
        # Check that the API key was set
        assert hasattr(mock_account, 'api_key_hash')
        
        # Check that the user was created
        assert mock_system_user_class.called
        user_create_call = mock_system_user_class.call_args[1]
        assert user_create_call['email'] == register_data.email
        assert user_create_call['hashed_password'] == "hashed_password"
        assert user_create_call['is_admin'] is True
        
        # Check that the admin role was created
        assert mock_role_repo.create.called
        role_create_call = mock_role_repo.create.call_args[0][0]
        assert role_create_call.name == RoleType.ADMIN
        
        # Check that the role was assigned to the user
        assert mock_user_role_repo.assign_role.called
        assert mock_user_role_repo.assign_role.call_args[0][0] == mock_user.id
        assert mock_user_role_repo.assign_role.call_args[0][1] == mock_role.id
