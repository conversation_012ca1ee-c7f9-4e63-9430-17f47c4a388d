# Separación de Lógica de Negocio

Este documento explica la separación de responsabilidades entre los repositorios y los servicios en nuestra aplicación.

## Principios de Diseño

Nuestra aplicación sigue el principio de separación de responsabilidades, donde:

1. **Repositorios**: Se centran exclusivamente en el acceso y manipulación de datos de su entidad principal.
2. **Servicios**: Encapsulan la lógica de negocio, incluyendo validaciones complejas, cálculos que involucran múltiples entidades, y orquestación de operaciones.

## Cambios Recientes

### Creación del AccountService

Se ha creado un nuevo servicio `AccountService` para encapsular la lógica de negocio relacionada con cuentas que anteriormente estaba en el `AccountRepository`. Los siguientes métodos se han movido del repositorio al servicio:

1. `count_users(account_id)`: Contar usuarios activos de una cuenta
2. `count_products(account_id)`: Contar productos activos de una cuenta
3. `count_items(account_id)`: Alias de count_products para compatibilidad
4. `get_usage_metrics(account_id)`: Obtener métricas de uso de una cuenta
5. `get_storage_usage(account_id)`: Obtener uso de almacenamiento de una cuenta

Además, se ha añadido un nuevo método `get_account_summary(account_id)` que proporciona un resumen completo de la información de la cuenta.

### Actualización de Servicios Existentes

Los servicios existentes se han actualizado para usar el nuevo `AccountService` en lugar de llamar directamente a los métodos del `AccountRepository`:

1. **LimitService**: Ahora usa `AccountService` para obtener información sobre usuarios, productos y uso de almacenamiento.
2. **SubscriptionService**: Ahora usa `AccountService` para obtener métricas de uso.

## Beneficios de la Separación

### 1. Mayor Cohesión

Cada componente tiene una responsabilidad clara y bien definida:
- Los repositorios se centran en el acceso a datos
- Los servicios se centran en la lógica de negocio

### 2. Mejor Testeabilidad

Es más fácil probar la lógica de negocio de forma aislada, sin depender de la implementación específica del acceso a datos.

```python
# Ejemplo de test para un servicio con mock de repositorio
async def test_account_service_count_users():
    # Arrange
    mock_db = AsyncMock()
    mock_db.execute.return_value.scalar_one.return_value = 5
    account_service = AccountService(mock_db)
    
    # Act
    result = await account_service.count_users(1)
    
    # Assert
    assert result == 5
    mock_db.execute.assert_called_once()
```

### 3. Flexibilidad para Cambios

Podemos cambiar la implementación de un repositorio sin afectar a la lógica de negocio, y viceversa.

### 4. Reutilización de Código

La lógica de negocio puede ser reutilizada en diferentes contextos sin duplicación.

## Estructura de Servicios

### AccountService

```python
class AccountService:
    """Service for handling account-related business logic."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self._account_repo = AccountRepository(db)

    async def count_users(self, account_id: int) -> int:
        """Count the total number of active users for an account."""
        # Implementación...

    async def count_products(self, account_id: int) -> int:
        """Count the total number of active products for an account."""
        # Implementación...

    async def get_usage_metrics(self, account_id: int) -> Optional[models.AccountUsageMetrics]:
        """Get usage metrics for an account."""
        # Implementación...

    async def get_storage_usage(self, account_id: int) -> int:
        """Get the current storage usage for an account."""
        # Implementación...

    async def get_account_summary(self, account_id: int) -> Dict[str, Any]:
        """Get a summary of account information."""
        # Implementación...
```

### SubscriptionService

```python
class SubscriptionService:
    """Service for handling subscription-related business logic."""

    def __init__(self, db: AsyncSession):
        self.db = db
        self._subscription_repo = SubscriptionRepository(db, account_id=None)
        self._account_service = AccountService(db)

    async def get_subscription(self, account_id: int) -> Optional[models.Subscription]:
        """Get the active subscription for an account."""
        # Implementación...

    async def get_permissions(self, account_id: int) -> Dict[str, Any]:
        """Get permissions and limits based on the account's subscription."""
        # Implementación...

    async def check_account_limits(self, account_id: int) -> bool:
        """Check if the account has exceeded its API call limits."""
        # Implementación...
```

### LimitService

```python
class LimitService:
    """Service for handling limit-related business logic."""

    def __init__(self, db: AsyncSession, account_id: int):
        self.db = db
        self.account_id = account_id
        self._account_service = AccountService(db)
        self._subscription_service = SubscriptionService(db)
        self._limits_cache: Dict[int, Dict[str, Any]] = {}

    async def validate_user_limit(self) -> None:
        """Validate user limit."""
        # Implementación...

    async def validate_product_limit(self) -> None:
        """Validate product limit."""
        # Implementación...
```

## Mejores Prácticas

### 1. Mantener los Repositorios Simples

Los repositorios deben centrarse en operaciones CRUD y consultas específicas para una entidad. Evitar incluir lógica de negocio compleja.

```python
# Bien: Método de repositorio simple
async def get_by_email(self, email: str) -> Optional[models.SystemUser]:
    query = select(models.SystemUser).filter(models.SystemUser.email == email)
    result = await self.db.execute(query)
    return result.scalars().first()

# Mal: Lógica de negocio en el repositorio
async def register_user(self, user_data: Dict[str, Any]) -> models.SystemUser:
    # Validar límites, crear usuario, asignar roles, etc.
    # Esta lógica debería estar en un servicio
```

### 2. Usar Servicios para Coordinar Operaciones

Los servicios deben coordinar operaciones entre múltiples repositorios y aplicar reglas de negocio.

```python
# Bien: Servicio que coordina operaciones
async def register_user(self, user_data: schemas.UserCreate) -> models.SystemUser:
    # Validar límites
    await self._limit_service.validate_user_limit()
    
    # Crear usuario
    user = await self._user_repo.create(user_data)
    
    # Asignar roles
    await self._permission_service.assign_default_roles(user.id)
    
    return user
```

### 3. Inyección de Dependencias

Usar inyección de dependencias para proporcionar servicios y repositorios a los controladores y otros servicios.

```python
# Bien: Inyección de dependencias
def get_account_service(db: AsyncSession = Depends(get_db)):
    return AccountService(db)

@router.get("/accounts/{account_id}")
async def get_account(
    account_id: int,
    account_service: AccountService = Depends(get_account_service)
):
    return await account_service.get_account_summary(account_id)
```

## Conclusión

La separación clara de responsabilidades entre repositorios y servicios mejora la mantenibilidad, la testeabilidad y la escalabilidad de la aplicación. Los repositorios se centran en el acceso a datos, mientras que los servicios encapsulan la lógica de negocio, lo que permite cambiar la implementación de una capa sin afectar a la otra.
