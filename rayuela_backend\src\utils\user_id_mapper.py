"""
Utilidad para mapear entre end_user_id y user_id durante la transición de nomenclatura.

Este módulo proporciona funciones para manejar la inconsistencia temporal
entre end_user_id (usado en modelos de BD) y user_id (usado en APIs).

NOTA: Después de la migración de nomenclatura, estas funciones son principalmente
para compatibilidad hacia atrás y pueden ser eliminadas en futuras versiones.
"""

from typing import Dict, Any, Optional


def map_end_user_id_to_user_id(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Mapea end_user_id a user_id en un diccionario de datos.

    Args:
        data: Diccionario que puede contener end_user_id

    Returns:
        Diccionario con user_id en lugar de end_user_id
    """
    if not isinstance(data, dict):
        return data

    # Crear una copia para no modificar el original
    mapped_data = data.copy()

    # Si existe end_user_id, mapearlo a user_id
    if "end_user_id" in mapped_data:
        mapped_data["user_id"] = mapped_data.pop("end_user_id")

    return mapped_data


def map_user_id_to_end_user_id(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Mapea user_id a end_user_id en un diccionario de datos.

    Args:
        data: Diccionario que puede contener user_id

    Returns:
        Diccionario con end_user_id en lugar de user_id
    """
    if not isinstance(data, dict):
        return data

    # Crear una copia para no modificar el original
    mapped_data = data.copy()

    # Si existe user_id, mapearlo a end_user_id
    if "user_id" in mapped_data:
        mapped_data["end_user_id"] = mapped_data.pop("user_id")

    return mapped_data


def extract_user_id_from_request_data(data: Dict[str, Any]) -> Optional[int]:
    """
    Extrae el ID de usuario de los datos de una solicitud,
    manejando ambas nomenclaturas (user_id y end_user_id).

    Args:
        data: Datos de la solicitud

    Returns:
        ID de usuario o None si no se encuentra
    """
    if not isinstance(data, dict):
        return None

    # Buscar en diferentes formatos posibles
    user_id = (
        data.get("user_id")
        or data.get("end_user_id")
        or data.get("userId")
        or data.get("endUserId")
    )

    if isinstance(user_id, int) and user_id > 0:
        return user_id
    elif isinstance(user_id, str) and user_id.isdigit():
        return int(user_id)

    return None


def normalize_user_id_field(
    data: Dict[str, Any], target_field: str = "user_id"
) -> Dict[str, Any]:
    """
    Normaliza el campo de ID de usuario a un nombre específico.

    Args:
        data: Diccionario de datos
        target_field: Nombre del campo objetivo ("user_id" o "end_user_id")

    Returns:
        Diccionario con el campo normalizado
    """
    if not isinstance(data, dict):
        return data

    # Crear una copia para no modificar el original
    normalized_data = data.copy()

    # Extraer el ID de usuario
    user_id = extract_user_id_from_request_data(normalized_data)

    if user_id is not None:
        # Remover todas las variantes del campo
        for field in ["user_id", "end_user_id", "userId", "endUserId"]:
            normalized_data.pop(field, None)

        # Establecer el campo objetivo
        normalized_data[target_field] = user_id

    return normalized_data


class UserIdMapper:
    """
    Clase para manejar el mapeo de IDs de usuario durante la transición.

    DEPRECATED: Esta clase es para compatibilidad hacia atrás.
    Después de la migración, todos los modelos usan 'user_id' consistentemente.
    """

    @staticmethod
    def to_api_format(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convierte datos del formato de BD (end_user_id) al formato de API (user_id).

        DEPRECATED: Ya no es necesario después de la migración.

        Args:
            data: Datos en formato de BD

        Returns:
            Datos en formato de API
        """
        return map_end_user_id_to_user_id(data)

    @staticmethod
    def to_db_format(data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Convierte datos del formato de API (user_id) al formato de BD (end_user_id).

        DEPRECATED: Ya no es necesario después de la migración.

        Args:
            data: Datos en formato de API

        Returns:
            Datos en formato de BD
        """
        return map_user_id_to_end_user_id(data)

    @staticmethod
    def extract_user_id(data: Dict[str, Any]) -> Optional[int]:
        """
        Extrae el ID de usuario de los datos, manejando ambas nomenclaturas.

        Args:
            data: Datos de entrada

        Returns:
            ID de usuario o None
        """
        return extract_user_id_from_request_data(data)
