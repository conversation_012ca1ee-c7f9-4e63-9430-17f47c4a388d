import pytest
from datetime import datetime, timedelta
from jose import jwt
from src.core.security.token import (
    create_access_token,
    decode_access_token,
    is_token_revoked,
    revoke_token
)
from src.core.config import settings

def test_create_access_token():
    """Test para verificar la creación de tokens de acceso."""
    # Datos de prueba
    data = {"sub": "<EMAIL>", "account_id": 1}
    expires_delta = timedelta(minutes=15)
    
    # Crear token
    token = create_access_token(data, expires_delta)
    
    # Verificar formato
    assert isinstance(token, str)
    assert len(token.split('.')) == 3  # Formato JWT: header.payload.signature

def test_decode_access_token():
    """Test para verificar la decodificación de tokens de acceso."""
    # Datos de prueba
    data = {"sub": "<EMAIL>", "account_id": 1}
    expires_delta = timedelta(minutes=15)
    
    # Crear token
    token = create_access_token(data, expires_delta)
    
    # Decodificar token
    decoded = decode_access_token(token)
    
    # Verificar datos
    assert decoded["sub"] == data["sub"]
    assert decoded["account_id"] == data["account_id"]
    assert "exp" in decoded  # Debe tener fecha de expiración

def test_token_expiration():
    """Test para verificar la expiración de tokens."""
    # Datos de prueba
    data = {"sub": "<EMAIL>", "account_id": 1}
    expires_delta = timedelta(milliseconds=100)  # Token muy corto
    
    # Crear token
    token = create_access_token(data, expires_delta)
    
    # Esperar a que expire
    import time
    time.sleep(0.2)
    
    # Intentar decodificar (debe fallar)
    with pytest.raises(jwt.ExpiredSignatureError):
        decode_access_token(token)

def test_token_revocation():
    """Test para verificar la revocación de tokens."""
    # Datos de prueba
    data = {"sub": "<EMAIL>", "account_id": 1}
    expires_delta = timedelta(minutes=15)
    
    # Crear token
    token = create_access_token(data, expires_delta)
    
    # Revocar token
    revoke_token(token)
    
    # Verificar que está revocado
    assert is_token_revoked(token) is True

def test_invalid_token():
    """Test para verificar el manejo de tokens inválidos."""
    # Token inválido
    invalid_token = "invalid.token.here"
    
    # Intentar decodificar (debe fallar)
    with pytest.raises(jwt.JWTError):
        decode_access_token(invalid_token)

def test_token_with_invalid_signature():
    """Test para verificar el manejo de tokens con firma inválida."""
    # Crear token válido
    data = {"sub": "<EMAIL>", "account_id": 1}
    token = create_access_token(data)
    
    # Modificar el token (cambiar un carácter)
    modified_token = token[:-1] + ('1' if token[-1] == '0' else '0')
    
    # Intentar decodificar (debe fallar)
    with pytest.raises(jwt.JWTError):
        decode_access_token(modified_token)

def test_token_with_missing_claims():
    """Test para verificar el manejo de tokens con claims faltantes."""
    # Crear token sin claims requeridos
    payload = {
        "exp": datetime.utcnow() + timedelta(minutes=15)
    }
    token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    
    # Intentar decodificar (debe fallar)
    with pytest.raises(jwt.JWTError):
        decode_access_token(token)

def test_token_reuse_after_revocation():
    """Test para verificar que un token revocado no puede ser reutilizado."""
    # Datos de prueba
    data = {"sub": "<EMAIL>", "account_id": 1}
    expires_delta = timedelta(minutes=15)
    
    # Crear token
    token = create_access_token(data, expires_delta)
    
    # Revocar token
    revoke_token(token)
    
    # Intentar decodificar (debe fallar)
    with pytest.raises(jwt.JWTError):
        decode_access_token(token) 