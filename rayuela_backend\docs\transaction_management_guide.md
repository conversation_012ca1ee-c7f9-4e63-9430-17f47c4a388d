# Guía de Gestión de Transacciones

## Introducción

Esta guía describe las mejores prácticas para la gestión de transacciones en nuestra aplicación. El objetivo es garantizar que las operaciones de base de datos que deben ejecutarse como una unidad atómica se manejen correctamente, manteniendo la integridad de los datos y la robustez de la aplicación.

## Principios Fundamentales

1. **Transacciones en la Capa de Servicios**: Las transacciones deben manejarse en la capa de servicios, no en los endpoints o repositorios.
2. **Atomicidad**: Las operaciones relacionadas deben ser atómicas: o todas tienen éxito o ninguna.
3. **Consistencia**: Los datos deben pasar de un estado consistente a otro.
4. **Aislamiento**: Las transacciones deben ejecutarse de forma aislada de otras transacciones.
5. **Durabilidad**: Una vez que una transacción se ha completado, sus efectos deben ser permanentes.

## Patrón Recomendado

### 1. Manejo de Transacciones en Servicios

```python
async def create_complex_entity(self, data: schemas.ComplexEntityCreate) -> models.ComplexEntity:
    """
    Crea una entidad compleja que involucra múltiples operaciones de base de datos.
    """
    try:
        # Iniciar una transacción explícita
        async with self.db.begin():
            # Realizar múltiples operaciones de base de datos
            entity = await self.entity_repo.create(data.entity)
            for item in data.items:
                item.entity_id = entity.id
                await self.item_repo.create(item)

            # No es necesario hacer flush explícito, se hará automáticamente
            # al salir del bloque with

        # La transacción se ha completado exitosamente en este punto
        return entity
    except Exception as e:
        # No es necesario hacer rollback explícito, se hará automáticamente
        log_error(f"Error creating complex entity: {str(e)}")
        raise
```

### 2. Transacciones de Solo Lectura

```python
async def get_complex_entity(self, entity_id: int) -> Dict[str, Any]:
    """
    Obtiene una entidad compleja con todos sus datos relacionados.
    """
    try:
        # Iniciar una transacción de solo lectura para garantizar consistencia
        async with self.db.begin():
            # Realizar múltiples operaciones de lectura
            entity = await self.entity_repo.get_by_id(entity_id)
            if not entity:
                raise ResourceNotFoundError(f"Entity {entity_id} not found")

            items = await self.item_repo.get_by_entity(entity_id)

            # Construir respuesta
            return {
                "entity": entity,
                "items": items
            }
    except ResourceNotFoundError:
        # Re-lanzar excepciones específicas
        raise
    except Exception as e:
        log_error(f"Error getting complex entity: {str(e)}")
        raise
```

### 3. Manejo de Excepciones

```python
async def update_complex_entity(self, entity_id: int, data: schemas.ComplexEntityUpdate) -> models.ComplexEntity:
    """
    Actualiza una entidad compleja y sus datos relacionados.
    """
    try:
        # Iniciar una transacción explícita
        async with self.db.begin():
            # Verificar que la entidad existe
            entity = await self.entity_repo.get_by_id(entity_id)
            if not entity:
                raise ResourceNotFoundError(f"Entity {entity_id} not found")

            # Actualizar la entidad
            updated_entity = await self.entity_repo.update(entity_id, data.entity)

            # Actualizar items relacionados
            for item in data.items:
                if item.id:
                    # Actualizar item existente
                    await self.item_repo.update(item.id, item)
                else:
                    # Crear nuevo item
                    item.entity_id = entity_id
                    await self.item_repo.create(item)

            # Eliminar items que ya no están en la lista
            current_items = await self.item_repo.get_by_entity(entity_id)
            current_item_ids = [item.id for item in current_items]
            updated_item_ids = [item.id for item in data.items if item.id]

            for item_id in current_item_ids:
                if item_id not in updated_item_ids:
                    await self.item_repo.delete(item_id)

        # La transacción se ha completado exitosamente en este punto
        return updated_entity
    except ResourceNotFoundError:
        # Re-lanzar excepciones específicas
        raise
    except Exception as e:
        # No es necesario hacer rollback explícito, se hará automáticamente
        log_error(f"Error updating complex entity: {str(e)}")
        raise
```

## Consideraciones Importantes

### 1. Evitar Transacciones Anidadas

SQLAlchemy no soporta transacciones anidadas de forma nativa. Si necesitas realizar operaciones en transacciones separadas, considera refactorizar tu código para evitar anidamiento.

### 2. Transacciones Largas

Evita transacciones que duren mucho tiempo, ya que pueden bloquear recursos y afectar el rendimiento. Si necesitas realizar operaciones de larga duración, considera dividirlas en transacciones más pequeñas.

### 3. Operaciones Externas

Las operaciones que involucran sistemas externos (como APIs, servicios de almacenamiento, etc.) deben manejarse con cuidado. Considera implementar patrones como Saga o Compensación para manejar fallos en operaciones distribuidas.

### 4. Logging

Asegúrate de registrar información detallada sobre las transacciones, especialmente en caso de error. Esto facilitará la depuración y el seguimiento de problemas.

## Ejemplos de Uso

### Ejemplo 1: Creación de Entidades Relacionadas

```python
async def create_product_with_variants(
    self,
    product_data: schemas.ProductCreate,
    variants_data: List[schemas.VariantCreate]
) -> Dict[str, Any]:
    """
    Crea un producto con sus variantes en una sola transacción.
    """
    try:
        # Iniciar una transacción explícita
        async with self.db.begin():
            # Crear el producto
            product = await self.product_repo.create(product_data)

            # Crear las variantes
            variants = []
            for variant_data in variants_data:
                variant_data.product_id = product.id
                variant = await self.variant_repo.create(variant_data)
                variants.append(variant)

        # La transacción se ha completado exitosamente
        return {
            "product": product,
            "variants": variants
        }
    except Exception as e:
        log_error(f"Error creating product with variants: {str(e)}")
        raise
```

### Ejemplo 2: Actualización de Múltiples Entidades

```python
async def update_user_and_preferences(
    self,
    user_id: int,
    user_data: schemas.UserUpdate,
    preferences_data: schemas.PreferencesUpdate
) -> Dict[str, Any]:
    """
    Actualiza un usuario y sus preferencias en una sola transacción.
    """
    try:
        # Iniciar una transacción explícita
        async with self.db.begin():
            # Verificar que el usuario existe
            user = await self.user_repo.get_by_id(user_id)
            if not user:
                raise ResourceNotFoundError(f"User {user_id} not found")

            # Actualizar el usuario
            updated_user = await self.user_repo.update(user_id, user_data)

            # Obtener preferencias existentes
            preferences = await self.preferences_repo.get_by_user(user_id)

            # Actualizar o crear preferencias
            if preferences:
                updated_preferences = await self.preferences_repo.update(
                    preferences.id, preferences_data
                )
            else:
                preferences_data.user_id = user_id
                updated_preferences = await self.preferences_repo.create(preferences_data)

        # La transacción se ha completado exitosamente
        return {
            "user": updated_user,
            "preferences": updated_preferences
        }
    except ResourceNotFoundError:
        # Re-lanzar excepciones específicas
        raise
    except Exception as e:
        log_error(f"Error updating user and preferences: {str(e)}")
        raise
```

## Estado Actual de la Implementación

Hemos aplicado el patrón de gestión de transacciones a los siguientes servicios:

1. **AccountService**
   - `get_account_summary`: Usa transacciones para garantizar lecturas consistentes.

2. **PermissionService**
   - `get_user_permissions`: Usa transacciones para garantizar lecturas consistentes.
   - `get_user_roles`: Usa transacciones para garantizar lecturas consistentes.
   - `assign_permission_to_role`: Usa transacciones para garantizar atomicidad.
   - `remove_permission_from_role`: Usa transacciones para garantizar atomicidad.
   - `assign_default_permissions_to_role`: Usa transacciones para garantizar atomicidad.

3. **LimitService**
   - `validate_training_frequency`: Usa transacciones para garantizar lecturas consistentes.
   - `_get_last_successful_training`: Usa transacciones para garantizar lecturas consistentes.

4. **SubscriptionService**
   - `update_usage_metrics`: Usa transacciones para garantizar atomicidad.
   - `reset_usage_metrics`: Usa transacciones para garantizar atomicidad.
   - `validate_and_update_subscription`: Usa transacciones para garantizar atomicidad.

5. **InteractionService**
   - `create_interaction`: Usa transacciones para garantizar atomicidad.

6. **DataIngestionService**
   - `create_batch_ingestion_job`: Usa transacciones para garantizar atomicidad.
   - `get_batch_job_status`: Usa transacciones para garantizar lecturas consistentes.

7. **AnalyticsService**
   - `get_account_metrics`: Usa transacciones para garantizar lecturas consistentes.
   - `get_endpoint_metrics`: Usa transacciones para garantizar lecturas consistentes.

8. **TransactionExampleService**
   - `create_product_with_initial_user`: Usa transacciones para garantizar atomicidad.
   - `update_product_and_user`: Usa transacciones para garantizar atomicidad.
   - `delete_product_and_related_data`: Usa transacciones para garantizar atomicidad.

## Conclusión

Seguir estas mejores prácticas para la gestión de transacciones garantizará que nuestra aplicación sea más robusta, mantenga la integridad de los datos y sea más fácil de mantener. Recuerda que las transacciones deben manejarse en la capa de servicios, no en los endpoints o repositorios, y que deben ser atómicas, consistentes, aisladas y durables.
