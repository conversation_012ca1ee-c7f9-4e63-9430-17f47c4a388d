"""Unit tests for the usage summary endpoint."""
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone, timedelta
from sqlalchemy.ext.asyncio import AsyncSession

from src.api.v1.endpoints.usage_summary import get_usage_summary
from src.db.enums import SubscriptionPlan, TrainingJobStatus


@pytest_asyncio.fixture
async def mock_db():
    """Mock database session."""
    db = AsyncMock(spec=AsyncSession)
    db.begin = AsyncMock(return_value=AsyncMock(__aenter__=AsyncMock(), __aexit__=AsyncMock()))
    return db


@pytest_asyncio.fixture
async def mock_current_user():
    """Mock current user."""
    user = MagicMock()
    user.account_id = 1
    user.account = MagicMock()
    user.account.stripe_customer_id = "cus_test123"
    return user


@pytest_asyncio.fixture
async def mock_subscription():
    """Mock subscription."""
    subscription = MagicMock()
    subscription.plan_type = SubscriptionPlan.STARTER
    subscription.is_active = True
    subscription.expires_at = datetime.now(timezone.utc) + timedelta(days=30)
    subscription.api_calls_limit = 10000
    subscription.monthly_api_calls_used = 2500
    subscription.storage_limit = 100_000_000  # 100MB
    subscription.last_reset_date = datetime.now(timezone.utc) - timedelta(days=15)
    subscription.available_models = ["basic", "standard"]
    subscription.last_successful_training_at = datetime.now(timezone.utc) - timedelta(days=3)
    return subscription


@pytest_asyncio.fixture
async def mock_training_job():
    """Mock training job."""
    job = MagicMock()
    job.status = TrainingJobStatus.COMPLETED
    job.completed_at = datetime.now(timezone.utc) - timedelta(days=3)
    return job


@pytest.mark.asyncio
async def test_get_usage_summary(
    mock_db, mock_current_user, mock_subscription, mock_training_job
):
    """Test get_usage_summary function."""
    # Mock services
    mock_subscription_service = AsyncMock()
    mock_subscription_service._subscription_repo = AsyncMock()
    mock_subscription_service._subscription_repo.get_by_account.return_value = mock_subscription
    
    mock_storage_tracker = AsyncMock()
    mock_storage_tracker.get_current_storage_usage.return_value = 25_000_000  # 25MB
    
    mock_limit_service = AsyncMock()
    
    mock_redis = AsyncMock()
    mock_redis_meter = AsyncMock()
    mock_redis_meter.get_storage_usage_bytes.return_value = 25_000_000  # 25MB
    mock_redis_meter.get_storage_details.return_value = {
        "measured_at": datetime.now(timezone.utc).isoformat(),
        "products_bytes": 10_000_000,
        "end_users_bytes": 5_000_000,
        "interactions_bytes": 8_000_000,
        "artifacts_bytes": 2_000_000,
    }
    
    mock_training_repo = AsyncMock()
    mock_training_repo.get_last_successful.return_value = mock_training_job
    
    # Patch dependencies
    with patch("src.api.v1.endpoints.usage_summary.RedisStorageMeterService", return_value=mock_redis_meter), \
         patch("src.api.v1.endpoints.usage_summary.TrainingJobRepository", return_value=mock_training_repo):
        
        # Call function
        result = await get_usage_summary(
            current_user=mock_current_user,
            subscription_service=mock_subscription_service,
            storage_tracker=mock_storage_tracker,
            limit_service=mock_limit_service,
            redis=mock_redis,
            db=mock_db,
        )
        
        # Verify result structure
        assert "subscription" in result
        assert "api_calls" in result
        assert "storage" in result
        assert "training" in result
        assert "features" in result
        assert "billing" in result
        
        # Verify subscription details
        assert result["subscription"]["plan"] == SubscriptionPlan.STARTER
        assert result["subscription"]["is_active"] is True
        
        # Verify API calls
        assert result["api_calls"]["used"] == 2500
        assert result["api_calls"]["limit"] == 10000
        assert result["api_calls"]["percentage"] == 25.0
        
        # Verify storage
        assert result["storage"]["used_bytes"] == 25_000_000
        assert result["storage"]["limit_bytes"] == 100_000_000
        assert result["storage"]["percentage"] == 25.0
        
        # Verify training
        assert "frequency_limit" in result["training"]
        assert "last_training_date" in result["training"]
        assert "next_training_available" in result["training"]
        assert "can_train_now" in result["training"]
        
        # Verify billing
        assert result["billing"]["stripe_customer_id"] == "cus_test123"
        assert result["billing"]["has_payment_method"] is True
        assert result["billing"]["portal_url"] == "/api/v1/billing/create-portal-session"
        assert result["billing"]["checkout_url"] == "/api/v1/billing/create-checkout-session"
        assert result["billing"]["upgrade_available"] is True


@pytest.mark.asyncio
async def test_get_usage_summary_no_redis_data(
    mock_db, mock_current_user, mock_subscription, mock_training_job
):
    """Test get_usage_summary function when Redis data is not available."""
    # Mock services
    mock_subscription_service = AsyncMock()
    mock_subscription_service._subscription_repo = AsyncMock()
    mock_subscription_service._subscription_repo.get_by_account.return_value = mock_subscription
    
    mock_storage_tracker = AsyncMock()
    mock_storage_tracker.get_current_storage_usage.return_value = 25_000_000  # 25MB
    
    mock_limit_service = AsyncMock()
    
    mock_redis = AsyncMock()
    mock_redis_meter = AsyncMock()
    mock_redis_meter.get_storage_usage_bytes.return_value = 0  # No Redis data
    mock_redis_meter.get_storage_details.return_value = None
    
    mock_training_repo = AsyncMock()
    mock_training_repo.get_last_successful.return_value = mock_training_job
    
    # Patch dependencies
    with patch("src.api.v1.endpoints.usage_summary.RedisStorageMeterService", return_value=mock_redis_meter), \
         patch("src.api.v1.endpoints.usage_summary.TrainingJobRepository", return_value=mock_training_repo):
        
        # Call function
        result = await get_usage_summary(
            current_user=mock_current_user,
            subscription_service=mock_subscription_service,
            storage_tracker=mock_storage_tracker,
            limit_service=mock_limit_service,
            redis=mock_redis,
            db=mock_db,
        )
        
        # Verify storage details
        assert result["storage"]["used_bytes"] == 25_000_000
        assert result["storage"]["source"] == "database_calculation"
        assert result["storage"]["last_measured"] == "Just now"
