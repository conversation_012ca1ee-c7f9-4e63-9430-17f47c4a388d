from pydantic import BaseModel
from typing import Dict, Any, Optional, List
from datetime import datetime
from src.db.enums import TrainingJobStatus as JobStatus


class TrainingResponse(BaseModel):
    """Respuesta para el inicio de entrenamiento"""

    message: str
    job_id: Optional[int] = None
    task_id: Optional[str] = None
    account_id: Optional[int] = None


class TrainingStatus(BaseModel):
    """Estado del entrenamiento y métricas"""

    # Métricas de rendimiento del modelo
    ndcg: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    map: Optional[float] = None
    diversity: Optional[float] = None
    coverage: Optional[float] = None

    # Métricas adicionales
    training_time: Optional[float] = None
    data_points: Optional[int] = None

    # Métricas personalizadas
    custom_metrics: Optional[Dict[str, Any]] = None


class ModelInfo(BaseModel):
    """Información básica de un modelo"""
    id: int
    artifact_name: str
    artifact_version: str
    description: Optional[str] = None
    training_date: datetime


class TrainingJobStatus(BaseModel):
    """Estado detallado de un trabajo de entrenamiento"""
    job_id: int
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    task_id: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    metrics: Optional[Dict[str, Any]] = None
    model: Optional[ModelInfo] = None
