#!/usr/bin/env python3
"""
Script de validación de datos para Rayuela.

Este script valida que los datos cumplan con el esquema requerido
antes de enviarlos al endpoint de ingesta masiva.
"""

import json
import csv
import argparse
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal, InvalidOperation


class DataValidator:
    """Validador de datos para ingesta masiva en Rayuela."""

    # Tipos de interacción válidos
    VALID_INTERACTION_TYPES = {
        "VIEW",
        "LIKE",
        "PURCHASE",
        "CART",
        "RATING",
        "WISHLIST",
        "CLICK",
        "SEARCH",
        "FAVORITE",
    }

    def __init__(self):
        self.errors = []
        self.warnings = []

    def validate_user(self, user: Dict[str, Any], index: int) -> bool:
        """Valida un usuario individual."""
        valid = True

        # Verificar external_id
        if "external_id" not in user:
            self.errors.append(f"Usuario {index}: Campo 'external_id' requerido")
            valid = False
        elif not user["external_id"] or not str(user["external_id"]).strip():
            self.errors.append(f"Usuario {index}: 'external_id' no puede estar vacío")
            valid = False
        elif len(str(user["external_id"])) > 255:
            self.errors.append(
                f"Usuario {index}: 'external_id' no puede exceder 255 caracteres"
            )
            valid = False

        return valid

    def validate_product(self, product: Dict[str, Any], index: int) -> bool:
        """Valida un producto individual."""
        valid = True

        # Verificar campos requeridos
        required_fields = ["name", "price", "category"]
        for field in required_fields:
            if field not in product:
                self.errors.append(f"Producto {index}: Campo '{field}' requerido")
                valid = False
                continue

            if not product[field] or not str(product[field]).strip():
                self.errors.append(f"Producto {index}: '{field}' no puede estar vacío")
                valid = False

        # Validar name
        if "name" in product and len(str(product["name"])) > 255:
            self.errors.append(
                f"Producto {index}: 'name' no puede exceder 255 caracteres"
            )
            valid = False

        # Validar price
        if "price" in product:
            try:
                price = Decimal(str(product["price"]))
                if price <= 0:
                    self.errors.append(
                        f"Producto {index}: 'price' debe ser mayor que 0"
                    )
                    valid = False
                elif price.as_tuple().exponent < -2:
                    self.errors.append(
                        f"Producto {index}: 'price' no puede tener más de 2 decimales"
                    )
                    valid = False
                elif len(str(price).replace(".", "")) > 10:
                    self.errors.append(
                        f"Producto {index}: 'price' no puede tener más de 10 dígitos en total"
                    )
                    valid = False
            except (InvalidOperation, ValueError):
                self.errors.append(
                    f"Producto {index}: 'price' debe ser un número válido"
                )
                valid = False

        # Validar category
        if "category" in product and len(str(product["category"])) > 100:
            self.errors.append(
                f"Producto {index}: 'category' no puede exceder 100 caracteres"
            )
            valid = False

        # Validar campos opcionales
        if "average_rating" in product:
            try:
                rating = float(product["average_rating"])
                if rating < 0.0 or rating > 5.0:
                    self.errors.append(
                        f"Producto {index}: 'average_rating' debe estar entre 0.0 y 5.0"
                    )
                    valid = False
            except (ValueError, TypeError):
                self.errors.append(
                    f"Producto {index}: 'average_rating' debe ser un número"
                )
                valid = False

        if "num_ratings" in product:
            try:
                num_ratings = int(product["num_ratings"])
                if num_ratings < 0:
                    self.errors.append(
                        f"Producto {index}: 'num_ratings' debe ser mayor o igual a 0"
                    )
                    valid = False
            except (ValueError, TypeError):
                self.errors.append(
                    f"Producto {index}: 'num_ratings' debe ser un entero"
                )
                valid = False

        if "inventory_count" in product:
            try:
                inventory = int(product["inventory_count"])
                if inventory < 0:
                    self.errors.append(
                        f"Producto {index}: 'inventory_count' debe ser mayor o igual a 0"
                    )
                    valid = False
            except (ValueError, TypeError):
                self.errors.append(
                    f"Producto {index}: 'inventory_count' debe ser un entero"
                )
                valid = False

        return valid

    def validate_interaction(self, interaction: Dict[str, Any], index: int) -> bool:
        """Valida una interacción individual."""
        valid = True

        # Verificar campos requeridos
        required_fields = ["user_id", "product_id", "interaction_type", "value"]
        for field in required_fields:
            if field not in interaction:
                self.errors.append(f"Interacción {index}: Campo '{field}' requerido")
                valid = False

        # Validar user_id
        if "user_id" in interaction:
            try:
                user_id = int(interaction["user_id"])
                if user_id <= 0:
                    self.errors.append(
                        f"Interacción {index}: 'user_id' debe ser un entero positivo"
                    )
                    valid = False
            except (ValueError, TypeError):
                self.errors.append(f"Interacción {index}: 'user_id' debe ser un entero")
                valid = False

        # Validar product_id
        if "product_id" in interaction:
            try:
                product_id = int(interaction["product_id"])
                if product_id <= 0:
                    self.errors.append(
                        f"Interacción {index}: 'product_id' debe ser un entero positivo"
                    )
                    valid = False
            except (ValueError, TypeError):
                self.errors.append(
                    f"Interacción {index}: 'product_id' debe ser un entero"
                )
                valid = False

        # Validar interaction_type
        if "interaction_type" in interaction:
            if interaction["interaction_type"] not in self.VALID_INTERACTION_TYPES:
                self.errors.append(
                    f"Interacción {index}: 'interaction_type' debe ser uno de: "
                    f"{', '.join(sorted(self.VALID_INTERACTION_TYPES))}"
                )
                valid = False

        # Validar value
        if "value" in interaction:
            try:
                value = float(interaction["value"])
                interaction_type = interaction.get("interaction_type", "")

                if interaction_type == "RATING":
                    if value < 1.0 or value > 5.0:
                        self.errors.append(
                            f"Interacción {index}: Para RATING, 'value' debe estar entre 1.0 y 5.0"
                        )
                        valid = False
                elif value < 0:
                    self.errors.append(
                        f"Interacción {index}: 'value' debe ser mayor o igual a 0"
                    )
                    valid = False
            except (ValueError, TypeError):
                self.errors.append(f"Interacción {index}: 'value' debe ser un número")
                valid = False

        return valid

    def validate_json_data(self, data: Dict[str, Any]) -> bool:
        """Valida datos en formato JSON."""
        valid = True

        # Verificar que al menos un tipo de entidad esté presente
        if not any(key in data for key in ["users", "products", "interactions"]):
            self.errors.append(
                "Debe incluir al menos uno de: 'users', 'products', 'interactions'"
            )
            return False

        # Validar usuarios
        if "users" in data:
            if not isinstance(data["users"], list):
                self.errors.append("'users' debe ser una lista")
                valid = False
            else:
                for i, user in enumerate(data["users"]):
                    if not self.validate_user(user, i + 1):
                        valid = False

        # Validar productos
        if "products" in data:
            if not isinstance(data["products"], list):
                self.errors.append("'products' debe ser una lista")
                valid = False
            else:
                for i, product in enumerate(data["products"]):
                    if not self.validate_product(product, i + 1):
                        valid = False

        # Validar interacciones
        if "interactions" in data:
            if not isinstance(data["interactions"], list):
                self.errors.append("'interactions' debe ser una lista")
                valid = False
            else:
                for i, interaction in enumerate(data["interactions"]):
                    if not self.validate_interaction(interaction, i + 1):
                        valid = False

        return valid

    def validate_csv_file(self, file_path: str, entity_type: str) -> bool:
        """Valida un archivo CSV."""
        valid = True

        try:
            with open(file_path, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)

                for i, row in enumerate(reader, 1):
                    if entity_type == "users":
                        if not self.validate_user(row, i):
                            valid = False
                    elif entity_type == "products":
                        if not self.validate_product(row, i):
                            valid = False
                    elif entity_type == "interactions":
                        if not self.validate_interaction(row, i):
                            valid = False

        except FileNotFoundError:
            self.errors.append(f"Archivo no encontrado: {file_path}")
            valid = False
        except Exception as e:
            self.errors.append(f"Error leyendo archivo {file_path}: {str(e)}")
            valid = False

        return valid

    def check_data_consistency(self, data: Dict[str, Any]) -> None:
        """Verifica la consistencia de los datos."""
        # Verificar duplicados en external_id de usuarios
        if "users" in data:
            external_ids = []
            for i, user in enumerate(data["users"]):
                if "external_id" in user:
                    if user["external_id"] in external_ids:
                        self.warnings.append(
                            f"Usuario {i + 1}: external_id duplicado '{user['external_id']}'"
                        )
                    else:
                        external_ids.append(user["external_id"])

        # Verificar que las interacciones referencien IDs válidos
        if "interactions" in data:
            max_user_id = len(data.get("users", []))
            max_product_id = len(data.get("products", []))

            for i, interaction in enumerate(data["interactions"]):
                if "user_id" in interaction:
                    try:
                        user_id = int(interaction["user_id"])
                        if user_id > max_user_id:
                            self.warnings.append(
                                f"Interacción {i + 1}: user_id {user_id} puede no existir "
                                f"(solo hay {max_user_id} usuarios en el lote)"
                            )
                    except (ValueError, TypeError):
                        pass  # Ya validado en validate_interaction

                if "product_id" in interaction:
                    try:
                        product_id = int(interaction["product_id"])
                        if product_id > max_product_id:
                            self.warnings.append(
                                f"Interacción {i + 1}: product_id {product_id} puede no existir "
                                f"(solo hay {max_product_id} productos en el lote)"
                            )
                    except (ValueError, TypeError):
                        pass  # Ya validado en validate_interaction

    def get_summary(self) -> Dict[str, Any]:
        """Obtiene un resumen de la validación."""
        return {
            "valid": len(self.errors) == 0,
            "error_count": len(self.errors),
            "warning_count": len(self.warnings),
            "errors": self.errors,
            "warnings": self.warnings,
        }


def load_json_file(file_path: str) -> Dict[str, Any]:
    """Carga un archivo JSON."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"❌ Error: Archivo no encontrado: {file_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"❌ Error: JSON inválido en {file_path}: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error leyendo archivo {file_path}: {e}")
        sys.exit(1)


def load_csv_files(
    users_file: Optional[str],
    products_file: Optional[str],
    interactions_file: Optional[str],
) -> Dict[str, Any]:
    """Carga archivos CSV y los convierte a formato JSON."""
    data = {}

    if users_file:
        try:
            with open(users_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)
                data["users"] = list(reader)
        except Exception as e:
            print(f"❌ Error leyendo archivo de usuarios {users_file}: {e}")
            sys.exit(1)

    if products_file:
        try:
            with open(products_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)
                products = []
                for row in reader:
                    # Convertir tipos de datos
                    if "price" in row and row["price"]:
                        row["price"] = float(row["price"])
                    if "average_rating" in row and row["average_rating"]:
                        row["average_rating"] = float(row["average_rating"])
                    if "num_ratings" in row and row["num_ratings"]:
                        row["num_ratings"] = int(row["num_ratings"])
                    if "inventory_count" in row and row["inventory_count"]:
                        row["inventory_count"] = int(row["inventory_count"])
                    products.append(row)
                data["products"] = products
        except Exception as e:
            print(f"❌ Error leyendo archivo de productos {products_file}: {e}")
            sys.exit(1)

    if interactions_file:
        try:
            with open(interactions_file, "r", encoding="utf-8") as f:
                reader = csv.DictReader(f)
                interactions = []
                for row in reader:
                    # Convertir tipos de datos
                    if "user_id" in row and row["user_id"]:
                        row["user_id"] = int(row["user_id"])
                    if "product_id" in row and row["product_id"]:
                        row["product_id"] = int(row["product_id"])
                    if "value" in row and row["value"]:
                        row["value"] = float(row["value"])
                    interactions.append(row)
                data["interactions"] = interactions
        except Exception as e:
            print(f"❌ Error leyendo archivo de interacciones {interactions_file}: {e}")
            sys.exit(1)

    return data


def print_summary(summary: Dict[str, Any], data: Dict[str, Any]) -> None:
    """Imprime un resumen de la validación."""
    print("\n" + "=" * 60)
    print("📊 RESUMEN DE VALIDACIÓN")
    print("=" * 60)

    # Estadísticas de datos
    users_count = len(data.get("users", []))
    products_count = len(data.get("products", []))
    interactions_count = len(data.get("interactions", []))

    print(f"📈 Estadísticas:")
    print(f"   • Usuarios: {users_count:,}")
    print(f"   • Productos: {products_count:,}")
    print(f"   • Interacciones: {interactions_count:,}")
    print(
        f"   • Total registros: {users_count + products_count + interactions_count:,}"
    )

    # Resultado de validación
    if summary["valid"]:
        print(f"\n✅ VALIDACIÓN EXITOSA")
        print(f"   • Todos los datos cumplen con el esquema requerido")
    else:
        print(f"\n❌ VALIDACIÓN FALLIDA")
        print(f"   • {summary['error_count']} errores encontrados")

    if summary["warning_count"] > 0:
        print(f"   • ⚠️  {summary['warning_count']} advertencias")

    # Mostrar errores
    if summary["errors"]:
        print(f"\n🚨 ERRORES ({len(summary['errors'])}):")
        for error in summary["errors"][:10]:  # Mostrar solo los primeros 10
            print(f"   • {error}")
        if len(summary["errors"]) > 10:
            print(f"   • ... y {len(summary['errors']) - 10} errores más")

    # Mostrar advertencias
    if summary["warnings"]:
        print(f"\n⚠️  ADVERTENCIAS ({len(summary['warnings'])}):")
        for warning in summary["warnings"][:5]:  # Mostrar solo las primeras 5
            print(f"   • {warning}")
        if len(summary["warnings"]) > 5:
            print(f"   • ... y {len(summary['warnings']) - 5} advertencias más")

    print("\n" + "=" * 60)


def main():
    parser = argparse.ArgumentParser(
        description="Valida datos para ingesta masiva en Rayuela",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Ejemplos de uso:

  # Validar archivo JSON
  python validate_data.py --json batch_payload.json

  # Validar archivos CSV
  python validate_data.py --users users.csv --products products.csv --interactions interactions.csv

  # Validar solo productos
  python validate_data.py --products products.csv

  # Modo silencioso (solo código de salida)
  python validate_data.py --json batch_payload.json --quiet
        """,
    )

    # Opciones de entrada
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument("--json", help="Archivo JSON con datos de ingesta")

    csv_group = parser.add_argument_group("Archivos CSV")
    csv_group.add_argument("--users", help="Archivo CSV de usuarios")
    csv_group.add_argument("--products", help="Archivo CSV de productos")
    csv_group.add_argument("--interactions", help="Archivo CSV de interacciones")

    # Opciones de salida
    parser.add_argument(
        "--quiet",
        "-q",
        action="store_true",
        help="Modo silencioso (solo código de salida)",
    )
    parser.add_argument("--output", "-o", help="Guardar reporte en archivo")

    args = parser.parse_args()

    # Validar argumentos
    if not args.json and not any([args.users, args.products, args.interactions]):
        parser.error(
            "Debe especificar --json o al menos uno de --users, --products, --interactions"
        )

    # Cargar datos
    if args.json:
        data = load_json_file(args.json)
    else:
        data = load_csv_files(args.users, args.products, args.interactions)

    # Validar datos
    validator = DataValidator()
    is_valid = validator.validate_json_data(data)
    validator.check_data_consistency(data)

    summary = validator.get_summary()

    # Mostrar resultados
    if not args.quiet:
        print_summary(summary, data)

        if summary["valid"]:
            print("🎉 Los datos están listos para ser enviados a Rayuela!")
        else:
            print("🔧 Corrija los errores antes de enviar los datos.")

    # Guardar reporte si se especifica
    if args.output:
        report = {
            "timestamp": str(datetime.now()),
            "summary": summary,
            "data_stats": {
                "users": len(data.get("users", [])),
                "products": len(data.get("products", [])),
                "interactions": len(data.get("interactions", [])),
            },
        }

        with open(args.output, "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)

        if not args.quiet:
            print(f"📄 Reporte guardado en: {args.output}")

    # Código de salida
    sys.exit(0 if summary["valid"] else 1)


if __name__ == "__main__":
    main()
