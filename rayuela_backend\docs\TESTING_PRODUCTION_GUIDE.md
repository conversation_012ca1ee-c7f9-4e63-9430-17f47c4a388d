# Guía de Tests para Producción - Rayuela

## 📋 Resumen Ejecutivo

Esta guía proporciona instrucciones completas para mantener una suite de tests robusta y actualizada para el despliegue seguro de Rayuela en Google Cloud Platform (GCP).

## 🎯 Estado Actual de Tests

### ✅ Logros Implementados
- **59 archivos de test** organizados por tipo (unitarios, integración, E2E, carga)
- **55+ funciones de test** identificadas y catalogadas
- **Sistema de diagnóstico automático** para detectar tests obsoletos
- **Pipeline de reparación automática** para corregir problemas comunes
- **Configuración Docker** para entorno de tests consistente
- **Pipeline CI/CD optimizado** para Google Cloud Build

### 🔧 Reparaciones Aplicadas
- Corrección de patrones obsoletos (pytest fixtures, asyncio, TestClient)
- Actualización de imports incorrectos
- Adición de marcadores `@pytest.mark.asyncio` faltantes
- Creación de archivos de utilidades faltantes
- Corrección de errores de sintaxis e indentación

## 🚀 Ejecutar Tests

### 1. Ejecución Local con Docker (Recomendado)

```bash
# Cambiar al directorio del backend
cd rayuela_backend

# Ejecutar suite completa de tests de producción
python scripts/run_tests_production.py

# O ejecutar tipos específicos de tests
docker-compose -f docker-compose.test.yml run --rm test-runner pytest tests/unit/ -v
docker-compose -f docker-compose.test.yml run --rm test-runner pytest tests/integration/ -v
```

### 2. Diagnóstico y Mantenimiento

```bash
# Diagnosticar estado de tests
python scripts/test_health_checker.py

# Reparar problemas automáticamente
python scripts/test_fixer.py

# Corregir errores de sintaxis específicos
python scripts/fix_syntax_errors.py
```

### 3. CI/CD en Google Cloud

```bash
# Ejecutar pipeline de tests únicamente
gcloud builds submit --config=rayuela_backend/cloudbuild-tests.yaml

# Pipeline completo (tests + despliegue)
gcloud builds submit --config=cloudbuild.yaml
```

## 📊 Métricas de Calidad

### Objetivos de Cobertura
- **Mínimo aceptable**: 70%
- **Objetivo recomendado**: 80%+
- **Crítico para producción**: 75%+

### Tests Críticos que DEBEN Pasar
1. **Tests de seguridad** (`tests/unit/core/security/`)
2. **Tests de multi-tenancy** (`tests/integration/test_multi_tenancy_security.py`)
3. **Tests unitarios core** (`tests/unit/`)
4. **Tests de autenticación** (`tests/integration/test_auth_flow.py`)

## 🔄 Proceso de Mantenimiento Continuo

### Después de Cada Refactorización

1. **Ejecutar diagnóstico**:
   ```bash
   python scripts/test_health_checker.py
   ```

2. **Aplicar reparaciones automáticas**:
   ```bash
   python scripts/test_fixer.py
   ```

3. **Revisar archivos con errores manuales**:
   - Verificar errores de indentación no corregidos
   - Actualizar tests que usan APIs cambiadas
   - Corregir mocks obsoletos

4. **Ejecutar suite completa**:
   ```bash
   python scripts/run_tests_production.py
   ```

### Antes de Cada Despliegue

1. **Verificar estado de tests críticos**:
   ```bash
   # Tests de seguridad
   docker-compose -f docker-compose.test.yml run --rm test-runner \
     pytest tests/unit/core/security/ -v

   # Tests de multi-tenancy
   docker-compose -f docker-compose.test.yml run --rm test-runner \
     pytest tests/integration/test_multi_tenancy_security.py -v
   ```

2. **Ejecutar pipeline completo localmente**:
   ```bash
   python scripts/run_tests_production.py
   ```

3. **Revisar métricas de cobertura**:
   - Debe ser > 70% para despliegue
   - Identificar áreas con cobertura < 50%

## 🛠️ Estructura de Tests Actualizada

```
tests/
├── unit/                          # Tests unitarios (aislados)
│   ├── core/security/            # Tests de seguridad críticos
│   ├── ml_pipeline/              # Tests de ML
│   ├── api/                      # Tests de endpoints
│   ├── db/                       # Tests de base de datos
│   └── services/                 # Tests de servicios
├── integration/                   # Tests de integración
│   ├── test_multi_tenancy*.py    # Multi-tenancy crítico
│   ├── test_auth_flow.py         # Autenticación crítica
│   ├── test_*_pipeline.py        # Pipelines ML
│   └── utils/                    # Utilidades compartidas
├── e2e/                          # Tests end-to-end
├── load/                         # Tests de carga
└── conftest.py                   # Configuración global
```

## 🔍 Herramientas de Diagnóstico

### 1. Test Health Checker
- **Propósito**: Detecta tests obsoletos, dependencias faltantes, errores de sintaxis
- **Uso**: `python scripts/test_health_checker.py`
- **Salida**: `test_health_report.json`

### 2. Test Fixer
- **Propósito**: Repara automáticamente problemas comunes
- **Uso**: `python scripts/test_fixer.py`
- **Crea backups**: `*.backup` antes de modificar archivos

### 3. Production Test Runner
- **Propósito**: Ejecuta suite completa preparada para producción
- **Uso**: `python scripts/run_tests_production.py`
- **Salida**: `test_results_production.json`

## 🚨 Problemas Conocidos y Soluciones

### 1. Errores de Sintaxis Manuales
**Archivos afectados**: 
- `tests/integration/test_data_ingestion.py`
- `tests/integration/test_multi_tenancy.py`
- `tests/middleware/test_usage_meter_middleware.py`

**Solución**: Revisar manualmente la indentación después de `:` en estos archivos.

### 2. Dependencias de TensorFlow en Windows
**Problema**: TensorFlow no funciona nativamente en algunos entornos Windows.
**Solución**: Usar Docker obligatoriamente:
```bash
docker-compose -f docker-compose.test.yml run --rm test-runner pytest
```

### 3. Imports Obsoletos
**Problema**: Algunos tests usan imports que ya no existen.
**Solución Automática**: El `test_fixer.py` corrige estos automáticamente.

## 📋 Checklist Pre-Despliegue

### Tests Críticos ✅
- [ ] Tests de seguridad pasan (100%)
- [ ] Tests de multi-tenancy pasan (100%)
- [ ] Tests unitarios pasan (>95%)
- [ ] Tests de integración pasan (>90%)

### Métricas de Calidad ✅
- [ ] Cobertura de código > 70%
- [ ] No hay vulnerabilidades críticas (bandit)
- [ ] No hay dependencias vulnerables (safety)
- [ ] Linting pasa sin errores críticos

### Configuración CI/CD ✅
- [ ] Pipeline de tests configurado en Cloud Build
- [ ] Secretos configurados en Secret Manager
- [ ] Artefactos de test almacenados en Cloud Storage
- [ ] Triggers configurados para branches principales

## 🔧 Configuración de CI/CD en GCP

### 1. Cloud Build Triggers

```bash
# Crear trigger para tests en cada PR
gcloud builds triggers create github \
  --repo-name=rayuela \
  --repo-owner=tu-usuario \
  --pull-request-pattern=".*" \
  --build-config=rayuela_backend/cloudbuild-tests.yaml

# Crear trigger para despliegue en main
gcloud builds triggers create github \
  --repo-name=rayuela \
  --repo-owner=tu-usuario \
  --branch-pattern="main" \
  --build-config=cloudbuild.yaml
```

### 2. Secret Manager

```bash
# Configurar secretos necesarios
gcloud secrets create DB_PASSWORD --data-file=password.txt
gcloud secrets create SECRET_KEY --data-file=secret_key.txt

# Dar permisos a Cloud Build
gcloud projects add-iam-policy-binding PROJECT_ID \
  --member="<EMAIL>" \
  --role="roles/secretmanager.secretAccessor"
```

### 3. Storage para Artefactos

```bash
# Crear bucket para artefactos de test
gsutil mb gs://rayuela-test-artifacts

# Configurar lifecycle
gsutil lifecycle set lifecycle.json gs://rayuela-test-artifacts
```

## 🎯 Roadmap de Mejoras

### Corto Plazo (1-2 semanas)
- [ ] Corregir manualmente los 6 archivos con errores de sintaxis
- [ ] Aumentar cobertura de tests a 80%+
- [ ] Implementar tests de performance básicos

### Medio Plazo (1 mes)
- [ ] Automatizar generación de tests para nuevas features
- [ ] Implementar mutation testing
- [ ] Configurar alertas de cobertura en CI/CD

### Largo Plazo (3 meses)
- [ ] Tests de chaos engineering
- [ ] Tests de seguridad automatizados con OWASP ZAP
- [ ] Dashboard de métricas de calidad en tiempo real

## 📞 Contacto y Soporte

Para preguntas sobre tests o problemas en el pipeline:

1. **Revisar logs**: `test_health_report.json` y `test_results_production.json`
2. **Documentación**: Esta guía y `tests/README.md`
3. **Herramientas**: Scripts en `scripts/` para diagnóstico y reparación

---

✅ **Estado**: Preparado para producción con monitoreo continuo de calidad de tests. 