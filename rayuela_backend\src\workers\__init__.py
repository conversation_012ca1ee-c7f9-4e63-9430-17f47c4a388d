"""
Workers package initialization.
Este archivo asegura que todas las tareas de Celery se importen correctamente.
"""

# Importamos la aplicación de Celery
from .celery_app import celery_app

# Importamos todas las tareas para asegurarnos de que están registradas correctamente
from .celery_tasks import (
    cleanup_old_audit_logs,
    cleanup_old_interactions,
    cleanup_old_data_secure_task,  # Nueva tarea segura para limpieza de datos
    monitor_high_volume_tables,  # Asegurar que esta tarea se importa explícitamente
    train_model,
    train_model_for_job,
    process_batch_data,
)

# Importamos tareas de particionamiento
from .celery_tasks_partition import manage_partitions_task

# Importamos tareas de medición de uso
try:
    from src.tasks.subscription_tasks import (
        sync_api_call_counters,
        reset_monthly_counters,
        reset_monthly_api_calls,
        update_storage_usage,
    )
except ImportError:
    import logging
    logging.warning("No se pudieron importar las tareas de subscription_tasks.")

# Importamos la tarea de medición de almacenamiento desde el módulo correcto
try:
    from src.tasks.storage_meter_tasks import measure_storage_usage, get_storage_usage
except ImportError:
    import logging
    logging.warning("No se pudo importar el módulo storage_meter_tasks.")

__all__ = [
    'celery_app',
    'cleanup_old_audit_logs',
    'cleanup_old_interactions',
    'cleanup_old_data_secure_task',  # Nueva tarea segura para limpieza de datos
    'monitor_high_volume_tables',
    'train_model',
    'train_model_for_job',
    'process_batch_data',
    'manage_partitions_task',
    'measure_storage_usage',
    'get_storage_usage',
    'sync_api_call_counters',
    'reset_monthly_counters',
    'reset_monthly_api_calls',
    'update_storage_usage',
]