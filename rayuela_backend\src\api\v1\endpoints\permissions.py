from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List

from src.core.deps import (
    get_current_account,
    get_current_admin_user,
    get_permission_service,
)
from src.db import models, schemas
from src.db.session import get_db
from src.db.repositories.auth import PermissionRepository
from src.utils.base_logger import log_info, log_error

router = APIRouter()


@router.get("/", response_model=List[schemas.Permission])
async def list_permissions(
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    List all permissions available for the current account.
    
    Requires admin privileges.
    """
    try:
        permission_repo = PermissionRepository(db, account_id=current_account.account_id)
        permissions = await permission_repo.get_all()
        return permissions
    except Exception as e:
        log_error(f"Error listing permissions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error listing permissions"
        )


@router.get("/{permission_id}", response_model=schemas.Permission)
async def get_permission(
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    Get a specific permission by ID.
    
    Requires admin privileges.
    """
    try:
        permission_repo = PermissionRepository(db, account_id=current_account.account_id)
        permission = await permission_repo.get_by_id(permission_id)
        
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Permission with ID {permission_id} not found"
            )
            
        return permission
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting permission {permission_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting permission"
        )


@router.post("/", response_model=schemas.Permission)
async def create_permission(
    permission: schemas.PermissionCreate,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    Create a new permission.
    
    Requires admin privileges.
    """
    try:
        permission_repo = PermissionRepository(db, account_id=current_account.account_id)
        
        # Check if permission with same name already exists
        existing_permission = await permission_repo.get_by_name(permission.name)
        if existing_permission:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=f"Permission with name '{permission.name}' already exists"
            )
        
        # Create the permission
        db_permission = await permission_repo.create(permission)
        
        # Log the action
        log_info(f"Permission '{permission.name}' created by user {current_user.id}")
        
        return db_permission
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error creating permission: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating permission"
        )


@router.get("/{permission_id}/roles", response_model=List[schemas.Role])
async def get_roles_with_permission(
    permission_id: int,
    db: AsyncSession = Depends(get_db),
    current_account: models.Account = Depends(get_current_account),
    current_user: models.SystemUser = Depends(get_current_admin_user),
):
    """
    Get all roles that have a specific permission.
    
    Requires admin privileges.
    """
    try:
        permission_repo = PermissionRepository(db, account_id=current_account.account_id)
        
        # Check if permission exists
        existing_permission = await permission_repo.get_by_id(permission_id)
        if not existing_permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Permission with ID {permission_id} not found"
            )
        
        # Get roles with this permission
        roles = await permission_repo.get_roles_with_permission(permission_id)
        
        return roles
    except HTTPException:
        raise
    except Exception as e:
        log_error(f"Error getting roles with permission {permission_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error getting roles with permission"
        )
