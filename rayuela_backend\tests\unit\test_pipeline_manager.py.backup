import pytest
import pytest_asyncio
from unittest.mock import MagicMock, patch, AsyncMock
from datetime import datetime, timezone
from src.ml_pipeline.training_pipeline import TrainingPipeline
from src.core.exceptions import ResourceNotFoundError
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.ml_pipeline.evaluation import RecommendationEvaluator


class TestTrainingPipeline:
    """Tests para el pipeline de entrenamiento"""

    @pytest.fixture
    def training_pipeline(self):
        """Fixture para crear una instancia del pipeline de entrenamiento"""
        with patch(
            "src.ml_pipeline.training_pipeline.ModelArtifactManager"
        ) as mock_artifact_manager_class, patch(
            "src.ml_pipeline.training_pipeline.MetricsTracker"
        ) as mock_metrics_tracker_class, patch(
            "src.ml_pipeline.training_pipeline.RecommendationEvaluator"
        ) as mock_evaluator_class:
            # Configurar mocks
            mock_artifact_manager = MagicMock(spec=ModelArtifactManager)
            mock_metrics_tracker = MagicMock(spec=MetricsTracker)
            mock_evaluator = MagicMock(spec=RecommendationEvaluator)

            mock_artifact_manager_class.return_value = mock_artifact_manager
            mock_metrics_tracker_class.return_value = mock_metrics_tracker
            mock_evaluator_class.return_value = mock_evaluator

            pipeline = TrainingPipeline(
                artifact_manager=mock_artifact_manager,
                metrics_tracker=mock_metrics_tracker,
                evaluator=mock_evaluator
            )
            yield pipeline

    @pytest.mark.asyncio
    async def test_train_validates_data_requirements(self, training_pipeline):
        """Test que verifica que el método train valida los requisitos de datos"""
        # Mock para la sesión de BD
        db_session = AsyncMock()

        # Mock para _prepare_training_data que devuelve DataFrames vacíos
        import pandas as pd
        training_pipeline._prepare_training_data = AsyncMock()
        training_pipeline._prepare_training_data.return_value = (
            pd.DataFrame(), pd.DataFrame(), pd.DataFrame(), pd.DataFrame()
        )

        # Ejecutar con error esperado por datos insuficientes
        result = await training_pipeline.train(db_session, 1)

        # Verificar que se devuelve un error
        assert result["success"] is False
        assert "Datos de entrenamiento insuficientes" in result.get("error", "")

    @pytest.mark.asyncio
    async def test_train_handles_exceptions(self, training_pipeline):
        """Test que verifica que train maneja excepciones correctamente"""
        # Mock para la sesión de BD
        db_session = AsyncMock()

        # Mock para _prepare_training_data que lanza excepción
        training_pipeline._prepare_training_data = AsyncMock(
            side_effect=ValueError("Test error")
        )

        # Ejecutar con error esperado
        result = await training_pipeline.train(db_session, 1)

        # Verificar que se devuelve un error
        assert result["success"] is False
        assert "Test error" in result["error"]

    @pytest.mark.asyncio
    async def test_train_successful_execution(self, training_pipeline):
        """Test que verifica la ejecución exitosa de train"""
        # Mock para la sesión de BD
        db_session = AsyncMock()

        # Mocks para los métodos del pipeline
        import pandas as pd

        # DataFrames no vacíos para pasar validación
        train_df = pd.DataFrame({'user_id': [1, 2], 'item_id': [1, 2]})
        test_df = pd.DataFrame({'user_id': [3], 'item_id': [3]})
        interactions_df = pd.DataFrame({'user_id': [1, 2, 3], 'item_id': [1, 2, 3]})
        products_df = pd.DataFrame({'item_id': [1, 2, 3]})

        training_pipeline._prepare_training_data = AsyncMock()
        training_pipeline._prepare_training_data.return_value = (
            train_df, test_df, interactions_df, products_df
        )

        training_pipeline._train_models_parallel = AsyncMock()
        training_pipeline._train_models_parallel.return_value = (
            {"model": "collab_model"}, {"model": "content_model"}
        )

        training_pipeline._evaluate_models = AsyncMock()
        training_pipeline._evaluate_models.return_value = (
            [{"user_id": 1, "recommendations": []}], {"precision": 0.8}
        )

        # Mock para el persistor
        persistor_mock = MagicMock()
        persistor_mock.persist_artifacts_and_metrics = AsyncMock()
        persistor_mock.persist_artifacts_and_metrics.return_value = 1
        training_pipeline.artifact_persistor = persistor_mock

        # Mock para _finalize_training
        training_pipeline._finalize_training = AsyncMock()

        # Ejecutar
        result = await training_pipeline.train(db_session, 1)

        # Verificar resultado
        assert result["success"] is True
        assert result["metrics"]["precision"] == 0.8
        assert result["model_id"] == 1

    @pytest.mark.asyncio
    async def test_prepare_training_data(self, training_pipeline):
        """Test que verifica la preparación de datos de entrenamiento"""
        # Mock para la sesión de BD
        db_session = AsyncMock()

        # Mock para DataFetcher
        import pandas as pd
        with patch("src.ml_pipeline.training_pipeline.DataFetcher") as mock_fetcher_class:
            mock_fetcher = MagicMock()
            mock_fetcher.fetch_training_data = AsyncMock()
            mock_fetcher_class.return_value = mock_fetcher

            # DataFrames de prueba
            interactions_df = pd.DataFrame({
                'user_id': [1, 1, 2, 2, 3],
                'item_id': [101, 102, 101, 103, 102],
                'rating': [5.0, 4.0, 3.0, 5.0, 2.0]
            })
            products_df = pd.DataFrame({
                'item_id': [101, 102, 103],
                'name': ['Prod1', 'Prod2', 'Prod3']
            })

            mock_fetcher.fetch_training_data.return_value = (interactions_df, products_df)

            # Mock para DataPreparer
            with patch("src.ml_pipeline.training_pipeline.DataPreparer") as mock_preparer_class:
                mock_preparer = MagicMock()
                mock_preparer_class.return_value = mock_preparer

                # DataFrames de entrenamiento y prueba
                train_df = interactions_df.iloc[:4]
                test_df = interactions_df.iloc[4:]

                mock_preparer.prepare_data.return_value = (train_df, test_df)

                # Ejecutar
                result = await training_pipeline._prepare_training_data(db_session, 1)

                # Verificar resultados
                assert len(result) == 4
                train_result, test_result, interactions_result, products_result = result

                assert len(train_result) == 4
                assert len(test_result) == 1
                assert len(interactions_result) == 5
                assert len(products_result) == 3
