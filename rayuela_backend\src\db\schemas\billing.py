from pydantic import BaseModel, Field
from typing import Optional, Dict, Any, Literal


class CreateCheckoutSessionRequest(BaseModel):
    """Schema para crear una sesión de checkout."""
    price_id: str = Field(..., description="Price ID (Stripe o Mercado Pago)")
    success_url: Optional[str] = Field(None, description="URL de redirección en caso de éxito")
    cancel_url: Optional[str] = Field(None, description="URL de redirección en caso de cancelación")
    payment_gateway: Optional[str] = Field("mercadopago", description="Pasarela de pago a utilizar (stripe o mercadopago)")


class CreateCheckoutSessionResponse(BaseModel):
    """Schema para la respuesta de creación de sesión de checkout."""
    url: str = Field(..., description="URL de la sesión de checkout")
    session_id: Optional[str] = Field(None, description="ID de la sesión de checkout (solo para Stripe)")


class CreatePortalSessionRequest(BaseModel):
    """Schema para crear una sesión del portal de facturación."""
    return_url: Optional[str] = Field(None, description="URL de retorno después de usar el portal")
    payment_gateway: Optional[str] = Field("mercadopago", description="Pasarela de pago a utilizar (stripe o mercadopago)")


class CreatePortalSessionResponse(BaseModel):
    """Schema para la respuesta de creación de sesión del portal de facturación."""
    url: str = Field(..., description="URL de la sesión del portal de facturación")


class StripeWebhookEvent(BaseModel):
    """Schema para los eventos de webhook de Stripe."""
    id: str
    type: str
    data: Dict[str, Any]
    created: int
    object: str = "event"
    pending_webhooks: int
    request: Optional[Dict[str, Any]] = None
    api_version: Optional[str] = None
    account: Optional[str] = None
    livemode: bool = False


class MercadoPagoWebhookEvent(BaseModel):
    """Schema para los eventos de webhook de Mercado Pago."""
    action: str
    api_version: str
    data: Dict[str, Any]
    date_created: str
    id: int
    live_mode: bool
    type: str
    user_id: str
