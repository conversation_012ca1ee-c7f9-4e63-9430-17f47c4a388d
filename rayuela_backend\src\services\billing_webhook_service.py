"""
Service for handling billing webhook events from Mercado Pago.
"""
from typing import Dict, Any, Optional
from fastapi import HTT<PERSON>Ex<PERSON>, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timezone

from src.core.config import settings
from src.db.repositories import AccountRepository, SubscriptionRepository
from src.db.enums import SubscriptionPlan, PLAN_LIMITS
from src.utils.base_logger import log_info, log_error, log_warning
from src.services.mercadopago_service import MercadoPagoService
from src.db import models


class BillingWebhookService:
    """
    Service for handling billing webhook events from Mercado Pago.
    This service centralizes the logic for processing webhook events from
    Mercado Pago payment gateway.
    """

    def __init__(self, db: AsyncSession):
        """Initialize the service with a database session."""
        self.db = db
        self.mercadopago_service = MercadoPagoService()

    async def handle_mercadopago_webhook(self, event_type: str, payload: Dict[str, Any]) -> None:
        """
        Handle Mercado Pago webhook events.

        Args:
            event_type: Type of Mercado Pago event
            payload: Data from the Mercado Pago event
        """
        try:
            if event_type == "payment":
                await self._handle_mercadopago_payment(payload)
            elif event_type == "subscription":
                await self._handle_mercadopago_subscription(payload)
            else:
                log_info(f"Unhandled Mercado Pago event type: {event_type}")
        except Exception as e:
            log_error(f"Error handling Mercado Pago webhook: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error handling Mercado Pago webhook: {str(e)}"
            )

    async def _handle_mercadopago_payment(self, payload: Dict[str, Any]) -> None:
        """
        Handle Mercado Pago payment events.

        Args:
            payload: Data from the Mercado Pago payment event
        """
        payment_id = payload.get("data", {}).get("id")
        if not payment_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid payment data"
            )

        # Get payment details
        payment_data = self.mercadopago_service.sdk.payment().get(payment_id)
        payment = payment_data.get("response", {})

        # Process payment based on status
        if payment.get("status") == "approved":
            await self._handle_mercadopago_payment_approved(payment)
        elif payment.get("status") in ["rejected", "cancelled"]:
            await self._handle_mercadopago_payment_failed(payment)

    async def _handle_mercadopago_subscription(self, payload: Dict[str, Any]) -> None:
        """
        Handle Mercado Pago subscription events.

        Args:
            payload: Data from the Mercado Pago subscription event
        """
        subscription_id = payload.get("data", {}).get("id")
        if not subscription_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid subscription data"
            )

        # Get subscription details
        subscription_data = self.mercadopago_service.sdk.preapproval().get(subscription_id)
        subscription = subscription_data.get("response", {})

        # Process subscription based on status
        if subscription.get("status") == "authorized":
            await self._handle_mercadopago_subscription_created(subscription)
        elif subscription.get("status") == "paused":
            await self._handle_mercadopago_subscription_paused(subscription)
        elif subscription.get("status") == "cancelled":
            await self._handle_mercadopago_subscription_cancelled(subscription)

    async def _handle_mercadopago_payment_approved(self, payment: Dict[str, Any]) -> None:
        """
        Handle an approved payment from Mercado Pago.

        Args:
            payment: Payment data from Mercado Pago
        """
        try:
            # Get payment metadata
            metadata = payment.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago payment metadata")
                return

            # Get price ID and subscription ID
            price_id = metadata.get("price_id")
            subscription_id = payment.get("subscription_id")

            if price_id:
                # Determine plan type
                plan_type = self.mercadopago_service._get_plan_from_price_id(price_id)

                # Prepare subscription data
                subscription_data = {
                    "plan_type": plan_type,
                    "api_calls_limit": PLAN_LIMITS.get(plan_type, {}).get("api_calls_limit", 0),
                    "storage_limit": PLAN_LIMITS.get(plan_type, {}).get("storage_limit", 0),
                    "mercadopago_subscription_id": subscription_id,
                    "mercadopago_price_id": price_id,
                    "payment_gateway": "mercadopago",
                    "is_active": True,
                }

                # Create or update subscription
                subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))

                # Check if subscription already exists
                existing_subscription = await subscription_repo.get_by_account(int(account_id))

                if existing_subscription:
                    # Update existing subscription
                    for key, value in subscription_data.items():
                        setattr(existing_subscription, key, value)
                    await self.db.commit()
                else:
                    # Create new subscription
                    subscription_data["account_id"] = int(account_id)
                    new_subscription = models.Subscription(**subscription_data)
                    self.db.add(new_subscription)
                    await self.db.commit()

                # We don't need to invalidate cache explicitly
                # The LimitService will read from the database on next request

                log_info(f"Subscription created/updated for account {account_id} from Mercado Pago payment")
            else:
                log_warning("No price_id found in Mercado Pago payment metadata")

        except Exception as e:
            log_error(f"Error handling Mercado Pago payment approved: {str(e)}")
            raise

    async def _handle_mercadopago_payment_failed(self, payment: Dict[str, Any]) -> None:
        """
        Handle a failed payment from Mercado Pago.

        Args:
            payment: Payment data from Mercado Pago
        """
        try:
            # Get payment metadata
            metadata = payment.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago payment metadata")
                return

            # Log the failed payment
            log_warning(f"Payment failed for account {account_id} in Mercado Pago")

            # We don't modify the subscription yet, just log the event
            # Mercado Pago will retry the payment

        except Exception as e:
            log_error(f"Error handling Mercado Pago payment failed: {str(e)}")
            raise

    async def _handle_mercadopago_subscription_created(self, subscription: Dict[str, Any]) -> None:
        """
        Handle a subscription created in Mercado Pago.

        Args:
            subscription: Subscription data from Mercado Pago
        """
        try:
            # Get subscription metadata
            metadata = subscription.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago subscription metadata")
                return

            # Get price ID
            price_id = metadata.get("price_id")
            if not price_id:
                log_warning("No price_id found in Mercado Pago subscription metadata")
                return

            # Determine plan type
            plan_type = self.mercadopago_service._get_plan_from_price_id(price_id)

            # Prepare subscription data
            subscription_data = {
                "plan_type": plan_type,
                "api_calls_limit": PLAN_LIMITS.get(plan_type, {}).get("api_calls_limit", 0),
                "storage_limit": PLAN_LIMITS.get(plan_type, {}).get("storage_limit", 0),
                "mercadopago_subscription_id": subscription["id"],
                "mercadopago_price_id": price_id,
                "payment_gateway": "mercadopago",
                "is_active": True,
            }

            # Create or update subscription
            subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))

            # Check if subscription already exists
            existing_subscription = await subscription_repo.get_by_account(int(account_id))

            if existing_subscription:
                # Update existing subscription
                for key, value in subscription_data.items():
                    setattr(existing_subscription, key, value)
                await self.db.commit()
            else:
                # Create new subscription
                subscription_data["account_id"] = int(account_id)
                new_subscription = models.Subscription(**subscription_data)
                self.db.add(new_subscription)
                await self.db.commit()

            # We don't need to invalidate cache explicitly
            # The LimitService will read from the database on next request

            log_info(f"Subscription created for account {account_id} from Mercado Pago")

        except Exception as e:
            log_error(f"Error handling Mercado Pago subscription created: {str(e)}")
            raise

    async def _handle_mercadopago_subscription_paused(self, subscription: Dict[str, Any]) -> None:
        """
        Handle a subscription paused in Mercado Pago.

        Args:
            subscription: Subscription data from Mercado Pago
        """
        try:
            # Get subscription metadata
            metadata = subscription.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago subscription metadata")
                return

            # Find subscription in database
            subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))

            # Get subscription by account ID
            db_subscription = await subscription_repo.get_by_account(int(account_id))

            if not db_subscription:
                log_warning(f"No subscription found for account {account_id}")
                return

            # Update subscription
            db_subscription.is_active = False
            await self.db.commit()

            # We don't need to invalidate cache explicitly
            # The LimitService will read from the database on next request

            log_info(f"Subscription paused for account {account_id} from Mercado Pago")

        except Exception as e:
            log_error(f"Error handling Mercado Pago subscription paused: {str(e)}")
            raise

    async def _handle_mercadopago_subscription_cancelled(self, subscription: Dict[str, Any]) -> None:
        """
        Handle a subscription cancelled in Mercado Pago.

        Args:
            subscription: Subscription data from Mercado Pago
        """
        try:
            # Get subscription metadata
            metadata = subscription.get("metadata", {})
            account_id = metadata.get("account_id")

            if not account_id:
                log_warning("No account_id found in Mercado Pago subscription metadata")
                return

            # Find subscription in database
            subscription_repo = SubscriptionRepository(self.db, account_id=int(account_id))

            # Get subscription by account ID
            db_subscription = await subscription_repo.get_by_account(int(account_id))

            if not db_subscription:
                log_warning(f"No subscription found for account {account_id}")
                return

            # Update subscription
            db_subscription.is_active = False
            db_subscription.expires_at = datetime.now(timezone.utc)
            await self.db.commit()

            # We don't need to invalidate cache explicitly
            # The LimitService will read from the database on next request

            log_info(f"Subscription cancelled for account {account_id} from Mercado Pago")

        except Exception as e:
            log_error(f"Error handling Mercado Pago subscription cancelled: {str(e)}")
            raise


