from fastapi import APIRouter, Depends, HTTPException, Request, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.core.config import settings
from src.core.deps import get_current_account, get_current_active_user
from src.db import schemas
from src.db.session import get_db
from src.utils.base_logger import log_info, log_error
from src.utils.mercadopago_utils import verify_mercadopago_signature
from src.db.enums import SubscriptionPlan
from src.services.subscription_service import SubscriptionService
from src.services.mercadopago_service import MercadoPagoService

# Inicializar servicio de Mercado Pago
mercadopago_service = MercadoPagoService()

router = APIRouter()


@router.post("/create-checkout-session", response_model=schemas.billing.CreateCheckoutSessionResponse)
async def create_checkout_session(
    request: schemas.billing.CreateCheckoutSessionRequest,
    account: schemas.AccountResponse = Depends(get_current_account),
    user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Crea una sesión de checkout para suscribirse o cambiar de plan.

    Requiere autenticación con API Key y JWT.
    """
    try:
        # Crear sesión de checkout con Mercado Pago
        checkout_data = await mercadopago_service.create_checkout_session(
            price_id=request.price_id,
            account_id=str(account.account_id),
            user_id=str(user.id),
            account_email=account.email,
            success_url=request.success_url,
            cancel_url=request.cancel_url
        )

        log_info(f"Mercado Pago checkout session created for account {account.account_id}, user {user.id}, price {request.price_id}")
        return {"url": checkout_data["url"]}

    except Exception as e:
        log_error(f"Error creating checkout session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating checkout session"
        )


@router.post("/create-portal-session", response_model=schemas.billing.CreatePortalSessionResponse)
async def create_portal_session(
    request: schemas.billing.CreatePortalSessionRequest = None,
    account: schemas.AccountResponse = Depends(get_current_account),
    user = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Crea una sesión del portal de facturación para gestionar la suscripción.

    Requiere autenticación con API Key y JWT.
    """
    try:
        # Obtener servicio de suscripción
        subscription_service = SubscriptionService(db)

        # Obtener suscripción activa
        subscription = await subscription_service.get_active_subscription(
            str(account.account_id)
        )
        if not subscription:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active subscription found"
            )

        # Crear sesión del portal con Mercado Pago
        if not subscription.mercadopago_subscription_id and not account.mercadopago_customer_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No Mercado Pago subscription found"
            )

        portal_data = await mercadopago_service.create_portal_session(
            customer_id=account.mercadopago_customer_id or "",
            return_url=request.return_url if request and request.return_url else f"{settings.FRONTEND_URL}/billing"
        )

        log_info(f"Mercado Pago portal session created for account {account.account_id}, user {user.id}")
        return {"url": portal_data["url"]}

    except Exception as e:
        log_error(f"Error creating portal session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error creating portal session"
        )





@router.post("/webhook/mercadopago", status_code=status.HTTP_200_OK)
async def mercadopago_webhook(
    request: Request,
    db: AsyncSession = Depends(get_db),
):
    """
    Webhook para recibir eventos de Mercado Pago.

    No requiere autenticación, pero verifica la firma de Mercado Pago.
    """
    try:
        # Verificar la firma del webhook
        await verify_mercadopago_signature(request)

        # Obtener payload
        payload = await request.json()

        # Validar que el payload tenga la estructura esperada
        if not isinstance(payload, dict) or "type" not in payload:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid payload"
            )

        # Obtener servicio de webhook
        from src.services.billing_webhook_service import BillingWebhookService
        webhook_service = BillingWebhookService(db)

        # Manejar evento según su tipo
        event_type = payload["type"]

        # Procesar el evento con el servicio de webhook
        await webhook_service.handle_mercadopago_webhook(event_type, payload)

        log_info(f"Mercado Pago webhook processed: {event_type}")
        return {"status": "success"}

    except Exception as e:
        log_error(f"Error handling webhook: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error handling webhook"
        )




def get_plan_from_price_id(price_id: str) -> SubscriptionPlan:
    """
    Determina el plan de suscripción basado en el price_id de Mercado Pago.

    Args:
        price_id: ID del precio de Mercado Pago

    Returns:
        Plan de suscripción correspondiente
    """
    # Verificar si es un price_id de Mercado Pago
    for plan_type, mp_price_id in settings.MERCADOPAGO_PRICE_IDS.items():
        if price_id == mp_price_id:
            return SubscriptionPlan[plan_type]

    # Si no se encuentra, devolver el plan gratuito
    return SubscriptionPlan.FREE

