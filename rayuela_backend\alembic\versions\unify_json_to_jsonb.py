"""Unify JSON columns to JSONB for better performance and indexing

Revision ID: unify_json_to_jsonb
Revises: unify_entity_id_types
Create Date: 2025-01-24 15:00:00.000000

This migration converts all JSON columns to JSONB for better performance:
- interactions.recommendation_metadata: JSON -> JSONB
- subscriptions.available_models: JSON -> JSONB
- account_usage_metrics.endpoint_usage: JSON -> JSONB
- account_usage_metrics.error_types: JSON -> JSONB
- artifact_metadata.performance_metrics: JSONType -> JSONB
- artifact_metadata.parameters: JSONType -> JSONB

JSONB provides:
- Better query performance
- Support for GIN indexes
- More efficient storage
- Better PostgreSQL integration

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import JSONB


# revision identifiers, used by Alembic.
revision: str = 'unify_json_to_jsonb'
down_revision: Union[str, None] = 'unify_entity_id_types'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Convert JSON columns to JSONB."""
    
    print("Converting JSON columns to JSONB for better performance...")
    
    # Convert interactions.recommendation_metadata
    print("1. Converting interactions.recommendation_metadata from JSON to JSONB...")
    op.alter_column(
        'interactions',
        'recommendation_metadata',
        type_=JSONB(),
        existing_type=sa.JSON(),
        existing_nullable=True,
        postgresql_using='recommendation_metadata::jsonb'
    )
    
    # Convert subscriptions.available_models
    print("2. Converting subscriptions.available_models from JSON to JSONB...")
    op.alter_column(
        'subscriptions',
        'available_models',
        type_=JSONB(),
        existing_type=sa.JSON(),
        existing_nullable=True,
        postgresql_using='available_models::jsonb'
    )
    
    # Convert account_usage_metrics.endpoint_usage
    print("3. Converting account_usage_metrics.endpoint_usage from JSON to JSONB...")
    op.alter_column(
        'account_usage_metrics',
        'endpoint_usage',
        type_=JSONB(),
        existing_type=sa.JSON(),
        existing_nullable=False,
        postgresql_using='endpoint_usage::jsonb'
    )
    
    # Convert account_usage_metrics.error_types
    print("4. Converting account_usage_metrics.error_types from JSON to JSONB...")
    op.alter_column(
        'account_usage_metrics',
        'error_types',
        type_=JSONB(),
        existing_type=sa.JSON(),
        existing_nullable=False,
        postgresql_using='error_types::jsonb'
    )

    # Convert artifact_metadata.performance_metrics
    print("5. Converting artifact_metadata.performance_metrics from JSONType to JSONB...")
    op.alter_column(
        'artifact_metadata',
        'performance_metrics',
        type_=JSONB(),
        existing_type=sa.JSON(),
        existing_nullable=True,
        postgresql_using='performance_metrics::jsonb'
    )

    # Convert artifact_metadata.parameters
    print("6. Converting artifact_metadata.parameters from JSONType to JSONB...")
    op.alter_column(
        'artifact_metadata',
        'parameters',
        type_=JSONB(),
        existing_type=sa.JSON(),
        existing_nullable=True,
        postgresql_using='parameters::jsonb'
    )

    print("JSON to JSONB conversion completed successfully!")
    print("Consider adding GIN indexes on JSONB columns for better query performance:")
    print("  CREATE INDEX CONCURRENTLY idx_interactions_recommendation_metadata_gin ON interactions USING gin (recommendation_metadata);")
    print("  CREATE INDEX CONCURRENTLY idx_subscriptions_available_models_gin ON subscriptions USING gin (available_models);")
    print("  CREATE INDEX CONCURRENTLY idx_account_usage_metrics_endpoint_usage_gin ON account_usage_metrics USING gin (endpoint_usage);")
    print("  CREATE INDEX CONCURRENTLY idx_account_usage_metrics_error_types_gin ON account_usage_metrics USING gin (error_types);")
    print("  CREATE INDEX CONCURRENTLY idx_artifact_metadata_performance_metrics_gin ON artifact_metadata USING gin (performance_metrics);")
    print("  CREATE INDEX CONCURRENTLY idx_artifact_metadata_parameters_gin ON artifact_metadata USING gin (parameters);")


def downgrade() -> None:
    """Convert JSONB columns back to JSON."""
    
    print("Converting JSONB columns back to JSON...")
    
    # Convert interactions.recommendation_metadata back to JSON
    print("1. Converting interactions.recommendation_metadata from JSONB to JSON...")
    op.alter_column(
        'interactions',
        'recommendation_metadata',
        type_=sa.JSON(),
        existing_type=JSONB(),
        existing_nullable=True,
        postgresql_using='recommendation_metadata::json'
    )
    
    # Convert subscriptions.available_models back to JSON
    print("2. Converting subscriptions.available_models from JSONB to JSON...")
    op.alter_column(
        'subscriptions',
        'available_models',
        type_=sa.JSON(),
        existing_type=JSONB(),
        existing_nullable=True,
        postgresql_using='available_models::json'
    )
    
    # Convert account_usage_metrics.endpoint_usage back to JSON
    print("3. Converting account_usage_metrics.endpoint_usage from JSONB to JSON...")
    op.alter_column(
        'account_usage_metrics',
        'endpoint_usage',
        type_=sa.JSON(),
        existing_type=JSONB(),
        existing_nullable=False,
        postgresql_using='endpoint_usage::json'
    )
    
    # Convert account_usage_metrics.error_types back to JSON
    print("4. Converting account_usage_metrics.error_types from JSONB to JSON...")
    op.alter_column(
        'account_usage_metrics',
        'error_types',
        type_=sa.JSON(),
        existing_type=JSONB(),
        existing_nullable=False,
        postgresql_using='error_types::json'
    )

    # Convert artifact_metadata.performance_metrics back to JSON
    print("5. Converting artifact_metadata.performance_metrics from JSONB to JSON...")
    op.alter_column(
        'artifact_metadata',
        'performance_metrics',
        type_=sa.JSON(),
        existing_type=JSONB(),
        existing_nullable=True,
        postgresql_using='performance_metrics::json'
    )

    # Convert artifact_metadata.parameters back to JSON
    print("6. Converting artifact_metadata.parameters from JSONB to JSON...")
    op.alter_column(
        'artifact_metadata',
        'parameters',
        type_=sa.JSON(),
        existing_type=JSONB(),
        existing_nullable=True,
        postgresql_using='parameters::json'
    )

    print("JSONB to JSON conversion completed successfully!")
