// src/app/(dashboard)/api-keys/page.tsx
"use client";

import { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { useAuth } from '@/lib/auth';
import { useMultiApiKeys } from '@/lib/useApiKeys';
import { ApiKey } from '@/lib/api';
import { toast } from "sonner";
import { handleApiError } from "@/lib/error-handler";
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { AlertCircle, Plus, Edit, Trash2, Key, Copy } from 'lucide-react';
import Link from 'next/link';
import InitialApiKeyModal from '@/components/auth/InitialApiKeyModal';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

export default function ApiKeysPage() {
  const { token, apiKey, user, isLoading: isAuthLoading } = useAuth();
  const [showNewKeyModal, setShowNewKeyModal] = useState(false);
  const [newApiKey, setNewApiKey] = useState<string | null>(null);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [editingApiKey, setEditingApiKey] = useState<ApiKey | null>(null);
  const [newKeyName, setNewKeyName] = useState('');
  const [editKeyName, setEditKeyName] = useState('');

  const {
    data: apiKeysData,
    error: apiKeysError,
    isLoading: isApiKeysLoading,
    createApiKey,
    updateApiKey,
    revokeApiKey,
    isCreating,
    isUpdating,
    isRevoking,
    operationError,
    getFormattedApiKey
  } = useMultiApiKeys({
    revalidateOnFocus: true,
    refreshInterval: 30000, // Actualizar cada 30 segundos
    dedupingInterval: 5000, // Evitar peticiones duplicadas en 5 segundos
    errorRetryCount: 3
  });

  const handleCreateApiKey = async () => {
    try {
      const result = await createApiKey({
        name: newKeyName.trim() || `API Key ${new Date().toLocaleDateString('es-ES')}`
      });
      if (result?.api_key) {
        setNewApiKey(result.api_key);
        setShowNewKeyModal(true);
        setShowCreateDialog(false);
        setNewKeyName('');
        toast.success("API Key creada con éxito");
      }
    } catch (error: any) {
      handleApiError(error, "Error al crear la API Key");
    }
  };

  const handleEditApiKey = async () => {
    if (!editingApiKey) return;

    try {
      const result = await updateApiKey(editingApiKey.id, {
        name: editKeyName.trim()
      });
      if (result) {
        setShowEditDialog(false);
        setEditingApiKey(null);
        setEditKeyName('');
        toast.success("API Key actualizada con éxito");
      }
    } catch (error: any) {
      handleApiError(error, "Error al actualizar la API Key");
    }
  };

  const handleRevokeApiKey = async (apiKeyId: number, apiKeyName?: string) => {
    try {
      const success = await revokeApiKey(apiKeyId);
      if (success) {
        toast.success(`API Key ${apiKeyName ? `"${apiKeyName}"` : ''} revocada con éxito`);
      }
    } catch (error: any) {
      handleApiError(error, "Error al revocar la API Key");
    }
  };

  const handleCopyApiKey = (apiKey: ApiKey) => {
    const formattedKey = getFormattedApiKey(apiKey);
    if (formattedKey) {
      navigator.clipboard.writeText(formattedKey);
      toast.success("API Key copiada al portapapeles");
    }
  };

  const openEditDialog = (apiKey: ApiKey) => {
    setEditingApiKey(apiKey);
    setEditKeyName(apiKey.name || '');
    setShowEditDialog(true);
  };

  const handleModalClose = () => {
    setShowNewKeyModal(false);
  };

  // Estado de carga combinado
  const isLoading = isAuthLoading || isApiKeysLoading;

  // Estado de Error (Auth o API Key)
  if (apiKeysError) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-red-600">
          <h1 className="text-2xl font-semibold mb-4">API Keys</h1>
          <p>Error al cargar las API Keys: {apiKeysError.message}</p>
          <p>Intenta refrescar la página o contacta a soporte.</p>
        </div>
      </div>
    );
  }

  // Estado: No autenticado
  if (!token || !user) {
    return (
      <div className="container mx-auto py-8">
        <div className="text-orange-600">
          <h1 className="text-2xl font-semibold mb-4">API Keys</h1>
          <p>No se pudo verificar tu sesión. Por favor, intenta <Link href="/login" className="underline">iniciar sesión</Link> de nuevo.</p>
        </div>
      </div>
    );
  }

  // Mostrar loading mientras se cargan los datos
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">API Keys</h1>
            <p className="text-muted-foreground">
              Gestiona múltiples claves de API para acceder a los servicios de Rayuela
            </p>
          </div>
          <Skeleton className="h-10 w-32" />
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Skeleton className="h-6 w-6" />
              <Skeleton className="h-6 w-48" />
            </CardTitle>
            <CardDescription>
              <Skeleton className="h-4 w-64" />
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">API Keys</h1>
          <p className="text-muted-foreground">
            Gestiona múltiples claves de API para acceder a los servicios de Rayuela
          </p>
        </div>
        <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Crear Nueva API Key
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Crear Nueva API Key</DialogTitle>
              <DialogDescription>
                Crea una nueva API Key para tu cuenta. Puedes asignarle un nombre descriptivo para identificarla fácilmente.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="keyName">Nombre de la API Key (opcional)</Label>
                <Input
                  id="keyName"
                  placeholder="ej. Producción, Desarrollo, Equipo Frontend..."
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                Cancelar
              </Button>
              <Button onClick={handleCreateApiKey} disabled={isCreating}>
                {isCreating ? 'Creando...' : 'Crear API Key'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Tus API Keys
          </CardTitle>
          <CardDescription>
            Gestiona todas las API Keys de tu cuenta. Cada clave puede tener un nombre descriptivo y ser revocada individualmente.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {apiKeysData?.api_keys && apiKeysData.api_keys.length > 0 ? (
            <div className="space-y-4">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Nombre</TableHead>
                    <TableHead>API Key</TableHead>
                    <TableHead>Estado</TableHead>
                    <TableHead>Creada</TableHead>
                    <TableHead>Último uso</TableHead>
                    <TableHead className="text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {apiKeysData.api_keys.map((apiKey) => (
                    <TableRow key={apiKey.id}>
                      <TableCell className="font-medium">
                        {apiKey.name || 'Sin nombre'}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="font-mono text-sm">
                            {getFormattedApiKey(apiKey)}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyApiKey(apiKey)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={apiKey.is_active ? "default" : "secondary"}>
                          {apiKey.is_active ? 'Activa' : 'Inactiva'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {apiKey.created_at ?
                          format(new Date(apiKey.created_at), 'PPP', { locale: es }) :
                          'N/A'
                        }
                      </TableCell>
                      <TableCell>
                        {apiKey.last_used ?
                          format(new Date(apiKey.last_used), 'PPP', { locale: es }) :
                          'Nunca'
                        }
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => openEditDialog(apiKey)}
                            disabled={isUpdating}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="text-red-600 hover:text-red-700"
                                disabled={isRevoking}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>¿Revocar API Key?</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Esta acción revocará permanentemente la API Key "{apiKey.name || 'Sin nombre'}".
                                  Cualquier aplicación que esté usando esta API Key dejará de funcionar inmediatamente.
                                  Esta acción no se puede deshacer.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleRevokeApiKey(apiKey.id, apiKey.name || undefined)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Revocar
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8">
              <Key className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No tienes API Keys</h3>
              <p className="text-muted-foreground mb-4">
                Crea tu primera API Key para comenzar a usar los servicios de Rayuela.
              </p>
              <Button onClick={() => setShowCreateDialog(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Crear Primera API Key
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Información importante sobre las API Keys */}
      <Card>
        <CardContent className="pt-6">
          <div className="p-4 border rounded-md bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-700">
            <h3 className="font-semibold text-amber-800 dark:text-amber-300 mb-2 flex items-center gap-2">
              <AlertCircle className="h-5 w-5" />
              Información importante sobre tus API Keys
            </h3>
            <div className="space-y-3 text-sm text-amber-700 dark:text-amber-400">
              <p>
                Por razones de seguridad, las API Keys completas <strong>solo se muestran una vez</strong> al momento de crearlas.
                Es crucial que las copies y almacenes en un lugar seguro inmediatamente.
              </p>
              <p className="font-medium">
                Ventajas del sistema multi-API Key:
              </p>
              <ul className="list-disc list-inside space-y-1 pl-2">
                <li><strong>Seguridad mejorada:</strong> Revoca claves específicas sin afectar otras integraciones</li>
                <li><strong>Organización:</strong> Asigna nombres descriptivos para diferentes entornos o equipos</li>
                <li><strong>Flexibilidad:</strong> Crea tantas claves como necesites para diferentes propósitos</li>
                <li><strong>Trazabilidad:</strong> Ve cuándo fue la última vez que se usó cada clave</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modal para mostrar la nueva API Key */}
      {showNewKeyModal && newApiKey && (
        <InitialApiKeyModal
          apiKey={newApiKey}
          onClose={handleModalClose}
        />
      )}

      {/* Dialog para editar API Key */}
      <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Editar API Key</DialogTitle>
            <DialogDescription>
              Actualiza el nombre descriptivo de tu API Key. La clave en sí no se puede modificar.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="editKeyName">Nombre de la API Key</Label>
              <Input
                id="editKeyName"
                placeholder="ej. Producción, Desarrollo, Equipo Frontend..."
                value={editKeyName}
                onChange={(e) => setEditKeyName(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowEditDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEditApiKey} disabled={isUpdating}>
              {isUpdating ? 'Actualizando...' : 'Actualizar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}