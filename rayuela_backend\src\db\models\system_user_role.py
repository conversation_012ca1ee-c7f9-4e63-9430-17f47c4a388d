from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ey, PrimaryKeyConstraint, ForeignKeyConstraint, Index
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class SystemUserRole(Base, TenantMixin):
    """Modelo para la relación entre usuarios del sistema y roles."""

    __tablename__ = "system_user_roles"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK))
    # Columnas
    system_user_id = Column(Integer, nullable=False)
    role_id = Column(Integer, nullable=False)

    # Definición explícita de la PK compuesta y FKs
    __table_args__ = (
        PrimaryKeyConstraint("account_id", "system_user_id", "role_id"),
        # Composite FKs for proper tenant isolation
        ForeignKeyConstraint(
            ["account_id", "system_user_id"],
            ["system_users.account_id", "system_users.id"],
            ondelete="CASCADE",
            name="fk_system_user_role_system_user"
        ),
        ForeignKeyConstraint(
            ["account_id", "role_id"],
            ["roles.account_id", "roles.id"],
            ondelete="CASCADE",
            name="fk_system_user_role_role"
        ),
        Index("idx_system_user_role_user", "account_id", "system_user_id"),
        Index("idx_system_user_role_role", "account_id", "role_id"),
        {"postgresql_partition_by": ACCOUNT_RANGE},
    )

    # Relaciones
    account = relationship("Account", back_populates="system_user_roles")
    system_user = relationship(
        "SystemUser",
        foreign_keys=[system_user_id],
        primaryjoin="and_(SystemUserRole.account_id==SystemUser.account_id, SystemUserRole.system_user_id==SystemUser.id)",
        back_populates="system_user_roles"
    )
    role = relationship(
        "Role",
        foreign_keys=[role_id],
        primaryjoin="and_(SystemUserRole.account_id==Role.account_id, SystemUserRole.role_id==Role.id)",
        back_populates="system_user_roles",
        overlaps="system_user"
    )
