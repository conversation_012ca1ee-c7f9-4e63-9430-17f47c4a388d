#!/usr/bin/env python
"""
Script para migrar las API keys existentes al nuevo formato hasheado.

Este script debe ejecutarse después de aplicar la migración que añade los campos
api_key_hash, api_key_prefix, api_key_last_chars y api_key_created_at a la tabla accounts.

Uso:
    python -m scripts.migrate_api_keys
"""
import asyncio
import logging
from datetime import datetime, timezone
from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from src.core.config import settings
from src.core.security.api_key import hash_api_key
from src.db.models.account import Account

# Configurar logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Crear engine y sessionmaker
engine = create_async_engine(settings.SQLALCHEMY_DATABASE_URI)
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)


async def migrate_api_keys():
    """Migra las API keys existentes al nuevo formato hasheado."""
    async with async_session() as session:
        # Este script ya no es necesario porque la columna api_key ya no existe
        # Mantenemos el script por razones históricas
        logger.info("Este script ya no es necesario porque la columna api_key ya no existe")

        # Este script ya no es necesario porque la migración ya se completó
        # y la columna api_key ya no existe en la base de datos
        logger.info("Migración completada anteriormente")


async def main():
    """Función principal."""
    try:
        logger.info("Iniciando migración de API keys")
        await migrate_api_keys()
        logger.info("Migración de API keys completada con éxito")
    except Exception as e:
        logger.error(f"Error durante la migración de API keys: {str(e)}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
