# Diferencias entre JWT Token y API Key en Rayuela

## Resumen Ejecutivo

Rayuela utiliza **dos tipos de autenticación diferentes** para diferentes propósitos:

- **JWT Token**: Para autenticación de usuarios en el dashboard y operaciones de gestión
- **API Key**: Para autenticación de aplicaciones en las llamadas de recomendación

## 🔑 JWT Token (JSON Web Token)

### ¿Qué es?
Un token temporal que identifica a un **usuario específico** dentro de una cuenta.

### ¿Para qué se usa?
- Acceso al dashboard web de Rayuela
- Operaciones de gestión de cuenta (usuarios, configuración, etc.)
- Endpoints administrativos (`/api/v1/accounts/`, `/api/v1/users/`, etc.)
- Gestión de API Keys (`/api/v1/api-keys/`)

### Características
- ⏰ **Temporal**: Expira después de un tiempo determinado
- 👤 **Específico del usuario**: Cada usuario tiene su propio JWT
- 🔄 **Renovable**: Se puede obtener un nuevo token haciendo login
- 🛡️ **Revocable**: Se puede invalidar al hacer logout

### ¿Cómo obtenerlo?
```bash
# Login para obtener JWT
curl -X POST "https://api.rayuela.ai/api/v1/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=tu-password"
```

### Respuesta
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "token_type": "bearer"
}
```

### ¿Cómo usarlo?
```bash
# Usar JWT en headers
curl -X GET "https://api.rayuela.ai/api/v1/accounts/current" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

---

## 🗝️ API Key

### ¿Qué es?
Una clave permanente que identifica a una **cuenta/organización** completa.

### ¿Para qué se usa?
- Llamadas de recomendación (`/api/v1/recommendations/`)
- Ingesta de datos (`/api/v1/products/`, `/api/v1/interactions/`)
- Cualquier operación de la API de recomendaciones
- Integración desde aplicaciones de producción

### Características
- 🔒 **Permanente**: No expira automáticamente
- 🏢 **Específica de la cuenta**: Una API Key por cuenta
- 🔐 **Secreta**: Solo se muestra una vez al generarla
- ⚡ **Alto rendimiento**: Optimizada para llamadas frecuentes

### ¿Cómo obtenerla?

#### Para cuentas nuevas (Recomendado)
```bash
# Registro - Devuelve JWT + API Key
curl -X POST "https://api.rayuela.ai/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "account_name": "Mi Empresa",
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

#### Para cuentas existentes
```bash
# 1. Primero hacer login para obtener JWT
curl -X POST "https://api.rayuela.ai/api/v1/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=tu-password"

# 2. Usar JWT para generar nueva API Key
curl -X POST "https://api.rayuela.ai/api/v1/api-keys/" \
  -H "Authorization: Bearer <tu-jwt-token>"
```

### ¿Cómo usarla?
```bash
# Usar API Key en headers
curl -X POST "https://api.rayuela.ai/api/v1/recommendations/" \
  -H "X-API-Key: ray_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789" \
  -H "Content-Type: application/json" \
  -d '{"user_id": "user123", "num_recommendations": 10}'
```

---

## 🚀 Flujo de Onboarding Recomendado

### Para Desarrolladores Nuevos

1. **Registro** (Una sola llamada para todo)
   ```bash
   curl -X POST "https://api.rayuela.ai/api/v1/auth/register" \
     -H "Content-Type: application/json" \
     -d '{
       "account_name": "Mi Empresa",
       "email": "<EMAIL>", 
       "password": "password123"
     }'
   ```
   
   **Respuesta:**
   ```json
   {
     "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
     "token_type": "bearer",
     "api_key": "ray_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789"
   }
   ```

2. **Guardar ambos tokens de forma segura**
   - JWT: Para acceso al dashboard
   - API Key: Para llamadas de recomendación

3. **Primera llamada de recomendación**
   ```bash
   curl -X POST "https://api.rayuela.ai/api/v1/recommendations/" \
     -H "X-API-Key: ray_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789" \
     -H "Content-Type: application/json" \
     -d '{"user_id": "user123", "num_recommendations": 10}'
   ```

### Para Usuarios Existentes

1. **Login** (Solo para dashboard)
   ```bash
   curl -X POST "https://api.rayuela.ai/api/v1/auth/token" \
     -H "Content-Type: application/x-www-form-urlencoded" \
     -d "username=<EMAIL>&password=tu-password"
   ```

2. **Si necesitas nueva API Key**
   ```bash
   curl -X POST "https://api.rayuela.ai/api/v1/api-keys/" \
     -H "Authorization: Bearer <tu-jwt-token>"
   ```

---

## 🔍 Tabla de Comparación Rápida

| Aspecto | JWT Token | API Key |
|---------|-----------|---------|
| **Propósito** | Dashboard/Gestión | API de Recomendaciones |
| **Duración** | Temporal (expira) | Permanente |
| **Alcance** | Usuario específico | Cuenta completa |
| **Obtención** | Login (`/auth/token`) | Registro o `/api-keys/` |
| **Uso** | `Authorization: Bearer <token>` | `X-API-Key: <key>` |
| **Renovación** | Automática (re-login) | Manual (generar nueva) |
| **Revocación** | Logout o expiración | Generar nueva (invalida anterior) |

---

## ❌ Errores Comunes

### ❌ Usar JWT para llamadas de recomendación
```bash
# INCORRECTO
curl -X POST "https://api.rayuela.ai/api/v1/recommendations/" \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
```

### ❌ Usar API Key para operaciones de dashboard
```bash
# INCORRECTO  
curl -X GET "https://api.rayuela.ai/api/v1/accounts/current" \
  -H "X-API-Key: ray_aBcDeFgHiJkLmNoPqRsTuVwXyZ0123456789"
```

### ❌ Esperar API Key en el endpoint de login
```bash
# INCORRECTO - /auth/token solo devuelve JWT
curl -X POST "https://api.rayuela.ai/api/v1/auth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=tu-password"
# No devuelve API Key, solo JWT
```

---

## 🛡️ Mejores Prácticas de Seguridad

### Para JWT Tokens
- ✅ Almacenar en memoria o sessionStorage (no localStorage)
- ✅ Implementar renovación automática antes de expiración
- ✅ Hacer logout al cerrar aplicación
- ✅ Validar expiración antes de cada uso

### Para API Keys
- ✅ Almacenar en variables de entorno (nunca en código)
- ✅ Usar HTTPS siempre
- ✅ Rotar periódicamente por seguridad
- ✅ Monitorear uso para detectar accesos no autorizados
- ✅ Revocar inmediatamente si se compromete

---

## 📞 Soporte

Si tienes dudas sobre cuál usar o cómo implementar la autenticación:

- 📧 Email: <EMAIL>
- 📖 Documentación: https://docs.rayuela.ai
- 💬 Chat: https://rayuela.ai/support 