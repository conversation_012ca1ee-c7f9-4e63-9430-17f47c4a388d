#!/usr/bin/env python3
"""
Script conveniente para ejecutar la migración completa de tipos de ID.

Este script automatiza:
1. Pre-validación
2. Migración (con confirmación del usuario)
3. Post-validación

Uso:
    python run_id_migration.py [--force]
"""

import sys
import subprocess
import os
from datetime import datetime

def run_command(command, description):
    """Ejecutar un comando y mostrar el resultado."""
    print(f"\n🔄 {description}")
    print("=" * 60)
    
    try:
        result = subprocess.run(command, shell=True, capture_output=True, text=True)
        
        # Mostrar la salida
        if result.stdout:
            print(result.stdout)
        
        if result.stderr:
            print("STDERR:", result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} - EXITOSO")
            return True
        else:
            print(f"❌ {description} - FALLÓ (código: {result.returncode})")
            return False
            
    except Exception as e:
        print(f"❌ Error ejecutando comando: {e}")
        return False

def ask_confirmation(message):
    """Pedir confirmación al usuario."""
    while True:
        response = input(f"\n{message} (y/n): ").lower().strip()
        if response in ['y', 'yes']:
            return True
        elif response in ['n', 'no']:
            return False
        else:
            print("Por favor responde 'y' o 'n'")

def main():
    print("🔧 Script de Migración de Unificación de Tipos de ID")
    print("=" * 60)
    print(f"Iniciado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Verificar argumentos
    force_mode = '--force' in sys.argv
    
    if force_mode:
        print("⚠️  MODO FORZADO: Se omitirán confirmaciones interactivas")
    
    # Paso 1: Pre-validación
    print("\n📋 PASO 1: PRE-VALIDACIÓN")
    if not run_command("python3 validate_id_migration.py", "Validación pre-migración"):
        print("\n❌ La pre-validación falló. Revisa los errores antes de continuar.")
        if not force_mode:
            return 1
        else:
            print("⚠️  Continuando en modo forzado...")
    
    # Confirmación para continuar
    if not force_mode:
        print("\n" + "="*60)
        print("⚠️  PUNTO DE NO RETORNO")
        print("La siguiente operación modificará permanentemente la estructura de la base de datos.")
        print("Asegúrate de tener una copia de seguridad completa.")
        
        if not ask_confirmation("¿Continuar con la migración?"):
            print("❌ Migración cancelada por el usuario.")
            return 0
    
    # Paso 2: Verificar estado de Alembic
    print("\n📋 PASO 2: VERIFICACIÓN DE ALEMBIC")
    if not run_command("alembic current", "Estado actual de migraciones"):
        print("❌ Error verificando estado de Alembic")
        if not force_mode:
            return 1
    
    # Paso 3: Ejecutar migración
    print("\n📋 PASO 3: EJECUTAR MIGRACIÓN")
    print("Aplicando migración 'unify_entity_id_types'...")
    
    if not run_command("alembic upgrade unify_entity_id_types", "Migración de tipos de ID"):
        print("\n❌ La migración falló!")
        print("🚨 ACCIÓN REQUERIDA:")
        print("1. Revisa los errores de Alembic arriba")
        print("2. Si es necesario, restaura desde backup")
        print("3. Contacta soporte si el problema persiste")
        return 1
    
    # Paso 4: Post-validación
    print("\n📋 PASO 4: POST-VALIDACIÓN")
    if not run_command("python3 post_migration_validation.py", "Validación post-migración"):
        print("\n⚠️  La post-validación detectó problemas.")
        print("La migración se completó pero puede requerir ajustes manuales.")
        print("Revisa la salida arriba para más detalles.")
        return 2
    
    # Éxito total
    print("\n" + "="*60)
    print("🎉 MIGRACIÓN COMPLETADA EXITOSAMENTE")
    print("="*60)
    print("✅ Pre-validación: PASÓ")
    print("✅ Migración: EXITOSA")
    print("✅ Post-validación: PASÓ")
    print(f"✅ Finalizado: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n📋 PRÓXIMOS PASOS:")
    print("1. Verificar que la aplicación funciona correctamente")
    print("2. Actualizar APIs que manejan estos IDs")
    print("3. Actualizar documentación de APIs")
    print("4. Considerar actualizar clientes que consumen las APIs")
    
    return 0

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n❌ Migración interrumpida por el usuario")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Error inesperado: {e}")
        sys.exit(1) 