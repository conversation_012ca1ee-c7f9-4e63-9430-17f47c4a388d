services:
  api:
    build: .
    ports:
      - "${API_PORT:-8001}:${API_PORT:-8001}"
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - API_HOST=0.0.0.0
      - API_PORT=${API_PORT:-8001}
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 1g
    deploy:
      resources:
        limits:
          memory: 2g

  # Worker consolidado (un solo worker maneja todas las colas)
  worker:
    build:
      context: .
      dockerfile: docker/worker.Dockerfile
    command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=1 --queues=default,maintenance,training,batch_processing --hostname=worker@%h
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 1g
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2g

  # Celery Beat scheduler para tareas periódicas
  celery-beat:
    build:
      context: .
      dockerfile: docker/worker.Dockerfile
    command: celery -A src.workers.celery_app beat --loglevel=info
    env_file:
      - .env.docker
    environment:
      - ENV=development
      - POSTGRES_SERVER=db
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
    depends_on:
      - db
      - redis
      - worker
    volumes:
      - ./:/app
    networks:
      - rayuela-network
    mem_reservation: 512m
    deploy:
      resources:
        limits:
          memory: 1g

  db:
    image: postgres:13
    ports:
      - "5432:5432"
    env_file:
      - .env.docker
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - rayuela-network

  redis:
    image: redis:6
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rayuela-network

volumes:
  postgres_data:
  redis_data:

networks:
  rayuela-network:
    driver: bridge
