# 🌐 Configuración de Dominio Personalizado - Rayuela.ai

## 📋 Resumen

Esta guía te ayudará a configurar el dominio personalizado `rayuela.ai` para tu aplicación Rayuela desplegada en Google Cloud Platform.

## 🎯 Objetivos

- **Frontend**: `https://rayuela.ai`
- **Backend API**: `https://api.rayuela.ai`

## 📚 Pasos de Configuración

### 1. ⚡ Verificación del Dominio

Primero, necesitas verificar que eres propietario del dominio `rayuela.ai`:

```bash
# Abrir Google Search Console para verificación
gcloud domains verify rayuela.ai
```

**Opciones de verificación en Search Console:**
- **Método DNS**: Agregar un registro TXT a tu DNS
- **Método HTML**: Subir un archivo HTML a tu sitio web
- **Método Meta Tag**: Agregar una meta etiqueta a tu HTML

### 2. 🌐 Configuración DNS

Ya hemos creado la zona DNS en Google Cloud:

```bash
# Ver los servidores de nombres que debes configurar
gcloud dns managed-zones describe rayuela-ai-zone
```

**Servidores DNS a configurar en tu registrador:**
- `ns-cloud-e1.googledomains.com.`
- `ns-cloud-e2.googledomains.com.`
- `ns-cloud-e3.googledomains.com.`
- `ns-cloud-e4.googledomains.com.`

### 3. 🚀 Configuración Automática

Una vez verificado el dominio, ejecuta:

```bash
# Configurar dominio personalizado
./scripts/setup-custom-domain.sh
```

Este script:
- ✅ Verifica que el dominio esté validado
- 🔗 Crea mappings de dominio para frontend y backend
- 🌐 Configura registros DNS automáticamente
- 📊 Muestra información de configuración

### 4. 🔧 Actualizar Frontend

Para que el frontend use el nuevo dominio del backend:

```bash
# Actualizar configuración del frontend
./scripts/update-frontend-config.sh
```

### 5. ✅ Verificación

```bash
# Verificar mappings de dominio
gcloud beta run domain-mappings list --region=us-central1

# Verificar configuración DNS
nslookup rayuela.ai
nslookup api.rayuela.ai
```

## 🔧 Configuración Manual (si es necesario)

### Crear Domain Mappings

```bash
# Frontend: rayuela.ai -> rayuela-frontend
gcloud beta run domain-mappings create \
    --service=rayuela-frontend \
    --domain=rayuela.ai \
    --region=us-central1

# Backend: api.rayuela.ai -> rayuela-backend
gcloud beta run domain-mappings create \
    --service=rayuela-backend \
    --domain=api.rayuela.ai \
    --region=us-central1
```

### Crear Registros DNS

```bash
# Obtener targets de los mappings
FRONTEND_TARGET=$(gcloud beta run domain-mappings describe rayuela.ai --region=us-central1 --format="value(status.resourceRecords[0].rrdata)")
BACKEND_TARGET=$(gcloud beta run domain-mappings describe api.rayuela.ai --region=us-central1 --format="value(status.resourceRecords[0].rrdata)")

# Crear registros CNAME
gcloud dns record-sets create rayuela.ai. \
    --zone=rayuela-ai-zone \
    --type=CNAME \
    --ttl=300 \
    --rrdatas=$FRONTEND_TARGET

gcloud dns record-sets create api.rayuela.ai. \
    --zone=rayuela-ai-zone \
    --type=CNAME \
    --ttl=300 \
    --rrdatas=$BACKEND_TARGET
```

## 🛡️ SSL/TLS

Google Cloud Run automáticamente provisiona certificados SSL gratuitos para dominios personalizados. Los certificados pueden tardar unos minutos en activarse.

## ⏰ Tiempos de Propagación

- **DNS**: 5-15 minutos (TTL de 300 segundos)
- **SSL**: 5-10 minutos para provisionar
- **Propagación global**: Hasta 48 horas

## 🔍 Herramientas de Verificación

- **DNS**: https://dnschecker.org
- **SSL**: https://www.ssllabs.com/ssltest/
- **Logs**: `gcloud run services logs read rayuela-frontend --region=us-central1`

## ❗ Troubleshooting

### Problema: "Domain not verified"
```bash
# Re-verificar dominio
gcloud domains verify rayuela.ai
```

### Problema: DNS no resuelve
```bash
# Verificar zona DNS
gcloud dns managed-zones describe rayuela-ai-zone

# Verificar registros
gcloud dns record-sets list --zone=rayuela-ai-zone
```

### Problema: SSL no funciona
```bash
# Verificar estado del mapping
gcloud beta run domain-mappings describe rayuela.ai --region=us-central1
```

## 📊 Estado Actual

### URLs Configuradas:
- **Frontend**: https://rayuela-frontend-lrw7xazcbq-uc.a.run.app
- **Backend**: https://rayuela-backend-lrw7xazcbq-uc.a.run.app

### URLs Objetivo:
- **Frontend**: https://rayuela.ai
- **Backend**: https://api.rayuela.ai

## 🔗 Enlaces Útiles

- [Cloud Run Custom Domains](https://cloud.google.com/run/docs/mapping-custom-domains)
- [Google Cloud DNS](https://cloud.google.com/dns/docs)
- [Google Search Console](https://search.google.com/search-console)
- [DNS Checker](https://dnschecker.org) 