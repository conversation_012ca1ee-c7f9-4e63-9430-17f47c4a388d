"""add_optimized_indexes_for_high_volume_tables

Revision ID: add_optimized_indexes
Revises: d6d03121d24c
Create Date: 2023-07-10 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_optimized_indexes'
down_revision = 'd6d03121d24c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Upgrade schema with optimized indexes for high-volume tables."""
    
    # Índices adicionales para la tabla interactions
    # Índice para consultas que filtran por account_id, end_user_id y timestamp
    op.create_index(
        'idx_interaction_account_user_timestamp',
        'interactions',
        ['account_id', 'end_user_id', 'timestamp'],
        unique=False
    )
    
    # Índice para consultas que filtran por account_id, product_id y timestamp
    # Útil para obtener interacciones recientes de un producto específico
    op.create_index(
        'idx_interaction_account_product_timestamp',
        'interactions',
        ['account_id', 'product_id', 'timestamp'],
        unique=False
    )
    
    # Índices adicionales para la tabla audit_logs
    # Índice para consultas que filtran por account_id, action y created_at
    op.create_index(
        'idx_audit_account_action_created',
        'audit_logs',
        ['account_id', 'action', 'created_at'],
        unique=False
    )
    
    # Índices adicionales para la tabla products
    # Índice para búsquedas por nombre de producto dentro de una cuenta
    op.create_index(
        'idx_product_account_name',
        'products',
        ['account_id', 'name'],
        unique=False
    )
    
    # Índice para filtrar productos por precio dentro de una cuenta
    op.create_index(
        'idx_product_account_price',
        'products',
        ['account_id', 'price'],
        unique=False
    )
    
    # Índice para filtrar productos por rating dentro de una cuenta
    op.create_index(
        'idx_product_account_rating',
        'products',
        ['account_id', 'average_rating'],
        unique=False
    )


def downgrade() -> None:
    """Downgrade schema by removing the optimized indexes."""
    
    # Eliminar índices de la tabla products
    op.drop_index('idx_product_account_rating', table_name='products')
    op.drop_index('idx_product_account_price', table_name='products')
    op.drop_index('idx_product_account_name', table_name='products')
    
    # Eliminar índices de la tabla audit_logs
    op.drop_index('idx_audit_account_action_created', table_name='audit_logs')
    
    # Eliminar índices de la tabla interactions
    op.drop_index('idx_interaction_account_product_timestamp', table_name='interactions')
    op.drop_index('idx_interaction_account_user_timestamp', table_name='interactions')
