from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from src.utils.base_logger import log_info, log_error
from src.db.session import get_db
from src.services.cache_service import CacheService
from typing import Optional, Callable
import asyncio


class CacheInvalidationMiddleware(BaseHTTPMiddleware):
    """
    Middleware para invalidar la caché de recomendaciones cuando ocurren
    interacciones significativas del usuario.
    """

    def __init__(self, app):
        super().__init__(app)
        self.cache_service = CacheService()
        self.interaction_endpoints = [
            "/api/v1/interactions",  # Endpoint para registrar interacciones
            "/api/v1/ratings",  # Endpoint para registrar ratings
            "/api/v1/purchases",  # Endpoint para registrar compras
        ]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Procesar la solicitud
        response = await call_next(request)

        # Solo procesar POST/PUT/PATCH a endpoints de interacción
        if request.method in ["POST", "PUT", "PATCH"] and any(
            request.url.path.startswith(endpoint)
            for endpoint in self.interaction_endpoints
        ):

            # Procesar en segundo plano para no bloquear la respuesta
            asyncio.create_task(self._process_cache_invalidation(request))

        return response

    async def _process_cache_invalidation(self, request: Request) -> None:
        """Procesa la invalidación de caché en segundo plano."""
        try:
            # Usar el servicio de caché para invalidar desde la solicitud
            async with get_db() as db:
                success = await self.cache_service.invalidate_from_request(request, db)

                if success:
                    log_info(f"Caché invalidada exitosamente desde middleware para {request.url.path}")
                else:
                    log_error(f"No se pudo invalidar la caché desde middleware para {request.url.path}")

        except Exception as e:
            # No interrumpir el flujo principal si hay un error
            log_error(f"Error invalidando caché en middleware: {str(e)}")

    async def _extract_user_id(self, request: Request) -> Optional[int]:
        """Extrae el ID de usuario del cuerpo de la solicitud."""
        try:
            body = await request.json()

            # Buscar user_id en diferentes formatos posibles
            user_id = (
                body.get("user_id") or body.get("end_user_id") or body.get("userId")
            )

            if isinstance(user_id, int) and user_id > 0:
                return user_id
            elif isinstance(user_id, str) and user_id.isdigit():
                return int(user_id)

            return None
        except Exception:
            return None

    def _extract_user_id_from_path(self, path: str) -> Optional[int]:
        """Extrae el ID de usuario de la ruta de la URL.

        Ejemplos de patrones:
        - /api/v1/users/123/interactions
        - /api/v1/end_users/123/recommendations
        """
        try:
            # Patrones comunes de URLs que contienen user_id
            # Usar split para extraer IDs de patrones comunes sin usar regex
            segments = path.split("/")

            # Buscar patrones como /users/123/ o /end_users/123/
            for i, segment in enumerate(segments):
                if segment in ["users", "end_users", "customers"] and i + 1 < len(
                    segments
                ):
                    try:
                        user_id = int(segments[i + 1])
                        if user_id > 0:
                            return user_id
                    except (ValueError, IndexError):
                        pass

            # Buscar patrones como ?user_id=123 o &end_user_id=123
            if "?" in path or "&" in path:
                params = path.split("?")[-1].split("&")
                for param in params:
                    if "=" in param:
                        key, value = param.split("=", 1)
                        if (
                            key in ["user_id", "end_user_id", "userId"]
                            and value.isdigit()
                        ):
                            return int(value)

            return None
        except Exception:
            return None
