"""
Tests for the AccountService.
"""
import pytest
import pytest_asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime, timezone

from src.services.account_service import AccountService
from src.db.models import Account, SystemUser, Product, AccountUsageMetrics, Subscription


class TestAccountService:
    """Tests for the AccountService class."""

    @pytest_asyncio.fixture
    async def service_setup(self):
        """Setup for account service tests."""
        # Mock database session
        mock_db = AsyncMock()
        mock_db.begin = AsyncMock().__aenter__.return_value
        
        # Mock repository
        mock_repo = AsyncMock()
        
        # Create service instance
        service = AccountService(db=mock_db)
        service._account_repo = mock_repo
        
        return service, mock_db, mock_repo

    @pytest.mark.asyncio
    async def test_count_users_success(self, service_setup):
        """Test count_users method when successful."""
        # Setup
        service, mock_db, _ = service_setup
        mock_result = AsyncMock()
        mock_result.scalar_one.return_value = 5
        mock_db.execute.return_value = mock_result
        
        # Execute
        result = await service.count_users(account_id=1)
        
        # Assert
        assert result == 5
        mock_db.execute.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_count_users_db_error(self, service_setup):
        """Test count_users method when database error occurs."""
        # Setup
        service, mock_db, _ = service_setup
        mock_db.execute.side_effect = SQLAlchemyError("Database error")
        
        # Execute
        result = await service.count_users(account_id=1)
        
        # Assert
        assert result == 0
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_count_products_success(self, service_setup):
        """Test count_products method when successful."""
        # Setup
        service, mock_db, _ = service_setup
        mock_result = AsyncMock()
        mock_result.scalar_one.return_value = 10
        mock_db.execute.return_value = mock_result
        
        # Execute
        result = await service.count_products(account_id=1)
        
        # Assert
        assert result == 10
        mock_db.execute.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_count_products_db_error(self, service_setup):
        """Test count_products method when database error occurs."""
        # Setup
        service, mock_db, _ = service_setup
        mock_db.execute.side_effect = SQLAlchemyError("Database error")
        
        # Execute
        result = await service.count_products(account_id=1)
        
        # Assert
        assert result == 0
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_count_items_calls_count_products(self, service_setup):
        """Test count_items method calls count_products."""
        # Setup
        service, mock_db, _ = service_setup
        mock_result = AsyncMock()
        mock_result.scalar_one.return_value = 15
        mock_db.execute.return_value = mock_result
        
        # Execute
        result = await service.count_items(account_id=1)
        
        # Assert
        assert result == 15
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_usage_metrics_success(self, service_setup):
        """Test get_usage_metrics method when successful."""
        # Setup
        service, mock_db, _ = service_setup
        mock_metrics = MagicMock(spec=AccountUsageMetrics)
        mock_metrics.account_id = 1
        mock_metrics.api_calls_count = 100
        mock_metrics.storage_used = 1024
        
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_metrics
        mock_db.execute.return_value = mock_result
        
        # Execute
        result = await service.get_usage_metrics(account_id=1)
        
        # Assert
        assert result == mock_metrics
        assert result.api_calls_count == 100
        assert result.storage_used == 1024
        mock_db.execute.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_get_usage_metrics_not_found(self, service_setup):
        """Test get_usage_metrics method when metrics not found."""
        # Setup
        service, mock_db, _ = service_setup
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db.execute.return_value = mock_result
        
        # Execute
        result = await service.get_usage_metrics(account_id=1)
        
        # Assert
        assert result is None
        mock_db.execute.assert_called_once()
        
    @pytest.mark.asyncio
    async def test_get_usage_metrics_db_error(self, service_setup):
        """Test get_usage_metrics method when database error occurs."""
        # Setup
        service, mock_db, _ = service_setup
        mock_db.execute.side_effect = SQLAlchemyError("Database error")
        
        # Execute
        result = await service.get_usage_metrics(account_id=1)
        
        # Assert
        assert result is None
        mock_db.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_storage_usage_success(self, service_setup):
        """Test get_storage_usage method when successful."""
        # Setup
        service, mock_db, _ = service_setup
        
        # Mock get_usage_metrics
        mock_metrics = MagicMock(spec=AccountUsageMetrics)
        mock_metrics.storage_used = 2048
        
        with patch.object(service, 'get_usage_metrics', return_value=mock_metrics):
            # Execute
            result = await service.get_storage_usage(account_id=1)
            
            # Assert
            assert result == 2048
            service.get_usage_metrics.assert_called_once_with(1)
        
    @pytest.mark.asyncio
    async def test_get_storage_usage_no_metrics(self, service_setup):
        """Test get_storage_usage method when no metrics found."""
        # Setup
        service, mock_db, _ = service_setup
        
        # Mock get_usage_metrics
        with patch.object(service, 'get_usage_metrics', return_value=None):
            # Execute
            result = await service.get_storage_usage(account_id=1)
            
            # Assert
            assert result == 0
            service.get_usage_metrics.assert_called_once_with(1)
        
    @pytest.mark.asyncio
    async def test_get_storage_usage_db_error(self, service_setup):
        """Test get_storage_usage method when database error occurs."""
        # Setup
        service, mock_db, _ = service_setup
        
        # Mock get_usage_metrics
        with patch.object(service, 'get_usage_metrics', side_effect=SQLAlchemyError("Database error")):
            # Execute
            result = await service.get_storage_usage(account_id=1)
            
            # Assert
            assert result == 0
            service.get_usage_metrics.assert_called_once_with(1)

    @pytest.mark.asyncio
    async def test_get_account_summary_success(self, service_setup):
        """Test get_account_summary method when successful."""
        # Setup
        service, mock_db, mock_repo = service_setup
        
        # Mock account
        mock_account = MagicMock(spec=Account)
        mock_account.id = 1
        mock_account.name = "Test Account"
        mock_account.is_active = True
        mock_account.created_at = datetime.now(timezone.utc)
        
        # Mock subscription
        mock_subscription = MagicMock(spec=Subscription)
        mock_subscription.is_active = True
        mock_subscription.plan_type = "PREMIUM"
        mock_account.subscription = mock_subscription
        
        # Mock repository
        mock_repo.get_by_id.return_value = mock_account
        
        # Mock methods
        with patch.object(service, 'count_users', return_value=5), \
             patch.object(service, 'count_products', return_value=10), \
             patch.object(service, 'get_usage_metrics') as mock_get_metrics:
            
            # Mock metrics
            mock_metrics = MagicMock(spec=AccountUsageMetrics)
            mock_metrics.api_calls_count = 100
            mock_metrics.storage_used = 1024
            mock_metrics.last_reset = datetime.now(timezone.utc)
            mock_metrics.last_billing_cycle = datetime.now(timezone.utc)
            mock_get_metrics.return_value = mock_metrics
            
            # Execute
            result = await service.get_account_summary(account_id=1)
            
            # Assert
            assert result["account_id"] == 1
            assert result["name"] == "Test Account"
            assert result["is_active"] is True
            assert result["subscription_plan"] == "PREMIUM"
            assert result["user_count"] == 5
            assert result["product_count"] == 10
            assert result["usage"]["api_calls"] == 100
            assert result["usage"]["storage_used"] == 1024
            assert "last_reset" in result["usage"]
            assert "last_billing_cycle" in result["usage"]
            
            # Verify method calls
            mock_repo.get_by_id.assert_called_once_with(1)
            service.count_users.assert_called_once_with(1)
            service.count_products.assert_called_once_with(1)
            service.get_usage_metrics.assert_called_once_with(1)
        
    @pytest.mark.asyncio
    async def test_get_account_summary_account_not_found(self, service_setup):
        """Test get_account_summary method when account not found."""
        # Setup
        service, mock_db, mock_repo = service_setup
        
        # Mock repository
        mock_repo.get_by_id.return_value = None
        
        # Execute
        result = await service.get_account_summary(account_id=1)
        
        # Assert
        assert "error" in result
        assert result["error"] == "Account not found"
        mock_repo.get_by_id.assert_called_once_with(1)
        
    @pytest.mark.asyncio
    async def test_get_account_summary_exception(self, service_setup):
        """Test get_account_summary method when exception occurs."""
        # Setup
        service, mock_db, mock_repo = service_setup
        
        # Mock repository to raise exception
        mock_repo.get_by_id.side_effect = Exception("Unexpected error")
        
        # Execute
        result = await service.get_account_summary(account_id=1)
        
        # Assert
        assert "error" in result
        assert "Unexpected error" in result["error"]
        mock_repo.get_by_id.assert_called_once_with(1)
