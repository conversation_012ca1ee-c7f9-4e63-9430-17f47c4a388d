from fastapi import Request
from sqlalchemy.ext.asyncio import AsyncSession
from jose import jwt, JW<PERSON><PERSON><PERSON>
from typing import Optional, Dict, Any, Tuple
from src.core.config import settings
from src.db.models import Account
from src.utils.base_logger import log_error


async def get_account_from_request(
    request: Request, db: AsyncSession
) -> Optional[Account]:
    """
    Extrae la cuenta del request usando el token de autorización o API key.
    Función compartida para uso en middlewares.

    Args:
        request: Request de FastAPI
        db: Sesión de base de datos

    Returns:
        Account o None si no se pudo obtener
    """
    try:
        # Primero intentar con el token de autorización
        token = request.headers.get("Authorization", "").replace("Bearer ", "")
        if token:
            account = await get_account_from_token(token, db)
            if account:
                return account

        # Si no hay token o no es válido, intentar con API key
        api_key = request.headers.get("X-API-Key")
        if api_key:
            account = await get_account_from_api_key(api_key, db)
            if account:
                return account

        return None
    except Exception as e:
        log_error(f"Error obteniendo cuenta desde request: {str(e)}")
        return None


async def get_account_from_token(token: str, db: AsyncSession) -> Optional[Account]:
    """
    Obtiene la cuenta a partir de un token JWT.

    Args:
        token: Token JWT
        db: Sesión de base de datos

    Returns:
        Account o None si no se pudo obtener
    """
    try:
        payload = jwt.decode(
            token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
        )

        # Extraer account_id del payload
        account_id = payload.get("account_id")
        if not account_id:
            return None

        # Obtener la cuenta de la base de datos
        from sqlalchemy.future import select

        stmt = select(Account).where(Account.account_id == account_id)
        result = await db.execute(stmt)
        account = result.scalar_one_or_none()

        return account
    except JWTError:
        return None
    except Exception as e:
        log_error(f"Error decodificando token: {str(e)}")
        return None


async def get_account_from_api_key(api_key: str, db: AsyncSession) -> Optional[Account]:
    """
    Obtiene la cuenta a partir de una API key.

    Args:
        api_key: API key
        db: Sesión de base de datos

    Returns:
        Account o None si no se pudo obtener
    """
    try:
        # Usar el repositorio de cuentas para obtener la cuenta por API key hasheada
        from src.db.repositories.account import AccountRepository

        account_repo = AccountRepository(db)
        account = await account_repo.get_by_api_key(api_key)

        return account
    except Exception as e:
        log_error(f"Error obteniendo cuenta desde API key: {str(e)}")
        return None


async def get_user_and_account_ids(
    request: Request, db: AsyncSession
) -> Tuple[Optional[int], Optional[int]]:
    """
    Extrae los IDs de usuario y cuenta del request.

    Args:
        request: Request de FastAPI
        db: Sesión de base de datos

    Returns:
        Tupla (user_id, account_id) o (None, None) si no se pudieron obtener
    """
    try:
        account = await get_account_from_request(request, db)
        if not account:
            return None, None

        # Extraer user_id del token
        token = request.headers.get("Authorization", "").replace("Bearer ", "")
        if token:
            try:
                payload = jwt.decode(
                    token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM]
                )
                user_id = payload.get("sub")
                if user_id:
                    return user_id, account.account_id
            except JWTError:
                pass

        # Si no hay token o no tiene user_id, devolver solo account_id
        return None, account.account_id
    except Exception as e:
        log_error(f"Error obteniendo IDs de usuario y cuenta: {str(e)}")
        return None, None
