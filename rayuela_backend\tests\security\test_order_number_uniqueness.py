"""
Tests para verificar la unicidad de order_number por cuenta en tablas particionadas.
"""

import pytest
from sqlalchemy.exc import IntegrityError
from sqlalchemy.ext.asyncio import AsyncSession
from decimal import Decimal

from src.db.models.order import Order
from src.db.models.account import Account
from src.db.models.end_user import EndUser
from src.db.enums import OrderStatus
from src.db import schemas


class TestOrderNumberUniqueness:
    """Test suite para verificar la unicidad de order_number por cuenta."""

    @pytest.fixture
    async def test_accounts(self, db_session: AsyncSession):
        """Crear cuentas de prueba."""
        # Create test accounts
        account1_data = schemas.AccountCreate(name="Test Account 1")
        account2_data = schemas.AccountCreate(name="Test Account 2")
        
        account1 = Account(**account1_data.model_dump())
        account2 = Account(**account2_data.model_dump())
        
        db_session.add(account1)
        db_session.add(account2)
        await db_session.flush()  # Get IDs
        
        # Create test end users for each account
        end_user1 = EndUser(
            account_id=account1.account_id,
            user_id="user1",
            email="<EMAIL>",
            first_name="User",
            last_name="One"
        )
        
        end_user2 = EndUser(
            account_id=account2.account_id,
            user_id="user2", 
            email="<EMAIL>",
            first_name="User",
            last_name="Two"
        )
        
        db_session.add(end_user1)
        db_session.add(end_user2)
        await db_session.commit()
        
        return {
            "account1": account1,
            "account2": account2,
            "end_user1": end_user1,
            "end_user2": end_user2
        }

    @pytest.mark.asyncio
    async def test_order_number_unique_per_account_constraint(self, db_session: AsyncSession, test_accounts):
        """Verifica que la base de datos impida order_number duplicados dentro de la misma cuenta."""
        
        account1 = test_accounts["account1"]
        end_user1 = test_accounts["end_user1"]
        
        # Create first order with a specific order number
        order1 = Order(
            account_id=account1.account_id,
            order_number="ORD-001",
            user_id=end_user1.user_id,
            status=OrderStatus.PENDING,
            total_amount=Decimal("100.00"),
            currency="USD"
        )
        
        db_session.add(order1)
        await db_session.commit()
        
        # Try to create second order with same order number in same account
        order2 = Order(
            account_id=account1.account_id,
            order_number="ORD-001",  # Same order number
            user_id=end_user1.user_id,
            status=OrderStatus.PENDING,
            total_amount=Decimal("200.00"),
            currency="USD"
        )
        
        # This should raise an IntegrityError due to the unique constraint
        with pytest.raises(IntegrityError) as exc_info:
            db_session.add(order2)
            await db_session.commit()
        
        # Verify the error is related to the unique constraint
        error_message = str(exc_info.value)
        assert "uq_order_account_number" in error_message or "order_number" in error_message

    @pytest.mark.asyncio
    async def test_same_order_number_different_accounts_allowed(self, db_session: AsyncSession, test_accounts):
        """Verifica que diferentes cuentas puedan usar el mismo order_number."""
        
        account1 = test_accounts["account1"]
        account2 = test_accounts["account2"]
        end_user1 = test_accounts["end_user1"]
        end_user2 = test_accounts["end_user2"]
        
        # Create order in first account
        order1 = Order(
            account_id=account1.account_id,
            order_number="ORD-001",
            user_id=end_user1.user_id,
            status=OrderStatus.PENDING,
            total_amount=Decimal("100.00"),
            currency="USD"
        )
        
        # Create order in second account with same order number
        order2 = Order(
            account_id=account2.account_id,
            order_number="ORD-001",  # Same order number, different account
            user_id=end_user2.user_id,
            status=OrderStatus.PENDING,
            total_amount=Decimal("200.00"),
            currency="USD"
        )
        
        # Both orders should be created successfully
        db_session.add(order1)
        db_session.add(order2)
        await db_session.commit()
        
        # Verify both orders exist
        assert order1.id is not None
        assert order2.id is not None
        assert order1.account_id != order2.account_id
        assert order1.order_number == order2.order_number

    @pytest.mark.asyncio
    async def test_different_order_numbers_same_account_allowed(self, db_session: AsyncSession, test_accounts):
        """Verifica que la misma cuenta pueda tener múltiples órdenes con números diferentes."""
        
        account1 = test_accounts["account1"]
        end_user1 = test_accounts["end_user1"]
        
        # Create multiple orders with different order numbers
        orders = []
        for i in range(5):
            order = Order(
                account_id=account1.account_id,
                order_number=f"ORD-{i:03d}",
                user_id=end_user1.user_id,
                status=OrderStatus.PENDING,
                total_amount=Decimal(f"{100 + i * 50}.00"),
                currency="USD"
            )
            orders.append(order)
            db_session.add(order)
        
        await db_session.commit()
        
        # Verify all orders were created
        for order in orders:
            assert order.id is not None
            assert order.account_id == account1.account_id

    @pytest.mark.asyncio
    async def test_order_number_case_sensitivity(self, db_session: AsyncSession, test_accounts):
        """Verifica que los order_number sean sensibles a mayúsculas/minúsculas."""
        
        account1 = test_accounts["account1"]
        end_user1 = test_accounts["end_user1"]
        
        # Create orders with different case
        order1 = Order(
            account_id=account1.account_id,
            order_number="ord-001",  # lowercase
            user_id=end_user1.user_id,
            status=OrderStatus.PENDING,
            total_amount=Decimal("100.00"),
            currency="USD"
        )
        
        order2 = Order(
            account_id=account1.account_id,
            order_number="ORD-001",  # uppercase
            user_id=end_user1.user_id,
            status=OrderStatus.PENDING,
            total_amount=Decimal("200.00"),
            currency="USD"
        )
        
        # Both should be allowed (case sensitive)
        db_session.add(order1)
        db_session.add(order2)
        await db_session.commit()
        
        assert order1.id is not None
        assert order2.id is not None
        assert order1.order_number != order2.order_number

    @pytest.mark.asyncio
    async def test_order_number_length_limits(self, db_session: AsyncSession, test_accounts):
        """Verifica los límites de longitud del order_number."""
        
        account1 = test_accounts["account1"]
        end_user1 = test_accounts["end_user1"]
        
        # Test maximum length (50 characters as defined in model)
        long_order_number = "A" * 50
        order = Order(
            account_id=account1.account_id,
            order_number=long_order_number,
            user_id=end_user1.user_id,
            status=OrderStatus.PENDING,
            total_amount=Decimal("100.00"),
            currency="USD"
        )
        
        db_session.add(order)
        await db_session.commit()
        
        assert order.id is not None
        assert order.order_number == long_order_number

    @pytest.mark.asyncio
    async def test_order_number_special_characters(self, db_session: AsyncSession, test_accounts):
        """Verifica que los order_number puedan contener caracteres especiales."""
        
        account1 = test_accounts["account1"]
        end_user1 = test_accounts["end_user1"]
        
        # Test various special characters
        special_order_numbers = [
            "ORD-001-A",
            "ORD_001_B", 
            "ORD.001.C",
            "ORD@001#D",
            "ORD 001 E"
        ]
        
        for order_number in special_order_numbers:
            order = Order(
                account_id=account1.account_id,
                order_number=order_number,
                user_id=end_user1.user_id,
                status=OrderStatus.PENDING,
                total_amount=Decimal("100.00"),
                currency="USD"
            )
            
            db_session.add(order)
        
        await db_session.commit()
        
        # Verify all orders were created
        created_orders = await db_session.execute(
            sa.select(Order).where(Order.account_id == account1.account_id)
        )
        orders = created_orders.scalars().all()
        
        assert len(orders) == len(special_order_numbers)
        created_order_numbers = {order.order_number for order in orders}
        assert created_order_numbers == set(special_order_numbers)

    @pytest.mark.asyncio
    async def test_constraint_name_exists(self, db_session: AsyncSession):
        """Verifica que la restricción de unicidad tenga el nombre correcto."""
        
        # Query the database to check if the constraint exists
        result = await db_session.execute(sa.text("""
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'orders' 
            AND constraint_name = 'uq_order_account_number'
            AND constraint_type = 'UNIQUE'
        """))
        
        constraint = result.fetchone()
        assert constraint is not None, "Unique constraint uq_order_account_number not found"
        assert constraint.constraint_type == "UNIQUE"
