# Guía de Seguridad Multi-Tenant para Rayuela

## 🔒 Introducción

Esta guía establece los estándares y mejores prácticas de seguridad para el sistema multi-tenant de Rayuela. **El aislamiento de datos entre tenants es CRÍTICO** y cualquier violación puede resultar en filtración de datos confidenciales de clientes.

## 🚨 Principios Fundamentales

### 1. Defensa en Profundidad
- **Row Level Security (RLS)** en la base de datos
- **Middleware de tenant** en la aplicación
- **Validación de permisos** en cada endpoint
- **Tests de penetración** automatizados
- **Auditoría continua** de políticas de seguridad

### 2. Principio de Menor Privilegio
- Cada tenant solo puede acceder a sus propios datos
- Las operaciones administrativas requieren permisos explícitos
- Los scripts de mantenimiento respetan el aislamiento de tenants

### 3. Seguridad por Defecto
- Todas las tablas tenant-scoped DEBEN tener RLS habilitado
- Los endpoints nuevos DEBEN incluir validación de tenant
- Los tests de seguridad DEBEN ser bloqueantes en CI/CD

## 🏗️ Arquitectura de Seguridad

### Capas de Protección

```mermaid
graph TD
    A[Cliente] --> B[Load Balancer]
    B --> C[API Gateway]
    C --> D[TenantMiddleware]
    D --> E[Authentication]
    E --> F[Authorization]
    F --> G[Business Logic]
    G --> H[Repository Layer]
    H --> I[Database RLS]
    I --> J[Data Storage]
```

### Componentes Críticos

1. **TenantMiddleware**: Establece contexto de tenant en cada request
2. **BaseRepository**: Aplica filtros de tenant automáticamente
3. **RLS Policies**: Protección a nivel de base de datos
4. **Audit System**: Registro de accesos y operaciones

## 📋 Checklist de Seguridad Pre-Deployment

### ✅ Tests Obligatorios (BLOQUEANTES)
- [ ] `test_multi_tenancy_security.py` - Inyección y bypass attempts
- [ ] `test_multi_tenancy_comprehensive.py` - CRUD isolation completo
- [ ] `test_tenant_middleware_comprehensive.py` - Middleware validation
- [ ] `test_celery_tenant_isolation_extended.py` - Async task isolation
- [ ] `test_multi_tenancy_penetration.py` - Penetration testing
- [ ] `verify_rls_comprehensive.py` - RLS policies verification
- [ ] `multi_tenancy_security_audit.py` - Comprehensive security audit

### ✅ Verificaciones de Base de Datos
- [ ] Todas las tablas tenant-scoped tienen RLS habilitado
- [ ] Políticas RLS cubren SELECT, INSERT, UPDATE, DELETE
- [ ] No existen datos huérfanos (sin account_id)
- [ ] Row security está habilitado globalmente

### ✅ Verificaciones de Aplicación
- [ ] TenantMiddleware está registrado y activo
- [ ] Todos los endpoints validan permisos de tenant
- [ ] Cache keys incluyen account_id para aislamiento
- [ ] Logs de auditoría registran account_id

## 🛡️ Tablas Tenant-Scoped

### Tablas que REQUIEREN RLS:
```sql
-- Datos principales
products
end_users
interactions
searches
recommendations

-- ML y entrenamiento
artifact_metadata
training_jobs
batch_ingestion_jobs
training_metrics

-- Sistema y auditoría
system_users
system_user_roles
audit_logs
notifications

-- Facturación y uso
account_usage_metrics
subscriptions
endpoint_metrics
```

### Ejemplo de Política RLS:
```sql
-- Política para tabla products
CREATE POLICY tenant_select_policy ON products
    FOR SELECT
    USING (account_id = current_setting('app.current_account_id')::bigint);

CREATE POLICY tenant_insert_policy ON products
    FOR INSERT
    WITH CHECK (account_id = current_setting('app.current_account_id')::bigint);

CREATE POLICY tenant_update_policy ON products
    FOR UPDATE
    USING (account_id = current_setting('app.current_account_id')::bigint)
    WITH CHECK (account_id = current_setting('app.current_account_id')::bigint);

CREATE POLICY tenant_delete_policy ON products
    FOR DELETE
    USING (account_id = current_setting('app.current_account_id')::bigint);

-- Habilitar RLS en la tabla
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
```

## 🔍 Patrones de Ataque Comunes y Mitigaciones

### 1. Inyección de account_id en URL
**Ataque**: `/api/v1/products?account_id=OTHER_TENANT_ID`
**Mitigación**: Middleware ignora parámetros de account_id del cliente

### 2. Manipulación de Headers HTTP
**Ataque**: `X-Account-ID: OTHER_TENANT_ID`
**Mitigación**: Headers de tenant no son respetados, solo auth token

### 3. Inyección en Request Body
**Ataque**: `{"name": "product", "account_id": OTHER_TENANT_ID}`
**Mitigación**: BaseRepository siempre sobrescribe account_id

### 4. SQL Injection para Bypass RLS
**Ataque**: `'; SET row_security = off; --`
**Mitigación**: Políticas RLS forzadas, prepared statements

### 5. Cache Poisoning
**Ataque**: Contaminar cache con claves de otros tenants
**Mitigación**: Cache keys incluyen account_id, aislamiento por tenant

## 🚀 Pipeline de CI/CD Seguro

### Pasos de Seguridad en cloudbuild.yaml:

1. **Tests Unitarios** - Validación básica
2. **Tests de Integración** - Funcionalidad end-to-end
3. **🔒 Tests Críticos de Multi-Tenancy** (BLOQUEANTE)
4. **🔐 Verificación RLS** (BLOQUEANTE)
5. **🕵️ Tests de Penetración** (BLOQUEANTE)
6. **📋 Auditoría Final de Seguridad** (BLOQUEANTE)
7. **Migraciones de DB** - Solo si todo pasa
8. **Deployment** - Solo si todo pasa

### Fallos Bloqueantes:
- Cualquier test de multi-tenancy que falle
- Políticas RLS faltantes o incorrectas
- Vulnerabilidades detectadas en penetration testing
- Problemas críticos en auditoría de seguridad

## 📊 Monitoreo y Alertas

### Métricas Críticas:
- **Cross-tenant data access attempts** - Debe ser 0
- **RLS policy violations** - Debe ser 0
- **Failed authentication by tenant** - Monitor for anomalies
- **Unusual data access patterns** - Baseline and alert

### Logs de Auditoría:
```python
# Ejemplo de log de auditoría
{
    "timestamp": "2024-01-01T12:00:00Z",
    "account_id": 123,
    "user_id": 456,
    "action": "product.read",
    "resource_id": "product_789",
    "ip_address": "***********",
    "user_agent": "...",
    "success": true
}
```

## 🔧 Desarrollo Seguro

### Checklist para Nuevos Features:

#### 📝 Al crear nuevas tablas:
- [ ] ¿La tabla necesita aislamiento por tenant?
- [ ] Si sí, ¿tiene columna `account_id`?
- [ ] ¿RLS está habilitado?
- [ ] ¿Políticas RLS están implementadas para CRUD?
- [ ] ¿Tests de aislamiento están escritos?

#### 📝 Al crear nuevos endpoints:
- [ ] ¿El endpoint está protegido por autenticación?
- [ ] ¿TenantMiddleware está aplicado?
- [ ] ¿Se valida que el usuario pertenece al tenant correcto?
- [ ] ¿Los datos retornados están filtrados por tenant?
- [ ] ¿Tests de cross-tenant access están escritos?

#### 📝 Al modificar repositorios:
- [ ] ¿Se usa BaseRepository o se aplican filtros manualmente?
- [ ] ¿Todas las queries incluyen filtro de account_id?
- [ ] ¿Los updates/deletes verifican ownership?
- [ ] ¿Tests cubren todos los casos de aislamiento?

## 🚨 Procedimiento de Incidentes de Seguridad

### En caso de sospecha de filtración de datos:

1. **INMEDIATO** - Aislar el sistema afectado
2. **INMEDIATO** - Revisar logs de acceso de las últimas 24h
3. **INMEDIATO** - Notificar al equipo de seguridad
4. **1 HORA** - Identificar alcance de la filtración
5. **2 HORAS** - Implementar parche de emergencia si es necesario
6. **4 HORAS** - Notificar a clientes afectados
7. **24 HORAS** - Post-mortem y plan de prevención

### Contactos de Emergencia:
- **Security Team**: <EMAIL>
- **DevOps Lead**: <EMAIL>
- **CTO**: <EMAIL>

## 📚 Recursos Adicionales

### Scripts de Utilidad:
- `scripts/verify_rls_comprehensive.py` - Verificación manual de RLS
- `scripts/multi_tenancy_security_audit.py` - Auditoría completa
- `scripts/check_tenant_data_integrity.py` - Verificación de integridad

### Tests de Referencia:
- `tests/integration/test_multi_tenancy_security.py`
- `tests/integration/test_multi_tenancy_comprehensive.py`
- `tests/integration/test_multi_tenancy_penetration.py`

### Documentación:
- [PostgreSQL Row Level Security](https://www.postgresql.org/docs/current/ddl-rowsecurity.html)
- [OWASP Multi-Tenancy Security](https://owasp.org/www-project-top-10-privacy-risks/)
- [FastAPI Security Best Practices](https://fastapi.tiangolo.com/tutorial/security/)

## ⚡ Quick Reference

### Comandos Útiles:

```bash
# Ejecutar todos los tests de seguridad multi-tenant
pytest tests/integration/test_multi_tenancy_*.py -v

# Verificar políticas RLS
python scripts/verify_rls_comprehensive.py

# Auditoría completa de seguridad
python scripts/multi_tenancy_security_audit.py --environment=production --read-only

# Verificar que no hay datos cross-tenant
python scripts/check_tenant_data_integrity.py

# Ejecutar tests de penetración
pytest tests/integration/test_multi_tenancy_penetration.py -v -x
```

### Queries de Verificación:

```sql
-- Verificar RLS habilitado en todas las tablas críticas
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('products', 'end_users', 'interactions');

-- Verificar políticas RLS existentes
SELECT schemaname, tablename, policyname, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Verificar datos huérfanos
SELECT 'products' as table_name, COUNT(*) as orphan_count 
FROM products WHERE account_id IS NULL
UNION ALL
SELECT 'end_users', COUNT(*) FROM end_users WHERE account_id IS NULL
UNION ALL
SELECT 'interactions', COUNT(*) FROM interactions WHERE account_id IS NULL;
```

---

**🔐 RECUERDA: La seguridad multi-tenant es responsabilidad de TODOS. Un solo error puede comprometer datos de clientes.**

**📞 En caso de dudas sobre seguridad, SIEMPRE consulta con el equipo de seguridad antes de proceder.** 