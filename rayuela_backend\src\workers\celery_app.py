"""
Celery application configuration for background tasks.
"""

from celery import Celery
from celery.schedules import crontab
from src.core.config import settings

# Create Celery app
celery_app = Celery(
    "ml_training_worker",
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL,
)

# Configure Celery
celery_app.conf.update(
    # Serialization settings
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",

    # Time settings
    timezone="UTC",
    enable_utc=True,
    task_track_started=True,

    # Default task settings
    task_time_limit=3600 * 2,  # 2 hours default time limit
    task_soft_time_limit=3600 * 1.5,  # 1.5 hours soft time limit

    # Worker settings
    worker_max_tasks_per_child=10,  # Restart worker after 10 tasks to prevent memory leaks
    worker_prefetch_multiplier=1,  # Don't prefetch more than one task

    # Queue settings
    task_default_queue="default",
    task_queues={
        "training": {
            "exchange": "training",
            "routing_key": "training",
        },
        "batch_processing": {
            "exchange": "batch_processing",
            "routing_key": "batch_processing",
        },
        "maintenance": {
            "exchange": "maintenance",
            "routing_key": "maintenance",
        },
        "default": {
            "exchange": "default",
            "routing_key": "default",
        },
    },

    # Task routing
    task_routes={
        # Training tasks
        "train_model": {"queue": "training"},
        "train_model_for_job": {"queue": "training"},

        # Batch processing tasks
        "process_batch_data": {"queue": "batch_processing"},

        # Maintenance tasks
        "cleanup_old_audit_logs": {"queue": "maintenance"},
        "cleanup_old_interactions": {"queue": "maintenance"},
        "cleanup_old_data_secure": {"queue": "maintenance"},
        "monitor_high_volume_tables": {"queue": "maintenance"},
        "manage_partitions_task": {"queue": "maintenance"},
        "reset_monthly_api_calls": {"queue": "maintenance"},
        "update_storage_usage": {"queue": "maintenance"},
        "measure_storage_usage": {"queue": "maintenance"},

        # Storage meter tasks
        "get_storage_usage": {"queue": "default"},
    }
)

# Configure periodic tasks
celery_app.conf.beat_schedule = {
    # Legacy cleanup tasks (kept for backward compatibility)
    "cleanup-old-audit-logs": {
        "task": "src.workers.celery_tasks.cleanup_old_audit_logs",
        "schedule": 86400.0,  # Once a day
        "kwargs": {"days_to_keep": 90, "account_id": None},  # None para todas las cuentas
        "options": {"queue": "maintenance"},
    },
    "cleanup-old-interactions": {
        "task": "src.workers.celery_tasks.cleanup_old_interactions",
        "schedule": 86400.0 * 7,  # Once a week
        "kwargs": {"days_to_keep": 180, "batch_size": 10000},
        "options": {"queue": "maintenance"},
    },

    # New archive and cleanup tasks (recommended) - OPTIMIZED FOR STARTUP COSTS
    "archive-and-cleanup-old-audit-logs": {
        "task": "archive_and_cleanup_old_audit_logs",
        "schedule": 86400.0,  # Once a day
        "kwargs": {"days_to_keep": 30, "account_id": None, "batch_size": 10000},  # Reduced from 90 to 30 days
        "options": {"queue": "maintenance"},
    },
    "archive-and-cleanup-old-interactions": {
        "task": "archive_and_cleanup_old_interactions",
        "schedule": 86400.0 * 7,  # Once a week
        "kwargs": {"days_to_keep": 60, "account_id": None, "batch_size": 10000},  # Reduced from 180 to 60 days
        "options": {"queue": "maintenance"},
    },

    # Soft delete physical cleanup tasks - OPTIMIZED FOR STARTUP COSTS
    "cleanup-soft-deleted-records": {
        "task": "cleanup_soft_deleted_records",
        "schedule": 86400.0 * 30,  # Once a month
        "kwargs": {"retention_days": 90, "account_id": None, "dry_run": False},  # Reduced from 365 to 90 days
        "options": {"queue": "maintenance"},
    },
    "cleanup-old-data-secure": {
        "task": "src.workers.celery_tasks.cleanup_old_data_secure_task",
        "schedule": 86400.0 * 14,  # Once every two weeks
        "kwargs": {"days_to_keep": 90},  # Reduced from 365 to 90 days
        "options": {"queue": "maintenance"},
    },
    "monitor-high-volume-tables": {
        "task": "src.workers.celery_tasks.monitor_high_volume_tables",
        "schedule": 3600.0,  # Once an hour
        "options": {"queue": "maintenance"},
    },
    "manage-partitions": {
        "task": "src.workers.celery_tasks_partition.manage_partitions_task",
        "schedule": crontab(hour=1, minute=0),  # Daily at 1:00 AM (low traffic time)
        "options": {"queue": "maintenance"},
    },
    "reset-monthly-api-calls": {
        "task": "src.tasks.subscription_tasks.reset_monthly_api_calls",
        "schedule": 86400.0,  # Once a day at midnight
        "options": {"queue": "maintenance"},
    },
    "update-storage-usage": {
        "task": "src.tasks.subscription_tasks.update_storage_usage",
        "schedule": 86400.0,  # Once a day
        "options": {"queue": "maintenance"},
    },
    "measure-storage-usage": {
        "task": "src.tasks.storage_meter_tasks.measure_storage_usage",
        "schedule": 86400.0,  # Once a day
        "options": {"queue": "maintenance"},
    },
}

# Importar tareas para asegurar que están registradas correctamente
# Esto evita el error de "Received unregistered task of type"
# No elimines estas importaciones
import src.workers.celery_tasks
import src.workers.celery_tasks_partition

# También importamos las tareas que están en otros módulos y son usadas en el beat_schedule
try:
    import src.tasks.subscription_tasks
    import src.tasks.storage_meter_tasks
except ImportError:
    # Si estos módulos no existen, lo registramos pero no fallamos
    # ya que podrían ser opcionales o estar en desarrollo
    import logging
    logging.warning("No se pudieron importar todos los módulos de tareas opcionales.")
