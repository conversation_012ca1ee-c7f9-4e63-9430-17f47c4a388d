"""
Repository for handling usage history queries.
"""
from datetime import datetime
from typing import Dict, List
from sqlalchemy import func, select, and_
from sqlalchemy.ext.asyncio import AsyncSession

from src.db.models import AuditLog
from src.db.repositories.base import BaseRepository

class UsageRepository(BaseRepository):
    def __init__(self, db: AsyncSession):
        super().__init__(db)
        
    async def get_daily_api_calls(
        self,
        account_id: int,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, int]:
        """
        Get daily API calls for an account within a date range.
        
        Args:
            account_id: The account ID to query
            start_date: Start date for the query
            end_date: End date for the query
            
        Returns:
            Dictionary mapping dates to API call counts
        """
        query = (
            select(
                func.date_trunc('day', AuditLog.created_at).label('date'),
                func.count().label('api_calls')
            )
            .where(
                and_(
                    AuditLog.account_id == account_id,
                    AuditLog.created_at >= start_date,
                    AuditLog.created_at <= end_date
                )
            )
            .group_by(func.date_trunc('day', AuditLog.created_at))
            .order_by(func.date_trunc('day', AuditLog.created_at))
        )
        
        result = await self.db.execute(query)
        return {
            row.date.strftime('%Y-%m-%d'): row.api_calls 
            for row in result
        } 