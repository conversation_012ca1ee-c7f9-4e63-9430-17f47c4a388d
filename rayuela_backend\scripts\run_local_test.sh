#!/bin/bash
# Script para ejecutar pruebas locales de Rayuela en Linux/Mac
# Este script configura y ejecuta todos los componentes necesarios para probar Rayuela localmente

# Obtener el directorio raíz
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# Cambiar al directorio raíz
cd "$ROOT_DIR" || { echo "Error: No se pudo cambiar al directorio raíz"; exit 1; }

# Lista para almacenar PIDs de procesos
PIDS=()
DOCKER_STARTED=false

# Función para limpiar procesos al salir
cleanup() {
    echo -e "\n\033[33mLimpiando procesos...\033[0m"

    # Detener contenedores Docker
    if [ "$DOCKER_STARTED" = true ]; then
        echo -e "\033[33mDeteniendo contenedores Docker...\033[0m"
        if [ -f "docker-compose.simple.yml" ]; then
            docker-compose -f docker-compose.simple.yml down
        else
            docker-compose down
        fi
    fi

    # Detener procesos
    for pid in "${PIDS[@]}"; do
        if ps -p "$pid" > /dev/null; then
            echo -e "\033[33mDeteniendo proceso $pid...\033[0m"
            kill -9 "$pid" 2>/dev/null
        fi
    done

    echo -e "\033[32mLimpieza completada.\033[0m"
}

# Registrar la función de limpieza para ejecutarse al salir
trap cleanup EXIT INT TERM

# Parsear argumentos
SKIP_DOCKER=false
SKIP_DB_INIT=false
SKIP_API=false
SKIP_WORKERS=false
SKIP_TESTS=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --skip-docker)
            SKIP_DOCKER=true
            shift
            ;;
        --skip-db-init)
            SKIP_DB_INIT=true
            shift
            ;;
        --skip-api)
            SKIP_API=true
            shift
            ;;
        --skip-workers)
            SKIP_WORKERS=true
            shift
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        *)
            echo "Opción desconocida: $1"
            exit 1
            ;;
    esac
done

# Iniciar servicios de Docker
if [ "$SKIP_DOCKER" = false ]; then
    echo -e "\033[36mIniciando servicios de Docker...\033[0m"

    # Verificar si docker-compose.simple.yml existe
    if [ -f "docker-compose.simple.yml" ]; then
        docker-compose -f docker-compose.simple.yml up -d
    else
        docker-compose up -d db redis
    fi

    if [ $? -ne 0 ]; then
        echo -e "\033[31mError al iniciar servicios de Docker.\033[0m"
        exit 1
    fi

    DOCKER_STARTED=true
    echo -e "\033[32mServicios de Docker iniciados correctamente.\033[0m"
    sleep 5  # Esperar a que los servicios estén listos
fi

# Activar entorno virtual si existe
if [ -f "venv/bin/activate" ]; then
    echo -e "\033[36mActivando entorno virtual...\033[0m"
    source venv/bin/activate
fi

# Inicializar base de datos
if [ "$SKIP_DB_INIT" = false ]; then
    echo -e "\033[36mInicializando base de datos...\033[0m"

    # Primero, crear la base de datos si no existe
    echo -e "\033[36mCreando base de datos si no existe...\033[0m"
    python -m scripts.start create-db

    if [ $? -ne 0 ]; then
        echo -e "\033[31mError al crear la base de datos.\033[0m"
        exit 1
    fi

    # Luego, aplicar migraciones
    echo -e "\033[36mAplicando migraciones...\033[0m"
    python -m scripts.start migrate-db

    if [ $? -ne 0 ]; then
        echo -e "\033[31mError al aplicar migraciones.\033[0m"
        exit 1
    fi

    # Finalmente, insertar datos iniciales
    echo -e "\033[36mInsertando datos iniciales...\033[0m"
    python -m scripts.start seed-db

    # No verificamos el código de salida aquí porque los datos iniciales podrían ser opcionales

    echo -e "\033[32mBase de datos inicializada correctamente.\033[0m"
fi

# Iniciar servidor API
if [ "$SKIP_API" = false ]; then
    echo -e "\033[36mIniciando servidor API...\033[0m"
    python main.py &
    API_PID=$!
    PIDS+=("$API_PID")

    echo -e "\033[32mServidor API iniciado correctamente (PID: $API_PID).\033[0m"
    sleep 3  # Esperar a que el servidor esté listo
fi

# Iniciar workers de Celery
if [ "$SKIP_WORKERS" = false ]; then
    echo -e "\033[36mIniciando workers de Celery...\033[0m"

    # Worker para tareas por defecto
    celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --queues=default --hostname=default@%h &
    DEFAULT_WORKER_PID=$!
    PIDS+=("$DEFAULT_WORKER_PID")

    # Worker para tareas de entrenamiento
    celery -A src.workers.celery_app worker --loglevel=info --concurrency=1 --max-tasks-per-child=1 --queues=training --hostname=training@%h &
    TRAINING_WORKER_PID=$!
    PIDS+=("$TRAINING_WORKER_PID")

    # Celery Beat para tareas periódicas
    celery -A src.workers.celery_app beat --loglevel=info &
    BEAT_PID=$!
    PIDS+=("$BEAT_PID")

    echo -e "\033[32mWorkers de Celery iniciados correctamente.\033[0m"
    sleep 3  # Esperar a que los workers estén listos
fi

# Ejecutar pruebas
if [ "$SKIP_TESTS" = false ]; then
    echo -e "\033[36mEjecutando pruebas...\033[0m"
    python -m scripts.run_tests all

    if [ $? -ne 0 ]; then
        echo -e "\033[31mError al ejecutar las pruebas.\033[0m"
        exit 1
    fi

    echo -e "\033[32mPruebas ejecutadas correctamente.\033[0m"
fi

echo -e "\n\033[32mTodo el proceso se completó correctamente.\033[0m"
echo -e "\033[33mPresiona Ctrl+C para detener todos los servicios.\033[0m"

# Mantener el script en ejecución hasta que el usuario lo detenga
while true; do
    sleep 1
done
