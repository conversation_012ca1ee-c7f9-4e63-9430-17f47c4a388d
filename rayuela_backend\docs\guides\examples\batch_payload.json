{"users": [{"external_id": "user001"}, {"external_id": "user002"}, {"external_id": "user003"}, {"external_id": "user004"}, {"external_id": "user005"}], "products": [{"name": "Smartphone XYZ", "description": "Un smartphone de última generación con pantalla OLED y cámara de 48MP", "price": 499.99, "category": "electronics", "average_rating": 4.5, "num_ratings": 120, "inventory_count": 50}, {"name": "Laptop ABC", "description": "Laptop para profesionales con procesador i7 y 16GB RAM", "price": 1299.99, "category": "electronics", "average_rating": 4.8, "num_ratings": 85, "inventory_count": 30}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "description": "Auriculares inalámbricos con cancelación de ruido", "price": 89.99, "category": "electronics", "average_rating": 4.2, "num_ratings": 210, "inventory_count": 75}, {"name": "Zapatillas Running Pro", "description": "Zapatillas para correr con amortiguación avanzada", "price": 79.99, "category": "sports", "average_rating": 4.6, "num_ratings": 95, "inventory_count": 120}, {"name": "Camiseta Deportiva", "description": "Camiseta transpirable para actividades físicas", "price": 24.99, "category": "clothing", "average_rating": 4.0, "num_ratings": 150, "inventory_count": 200}, {"name": "Libro 'Aprendiendo Python'", "description": "Guía completa para principiantes en Python", "price": 34.99, "category": "books", "average_rating": 4.7, "num_ratings": 65, "inventory_count": 40}, {"name": "Cafetera Automática", "description": "Cafetera con molinillo integrado y programable", "price": 149.99, "category": "home", "average_rating": 4.3, "num_ratings": 110, "inventory_count": 25}, {"name": "<PERSON><PERSON><PERSON>", "description": "Mochila resistente al agua con múltiples compartimientos", "price": 59.99, "category": "travel", "average_rating": 4.4, "num_ratings": 78, "inventory_count": 60}], "interactions": [{"user_id": 1, "product_id": 1, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 1, "product_id": 1, "interaction_type": "CLICK", "value": 1.0}, {"user_id": 1, "product_id": 2, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 1, "product_id": 2, "interaction_type": "CART", "value": 1.0}, {"user_id": 1, "product_id": 2, "interaction_type": "PURCHASE", "value": 1.0}, {"user_id": 2, "product_id": 1, "interaction_type": "SEARCH", "value": 1.0}, {"user_id": 2, "product_id": 1, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 2, "product_id": 1, "interaction_type": "LIKE", "value": 1.0}, {"user_id": 2, "product_id": 3, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 2, "product_id": 3, "interaction_type": "RATING", "value": 4.5}, {"user_id": 3, "product_id": 4, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 3, "product_id": 4, "interaction_type": "CART", "value": 1.0}, {"user_id": 3, "product_id": 4, "interaction_type": "PURCHASE", "value": 1.0}, {"user_id": 3, "product_id": 5, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 3, "product_id": 5, "interaction_type": "WISHLIST", "value": 1.0}, {"user_id": 4, "product_id": 6, "interaction_type": "SEARCH", "value": 1.0}, {"user_id": 4, "product_id": 6, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 4, "product_id": 6, "interaction_type": "PURCHASE", "value": 1.0}, {"user_id": 4, "product_id": 6, "interaction_type": "RATING", "value": 5.0}, {"user_id": 5, "product_id": 7, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 5, "product_id": 8, "interaction_type": "VIEW", "value": 1.0}, {"user_id": 5, "product_id": 8, "interaction_type": "FAVORITE", "value": 1.0}]}