"""update_subscription_plan_enum

Revision ID: f2d345c271dd
Revises: 1d65dfd484b3
Create Date: 2025-04-21 22:51:44.164266

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f2d345c271dd'
down_revision: Union[str, None] = '1d65dfd484b3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to update subscription plan enum."""
    # Since we're having issues with the enum type, let's just create a new column
    # with the new enum type and drop the old one
    
    # Create the new enum type
    op.execute("CREATE TYPE subscriptionplan_new AS ENUM ('FREE', 'STARTER', 'PRO', 'ENTERPRISE');")
    
    # Add a new column with the new enum type
    op.execute("ALTER TABLE subscriptions ADD COLUMN plan_type_new subscriptionplan_new;")
    
    # Copy the data from the old column to the new one
    op.execute("""
    UPDATE subscriptions 
    SET plan_type_new = 
        CASE 
            WHEN plan_type::text = 'BASIC' THEN 'STARTER'::subscriptionplan_new
            WHEN plan_type::text = 'FREE' THEN 'FREE'::subscriptionplan_new
            WHEN plan_type::text = 'PRO' THEN 'PRO'::subscriptionplan_new
            WHEN plan_type::text = 'ENTERPRISE' THEN 'ENTERPRISE'::subscriptionplan_new
            ELSE 'FREE'::subscriptionplan_new
        END;
    """)
    
    # Drop the old column
    op.execute("ALTER TABLE subscriptions DROP COLUMN plan_type;")
    
    # Rename the new column to the original name
    op.execute("ALTER TABLE subscriptions RENAME COLUMN plan_type_new TO plan_type;")
    
    # Rename the new enum type to the original name
    op.execute("DROP TYPE subscriptionplan;")
    op.execute("ALTER TYPE subscriptionplan_new RENAME TO subscriptionplan;")


def downgrade() -> None:
    """Downgrade schema to revert subscription plan enum changes."""
    # Since we're having issues with the enum type, let's just create a new column
    # with the old enum type and drop the new one
    
    # Create the old enum type
    op.execute("CREATE TYPE subscriptionplan_old AS ENUM ('FREE', 'BASIC', 'PRO', 'ENTERPRISE');")
    
    # Add a new column with the old enum type
    op.execute("ALTER TABLE subscriptions ADD COLUMN plan_type_old subscriptionplan_old;")
    
    # Copy the data from the new column to the old one
    op.execute("""
    UPDATE subscriptions 
    SET plan_type_old = 
        CASE 
            WHEN plan_type::text = 'STARTER' THEN 'BASIC'::subscriptionplan_old
            WHEN plan_type::text = 'FREE' THEN 'FREE'::subscriptionplan_old
            WHEN plan_type::text = 'PRO' THEN 'PRO'::subscriptionplan_old
            WHEN plan_type::text = 'ENTERPRISE' THEN 'ENTERPRISE'::subscriptionplan_old
            ELSE 'FREE'::subscriptionplan_old
        END;
    """)
    
    # Drop the new column
    op.execute("ALTER TABLE subscriptions DROP COLUMN plan_type;")
    
    # Rename the old column to the original name
    op.execute("ALTER TABLE subscriptions RENAME COLUMN plan_type_old TO plan_type;")
    
    # Rename the old enum type to the original name
    op.execute("DROP TYPE subscriptionplan;")
    op.execute("ALTER TYPE subscriptionplan_old RENAME TO subscriptionplan;")
