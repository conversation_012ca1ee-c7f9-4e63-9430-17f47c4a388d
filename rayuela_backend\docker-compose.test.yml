version: '3.8'

services:
  # Test database for integration tests
  test-db:
    image: postgres:13-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: rayuela_test
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Test Redis for integration tests
  test-redis:
    image: redis:6-alpine
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Test runner service
  test-runner:
    build:
      context: .
      dockerfile: Dockerfile.test
    depends_on:
      test-db:
        condition: service_healthy
      test-redis:
        condition: service_healthy
    environment:
      - ENV=test
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_SERVER=test-db
      - POSTGRES_PORT=5432
      - POSTGRES_DB=rayuela_test
      - REDIS_HOST=test-redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=
      - SECRET_KEY=test_secret_key_for_ci_cd_pipeline_at_least_32_chars
    volumes:
      - ./:/app
    command: >
      bash -c "
        echo 'Waiting for database...' &&
        sleep 5 &&
        alembic upgrade head &&
        echo 'Running comprehensive multi-tenancy, transaction atomicity, and security tests...' &&
        python -m pytest tests/integration/test_multi_tenancy_comprehensive.py tests/middleware/test_tenant_middleware_comprehensive.py tests/unit/db/repositories/test_base_repository_tenant.py tests/integration/test_celery_tenant_isolation_extended.py tests/integration/test_transaction_atomicity.py -v --tb=short --cov=src --cov-report=xml --cov-fail-under=80
      "
