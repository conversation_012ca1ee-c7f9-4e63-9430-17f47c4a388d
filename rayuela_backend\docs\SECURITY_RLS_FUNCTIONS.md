# Security Considerations for RLS Functions

This document outlines the security considerations and improvements made to the PostgreSQL `SECURITY DEFINER` functions used in Rayuela's Row-Level Security (RLS) implementation.

## Overview

Rayuela uses PostgreSQL's Row-Level Security (RLS) to enforce tenant isolation at the database level. This provides a strong additional layer of defense, ensuring that even if application-level filtering is bypassed, the database itself will restrict access to only the current tenant's data.

To support maintenance operations that need to operate across tenant boundaries, PostgreSQL `SECURITY DEFINER` functions are used. These functions run with the privileges of the function creator (typically a superuser) rather than the caller, allowing them to bypass RLS policies when necessary.

## Security Risks

The original implementation had several security risks:

1. **SQL Injection**: The `cleanup_old_data` function used string concatenation with user-provided parameters, creating a potential SQL injection vulnerability.
2. **Lack of Parameter Validation**: No validation was performed on input parameters, allowing potentially harmful values.
3. **Excessive Privileges**: The `bypass_rls` function had no tenant context, allowing it to bypass RLS for any tenant.
4. **No Audit Trail**: Operations that bypassed <PERSON><PERSON> were not logged, making it difficult to track potential misuse.
5. **Error Handling**: Poor error handling could expose sensitive information or leave the database in an inconsistent state.

## Security Improvements

The following improvements have been implemented:

### 1. Secure `cleanup_old_data` Function

The new implementation:

- Validates all input parameters before use
- Uses parameterized queries instead of string concatenation
- Checks if tables exist before operating on them
- Adds proper error handling and logging
- Returns detailed operation results for auditing
- Uses explicit tenant filtering to ensure operations only affect the specified tenant

```sql
CREATE OR REPLACE FUNCTION cleanup_old_data(
    p_account_id integer,
    p_days_old integer
)
RETURNS json AS $$
DECLARE
    v_deleted_interactions integer := 0;
    v_archived_products integer := 0;
    v_inactivated_users integer := 0;
    v_result json;
    v_table_exists boolean;
    v_cutoff_date timestamp with time zone;
BEGIN
    -- Validate input parameters
    IF p_account_id IS NULL OR p_account_id <= 0 THEN
        RAISE EXCEPTION 'Invalid account_id: %. Must be a positive integer.', p_account_id;
    END IF;
    
    IF p_days_old IS NULL OR p_days_old < 1 OR p_days_old > 3650 THEN
        RAISE EXCEPTION 'Invalid days_old: %. Must be between 1 and 3650.', p_days_old;
    END IF;
    
    -- Calculate cutoff date using a safe method
    v_cutoff_date := NOW() - (p_days_old * INTERVAL '1 day');
    
    -- Log operation for audit purposes
    RAISE NOTICE 'Cleanup operation started for account_id=% with days_old=%', p_account_id, p_days_old;
    
    -- Execute as superuser to bypass RLS
    SET SESSION AUTHORIZATION DEFAULT;
    
    -- Perform cleanup operations with explicit tenant filtering
    -- ...
    
    -- Return detailed results
    RETURN v_result;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE NOTICE 'Error in cleanup_old_data: %', SQLERRM;
        RAISE;
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER;
```

### 2. Secure `bypass_rls` Function

The new implementation:

- Requires a specific account ID parameter
- Logs usage for audit purposes
- Sets the tenant context to ensure operations only affect the specified tenant
- Provides clear documentation on proper usage

```sql
CREATE OR REPLACE FUNCTION bypass_rls(
    p_account_id integer
)
RETURNS void AS $$
DECLARE
    v_original_user text;
BEGIN
    -- Validate input parameter
    IF p_account_id IS NULL OR p_account_id <= 0 THEN
        RAISE EXCEPTION 'Invalid account_id: %. Must be a positive integer.', p_account_id;
    END IF;
    
    -- Store original user for restoration
    SELECT current_user INTO v_original_user;
    
    -- Log operation for audit purposes
    RAISE NOTICE 'RLS bypass started for account_id=% by user=%', p_account_id, v_original_user;
    
    -- Execute as superuser to bypass RLS
    SET SESSION AUTHORIZATION DEFAULT;
    
    -- Set tenant context to ensure operations only affect the specified tenant
    PERFORM set_config('app.tenant_id', p_account_id::text, false);
    
    -- Note: The caller is responsible for restoring the session when done
    -- by calling RESET SESSION AUTHORIZATION;
EXCEPTION
    WHEN OTHERS THEN
        -- Log error and re-raise
        RAISE NOTICE 'Error in bypass_rls: %', SQLERRM;
        RAISE;
END;
$$ LANGUAGE plpgsql
SECURITY DEFINER;
```

### 3. Secure Utility Functions

New utility functions have been added to safely interact with these PostgreSQL functions:

- `execute_cleanup_old_data`: Safely executes the `cleanup_old_data` function with proper error handling
- `RLSBypassContext`: A context manager for safely bypassing RLS and ensuring proper session restoration

```python
async def execute_cleanup_old_data(
    db: AsyncSession,
    account_id: int,
    days_to_keep: int
) -> Dict[str, Any]:
    """
    Executes the cleanup_old_data PostgreSQL function securely.
    """
    # Implementation with parameter validation and error handling
    ...

class RLSBypassContext:
    """
    Context manager for safely bypassing RLS for a specific account.
    """
    async def __aenter__(self):
        # Implementation that safely bypasses RLS
        ...
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Implementation that ensures session restoration
        ...
```

### 4. Celery Task and API Endpoint

A new Celery task and API endpoint have been added to use these secure functions:

- `cleanup_old_data_secure_task`: A Celery task that uses the secure PostgreSQL function
- `/maintenance/cleanup-data-secure`: An API endpoint that requires admin privileges and a specific account ID

## Best Practices for Using RLS Functions

1. **Always specify a tenant ID**: Never bypass RLS without specifying a specific tenant ID.
2. **Use the utility functions**: Always use the provided utility functions rather than calling the PostgreSQL functions directly.
3. **Limit access to maintenance role**: Strictly control which users have the `maintenance_role` role.
4. **Monitor usage**: Regularly review database logs for usage of these functions.
5. **Run tests**: Regularly run the RLS tests to ensure tenant isolation is maintained.

## Testing

The security of these functions is verified by the tests in `tests/integration/test_rls_functions_security.py`, which check:

1. Parameter validation
2. SQL injection protection
3. Tenant isolation
4. Proper functionality

## Conclusion

These improvements significantly enhance the security of Rayuela's RLS implementation by addressing the risks of SQL injection, parameter validation, excessive privileges, and lack of audit trail. By following the best practices outlined in this document, you can safely perform maintenance operations while maintaining tenant isolation.
