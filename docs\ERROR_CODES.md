# Códigos de Error de la API de Rayuela

Este documento describe los códigos de error estandarizados que puede devolver la API. Estos códigos permiten a los desarrolladores manejar errores específicos de forma programática, sin depender del mensaje de error que podría cambiar.

## Formato de Respuesta de Error

Todas las respuestas de error siguen un formato consistente:

```json
{
  "message": "Descripción del error",
  "error_code": "CÓDIGO_DE_ERROR",
  "path": "/api/v1/ruta/del/endpoint"
}
```

- `message`: Descripción legible del error
- `error_code`: Código de error estandarizado (ver lista a continuación)
- `path`: Ruta del endpoint que generó el error
- `error_id`: (Opcional) Identificador único del error para referencia en soporte

## Categorías de Errores

### Errores de Autenticación y Autorización (401, 403)

| Código | Descripción | HTTP Status | Cuan<PERSON> |
|--------|-------------|-------------|--------------|
| `INVALID_CREDENTIALS` | Credenciales inválidas (usuario/contraseña incorrectos) | 401 | Al intentar obtener un token JWT con credenciales incorrectas |
| `INVALID_TOKEN` | Token JWT inválido o expirado | 401 | Cuando un token ha caducado o ha sido manipulado |
| `INVALID_API_KEY` | API Key inválida o no proporcionada | 401 | Al usar una API Key incorrecta o no proporcionar ninguna |
| `INSUFFICIENT_PERMISSIONS` | Permisos insuficientes para realizar la acción | 403 | Al intentar acceder a un recurso sin los permisos necesarios |
| `EMAIL_NOT_VERIFIED` | Email no verificado | 403 | Al intentar ciertas operaciones con una cuenta cuyo email no ha sido verificado |

### Errores de Recursos No Encontrados (404)

| Código | Descripción | HTTP Status | Cuando Ocurre |
|--------|-------------|-------------|--------------|
| `ACCOUNT_NOT_FOUND` | Cuenta no encontrada | 404 | Al intentar acceder a una cuenta que no existe |
| `USER_NOT_FOUND` | Usuario no encontrado | 404 | Al buscar un usuario que no existe en la cuenta |
| `PRODUCT_NOT_FOUND` | Producto no encontrado | 404 | Al intentar acceder a un producto inexistente |
| `RESOURCE_NOT_FOUND` | Recurso genérico no encontrado | 404 | Al intentar acceder a cualquier otro recurso inexistente |
| `MODEL_NOT_FOUND` | Modelo no encontrado | 404 | Al intentar acceder a un modelo entrenado inexistente |

### Errores de Validación y Conflictos (400, 409, 422)

| Código | Descripción | HTTP Status | Cuando Ocurre |
|--------|-------------|-------------|--------------|
| `VALIDATION_ERROR` | Error de validación de datos | 422 | Cuando los datos enviados no cumplen con el esquema requerido |
| `DUPLICATE_ENTRY` | Entrada duplicada | 409 | Al intentar crear un recurso que ya existe (ej. usuario con el mismo email) |
| `INVALID_DATA` | Datos inválidos | 400 | Cuando los datos son válidos en formato pero inválidos en contexto |
| `CONFLICT` | Conflicto con el estado actual | 409 | Cuando la operación no es posible debido al estado actual del recurso |

### Errores de Límites (429, 400)

| Código | Descripción | HTTP Status | Cuando Ocurre |
|--------|-------------|-------------|--------------|
| `RATE_LIMIT_EXCEEDED` | Límite de tasa excedido (demasiadas peticiones) | 429 | Al exceder el número de solicitudes permitidas por minuto |
| `RESOURCE_LIMIT_EXCEEDED` | Límite de recursos excedido (cuota de plan) | 429 | Al exceder límites como almacenamiento o número de llamadas a la API |
| `SUBSCRIPTION_LIMIT` | Límite de suscripción alcanzado | 400 | Al intentar usar características que exceden los límites del plan actual |
| `TRAINING_FREQUENCY_LIMIT` | Límite de frecuencia de entrenamiento excedido | 429 | Al intentar entrenar modelos con mayor frecuencia de la permitida |

### Errores de Recomendación (500, específicos del dominio)

| Código | Descripción | HTTP Status | Cuando Ocurre |
|--------|-------------|-------------|--------------|
| `RECOMMENDATION_ERROR` | Error general de recomendación | 500 | Cuando ocurre un error al generar recomendaciones |
| `MODEL_NOT_TRAINED` | Modelo no entrenado | 500 | Al solicitar recomendaciones sin haber entrenado un modelo previamente |
| `TRAINING_ERROR` | Error en el entrenamiento | 500 | Cuando ocurre un error durante el proceso de entrenamiento |

### Errores de Base de Datos y Sistema (500)

| Código | Descripción | HTTP Status | Cuando Ocurre |
|--------|-------------|-------------|--------------|
| `DATABASE_ERROR` | Error de base de datos | 500 | Cuando ocurre un error en operaciones de base de datos |
| `PARTITION_ERROR` | Error de partición | 500 | Cuando ocurre un error en el manejo de particiones de tablas |
| `INTERNAL_ERROR` | Error interno del servidor | 500 | Error genérico del servidor |
| `EXTERNAL_SERVICE_ERROR` | Error de servicio externo | 500 | Cuando falla un servicio externo del que depende la API |

## Detalles Adicionales para Errores Específicos

### `VALIDATION_ERROR` (422)

Para errores de validación, la respuesta incluirá detalles adicionales sobre los campos específicos que fallaron la validación:

```json
{
  "message": "Validation error",
  "error_code": "VALIDATION_ERROR",
  "path": "/api/v1/products",
  "detail": [
    {
      "loc": ["body", "name"],
      "msg": "field required",
      "type": "value_error.missing"
    },
    {
      "loc": ["body", "price"],
      "msg": "ensure this value is greater than 0",
      "type": "value_error.number.not_gt"
    }
  ]
}
```

### `RESOURCE_LIMIT_EXCEEDED` y `SUBSCRIPTION_LIMIT` (429, 400)

Para errores de límites, la respuesta puede incluir información adicional sobre el límite excedido:

```json
{
  "message": "API call limit exceeded for your plan. Current: 15000, Limit: 10000",
  "error_code": "RESOURCE_LIMIT_EXCEEDED",
  "path": "/api/v1/recommendations/personalized",
  "account_id": 123,
  "current_usage": 15000,
  "limit": 10000,
  "upgrade_url": "https://account.rayuela.com/billing/plans"
}
```

### `TRAINING_FREQUENCY_LIMIT` (429)

```json
{
  "message": "Training frequency limit exceeded. You can train again in 23 hours.",
  "error_code": "TRAINING_FREQUENCY_LIMIT",
  "path": "/api/v1/pipeline/train",
  "account_id": 123,
  "next_available_at": "2023-10-25T14:30:00Z"
}
```

## Ejemplos de Manejo de Errores

### Ejemplo en JavaScript/TypeScript

```javascript
async function fetchRecommendations(apiKey, userId, filters = {}) {
  try {
    const response = await fetch('https://api.rayuela.com/v1/recommendations/personalized/query', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey
      },
      body: JSON.stringify({ user_id: userId, filters })
    });
    
    if (!response.ok) {
      const error = await response.json();
      const errorCode = error.error_code;
      
      switch(errorCode) {
        case 'INVALID_API_KEY':
          console.error('API Key inválida o expirada');
          // Redirigir a página de configuración de API Key
          window.location.href = '/settings/api-keys';
          break;
          
        case 'USER_NOT_FOUND':
          console.error(`Usuario ${userId} no encontrado`);
          // Podríamos crear el usuario automáticamente o mostrar recomendaciones generales
          return fetchPopularRecommendations(apiKey);
          
        case 'MODEL_NOT_TRAINED':
          console.error('No hay modelo entrenado para esta cuenta');
          // Mostrar recomendaciones populares como fallback
          return fetchPopularRecommendations(apiKey);
          
        case 'RATE_LIMIT_EXCEEDED':
          console.error('Límite de tasa excedido, esperando...');
          // Esperar y reintentar con backoff exponencial
          await sleep(2000); // 2 segundos
          return fetchRecommendations(apiKey, userId, filters);
          
        case 'RESOURCE_LIMIT_EXCEEDED':
        case 'SUBSCRIPTION_LIMIT':
          console.error(`Límite de plan alcanzado: ${error.message}`);
          // Mostrar notificación para actualizar el plan
          showUpgradePlanModal(error.upgrade_url);
          return fetchPopularRecommendations(apiKey); // Fallback a recomendaciones populares
          
        case 'VALIDATION_ERROR':
          console.error('Error de validación:', error.detail);
          // Corregir datos y reintentar
          const correctedFilters = fixFilterValidationErrors(filters, error.detail);
          return fetchRecommendations(apiKey, userId, correctedFilters);
          
        default:
          console.error(`Error: [${errorCode}] ${error.message}`);
          // Registrar error para análisis
          logErrorToAnalytics(errorCode, error.message, userId);
          // Mostrar error genérico
          showGenericError();
          return null;
      }
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error de red:', error);
    return null;
  }
}

// Función auxiliar para delay
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}
```

### Ejemplo en Python

```python
import time
import requests
from requests.exceptions import RequestException

class RayuelaApiError(Exception):
    """Excepción personalizada para errores de la API de Rayuela"""
    def __init__(self, error_code, message, path=None, status_code=None, details=None):
        self.error_code = error_code
        self.message = message
        self.path = path
        self.status_code = status_code
        self.details = details
        super().__init__(f"[{error_code}] {message}")

def get_recommendations(api_key, user_id, filters=None, max_retries=3, retry_delay=2):
    """
    Obtiene recomendaciones personalizadas con manejo exhaustivo de errores
    
    Args:
        api_key (str): API Key de Rayuela
        user_id (str): ID del usuario para las recomendaciones
        filters (dict, optional): Filtros a aplicar
        max_retries (int, optional): Número máximo de reintentos para errores recuperables
        retry_delay (int, optional): Segundos a esperar entre reintentos (se duplica en cada intento)
    
    Returns:
        dict: Datos de recomendaciones
        
    Raises:
        RayuelaApiError: Para errores no recuperables
    """
    url = "https://api.rayuela.com/v1/recommendations/personalized/query"
    headers = {
        "Content-Type": "application/json",
        "X-API-Key": api_key
    }
    payload = {
        "user_id": user_id
    }
    
    if filters:
        payload["filters"] = filters
        
    retries = 0
    current_delay = retry_delay
    
    while retries <= max_retries:
        try:
            response = requests.post(url, headers=headers, json=payload)
            
            if response.status_code == 200:
                return response.json()
                
            # Procesar error
            error_data = response.json()
            error_code = error_data.get("error_code", "UNKNOWN_ERROR")
            message = error_data.get("message", "Unknown error")
            path = error_data.get("path")
            
            # Clasificar errores
            # 1. Errores recuperables con reintento
            if error_code in ["RATE_LIMIT_EXCEEDED"] and retries < max_retries:
                print(f"Error recuperable: {error_code}. Reintentando en {current_delay} segundos...")
                time.sleep(current_delay)
                retries += 1
                current_delay *= 2  # Backoff exponencial
                continue
                
            # 2. Errores que requieren fallback a recomendaciones populares
            if error_code in ["USER_NOT_FOUND", "MODEL_NOT_TRAINED"]:
                print(f"Usando recomendaciones populares como fallback debido a: {error_code}")
                return get_popular_recommendations(api_key)
                
            # 3. Errores de autenticación/autorización
            if error_code in ["INVALID_API_KEY", "INVALID_CREDENTIALS", "INSUFFICIENT_PERMISSIONS"]:
                raise RayuelaApiError(
                    error_code=error_code,
                    message=f"Error de autenticación: {message}",
                    path=path,
                    status_code=response.status_code
                )
                
            # 4. Errores de validación que podemos intentar corregir
            if error_code == "VALIDATION_ERROR" and retries < max_retries:
                details = error_data.get("detail", [])
                print(f"Error de validación: {details}")
                
                # Intentar corregir filtros basados en errores
                if filters and any("filters" in loc[0] for loc in details if isinstance(loc, list)):
                    filters = fix_filters(filters, details)
                    payload["filters"] = filters
                    retries += 1
                    continue
                    
            # 5. Errores de límites de plan
            if error_code in ["RESOURCE_LIMIT_EXCEEDED", "SUBSCRIPTION_LIMIT"]:
                print(f"Límite de plan alcanzado: {message}")
                # Podríamos enviar un email al administrador
                notify_admin_about_plan_limits(error_data)
                return get_popular_recommendations(api_key)
                
            # 6. Cualquier otro error
            raise RayuelaApiError(
                error_code=error_code,
                message=message,
                path=path,
                status_code=response.status_code,
                details=error_data.get("detail")
            )
                
        except RequestException as e:
            # Error de red
            if retries < max_retries:
                print(f"Error de red. Reintentando en {current_delay} segundos...")
                time.sleep(current_delay)
                retries += 1
                current_delay *= 2
                continue
            else:
                raise RayuelaApiError(
                    error_code="NETWORK_ERROR",
                    message=f"Error de red después de {max_retries} intentos: {str(e)}"
                )
    
    # No debería llegar aquí, pero por si acaso
    raise RayuelaApiError(
        error_code="MAX_RETRIES_EXCEEDED",
        message=f"Se excedió el número máximo de reintentos ({max_retries})"
    )

def get_popular_recommendations(api_key):
    """Obtiene recomendaciones populares como fallback"""
    url = "https://api.rayuela.com/v1/recommendations/most-sold"
    headers = {"X-API-Key": api_key}
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json()
    except Exception as e:
        # En caso de error extremo, devolver lista vacía
        print(f"Error al obtener recomendaciones populares: {e}")
        return {"items": []}

def fix_filters(filters, validation_errors):
    """Intenta corregir filtros basados en errores de validación"""
    # Implementación específica para corregir errores comunes de filtros
    # ...
    return filters

def notify_admin_about_plan_limits(error_data):
    """Notifica al administrador sobre límites de plan alcanzados"""
    # Implementación para enviar email, generar alerta, etc.
    # ...
    pass
```

### Ejemplo en PHP

```php
<?php
/**
 * Cliente de API de Rayuela con manejo detallado de errores
 */
class RayuelaClient {
    private $apiKey;
    private $baseUrl;
    
    public function __construct($apiKey, $baseUrl = 'https://api.rayuela.com/v1') {
        $this->apiKey = $apiKey;
        $this->baseUrl = $baseUrl;
    }
    
    /**
     * Obtiene recomendaciones personalizadas para un usuario
     */
    public function getRecommendations($userId, $filters = null, $maxRetries = 3) {
        $url = "{$this->baseUrl}/recommendations/personalized/query";
        $payload = [
            'user_id' => $userId
        ];
        
        if ($filters) {
            $payload['filters'] = $filters;
        }
        
        $retries = 0;
        $retryDelay = 2;
        
        while ($retries <= $maxRetries) {
            try {
                $response = $this->makeRequest('POST', $url, $payload);
                return $response;
            } catch (RayuelaApiException $e) {
                $errorCode = $e->getErrorCode();
                
                // Errores recuperables con reintento
                if ($errorCode === 'RATE_LIMIT_EXCEEDED' && $retries < $maxRetries) {
                    $currentDelay = $retryDelay * pow(2, $retries);
                    $this->log("Rate limit excedido. Reintentando en {$currentDelay} segundos...");
                    sleep($currentDelay);
                    $retries++;
                    continue;
                }
                
                // Fallback a recomendaciones populares
                if (in_array($errorCode, ['USER_NOT_FOUND', 'MODEL_NOT_TRAINED'])) {
                    $this->log("Usando recomendaciones populares como fallback debido a: {$errorCode}");
                    return $this->getPopularRecommendations();
                }
                
                // Errores de límites de plan
                if (in_array($errorCode, ['RESOURCE_LIMIT_EXCEEDED', 'SUBSCRIPTION_LIMIT'])) {
                    $this->log("Límite de plan alcanzado: {$e->getMessage()}");
                    // Notificar al administrador
                    $this->notifyAdminAboutPlanLimits($e->getData());
                    return $this->getPopularRecommendations();
                }
                
                // Errores de validación que podemos intentar corregir
                if ($errorCode === 'VALIDATION_ERROR' && $retries < $maxRetries) {
                    $this->log("Error de validación: " . json_encode($e->getDetails()));
                    
                    if ($filters && $this->hasFilterValidationErrors($e->getDetails())) {
                        $filters = $this->fixFilters($filters, $e->getDetails());
                        $payload['filters'] = $filters;
                        $retries++;
                        continue;
                    }
                }
                
                // Para otros errores, simplemente relanzar
                throw $e;
            }
        }
        
        throw new RayuelaApiException(
            'MAX_RETRIES_EXCEEDED',
            "Se excedió el número máximo de reintentos ({$maxRetries})"
        );
    }
    
    /**
     * Realiza una solicitud a la API de Rayuela
     */
    private function makeRequest($method, $url, $data = null) {
        $ch = curl_init();
        
        $options = [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                "X-API-Key: {$this->apiKey}"
            ]
        ];
        
        if ($method === 'POST') {
            $options[CURLOPT_POST] = true;
            $options[CURLOPT_POSTFIELDS] = json_encode($data);
        }
        
        curl_setopt_array($ch, $options);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if (curl_errno($ch)) {
            throw new Exception("Error de cURL: " . curl_error($ch));
        }
        
        curl_close($ch);
        
        $responseData = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorCode = $responseData['error_code'] ?? 'UNKNOWN_ERROR';
            $message = $responseData['message'] ?? 'Error desconocido';
            $path = $responseData['path'] ?? '';
            $details = $responseData['detail'] ?? null;
            
            throw new RayuelaApiException($errorCode, $message, $path, $httpCode, $details, $responseData);
        }
        
        return $responseData;
    }
    
    private function getPopularRecommendations() {
        try {
            return $this->makeRequest('GET', "{$this->baseUrl}/recommendations/most-sold");
        } catch (Exception $e) {
            // En caso extremo, devolver array vacío
            $this->log("Error al obtener recomendaciones populares: {$e->getMessage()}");
            return ['items' => []];
        }
    }
    
    private function hasFilterValidationErrors($details) {
        // Verificar si hay errores relacionados con filtros
        // Implementación específica
        return true;
    }
    
    private function fixFilters($filters, $validationErrors) {
        // Corregir filtros basados en errores de validación
        // Implementación específica
        return $filters;
    }
    
    private function notifyAdminAboutPlanLimits($errorData) {
        // Notificar al administrador sobre límites de plan alcanzados
        // Implementación específica
    }
    
    private function log($message) {
        // Log para debugging
        error_log("[RayuelaClient] {$message}");
    }
}

/**
 * Excepción personalizada para errores de la API de Rayuela
 */
class RayuelaApiException extends Exception {
    private $errorCode;
    private $path;
    private $statusCode;
    private $details;
    private $data;
    
    public function __construct($errorCode, $message, $path = '', $statusCode = null, $details = null, $data = []) {
        $this->errorCode = $errorCode;
        $this->path = $path;
        $this->statusCode = $statusCode;
        $this->details = $details;
        $this->data = $data;
        
        parent::__construct($message);
    }
    
    public function getErrorCode() {
        return $this->errorCode;
    }
    
    public function getPath() {
        return $this->path;
    }
    
    public function getStatusCode() {
        return $this->statusCode;
    }
    
    public function getDetails() {
        return $this->details;
    }
    
    public function getData() {
        return $this->data;
    }
}
```

### Ejemplo en cURL

```bash
#!/bin/bash

# Función para manejar errores
handle_error() {
    local response=$1
    local error_code=$(echo $response | jq -r '.error_code')
    local message=$(echo $response | jq -r '.message')
    
    echo "Error: [$error_code] $message"
    
    case $error_code in
        "INVALID_API_KEY")
            echo "La API Key no es válida. Verifique sus credenciales."
            exit 1
            ;;
        "RATE_LIMIT_EXCEEDED")
            echo "Se excedió el límite de tasa. Esperando 5 segundos..."
            sleep 5
            # Aquí se podría llamar de nuevo al script para reintentar
            ;;
        "USER_NOT_FOUND")
            echo "Usuario no encontrado. Obteniendo recomendaciones populares..."
            get_popular_recommendations
            ;;
        *)
            echo "Error no manejado específicamente."
            exit 1
            ;;
    esac
}

# Función para obtener recomendaciones populares
get_popular_recommendations() {
    echo "Obteniendo recomendaciones populares..."
    response=$(curl -s -X GET "https://api.rayuela.com/v1/recommendations/most-sold" \
                   -H "X-API-Key: $API_KEY")
    
    http_code=$?
    
    if [ $http_code -ne 0 ]; then
        echo "Error de red al obtener recomendaciones populares"
        exit 1
    fi
    
    # Verificar si hay un error
    if [ $(echo $response | jq 'has("error_code")') = true ]; then
        handle_error "$response"
    else
        echo "Recomendaciones populares obtenidas con éxito:"
        echo $response | jq '.items[]'
    fi
}

# Función principal
main() {
    API_KEY="tu_api_key"
    USER_ID="usuario123"
    
    echo "Obteniendo recomendaciones para $USER_ID..."
    
    response=$(curl -s -X POST "https://api.rayuela.com/v1/recommendations/personalized/query" \
                   -H "X-API-Key: $API_KEY" \
                   -H "Content-Type: application/json" \
                   -d "{\"user_id\": \"$USER_ID\"}")
    
    http_code=$?
    
    if [ $http_code -ne 0 ]; then
        echo "Error de red al obtener recomendaciones"
        exit 1
    fi
    
    # Verificar si hay un error
    if [ $(echo $response | jq 'has("error_code")') = true ]; then
        handle_error "$response"
    else
        echo "Recomendaciones obtenidas con éxito:"
        echo $response | jq '.items[]'
    fi
}

# Ejecutar script
main
```

## Notas para Desarrolladores

- **Importante:** Siempre verifique el código de error (`error_code`) en lugar de analizar el mensaje de error (`message`), ya que los mensajes pueden cambiar pero los códigos son estables.
- Los códigos de error son constantes y no cambiarán entre versiones de la API.
- Si recibe un error con código `INTERNAL_ERROR`, por favor repórtelo al equipo de soporte con el ID de error si está disponible.
- Para errores de validación (`VALIDATION_ERROR`), la respuesta puede incluir detalles adicionales sobre los campos específicos que fallaron la validación.
- Implemente una estrategia de reintento con backoff exponencial para errores recuperables como `RATE_LIMIT_EXCEEDED`.
- Tenga en cuenta que algunos errores como `MODEL_NOT_TRAINED` pueden requerir acciones específicas (como entrenar un modelo) antes de reintentar.
- Si recibe errores de límites (`RESOURCE_LIMIT_EXCEEDED`, `SUBSCRIPTION_LIMIT`), considere actualizar su plan de suscripción.

## Contacto de Soporte

Si encuentra errores que no puede resolver o tiene preguntas sobre cómo manejar ciertos códigos de error, contáctenos:

- **Email:** <EMAIL>
- **Portal de Soporte:** https://support.rayuela.com
- **Documentación:** https://docs.rayuela.com 