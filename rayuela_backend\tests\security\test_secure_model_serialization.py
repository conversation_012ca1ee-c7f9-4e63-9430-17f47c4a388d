"""
Tests para verificar la serialización segura de modelos ML usando joblib.
"""

import pytest
import tempfile
import os
import joblib
import numpy as np
from unittest.mock import patch, MagicMock
from typing import Dict, Any

from src.ml_pipeline.model_artifact_manager import ModelArtifactManager


class TestSecureModelSerialization:
    """Test suite para verificar la serialización segura de modelos."""

    def test_joblib_serialization_basic(self):
        """Verifica que joblib pueda serializar y deserializar modelos básicos."""
        
        # Create a mock model with various data types
        mock_model = {
            "model_type": "collaborative",
            "weights": np.array([0.1, 0.2, 0.3, 0.4, 0.5]),
            "biases": np.array([0.01, 0.02, 0.03]),
            "user_factors": {"user_1": [0.1, 0.2, 0.3], "user_2": [0.4, 0.5, 0.6]},
            "item_factors": {"item_1": [0.7, 0.8, 0.9], "item_2": [0.1, 0.2, 0.3]},
            "metadata": {
                "training_date": "2024-01-01",
                "version": "1.0.0",
                "parameters": {"learning_rate": 0.01, "epochs": 100}
            }
        }
        
        # Test serialization and deserialization
        with tempfile.NamedTemporaryFile(suffix='.joblib', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            # Serialize
            joblib.dump(mock_model, temp_path)
            assert os.path.exists(temp_path)
            
            # Deserialize
            loaded_model = joblib.load(temp_path)
            
            # Verify data integrity
            assert loaded_model["model_type"] == mock_model["model_type"]
            np.testing.assert_array_equal(loaded_model["weights"], mock_model["weights"])
            np.testing.assert_array_equal(loaded_model["biases"], mock_model["biases"])
            assert loaded_model["user_factors"] == mock_model["user_factors"]
            assert loaded_model["item_factors"] == mock_model["item_factors"]
            assert loaded_model["metadata"] == mock_model["metadata"]
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_joblib_vs_pickle_security(self):
        """Verifica que joblib es más seguro que pickle para modelos ML."""
        
        # Create a simple model
        model_data = {
            "weights": np.random.rand(10, 5),
            "biases": np.random.rand(5),
            "config": {"type": "neural_network", "layers": [10, 5, 1]}
        }
        
        # Test joblib serialization
        with tempfile.NamedTemporaryFile(suffix='.joblib', delete=False) as tmp_joblib:
            joblib_path = tmp_joblib.name
        
        try:
            # Serialize with joblib
            joblib.dump(model_data, joblib_path)
            
            # Verify file exists and has content
            assert os.path.exists(joblib_path)
            assert os.path.getsize(joblib_path) > 0
            
            # Deserialize with joblib
            loaded_data = joblib.load(joblib_path)
            
            # Verify data integrity
            np.testing.assert_array_equal(loaded_data["weights"], model_data["weights"])
            np.testing.assert_array_equal(loaded_data["biases"], model_data["biases"])
            assert loaded_data["config"] == model_data["config"]
            
        finally:
            if os.path.exists(joblib_path):
                os.unlink(joblib_path)

    def test_model_artifact_manager_uses_joblib(self):
        """Verifica que ModelArtifactManager use joblib para serialización."""
        
        # Create mock artifacts
        mock_artifacts = {
            "collaborative": {
                "user_factors": np.random.rand(100, 10),
                "item_factors": np.random.rand(50, 10),
                "metrics": {"rmse": 0.85, "mae": 0.65}
            },
            "content_based": {
                "feature_weights": np.random.rand(20),
                "similarity_matrix": np.random.rand(50, 50),
                "metrics": {"precision": 0.75, "recall": 0.80}
            },
            "performance_metrics": {
                "overall_score": 0.78,
                "training_time": 120.5
            }
        }
        
        manager = ModelArtifactManager()
        
        # Test local saving (which uses joblib)
        with tempfile.TemporaryDirectory() as temp_dir:
            artifacts_path = os.path.join(temp_dir, "test_artifacts")
            
            # Mock the async save method
            import asyncio
            async def test_save():
                await manager._save_to_local(artifacts_path, mock_artifacts)
                return artifacts_path
            
            # Run the async save
            saved_path = asyncio.run(test_save())
            
            # Verify files were created
            model_file = os.path.join(artifacts_path, "model.joblib")
            metadata_file = os.path.join(artifacts_path, "metadata.json")
            
            assert os.path.exists(model_file)
            assert os.path.exists(metadata_file)
            
            # Test loading
            async def test_load():
                return await manager._load_from_local(artifacts_path)
            
            loaded_artifacts = asyncio.run(test_load())
            
            # Verify data integrity for numpy arrays
            np.testing.assert_array_equal(
                loaded_artifacts["collaborative"]["user_factors"],
                mock_artifacts["collaborative"]["user_factors"]
            )
            np.testing.assert_array_equal(
                loaded_artifacts["collaborative"]["item_factors"],
                mock_artifacts["collaborative"]["item_factors"]
            )

    def test_gcs_serialization_uses_joblib(self):
        """Verifica que la serialización GCS use joblib."""
        
        mock_artifacts = {
            "model_data": np.random.rand(10, 5),
            "config": {"type": "test_model"},
            "performance_metrics": {"accuracy": 0.95}
        }
        
        manager = ModelArtifactManager()
        
        # Mock the storage client and bucket
        with patch.object(manager, 'storage_client') as mock_client:
            mock_bucket = MagicMock()
            mock_blob = MagicMock()
            mock_client.bucket.return_value = mock_bucket
            mock_bucket.blob.return_value = mock_blob
            
            # Test GCS saving
            import asyncio
            async def test_gcs_save():
                await manager._save_to_gcs("test/path", mock_artifacts)
            
            asyncio.run(test_gcs_save())
            
            # Verify that joblib.dumps was used (indirectly through upload_from_string)
            mock_blob.upload_from_string.assert_called()
            
            # Get the call arguments
            call_args = mock_blob.upload_from_string.call_args_list
            
            # Verify that the first call (model data) used joblib serialization
            # The data should be bytes (result of joblib.dumps)
            model_call = call_args[0]
            model_data = model_call[0][0]  # First positional argument
            assert isinstance(model_data, bytes)

    def test_file_extension_consistency(self):
        """Verifica que las extensiones de archivo sean consistentes con joblib."""
        
        manager = ModelArtifactManager()
        
        # Test that local paths use .joblib extension
        with tempfile.TemporaryDirectory() as temp_dir:
            artifacts_path = os.path.join(temp_dir, "test_model")
            mock_artifacts = {"test": "data"}
            
            import asyncio
            async def test_extension():
                await manager._save_to_local(artifacts_path, mock_artifacts)
                
                # Check that the model file has .joblib extension
                model_file = os.path.join(artifacts_path, "model.joblib")
                assert os.path.exists(model_file)
                
                # Verify it's a valid joblib file
                loaded_data = joblib.load(model_file)
                assert loaded_data == mock_artifacts
            
            asyncio.run(test_extension())

    def test_numpy_array_serialization(self):
        """Verifica que los arrays de NumPy se serialicen correctamente con joblib."""
        
        # Create various NumPy array types
        test_arrays = {
            "float_array": np.random.rand(100, 50).astype(np.float32),
            "int_array": np.random.randint(0, 100, size=(20, 10)),
            "bool_array": np.random.choice([True, False], size=(10, 10)),
            "complex_array": np.random.rand(5, 5) + 1j * np.random.rand(5, 5),
            "sparse_data": {
                "indices": np.array([0, 1, 2, 5, 8]),
                "values": np.array([1.0, 2.5, 3.2, 4.1, 5.9])
            }
        }
        
        with tempfile.NamedTemporaryFile(suffix='.joblib', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            # Serialize
            joblib.dump(test_arrays, temp_path)
            
            # Deserialize
            loaded_arrays = joblib.load(temp_path)
            
            # Verify all arrays
            for key in test_arrays:
                if isinstance(test_arrays[key], np.ndarray):
                    np.testing.assert_array_equal(loaded_arrays[key], test_arrays[key])
                    assert loaded_arrays[key].dtype == test_arrays[key].dtype
                else:
                    # Handle nested dictionaries with arrays
                    for subkey in test_arrays[key]:
                        np.testing.assert_array_equal(
                            loaded_arrays[key][subkey], 
                            test_arrays[key][subkey]
                        )
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_large_model_serialization_performance(self):
        """Verifica que joblib maneje eficientemente modelos grandes."""
        
        # Create a large model (simulating a real ML model)
        large_model = {
            "embedding_matrix": np.random.rand(10000, 300),  # Large embedding matrix
            "weight_matrices": [
                np.random.rand(300, 128),
                np.random.rand(128, 64),
                np.random.rand(64, 1)
            ],
            "bias_vectors": [
                np.random.rand(128),
                np.random.rand(64),
                np.random.rand(1)
            ],
            "vocabulary": {f"word_{i}": i for i in range(10000)},
            "config": {
                "model_type": "neural_network",
                "layers": [300, 128, 64, 1],
                "activation": "relu",
                "optimizer": "adam"
            }
        }
        
        with tempfile.NamedTemporaryFile(suffix='.joblib', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            import time
            
            # Measure serialization time
            start_time = time.time()
            joblib.dump(large_model, temp_path)
            serialize_time = time.time() - start_time
            
            # Measure deserialization time
            start_time = time.time()
            loaded_model = joblib.load(temp_path)
            deserialize_time = time.time() - start_time
            
            # Verify performance is reasonable (should be under 5 seconds each)
            assert serialize_time < 5.0, f"Serialization too slow: {serialize_time}s"
            assert deserialize_time < 5.0, f"Deserialization too slow: {deserialize_time}s"
            
            # Verify data integrity for a few key components
            np.testing.assert_array_equal(
                loaded_model["embedding_matrix"], 
                large_model["embedding_matrix"]
            )
            assert loaded_model["vocabulary"] == large_model["vocabulary"]
            assert loaded_model["config"] == large_model["config"]
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def test_cross_platform_compatibility(self):
        """Verifica que los modelos serializados con joblib sean compatibles entre plataformas."""
        
        model_data = {
            "version": "1.0.0",
            "platform_info": {
                "python_version": "3.9+",
                "joblib_version": joblib.__version__
            },
            "model_weights": np.random.rand(50, 20),
            "feature_names": [f"feature_{i}" for i in range(20)]
        }
        
        with tempfile.NamedTemporaryFile(suffix='.joblib', delete=False) as tmp_file:
            temp_path = tmp_file.name
        
        try:
            # Serialize
            joblib.dump(model_data, temp_path)
            
            # Verify file is not empty and has reasonable size
            file_size = os.path.getsize(temp_path)
            assert file_size > 0
            assert file_size < 10 * 1024 * 1024  # Should be less than 10MB for this test
            
            # Deserialize and verify
            loaded_data = joblib.load(temp_path)
            
            assert loaded_data["version"] == model_data["version"]
            assert loaded_data["feature_names"] == model_data["feature_names"]
            np.testing.assert_array_equal(
                loaded_data["model_weights"], 
                model_data["model_weights"]
            )
            
        finally:
            if os.path.exists(temp_path):
                os.unlink(temp_path)
