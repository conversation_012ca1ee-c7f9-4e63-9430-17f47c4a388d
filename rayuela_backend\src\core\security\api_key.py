"""
Utilidades para la generación y verificación segura de API keys.
"""

import secrets
import hashlib
import base64
from typing import <PERSON><PERSON>


def generate_api_key_simple(length: int = 48) -> str:
    """
    Genera una API key única y segura sin hash.

    Args:
        length: Longitud en bytes de la API key a generar

    Returns:
        La API key en texto plano
    """
    return secrets.token_urlsafe(length)


def generate_api_key(prefix: str = "sk", length: int = 32) -> Tuple[str, str]:
    """
    Genera una nueva API key y su hash correspondiente.

    Args:
        prefix: Prefijo para la API key (por defecto: "sk" para "secret key")
        length: Longitud en bytes de la API key a generar (por defecto: 32 bytes = 256 bits)

    Returns:
        Tupla (api_key, api_key_hash) donde:
            - api_key: La API key en texto plano para mostrar al usuario una sola vez
            - api_key_hash: El hash de la API key para almacenar en la base de datos
    """
    # Generar bytes aleatorios (256 bits) de alta entropía
    random_bytes = secrets.token_bytes(length)

    # Codificar en base64 y eliminar caracteres no alfanuméricos
    encoded = base64.urlsafe_b64encode(random_bytes).decode("utf-8").rstrip("=")

    # Crear la API key con formato: prefijo_base64
    api_key = f"{prefix}_{encoded}"

    # Generar el hash de la API key
    api_key_hash = hash_api_key(api_key)

    return api_key, api_key_hash


def hash_api_key(api_key: str) -> str:
    """
    Genera un hash seguro de una API key.

    Args:
        api_key: La API key en texto plano

    Returns:
        El hash de la API key
    """
    # Usar SHA-256 para el hashing
    return hashlib.sha256(api_key.encode("utf-8")).hexdigest()


def verify_api_key(api_key: str, stored_hash: str) -> bool:
    """
    Verifica si una API key coincide con un hash almacenado.

    Args:
        api_key: La API key proporcionada por el usuario
        stored_hash: El hash almacenado en la base de datos

    Returns:
        True si la API key es válida, False en caso contrario
    """
    # Calcular el hash de la API key proporcionada
    calculated_hash = hash_api_key(api_key)

    # Comparar con el hash almacenado (comparación de tiempo constante)
    return secrets.compare_digest(calculated_hash, stored_hash)
