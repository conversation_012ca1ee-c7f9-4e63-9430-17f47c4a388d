"""
Servicio para análisis de datos y métricas.
"""
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from fastapi import HTTPException, status
from datetime import datetime

from src.utils.api_analytics import APIAnalyticsService
from src.utils.base_logger import log_error
from src.db.models import SystemUser


class AnalyticsService:
    """
    Servicio para análisis de datos y métricas.

    Este servicio encapsula la lógica de acceso a métricas de análisis,
    incluyendo la gestión de permisos y selección de cuenta objetivo.
    """

    def __init__(self, db: AsyncSession):
        """
        Inicializa el servicio de análisis.

        Args:
            db: Sesión de base de datos
        """
        self.db = db

    def _get_target_account(
        self,
        current_account_id: int,
        requested_account_id: Optional[int],
        current_user: SystemUser
    ) -> int:
        """
        Determina la cuenta objetivo para las consultas de análisis.

        Args:
            current_account_id: ID de la cuenta actual
            requested_account_id: ID de la cuenta solicitada (opcional)
            current_user: Usuario actual

        Returns:
            ID de la cuenta objetivo

        Raises:
            HTTPException: Si el usuario no tiene permisos para acceder a la cuenta solicitada
        """
        # Si no se solicita una cuenta específica, usar la cuenta actual
        if requested_account_id is None:
            return current_account_id

        # Si se solicita una cuenta específica, verificar permisos
        if requested_account_id != current_account_id and not current_user.is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You don't have permission to access analytics for this account"
            )

        return requested_account_id

    async def get_account_metrics(
        self,
        current_account_id: int,
        requested_account_id: Optional[int],
        current_user: SystemUser
    ) -> Dict[str, Any]:
        """
        Obtiene métricas a nivel de cuenta.

        Args:
            current_account_id: ID de la cuenta actual
            requested_account_id: ID de la cuenta solicitada (opcional)
            current_user: Usuario actual

        Returns:
            Diccionario con métricas de la cuenta
        """
        try:
            # Determinar la cuenta objetivo
            target_account_id = self._get_target_account(
                current_account_id,
                requested_account_id,
                current_user
            )

            # Usar una transacción para garantizar lecturas consistentes
            async with self.db.begin():
                # Crear servicio de análisis para la cuenta objetivo
                analytics_service = APIAnalyticsService(self.db, target_account_id)

                # Obtener métricas de la cuenta
                return await analytics_service.get_account_metrics()

        except HTTPException:
            # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
            raise
        except Exception as e:
            log_error(f"Error getting account metrics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting account metrics: {str(e)}"
            )

    async def get_endpoint_metrics(
        self,
        current_account_id: int,
        requested_account_id: Optional[int],
        current_user: SystemUser,
        endpoint: Optional[str] = None,
        method: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Obtiene métricas a nivel de endpoint.

        Args:
            current_account_id: ID de la cuenta actual
            requested_account_id: ID de la cuenta solicitada (opcional)
            current_user: Usuario actual
            endpoint: Endpoint específico para filtrar (opcional)
            method: Método HTTP para filtrar (opcional)

        Returns:
            Lista de diccionarios con métricas de endpoints
        """
        try:
            # Determinar la cuenta objetivo
            target_account_id = self._get_target_account(
                current_account_id,
                requested_account_id,
                current_user
            )

            # Usar una transacción para garantizar lecturas consistentes
            async with self.db.begin():
                # Crear servicio de análisis para la cuenta objetivo
                analytics_service = APIAnalyticsService(self.db, target_account_id)

                # Obtener métricas de endpoints
                return await analytics_service.get_endpoint_metrics(endpoint, method)

        except HTTPException:
            # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
            raise
        except Exception as e:
            log_error(f"Error getting endpoint metrics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting endpoint metrics: {str(e)}"
            )

    async def get_recommendation_metrics(
        self,
        current_account_id: int,
        requested_account_id: Optional[int],
        current_user: SystemUser
    ) -> Dict[str, Any]:
        """
        Obtiene métricas de rendimiento de recomendaciones online.

        Estas métricas incluyen:
        - CTR (Click-Through Rate): Porcentaje de recomendaciones en las que se hizo clic
        - CVR (Conversion Rate): Porcentaje de recomendaciones que resultaron en conversiones
        - Engagement: Nivel de interacción con las recomendaciones
        - Latencia: Tiempo de respuesta de las recomendaciones (p50, p90, p99)
        - Cobertura del catálogo: Porcentaje del catálogo que se está recomendando
        - Diversidad: Variedad en las recomendaciones
        - Tiempo de inferencia: Tiempo promedio para generar recomendaciones

        Args:
            current_account_id: ID de la cuenta actual
            requested_account_id: ID de la cuenta solicitada (opcional)
            current_user: Usuario actual

        Returns:
            Diccionario con métricas de rendimiento de recomendaciones
        """
        try:
            # Determinar la cuenta objetivo
            target_account_id = self._get_target_account(
                current_account_id,
                requested_account_id,
                current_user
            )

            # Usar una transacción para garantizar lecturas consistentes
            async with self.db.begin():
                # Crear servicio de análisis para la cuenta objetivo
                analytics_service = APIAnalyticsService(self.db, target_account_id)

                # Obtener métricas de endpoints relacionados con recomendaciones
                recommendation_endpoints = await analytics_service.get_endpoint_metrics(
                    endpoint="/api/v1/recommendations",
                    method=None
                )

                # Calcular métricas específicas de recomendaciones
                total_recommendations = 0
                total_impressions = 0
                total_clicks = 0
                total_conversions = 0
                avg_response_time = 0.0
                p95_response_time = 0.0
                p99_response_time = 0.0

                # Extraer datos de los endpoints de recomendaciones
                for endpoint in recommendation_endpoints:
                    if "personalized" in endpoint.get("endpoint", ""):
                        total_recommendations += endpoint.get("call_count", 0)
                        avg_response_time += endpoint.get("avg_response_time", 0.0)
                        # Acumular percentiles de tiempo de respuesta (si están disponibles)
                        if endpoint.get("p95_response_time"):
                            p95_response_time += endpoint.get("p95_response_time", 0.0)
                        if endpoint.get("p99_response_time"):
                            p99_response_time += endpoint.get("p99_response_time", 0.0)

                # Obtener datos de impresiones (endpoint de interacciones con tipo=impression)
                impression_endpoints = await analytics_service.get_endpoint_metrics(
                    endpoint="/api/v1/interactions",
                    method="POST"
                )

                # Filtrar por tipo de interacción
                from src.db.models import Interaction
                from src.db.enums import InteractionType
                from sqlalchemy import select, func, and_

                # Consultar impresiones, clicks y conversiones directamente
                stmt_impressions = select(func.count()).where(
                    and_(
                        Interaction.account_id == target_account_id,
                        Interaction.interaction_type == InteractionType.VIEW,
                        # Nota: Interaction no tiene recommendation_id actualmente
                        # Esto es una aproximación
                    )
                )
                result = await self.db.execute(stmt_impressions)
                total_impressions = result.scalar() or 0

                stmt_clicks = select(func.count()).where(
                    and_(
                        Interaction.account_id == target_account_id,
                        Interaction.interaction_type == InteractionType.CLICK,
                        # Nota: Interaction no tiene recommendation_id actualmente
                    )
                )
                result = await self.db.execute(stmt_clicks)
                total_clicks = result.scalar() or 0

                stmt_conversions = select(func.count()).where(
                    and_(
                        Interaction.account_id == target_account_id,
                        Interaction.interaction_type == InteractionType.PURCHASE,
                        # Nota: Interaction no tiene recommendation_id actualmente
                    )
                )
                result = await self.db.execute(stmt_conversions)
                total_conversions = result.scalar() or 0

                # Calcular CTR y CVR si hay impresiones
                ctr = (total_clicks / total_impressions) if total_impressions > 0 else 0.0
                cvr = (total_conversions / total_impressions) if total_impressions > 0 else 0.0

                # Calcular tasa de engagement (clicks + conversiones / impresiones)
                engagement_rate = ((total_clicks + total_conversions) / total_impressions) if total_impressions > 0 else 0.0

                # Normalizar los tiempos de respuesta promedio
                if recommendation_endpoints:
                    avg_response_time = avg_response_time / len(recommendation_endpoints)
                    p95_response_time = p95_response_time / len(recommendation_endpoints)
                    p99_response_time = p99_response_time / len(recommendation_endpoints)

                # Construir el resultado
                return {
                    # Métricas de interacción
                    "ctr": ctr,
                    "cvr": cvr,
                    "engagement_rate": engagement_rate,

                    # Contadores
                    "total_recommendations": total_recommendations,
                    "total_impressions": total_impressions,
                    "total_clicks": total_clicks,
                    "total_conversions": total_conversions,

                    # Métricas de rendimiento
                    "avg_response_time": avg_response_time,
                    "p95_response_time": p95_response_time,
                    "p99_response_time": p99_response_time,
                    "recommendation_endpoints": len(recommendation_endpoints),

                    # Metadatos
                    "last_updated": datetime.now().isoformat()
                }

        except HTTPException:
            # Re-lanzar excepciones HTTP para mantener el mismo comportamiento
            raise
        except Exception as e:
            log_error(f"Error getting recommendation metrics: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error getting recommendation metrics: {str(e)}"
            )
