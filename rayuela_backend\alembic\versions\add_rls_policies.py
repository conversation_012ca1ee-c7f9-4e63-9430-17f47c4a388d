"""
Migración para implementar Row-Level Security (RLS) en PostgreSQL.

IMPORTANTE: Esta migración requiere las siguientes variables de entorno:
- APP_DB_PASSWORD: Contraseña para el rol de aplicación
- MAINTENANCE_DB_PASSWORD: Contraseña para el rol de mantenimiento

Estas variables deben ser configuradas antes de ejecutar la migración.
En entornos de producción, estas contraseñas deben ser obtenidas de forma segura
desde un gestor de secretos como GCP Secret Manager.

Revision ID: 001_add_rls_policies
Revises:
Create Date: 2023-05-14 00:00:00.000000
"""

# revision identifiers, used by Alembic
revision = '001_add_rls_policies'
down_revision = None
branch_labels = None
depends_on = None
from typing import List, Dict, Any
from sqlalchemy import text
from alembic import op
import sqlalchemy as sa
import os
import logging

# Lista de tablas que requieren RLS (todas las tablas que usan TenantMixin)
# Solo incluimos tablas que existen en la base de datos actual
TABLES_WITH_RLS = [
    'products',
    'end_users',
    'interactions',
    'searches',
    'system_users',
    'roles',
    'permissions',
    # 'system_user_roles',  # Esta tabla no existe en la migración inicial
    'training_jobs',
    # 'batch_ingestion_jobs',  # Esta tabla no existe en la migración inicial
    'artifact_metadata',  # ModelMetadata.__tablename__
    # 'model_metrics',  # Esta tabla no existe en la migración inicial
    'audit_logs',
    'notifications',
    'account_usage_metrics',
    'subscriptions',
    # Tablas agregadas posteriormente que requieren RLS
    'api_keys',      # Tabla de API keys multi-tenant
    'orders',        # Tabla de órdenes multi-tenant
    'order_items'    # Tabla de items de órdenes multi-tenant
]

def get_secret_from_gcp(secret_name: str):
    """Obtiene un secreto desde Google Cloud Secret Manager."""
    try:
        # Verificar que estamos en producción
        if os.getenv('ENV') != 'production':
            return None

        # Importar cliente de Secret Manager
        from google.cloud import secretmanager
        
        # Obtener ID del proyecto
        project_id = os.getenv('GCP_PROJECT_ID')
        if not project_id:
            logging.warning("GCP_PROJECT_ID no está definido, no se pueden cargar secretos")
            return None
            
        # Crear cliente y obtener secreto
        client = secretmanager.SecretManagerServiceClient()
        secret_path = f"projects/{project_id}/secrets/{secret_name}/versions/latest"
        
        response = client.access_secret_version(name=secret_path)
        return response.payload.data.decode("UTF-8")
    except Exception as e:
        logging.error(f"Error al cargar secreto {secret_name} desde GCP: {str(e)}")
        return None


def upgrade() -> None:
    """Aplica RLS a todas las tablas relevantes."""
    # Habilitar RLS en todas las tablas
    for table in TABLES_WITH_RLS:
        _enable_rls(table)
        _create_rls_policies(table)

    # Crear roles y permisos
    _create_roles()

    # Configurar permisos de bypass para mantenimiento
    _setup_maintenance_permissions()

def downgrade() -> None:
    """Desactiva RLS de todas las tablas."""
    for table in TABLES_WITH_RLS:
        _disable_rls(table)

    # Eliminar roles y permisos
    _drop_roles()

def _enable_rls(table: str) -> None:
    """Habilita RLS en una tabla."""
    op.execute(f"ALTER TABLE {table} ENABLE ROW LEVEL SECURITY")

def _disable_rls(table: str) -> None:
    """Desactiva RLS en una tabla."""
    op.execute(f"ALTER TABLE {table} DISABLE ROW LEVEL SECURITY")

def _create_rls_policies(table: str) -> None:
    """Crea políticas RLS para una tabla."""
    # Política para SELECT
    op.execute(f"""
        CREATE POLICY {table}_select_policy ON {table}
        FOR SELECT
        USING (account_id = current_setting('app.tenant_id')::integer)
    """)

    # Política para INSERT
    op.execute(f"""
        CREATE POLICY {table}_insert_policy ON {table}
        FOR INSERT
        WITH CHECK (account_id = current_setting('app.tenant_id')::integer)
    """)

    # Política para UPDATE
    op.execute(f"""
        CREATE POLICY {table}_update_policy ON {table}
        FOR UPDATE
        USING (account_id = current_setting('app.tenant_id')::integer)
        WITH CHECK (account_id = current_setting('app.tenant_id')::integer)
    """)

    # Política para DELETE
    op.execute(f"""
        CREATE POLICY {table}_delete_policy ON {table}
        FOR DELETE
        USING (account_id = current_setting('app.tenant_id')::integer)
    """)

def _create_roles() -> None:
    """Crea roles necesarios para RLS."""
    # Obtener contraseñas de variables de entorno o GCP Secret Manager
    app_db_password = None
    maintenance_db_password = None
    
    # Primero intentar cargar desde GCP en producción
    if os.getenv('ENV') == 'production':
        app_db_password = get_secret_from_gcp('app-db-password')
        maintenance_db_password = get_secret_from_gcp('maintenance-db-password')
        
        # Registrar resultado de la carga
        if app_db_password:
            logging.info("APP_DB_PASSWORD cargada desde GCP Secret Manager")
        if maintenance_db_password:
            logging.info("MAINTENANCE_DB_PASSWORD cargada desde GCP Secret Manager")
    
    # Si no se pudieron cargar desde GCP o no estamos en producción, usar variables de entorno
    if not app_db_password:
        app_db_password = os.getenv('APP_DB_PASSWORD')
    if not maintenance_db_password:
        maintenance_db_password = os.getenv('MAINTENANCE_DB_PASSWORD')

    # Si todavía no tenemos contraseñas, usar valores por defecto solo para desarrollo
    if not app_db_password:
        app_db_password = 'app_role_password' if os.getenv('ENV') != 'production' else None
        if os.getenv('ENV') != 'production':
            logging.warning("APP_DB_PASSWORD no encontrada, usando contraseña por defecto para desarrollo")
        else:
            raise ValueError("APP_DB_PASSWORD no disponible en producción. Se requiere para crear roles de seguridad.")
    
    if not maintenance_db_password:
        maintenance_db_password = 'maintenance_role_password' if os.getenv('ENV') != 'production' else None
        if os.getenv('ENV') != 'production':
            logging.warning("MAINTENANCE_DB_PASSWORD no encontrada, usando contraseña por defecto para desarrollo")
        else:
            raise ValueError("MAINTENANCE_DB_PASSWORD no disponible en producción. Se requiere para crear roles de seguridad.")

    # Rol para aplicación - Usar SQL directo con comillas simples para la contraseña
    op.execute(f"""
        CREATE ROLE app_role WITH
        LOGIN
        PASSWORD '{app_db_password}'
        INHERIT
    """)

    # Rol para mantenimiento - Usar SQL directo con comillas simples para la contraseña
    op.execute(f"""
        CREATE ROLE maintenance_role WITH
        LOGIN
        PASSWORD '{maintenance_db_password}'
        INHERIT
        BYPASSRLS
    """)

    # Otorgar permisos
    for table in TABLES_WITH_RLS:
        op.execute(f"GRANT SELECT, INSERT, UPDATE, DELETE ON {table} TO app_role")
        op.execute(f"GRANT ALL ON {table} TO maintenance_role")

def _setup_maintenance_permissions() -> None:
    """Configura permisos especiales para tareas de mantenimiento."""
    # Crear función para bypass RLS
    op.execute("""
        CREATE OR REPLACE FUNCTION bypass_rls()
        RETURNS void AS $$
        BEGIN
            SET SESSION AUTHORIZATION DEFAULT;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """)

    # Otorgar permisos de ejecución
    op.execute("GRANT EXECUTE ON FUNCTION bypass_rls() TO maintenance_role")

    # Crear función para limpiar datos antiguos
    op.execute("""
        CREATE OR REPLACE FUNCTION cleanup_old_data(
            p_account_id integer,
            p_days_old integer
        )
        RETURNS void AS $$
        BEGIN
            -- Ejecutar como superusuario para bypass RLS
            SET SESSION AUTHORIZATION DEFAULT;

            -- Limpiar datos antiguos
            DELETE FROM interactions
            WHERE account_id = p_account_id
            AND created_at < NOW() - (p_days_old || ' days')::interval;

            -- Archivar productos inactivos
            UPDATE products
            SET status = 'archived'
            WHERE account_id = p_account_id
            AND last_interaction_at < NOW() - (p_days_old || ' days')::interval;

            -- Inactivar usuarios inactivos
            UPDATE users
            SET status = 'inactive'
            WHERE account_id = p_account_id
            AND last_login_at < NOW() - (p_days_old || ' days')::interval;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """)

    # Otorgar permisos de ejecución
    op.execute("GRANT EXECUTE ON FUNCTION cleanup_old_data(integer, integer) TO maintenance_role")

def _drop_roles() -> None:
    """Elimina roles y funciones de mantenimiento."""
    # Eliminar funciones
    op.execute("DROP FUNCTION IF EXISTS bypass_rls()")
    op.execute("DROP FUNCTION IF EXISTS cleanup_old_data(integer, integer)")

    # Eliminar roles
    op.execute("DROP ROLE IF EXISTS app_role")
    op.execute("DROP ROLE IF EXISTS maintenance_role")