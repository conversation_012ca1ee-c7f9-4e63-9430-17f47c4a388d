#!/usr/bin/env python
"""
Script para ejecutar un test específico sin depender del conftest.py global.
Este script permite ejecutar tests unitarios específicos sin cargar toda la aplicación.
"""

import os
import sys
import pytest
import importlib.util
from pathlib import Path

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)


def run_test(test_file_path):
    """Ejecutar un test específico"""
    # Verificar que el archivo existe
    test_path = Path(test_file_path)
    if not test_path.exists():
        print(f"Error: El archivo {test_file_path} no existe.")
        return 1
    
    # Cargar el módulo de test
    spec = importlib.util.spec_from_file_location("test_module", test_path)
    test_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(test_module)
    
    # Ejecutar los tests en el módulo
    test_functions = [
        name for name in dir(test_module) 
        if name.startswith("test_") and callable(getattr(test_module, name))
    ]
    
    if not test_functions:
        print(f"No se encontraron funciones de test en {test_file_path}")
        return 1
    
    print(f"Ejecutando {len(test_functions)} tests de {test_file_path}:")
    
    # Ejecutar cada función de test
    failures = 0
    for test_name in test_functions:
        test_func = getattr(test_module, test_name)
        print(f"  - Ejecutando {test_name}...", end=" ")
        try:
            test_func()
            print("PASÓ")
        except Exception as e:
            print(f"FALLÓ: {e}")
            failures += 1
    
    # Mostrar resumen
    if failures == 0:
        print(f"\nTodos los {len(test_functions)} tests pasaron correctamente.")
        return 0
    else:
        print(f"\n{failures} de {len(test_functions)} tests fallaron.")
        return 1


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Uso: python -m scripts.run_specific_test <ruta_al_archivo_de_test>")
        sys.exit(1)
    
    test_file_path = sys.argv[1]
    sys.exit(run_test(test_file_path))
