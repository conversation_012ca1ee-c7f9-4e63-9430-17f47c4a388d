# Relaciones en SQLAlchemy

Este documento explica cómo se manejan las relaciones entre modelos en nuestra aplicación utilizando SQLAlchemy.

## Relaciones con `viewonly=True`

En nuestra base de datos, utilizamos el parámetro `viewonly=True` en ciertas relaciones para evitar que SQLAlchemy intente gestionar la tabla de asociación desde ambos lados de una relación, lo que puede causar problemas de sincronización.

### ¿Qué es `viewonly=True`?

El parámetro `viewonly=True` en una relación de SQLAlchemy indica que la relación es de "solo lectura". Esto significa que:

1. No se pueden añadir, eliminar o modificar objetos a través de esta relación.
2. SQLAlchemy no intentará sincronizar cambios en esta relación con la base de datos.
3. La relación solo se utiliza para consultar datos, no para modificarlos.

### ¿Cuándo usamos `viewonly=True`?

Utilizamos `viewonly=True` en los siguientes casos:

1. **Relaciones muchos a muchos (M:N) con clase de asociación explícita**: Cuando tenemos una relación M:N que se gestiona a través de una clase de asociación explícita (como `SystemUserRole`), marcamos como `viewonly=True` las relaciones directas entre las entidades principales para evitar conflictos.

2. **Relaciones inversas en modelos con relaciones uno a muchos (1:N)**: En algunos casos, marcamos como `viewonly=True` la relación inversa (la parte "muchos") para asegurar que los cambios se gestionen solo desde el lado "uno" de la relación.

### Ejemplos en nuestro código

#### Relación M:N entre SystemUser y Role

```python
# En SystemUser
roles = relationship(
    "Role",
    secondary="system_user_roles",
    back_populates="users",
    viewonly=True  # Avoids SQLAlchemy managing the association table from this side.
                  # Use SystemUserRole relationship to manage the association.
)

# En Role
users = relationship(
    "SystemUser",
    secondary="system_user_roles",
    back_populates="roles",
    viewonly=True  # Avoids SQLAlchemy managing the association table from this side.
                  # Use SystemUserRole relationship to manage the association.
)
```

En este ejemplo, tanto `SystemUser.roles` como `Role.users` están marcados como `viewonly=True` porque la relación se gestiona a través de la clase `SystemUserRole`. Para añadir un rol a un usuario, deberíamos crear una instancia de `SystemUserRole` en lugar de modificar directamente `SystemUser.roles` o `Role.users`.

#### Relación 1:N entre EndUser e Interaction

```python
# En EndUser
interactions = relationship(
    "Interaction",
    back_populates="end_user",
    viewonly=True  # Avoids SQLAlchemy managing the relationship from this side.
                  # The relationship is managed through the Interaction model.
)
```

En este caso, `EndUser.interactions` está marcado como `viewonly=True` porque la relación se gestiona desde el lado `Interaction`. Para añadir una interacción a un usuario, deberíamos crear una instancia de `Interaction` con el `end_user_id` correspondiente, en lugar de modificar directamente `EndUser.interactions`.

### Mejores prácticas

1. **Documentar claramente**: Siempre añadir un comentario explicando por qué se usa `viewonly=True` en una relación.
2. **Consistencia**: Si una relación es `viewonly=True`, asegurarse de que el código no intente modificarla directamente.
3. **Gestión explícita**: Utilizar la clase de asociación o el modelo principal para gestionar la relación, no la relación marcada como `viewonly=True`.

## Otras consideraciones sobre relaciones

### Relaciones con `passive_deletes=True`

El parámetro `passive_deletes=True` indica a SQLAlchemy que no debe emitir una consulta DELETE adicional para los objetos relacionados cuando se elimina un objeto principal. En su lugar, confía en que las restricciones de clave externa con `ON DELETE CASCADE` de la base de datos manejarán la eliminación.

```python
system_user_roles = relationship(
    "SystemUserRole",
    back_populates="system_user",
    foreign_keys="[SystemUserRole.system_user_id]",
    primaryjoin="and_(SystemUser.account_id==SystemUserRole.account_id, SystemUser.id==SystemUserRole.system_user_id)",
    passive_deletes=True,
)
```

### Relaciones con `uselist=False`

El parámetro `uselist=False` indica a SQLAlchemy que la relación debe devolver un solo objeto en lugar de una lista, lo que es útil para relaciones uno a uno.

```python
subscription = relationship(
    "Subscription", back_populates="account", uselist=False, passive_deletes=True
)
```

### Relaciones con `foreign_keys` y `primaryjoin`

Estos parámetros se utilizan para especificar explícitamente cómo se debe unir la relación, lo que es especialmente útil en relaciones complejas o cuando hay múltiples claves foráneas entre las mismas tablas.

```python
system_user_roles = relationship(
    "SystemUserRole",
    back_populates="system_user",
    foreign_keys="[SystemUserRole.system_user_id]",
    primaryjoin="and_(SystemUser.account_id==SystemUserRole.account_id, SystemUser.id==SystemUserRole.system_user_id)",
    passive_deletes=True,
)
```
