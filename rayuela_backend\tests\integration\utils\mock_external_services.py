"""
Utilities for mocking external services in integration tests.
"""
import os
import json
import tempfile
from typing import Dict, Any, List, Optional
from unittest.mock import patch, MagicMock
import pytest

from src.core.config import settings


class MockGCSClient:
    """Mock for Google Cloud Storage client."""
    
    def __init__(self):
        self.buckets = {}
        self.temp_dir = tempfile.mkdtemp()
    
    def bucket(self, bucket_name: str):
        """Get a bucket by name."""
        if bucket_name not in self.buckets:
            self.buckets[bucket_name] = MockGCSBucket(bucket_name, self.temp_dir)
        return self.buckets[bucket_name]
    
    def list_buckets(self):
        """List all buckets."""
        return [bucket for bucket in self.buckets.values()]
    
    def create_bucket(self, bucket_name: str):
        """Create a new bucket."""
        if bucket_name not in self.buckets:
            self.buckets[bucket_name] = MockGCSBucket(bucket_name, self.temp_dir)
        return self.buckets[bucket_name]
    
    def get_bucket(self, bucket_name: str):
        """Get a bucket by name."""
        return self.bucket(bucket_name)
    
    def cleanup(self):
        """Clean up temporary files."""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)


class MockGCSBucket:
    """Mock for a Google Cloud Storage bucket."""
    
    def __init__(self, name: str, base_dir: str):
        self.name = name
        self.base_dir = os.path.join(base_dir, name)
        os.makedirs(self.base_dir, exist_ok=True)
        self.blobs = {}
    
    def blob(self, blob_name: str):
        """Get a blob by name."""
        if blob_name not in self.blobs:
            self.blobs[blob_name] = MockGCSBlob(blob_name, self.base_dir)
        return self.blobs[blob_name]
    
    def list_blobs(self, prefix: Optional[str] = None):
        """List all blobs with an optional prefix."""
        if prefix:
            return [blob for name, blob in self.blobs.items() if name.startswith(prefix)]
        return [blob for blob in self.blobs.values()]


class MockGCSBlob:
    """Mock for a Google Cloud Storage blob."""
    
    def __init__(self, name: str, base_dir: str):
        self.name = name
        self.path = os.path.join(base_dir, name)
        self.metadata = {}
        self.content_type = None
    
    def upload_from_string(self, data: str, content_type: Optional[str] = None):
        """Upload data from a string."""
        os.makedirs(os.path.dirname(self.path), exist_ok=True)
        with open(self.path, 'w') as f:
            f.write(data)
        self.content_type = content_type
    
    def upload_from_filename(self, filename: str):
        """Upload data from a file."""
        os.makedirs(os.path.dirname(self.path), exist_ok=True)
        with open(filename, 'rb') as src, open(self.path, 'wb') as dst:
            dst.write(src.read())
    
    def download_to_filename(self, filename: str):
        """Download data to a file."""
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        with open(self.path, 'rb') as src, open(filename, 'wb') as dst:
            dst.write(src.read())
    
    def download_as_string(self):
        """Download data as a string."""
        with open(self.path, 'r') as f:
            return f.read()
    
    def download_as_bytes(self):
        """Download data as bytes."""
        with open(self.path, 'rb') as f:
            return f.read()
    
    def exists(self):
        """Check if the blob exists."""
        return os.path.exists(self.path)
    
    def delete(self):
        """Delete the blob."""
        if os.path.exists(self.path):
            os.remove(self.path)


class MockStripeClient:
    """Mock for Stripe client."""
    
    def __init__(self):
        self.customers = MockStripeCustomers()
        self.subscriptions = MockStripeSubscriptions()
        self.products = MockStripeProducts()
        self.prices = MockStripePrices()
        self.invoices = MockStripeInvoices()
        self.payment_methods = MockStripePaymentMethods()
        self.checkout = MockStripeCheckout()
        self.billing_portal = MockStripeBillingPortal()


class MockStripeCustomers:
    """Mock for Stripe customers."""
    
    def __init__(self):
        self.customers = {}
    
    def create(self, **kwargs):
        """Create a new customer."""
        customer_id = f"cus_{len(self.customers) + 1}"
        customer = {
            "id": customer_id,
            "email": kwargs.get("email", "<EMAIL>"),
            "name": kwargs.get("name", "Test Customer"),
            "metadata": kwargs.get("metadata", {}),
            "created": 1609459200,  # 2021-01-01
        }
        self.customers[customer_id] = customer
        return customer
    
    def retrieve(self, customer_id: str):
        """Retrieve a customer by ID."""
        if customer_id not in self.customers:
            raise ValueError(f"Customer {customer_id} not found")
        return self.customers[customer_id]
    
    def modify(self, customer_id: str, **kwargs):
        """Modify a customer."""
        if customer_id not in self.customers:
            raise ValueError(f"Customer {customer_id} not found")
        self.customers[customer_id].update(kwargs)
        return self.customers[customer_id]
    
    def delete(self, customer_id: str):
        """Delete a customer."""
        if customer_id in self.customers:
            del self.customers[customer_id]
        return {"deleted": True, "id": customer_id}


class MockStripeSubscriptions:
    """Mock for Stripe subscriptions."""
    
    def __init__(self):
        self.subscriptions = {}
    
    def create(self, **kwargs):
        """Create a new subscription."""
        subscription_id = f"sub_{len(self.subscriptions) + 1}"
        subscription = {
            "id": subscription_id,
            "customer": kwargs.get("customer", "cus_1"),
            "items": kwargs.get("items", []),
            "status": "active",
            "current_period_start": 1609459200,  # 2021-01-01
            "current_period_end": 1612137600,  # 2021-02-01
            "metadata": kwargs.get("metadata", {}),
        }
        self.subscriptions[subscription_id] = subscription
        return subscription
    
    def retrieve(self, subscription_id: str):
        """Retrieve a subscription by ID."""
        if subscription_id not in self.subscriptions:
            raise ValueError(f"Subscription {subscription_id} not found")
        return self.subscriptions[subscription_id]
    
    def modify(self, subscription_id: str, **kwargs):
        """Modify a subscription."""
        if subscription_id not in self.subscriptions:
            raise ValueError(f"Subscription {subscription_id} not found")
        self.subscriptions[subscription_id].update(kwargs)
        return self.subscriptions[subscription_id]
    
    def cancel(self, subscription_id: str):
        """Cancel a subscription."""
        if subscription_id in self.subscriptions:
            self.subscriptions[subscription_id]["status"] = "canceled"
        return self.subscriptions.get(subscription_id, {"status": "canceled"})


class MockStripeProducts:
    """Mock for Stripe products."""
    
    def __init__(self):
        self.products = {}
    
    def create(self, **kwargs):
        """Create a new product."""
        product_id = f"prod_{len(self.products) + 1}"
        product = {
            "id": product_id,
            "name": kwargs.get("name", "Test Product"),
            "active": True,
            "metadata": kwargs.get("metadata", {}),
        }
        self.products[product_id] = product
        return product
    
    def retrieve(self, product_id: str):
        """Retrieve a product by ID."""
        if product_id not in self.products:
            raise ValueError(f"Product {product_id} not found")
        return self.products[product_id]
    
    def list(self, **kwargs):
        """List all products."""
        return {"data": list(self.products.values())}


class MockStripePrices:
    """Mock for Stripe prices."""
    
    def __init__(self):
        self.prices = {}
    
    def create(self, **kwargs):
        """Create a new price."""
        price_id = f"price_{len(self.prices) + 1}"
        price = {
            "id": price_id,
            "product": kwargs.get("product", "prod_1"),
            "unit_amount": kwargs.get("unit_amount", 1000),
            "currency": kwargs.get("currency", "usd"),
            "recurring": kwargs.get("recurring", {"interval": "month"}),
            "active": True,
            "metadata": kwargs.get("metadata", {}),
        }
        self.prices[price_id] = price
        return price
    
    def retrieve(self, price_id: str):
        """Retrieve a price by ID."""
        if price_id not in self.prices:
            raise ValueError(f"Price {price_id} not found")
        return self.prices[price_id]
    
    def list(self, **kwargs):
        """List all prices."""
        return {"data": list(self.prices.values())}


class MockStripeInvoices:
    """Mock for Stripe invoices."""
    
    def __init__(self):
        self.invoices = {}
    
    def create(self, **kwargs):
        """Create a new invoice."""
        invoice_id = f"inv_{len(self.invoices) + 1}"
        invoice = {
            "id": invoice_id,
            "customer": kwargs.get("customer", "cus_1"),
            "subscription": kwargs.get("subscription", "sub_1"),
            "status": "draft",
            "total": 1000,
            "currency": "usd",
            "lines": {"data": []},
        }
        self.invoices[invoice_id] = invoice
        return invoice
    
    def finalize_invoice(self, invoice_id: str):
        """Finalize an invoice."""
        if invoice_id in self.invoices:
            self.invoices[invoice_id]["status"] = "open"
        return self.invoices.get(invoice_id, {"status": "open"})
    
    def pay(self, invoice_id: str):
        """Pay an invoice."""
        if invoice_id in self.invoices:
            self.invoices[invoice_id]["status"] = "paid"
        return self.invoices.get(invoice_id, {"status": "paid"})
    
    def retrieve(self, invoice_id: str):
        """Retrieve an invoice by ID."""
        if invoice_id not in self.invoices:
            raise ValueError(f"Invoice {invoice_id} not found")
        return self.invoices[invoice_id]


class MockStripePaymentMethods:
    """Mock for Stripe payment methods."""
    
    def __init__(self):
        self.payment_methods = {}
    
    def create(self, **kwargs):
        """Create a new payment method."""
        payment_method_id = f"pm_{len(self.payment_methods) + 1}"
        payment_method = {
            "id": payment_method_id,
            "type": kwargs.get("type", "card"),
            "card": kwargs.get("card", {"brand": "visa", "last4": "4242"}),
            "customer": kwargs.get("customer", None),
        }
        self.payment_methods[payment_method_id] = payment_method
        return payment_method
    
    def attach(self, payment_method_id: str, **kwargs):
        """Attach a payment method to a customer."""
        if payment_method_id in self.payment_methods:
            self.payment_methods[payment_method_id]["customer"] = kwargs.get("customer")
        return self.payment_methods.get(payment_method_id, {"customer": kwargs.get("customer")})
    
    def detach(self, payment_method_id: str):
        """Detach a payment method from a customer."""
        if payment_method_id in self.payment_methods:
            self.payment_methods[payment_method_id]["customer"] = None
        return self.payment_methods.get(payment_method_id, {"customer": None})
    
    def list(self, **kwargs):
        """List all payment methods for a customer."""
        customer = kwargs.get("customer")
        if customer:
            return {"data": [pm for pm in self.payment_methods.values() if pm.get("customer") == customer]}
        return {"data": list(self.payment_methods.values())}


class MockStripeCheckout:
    """Mock for Stripe Checkout."""
    
    def __init__(self):
        self.sessions = {}
    
    def create(self, **kwargs):
        """Create a new checkout session."""
        session_id = f"cs_{len(self.sessions) + 1}"
        session = {
            "id": session_id,
            "url": f"https://checkout.stripe.com/pay/{session_id}",
            "customer": kwargs.get("customer", "cus_1"),
            "subscription": kwargs.get("subscription", None),
            "payment_status": "unpaid",
            "status": "open",
            "metadata": kwargs.get("metadata", {}),
        }
        self.sessions[session_id] = session
        return session
    
    def retrieve(self, session_id: str):
        """Retrieve a checkout session by ID."""
        if session_id not in self.sessions:
            raise ValueError(f"Checkout session {session_id} not found")
        return self.sessions[session_id]


class MockStripeBillingPortal:
    """Mock for Stripe Billing Portal."""
    
    def __init__(self):
        self.sessions = {}
    
    def create(self, **kwargs):
        """Create a new billing portal session."""
        session_id = f"bps_{len(self.sessions) + 1}"
        session = {
            "id": session_id,
            "url": f"https://billing.stripe.com/session/{session_id}",
            "customer": kwargs.get("customer", "cus_1"),
            "return_url": kwargs.get("return_url", "https://example.com"),
        }
        self.sessions[session_id] = session
        return session


@pytest.fixture(scope="function")
def mock_gcs():
    """Fixture for mock Google Cloud Storage."""
    mock_client = MockGCSClient()
    
    # Create a default bucket
    mock_client.create_bucket(settings.GCS_BUCKET_NAME or "default-bucket")
    
    with patch("google.cloud.storage.Client", return_value=mock_client):
        yield mock_client
    
    # Clean up
    mock_client.cleanup()


@pytest.fixture(scope="function")
def mock_stripe():
    """Fixture for mock Stripe."""
    mock_client = MockStripeClient()
    
    with patch("stripe.Customer", mock_client.customers), \
         patch("stripe.Subscription", mock_client.subscriptions), \
         patch("stripe.Product", mock_client.products), \
         patch("stripe.Price", mock_client.prices), \
         patch("stripe.Invoice", mock_client.invoices), \
         patch("stripe.PaymentMethod", mock_client.payment_methods), \
         patch("stripe.checkout.Session", mock_client.checkout.sessions), \
         patch("stripe.billing_portal.Session", mock_client.billing_portal.sessions):
        
        yield mock_client
