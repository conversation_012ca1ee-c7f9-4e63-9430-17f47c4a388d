"""Add deleted_at column for soft deletes

Revision ID: 20240520_add_deleted_at
Revises: 
Create Date: 2024-05-20 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '20240520_add_deleted_at'
down_revision = None  # Reemplazar con la revisión anterior real
branch_labels = None
depends_on = None


def upgrade():
    # Añadir columna deleted_at a la tabla accounts
    op.add_column('accounts', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    
    # Añadir columna deleted_at a la tabla system_users
    op.add_column('system_users', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    
    # Añadir columna deleted_at a la tabla end_users
    op.add_column('end_users', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    
    # Añadir columna deleted_at a la tabla products
    op.add_column('products', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    
    # Añadir columna is_active a la tabla products si no existe
    op.add_column('products', sa.Column('is_active', sa.Boolean(), server_default='true', nullable=False))


def downgrade():
    # Eliminar columna deleted_at de la tabla products
    op.drop_column('products', 'deleted_at')
    
    # Eliminar columna is_active de la tabla products
    op.drop_column('products', 'is_active')
    
    # Eliminar columna deleted_at de la tabla end_users
    op.drop_column('end_users', 'deleted_at')
    
    # Eliminar columna deleted_at de la tabla system_users
    op.drop_column('system_users', 'deleted_at')
    
    # Eliminar columna deleted_at de la tabla accounts
    op.drop_column('accounts', 'deleted_at')
