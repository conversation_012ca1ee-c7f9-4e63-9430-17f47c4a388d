from typing import Dict, Any, List, Optional, Set, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, desc, or_, and_
from datetime import datetime, timedelta

from src.db.models import Product, Interaction, Search, Order, OrderItem
from src.utils.base_logger import log_info, log_warning


class CatalogInsightsService:
    """
    Servicio para obtener insights del catálogo y recomendaciones no personalizadas.
    """

    async def get_most_searched(
        self,
        db: AsyncSession,
        account_id: int,
        skip: int = 0,
        limit: int = 10,
        timeframe: str = "week",
    ) -> Dict[str, Any]:
        """
        Obtiene los términos más buscados en un periodo determinado.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver
            timeframe: Periodo de tiempo ('day', 'week', 'month', 'year', 'all')

        Returns:
            Diccionario con términos más buscados
        """
        # Determinar fecha límite según timeframe
        date_limit = self._get_date_limit(timeframe)

        # Construir consulta
        query = select(Search.query, func.count(Search.id).label("count")).where(
            Search.account_id == account_id
        )

        if date_limit:
            query = query.where(Search.timestamp >= date_limit)

        query = (
            query.group_by(Search.query)
            .order_by(desc("count"))
            .offset(skip)
            .limit(limit)
        )

        # Ejecutar consulta
        result = await db.execute(query)
        items = result.all()

        # Formatear resultados
        search_terms = [{"term": item.query, "count": item.count} for item in items]

        return {
            "items": search_terms,
            "count": len(search_terms),
            "timeframe": timeframe,
        }

    async def get_trending_searches(
        self, db: AsyncSession, account_id: int, skip: int = 0, limit: int = 10
    ) -> Dict[str, Any]:
        """
        Obtiene términos de búsqueda con tendencia creciente.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver

        Returns:
            Diccionario con términos de búsqueda tendencia
        """
        # Definir periodos para comparación
        recent_days = 7
        previous_days = 30

        recent_date = datetime.now() - timedelta(days=recent_days)
        older_date = recent_date - timedelta(days=previous_days)

        # Consulta para búsquedas recientes
        recent_query = (
            select(Search.query, func.count(Search.id).label("recent_count"))
            .where(
                (Search.account_id == account_id) & (Search.timestamp >= recent_date)
            )
            .group_by(Search.query)
        )

        # Consulta para búsquedas anteriores
        older_query = (
            select(Search.query, func.count(Search.id).label("older_count"))
            .where(
                (Search.account_id == account_id)
                & (Search.timestamp >= older_date)
                & (Search.timestamp < recent_date)
            )
            .group_by(Search.query)
        )

        # Ejecutar consultas
        recent_result = await db.execute(recent_query)
        recent_searches = {item.query: item.recent_count for item in recent_result}

        older_result = await db.execute(older_query)
        older_searches = {item.query: item.older_count for item in older_result}

        # Calcular tendencia (crecimiento reciente)
        trending_searches = []
        for term, recent_count in recent_searches.items():
            # Solo considerar términos con al menos 5 búsquedas recientes
            if recent_count < 5:
                continue

            older_count = older_searches.get(term, 0)

            # Evitar división por cero
            if older_count == 0:
                # Si no hay búsquedas anteriores, es un término totalmente nuevo
                trending_searches.append(
                    {
                        "term": term,
                        "count": recent_count,
                        "growth": float("inf"),  # Crecimiento infinito
                    }
                )
            else:
                # Normalizar por días para tener tasa por día
                recent_rate = recent_count / recent_days
                older_rate = older_count / previous_days

                # Calcular tasa de crecimiento
                growth_rate = recent_rate / older_rate

                # Considerar tendencia si hay crecimiento significativo
                if growth_rate > 1.5:  # 50% de crecimiento
                    trending_searches.append(
                        {"term": term, "count": recent_count, "growth": growth_rate}
                    )

        # Ordenar por tasa de crecimiento
        trending_searches.sort(key=lambda x: x["growth"], reverse=True)

        # Aplicar paginación
        paged_searches = trending_searches[skip : skip + limit]

        return {"items": paged_searches, "count": len(paged_searches)}

    async def get_related_searches(
        self,
        db: AsyncSession,
        account_id: int,
        product_id: int,
        skip: int = 0,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Obtiene términos de búsqueda relacionados con un producto.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            product_id: ID del producto
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver

        Returns:
            Diccionario con términos de búsqueda relacionados
        """
        # Primero, obtener el producto para referencias
        product_query = select(Product).where(
            (Product.account_id == account_id) & (Product.id == product_id)
        )

        product_result = await db.execute(product_query)
        product = product_result.scalar_one_or_none()

        if not product:
            return {"items": [], "count": 0}

        # Obtener búsquedas que llevaron a este producto
        search_product_query = (
            select(Search.query, func.count(Search.id).label("count"))
            .where((Search.account_id == account_id))
            .group_by(Search.query)
            .order_by(desc("count"))
        )

        search_result = await db.execute(search_product_query)
        direct_searches = [(item.query, item.count) for item in search_result]

        # Si no hay suficientes búsquedas directas, buscar términos relacionados
        if len(direct_searches) < limit:
            # Obtener términos clave del producto
            keywords = []
            if product.name:
                keywords.extend(product.name.lower().split())
            if product.category:
                keywords.extend(product.category.lower().split())

            # Filtrar palabras comunes
            keywords = [k for k in keywords if len(k) > 3]

            if keywords:
                # Buscar términos que contengan palabras clave similares
                related_terms_query = (
                    select(Search.query, func.count(Search.id).label("count"))
                    .where(
                        (Search.account_id == account_id)
                        & (or_(*[Search.query.ilike(f"%{k}%") for k in keywords]))
                    )
                    .group_by(Search.query)
                    .order_by(desc("count"))
                )

                related_result = await db.execute(related_terms_query)
                related_searches = [(item.query, item.count) for item in related_result]

                # Filtrar términos ya incluidos en las búsquedas directas
                direct_terms = {term for term, _ in direct_searches}
                related_searches = [
                    (term, count)
                    for term, count in related_searches
                    if term not in direct_terms
                ]

                # Combinar resultados
                all_searches = direct_searches + related_searches
            else:
                all_searches = direct_searches
        else:
            all_searches = direct_searches

        # Ordenar por conteo y aplicar paginación
        all_searches.sort(key=lambda x: x[1], reverse=True)
        paged_searches = all_searches[skip : skip + limit]

        # Formatear respuesta
        search_items = [
            {"term": term, "count": count} for term, count in paged_searches
        ]

        return {
            "items": search_items,
            "count": len(search_items),
            "product_id": product_id,
            "product_name": product.name,
        }

    async def get_most_sold(
        self,
        db: AsyncSession,
        account_id: int,
        timeframe: str = "month",
        category: Optional[str] = None,
        skip: int = 0,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Obtiene los productos más vendidos.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            timeframe: Periodo de tiempo ('day', 'week', 'month', 'year', 'all')
            category: Categoría específica para filtrar (opcional)
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver

        Returns:
            Diccionario con productos más vendidos
        """
        try:
            # Determinar fecha límite según timeframe
            date_limit = self._get_date_limit(timeframe)

            # Consulta base para ventas
            query = (
                select(
                    OrderItem.product_id,
                    func.sum(OrderItem.quantity).label("total_sold"),
                )
                .join(Order, OrderItem.order_id == Order.id)
                .where(Order.account_id == account_id)
            )

            # Aplicar filtro por fecha
            if date_limit:
                query = query.where(Order.order_date >= date_limit)

            # Aplicar filtro por categoría
            if category:
                product_subquery = select(Product.id).where(
                    (Product.account_id == account_id) & (Product.category == category)
                )
                query = query.where(OrderItem.product_id.in_(product_subquery))

            # Agrupar y ordenar
            query = (
                query.group_by(OrderItem.product_id)
                .order_by(desc("total_sold"))
                .offset(skip)
                .limit(limit)
            )

            # Ejecutar consulta
            result = await db.execute(query)
            sales = result.all()

            # Obtener información de productos
            top_products = []

            if sales:
                product_ids = [item.product_id for item in sales]

                products_query = select(Product).where(
                    (Product.account_id == account_id) & (Product.id.in_(product_ids))
                )

                products_result = await db.execute(products_query)
                products = products_result.scalars().all()

                # Crear diccionario para acceso rápido
                products_dict = {p.id: p for p in products}

                # Construir respuesta
                for item in sales:
                    if item.product_id in products_dict:
                        product = products_dict[item.product_id]

                        top_products.append(
                            {
                                "product_id": product.id,
                                "name": product.name,
                                "category": product.category,
                                "price": (
                                    float(product.price) if product.price else None
                                ),
                                "description": product.description,
                                "average_rating": product.average_rating or 0.0,
                                "num_ratings": product.num_ratings or 0,
                                "total_sold": int(item.total_sold),
                            }
                        )

            log_info(
                f"Retrieved {len(top_products)} most sold products for account {account_id}"
            )

            return {
                "items": top_products,
                "count": len(top_products),
                "timeframe": timeframe,
                "category": category,
            }

        except Exception as e:
            log_warning(
                f"Error getting most sold products for account {account_id}: {str(e)}"
            )
            return {
                "items": [],
                "count": 0,
                "timeframe": timeframe,
                "category": category,
                "error": str(e),
            }

    async def get_category_products(
        self,
        db: AsyncSession,
        account_id: int,
        category: str,
        skip: int = 0,
        limit: int = 10,
        sort_by: str = "popularity",
    ) -> Dict[str, Any]:
        """
        Obtiene productos de una categoría específica.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            category: Categoría de productos
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver
            sort_by: Criterio de ordenamiento ('popularity', 'rating', 'price_asc', 'price_desc', 'newest')

        Returns:
            Diccionario con productos de la categoría
        """
        # Consulta base para productos de esta categoría
        query = select(Product).where(
            (Product.account_id == account_id) & (Product.category == category)
        )

        # Aplicar criterio de ordenamiento
        if sort_by == "popularity":
            # Usar subconsulta para obtener conteo de interacciones
            interactions_subquery = (
                select(
                    Interaction.item_id,
                    func.count(Interaction.id).label("interaction_count"),
                )
                .where(Interaction.account_id == account_id)
                .group_by(Interaction.item_id)
                .subquery()
            )

            query = query.outerjoin(
                interactions_subquery, Product.id == interactions_subquery.c.item_id
            ).order_by(desc(interactions_subquery.c.interaction_count.nullsfirst()))
        elif sort_by == "rating":
            query = query.order_by(desc(Product.average_rating.nullslast()))
        elif sort_by == "price_asc":
            query = query.order_by(Product.price)
        elif sort_by == "price_desc":
            query = query.order_by(desc(Product.price))
        elif sort_by == "newest":
            query = query.order_by(desc(Product.created_at))
        else:
            # Si no es un criterio válido, usar popularidad por defecto
            query = query.order_by(desc(Product.num_ratings.nullslast()))

        # Aplicar paginación
        query = query.offset(skip).limit(limit)

        # Ejecutar consulta
        result = await db.execute(query)
        products = result.scalars().all()

        # Formatear respuesta
        category_products = []

        for product in products:
            category_products.append(
                {
                    "product_id": product.id,
                    "name": product.name,
                    "category": product.category,
                    "price": float(product.price) if product.price else None,
                    "description": product.description,
                    "average_rating": product.average_rating or 0.0,
                    "num_ratings": product.num_ratings or 0,
                }
            )

        return {
            "items": category_products,
            "count": len(category_products),
            "category": category,
            "sort_by": sort_by,
        }

    async def get_related_categories(
        self,
        db: AsyncSession,
        account_id: int,
        category: str,
        skip: int = 0,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Obtiene categorías relacionadas con una categoría específica.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            category: Categoría base
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver

        Returns:
            Diccionario con categorías relacionadas
        """
        # Obtener usuarios que han interactuado con productos de esta categoría
        category_users_query = (
            select(Interaction.user_id)
            .join(Product, Interaction.item_id == Product.id)
            .where(
                (Interaction.account_id == account_id) & (Product.category == category)
            )
            .group_by(Interaction.user_id)
        )

        category_users_result = await db.execute(category_users_query)
        category_users = [user.user_id for user in category_users_result]

        if not category_users:
            return {"items": [], "count": 0, "category": category}

        # Obtener otras categorías con las que estos usuarios han interactuado
        related_categories_query = (
            select(
                Product.category,
                func.count(distinct(Interaction.user_id)).label("user_count"),
            )
            .join(Interaction, Product.id == Interaction.item_id)
            .where(
                (Product.account_id == account_id)
                & (Product.category != category)
                & (Interaction.user_id.in_(category_users))
            )
            .group_by(Product.category)
            .order_by(desc("user_count"))
            .offset(skip)
            .limit(limit)
        )

        related_result = await db.execute(related_categories_query)
        related_categories = related_result.all()

        # Obtener información adicional de cada categoría
        categories_info = []

        for cat in related_categories:
            # Contar productos en esta categoría
            count_query = select(func.count(Product.id)).where(
                (Product.account_id == account_id) & (Product.category == cat.category)
            )

            count_result = await db.execute(count_query)
            product_count = count_result.scalar_one_or_none() or 0

            # Comentado temporalmente: Obtener metadatos de categoría
            # Este código necesita el modelo Category que no existe actualmente
            """
            metadata_query = select(Category).where(
                (Category.account_id == account_id) &
                (Category.name == cat.category)
            )
            
            metadata_result = await db.execute(metadata_query)
            metadata = metadata_result.scalar_one_or_none()
            """

            categories_info.append(
                {
                    "name": cat.category,
                    "user_overlap": cat.user_count,
                    "product_count": product_count,
                    "description": None,  # metadata.description if metadata else None,
                    "image_url": None,  # metadata.image_url if metadata else None
                }
            )

        return {
            "items": categories_info,
            "count": len(categories_info),
            "category": category,
        }

    async def get_also_bought(
        self,
        db: AsyncSession,
        account_id: int,
        product_id: int,
        skip: int = 0,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Obtiene productos que son comprados junto con un producto específico.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            product_id: ID del producto
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver

        Returns:
            Diccionario con productos comprados juntos
        """
        # Verificar que el producto existe
        product_query = select(Product).where(
            (Product.account_id == account_id) & (Product.id == product_id)
        )

        product_result = await db.execute(product_query)
        product = product_result.scalar_one_or_none()

        if not product:
            return {"items": [], "count": 0, "product_id": product_id}

        try:
            # Obtener órdenes que contienen este producto
            orders_with_product_query = select(OrderItem.order_id).where(
                OrderItem.product_id == product_id
            )

            orders_result = await db.execute(orders_with_product_query)
            order_ids = [order.order_id for order in orders_result]

            if not order_ids:
                return {
                    "items": [],
                    "count": 0,
                    "product_id": product_id,
                    "product_name": product.name,
                }

            # Obtener otros productos en las mismas órdenes
            co_purchased_query = (
                select(
                    OrderItem.product_id,
                    func.count(OrderItem.order_id).label("order_count"),
                )
                .where(
                    (OrderItem.order_id.in_(order_ids))
                    & (OrderItem.product_id != product_id)
                )
                .group_by(OrderItem.product_id)
                .order_by(desc("order_count"))
                .offset(skip)
                .limit(limit)
            )

            co_purchased_result = await db.execute(co_purchased_query)
            co_purchased_items = co_purchased_result.all()

            # Obtener información de productos
            also_bought = []

            if co_purchased_items:
                item_ids = [item.product_id for item in co_purchased_items]

                products_query = select(Product).where(
                    (Product.account_id == account_id) & (Product.id.in_(item_ids))
                )

                products_result = await db.execute(products_query)
                products = products_result.scalars().all()

                # Crear diccionario para acceso rápido
                products_dict = {p.id: p for p in products}

                # Construir respuesta
                for item in co_purchased_items:
                    if item.product_id in products_dict:
                        p = products_dict[item.product_id]

                        also_bought.append(
                            {
                                "product_id": p.id,
                                "name": p.name,
                                "category": p.category,
                                "price": float(p.price) if p.price else None,
                                "description": p.description,
                                "average_rating": p.average_rating or 0.0,
                                "num_ratings": p.num_ratings or 0,
                                "order_count": int(item.order_count),
                            }
                        )

            log_info(
                f"Retrieved {len(also_bought)} also-bought products for product {product_id} in account {account_id}"
            )

            return {
                "items": also_bought,
                "count": len(also_bought),
                "product_id": product_id,
                "product_name": product.name,
            }

        except Exception as e:
            log_warning(
                f"Error getting also-bought products for product {product_id} in account {account_id}: {str(e)}"
            )
            return {
                "items": [],
                "count": 0,
                "product_id": product_id,
                "product_name": product.name,
                "error": str(e),
            }

    async def get_similar_products(
        self,
        db: AsyncSession,
        account_id: int,
        product_id: int,
        skip: int = 0,
        limit: int = 10,
    ) -> Dict[str, Any]:
        """
        Obtiene productos similares a un producto específico.

        Args:
            db: Sesión de base de datos
            account_id: ID de la cuenta
            product_id: ID del producto
            skip: Número de resultados a saltar
            limit: Número de resultados a devolver

        Returns:
            Diccionario con productos similares
        """
        # Verificar que el producto existe
        product_query = select(Product).where(
            (Product.account_id == account_id) & (Product.id == product_id)
        )

        product_result = await db.execute(product_query)
        product = product_result.scalar_one_or_none()

        if not product:
            return {"items": [], "count": 0, "product_id": product_id}

        # Obtener productos de la misma categoría
        category_query = select(Product).where(
            (Product.account_id == account_id)
            & (Product.category == product.category)
            & (Product.id != product_id)
        )

        # Si el producto tiene precio, priorizar productos de precio similar
        if product.price:
            # Definir rango de precio similar (±30%)
            min_price = product.price * 0.7
            max_price = product.price * 1.3

            category_query = category_query.where(
                (Product.price >= min_price) & (Product.price <= max_price)
            )

        # Ordenar por rating o popularidad
        category_query = (
            category_query.order_by(desc(Product.average_rating.nullslast()))
            .offset(skip)
            .limit(limit * 2)
        )  # Obtener más para tener margen

        category_result = await db.execute(category_query)
        similar_products = category_result.scalars().all()

        # Si no hay suficientes productos de la misma categoría, buscar por atributos similares
        if len(similar_products) < limit:
            # Extraer atributos clave del producto base
            keywords = []
            if product.name:
                keywords.extend([w for w in product.name.lower().split() if len(w) > 3])
            if product.description:
                keywords.extend(
                    [w for w in product.description.lower().split() if len(w) > 3]
                )

            if keywords:
                # Buscar productos con atributos similares pero de diferentes categorías
                attribute_query = select(Product).where(
                    (Product.account_id == account_id)
                    & (Product.id != product_id)
                    & (Product.category != product.category)
                    & (
                        or_(
                            *[
                                or_(
                                    Product.name.ilike(f"%{k}%"),
                                    Product.description.ilike(f"%{k}%"),
                                )
                                for k in keywords[
                                    :5
                                ]  # Limitar a 5 keywords para no hacer la query muy compleja
                            ]
                        )
                    )
                )

                # Ordenar
                attribute_query = attribute_query.order_by(
                    desc(Product.average_rating.nullslast())
                ).limit(limit)

                attribute_result = await db.execute(attribute_query)
                attribute_products = attribute_result.scalars().all()

                # Combinar resultados
                all_products = list(similar_products) + list(attribute_products)
            else:
                all_products = similar_products
        else:
            all_products = similar_products

        # Formatear respuesta
        similar_items = []

        for p in all_products[:limit]:
            similarity_score = 0.8  # Base score

            # Ajustar score por similitud
            if p.category == product.category:
                similarity_score += 0.1

            if product.price and p.price:
                price_ratio = min(p.price, product.price) / max(p.price, product.price)
                if price_ratio > 0.9:  # Muy similar
                    similarity_score += 0.1
                elif price_ratio > 0.7:  # Bastante similar
                    similarity_score += 0.05

            similar_items.append(
                {
                    "product_id": p.id,
                    "name": p.name,
                    "category": p.category,
                    "price": float(p.price) if p.price else None,
                    "description": p.description,
                    "average_rating": p.average_rating or 0.0,
                    "similarity_score": min(similarity_score, 1.0),  # Limitar a 1.0
                }
            )

        return {
            "items": similar_items,
            "count": len(similar_items),
            "product_id": product_id,
            "product_name": product.name,
            "product_category": product.category,
        }

    def _get_date_limit(self, timeframe: str) -> Optional[datetime]:
        """Determina la fecha límite según el timeframe especificado."""
        now = datetime.now()

        if timeframe == "day":
            return now - timedelta(days=1)
        elif timeframe == "week":
            return now - timedelta(days=7)
        elif timeframe == "month":
            return now - timedelta(days=30)
        elif timeframe == "year":
            return now - timedelta(days=365)
        else:  # "all"
            return None
