"""Unit tests for Free plan abuse prevention."""
import pytest
import pytest_asyncio
from unittest.mock import Async<PERSON>ock, MagicMock, patch
from fastapi import HTT<PERSON><PERSON>x<PERSON>
from sqlalchemy.ext.asyncio import AsyncSession

from src.middleware.usage_meter_middleware import UsageMeterMiddleware
from src.db.models import Account, Subscription
from src.db.enums import SubscriptionPlan


@pytest_asyncio.fixture
async def mock_db():
    """Mock database session."""
    db = AsyncMock(spec=AsyncSession)
    db.begin = AsyncMock(return_value=AsyncMock(__aenter__=AsyncMock(), __aexit__=AsyncMock()))
    return db


@pytest_asyncio.fixture
async def mock_account():
    """Mock account."""
    account = MagicMock(spec=Account)
    account.account_id = 1
    account.is_active = True
    return account


@pytest_asyncio.fixture
async def mock_free_subscription():
    """Mock FREE subscription."""
    subscription = MagicMock(spec=Subscription)
    subscription.account_id = 1
    subscription.plan_type = SubscriptionPlan.FREE
    subscription.api_calls_limit = 1000
    subscription.monthly_api_calls_used = 999
    subscription.is_active = True
    return subscription


@pytest_asyncio.fixture
async def mock_pro_subscription():
    """Mock PRO subscription."""
    subscription = MagicMock(spec=Subscription)
    subscription.account_id = 1
    subscription.plan_type = SubscriptionPlan.PRO
    subscription.api_calls_limit = 100000
    subscription.monthly_api_calls_used = 99999
    subscription.is_active = True
    return subscription


@pytest.mark.asyncio
async def test_api_call_counter_free_plan_limit_exceeded(mock_db, mock_account, mock_free_subscription):
    """Test usage meter middleware when FREE plan limit is exceeded."""
    # Create the middleware
    middleware = UsageMeterMiddleware(app=None)
    
    # Mock the request
    request = MagicMock()
    request.url.path = "/api/v1/recommendations"
    request.headers = {"x-api-key": "test_api_key"}
    request.scope = {"db": mock_db}
    
    # Mock the call_next function
    call_next = AsyncMock()
    
    # Mock the _get_account_from_api_key method
    with patch.object(middleware, '_get_account_from_api_key', return_value=mock_account), \
         patch.object(middleware, '_get_subscription', return_value=mock_free_subscription), \
         patch.object(middleware, '_check_and_reset_counter'), \
         patch.object(middleware, '_increment_api_call_counter'), \
         patch.object(middleware, '_should_skip', return_value=False):
        
        # Set the monthly_api_calls_used to exceed the limit
        mock_free_subscription.monthly_api_calls_used = 1001
        
        # Call the middleware and expect an exception
        with pytest.raises(HTTPException) as excinfo:
            await middleware.dispatch(request, call_next)
        
        # Verify the exception
        assert excinfo.value.status_code == 429
        assert "FREE plan" in excinfo.value.detail
        assert "upgrade to a paid plan" in excinfo.value.detail


@pytest.mark.asyncio
async def test_api_call_counter_pro_plan_limit_exceeded(mock_db, mock_account, mock_pro_subscription):
    """Test usage meter middleware when PRO plan limit is exceeded."""
    # Create the middleware
    middleware = UsageMeterMiddleware(app=None)
    
    # Mock the request
    request = MagicMock()
    request.url.path = "/api/v1/recommendations"
    request.headers = {"x-api-key": "test_api_key"}
    request.scope = {"db": mock_db}
    
    # Mock the call_next function
    call_next = AsyncMock()
    
    # Mock the _get_account_from_api_key method
    with patch.object(middleware, '_get_account_from_api_key', return_value=mock_account), \
         patch.object(middleware, '_get_subscription', return_value=mock_pro_subscription), \
         patch.object(middleware, '_check_and_reset_counter'), \
         patch.object(middleware, '_increment_api_call_counter'), \
         patch.object(middleware, '_should_skip', return_value=False):
        
        # Set the monthly_api_calls_used to exceed the limit
        mock_pro_subscription.monthly_api_calls_used = 100001
        
        # Call the middleware and expect an exception
        with pytest.raises(HTTPException) as excinfo:
            await middleware.dispatch(request, call_next)
        
        # Verify the exception
        assert excinfo.value.status_code == 429
        assert "Monthly API call limit exceeded" in excinfo.value.detail
        assert "FREE plan" not in excinfo.value.detail


@pytest.mark.asyncio
async def test_api_call_counter_within_limit(mock_db, mock_account, mock_free_subscription):
    """Test usage meter middleware when within the limit."""
    # Create the middleware
    middleware = UsageMeterMiddleware(app=None)
    
    # Mock the request
    request = MagicMock()
    request.url.path = "/api/v1/recommendations"
    request.headers = {"x-api-key": "test_api_key"}
    request.scope = {"db": mock_db}
    
    # Mock the call_next function
    call_next = AsyncMock()
    call_next.return_value = MagicMock(headers={})
    
    # Mock the _get_account_from_api_key method
    with patch.object(middleware, '_get_account_from_api_key', return_value=mock_account), \
         patch.object(middleware, '_get_subscription', return_value=mock_free_subscription), \
         patch.object(middleware, '_check_and_reset_counter'), \
         patch.object(middleware, '_increment_api_call_counter'), \
         patch.object(middleware, '_should_skip', return_value=False):
        
        # Set the monthly_api_calls_used to be within the limit
        mock_free_subscription.monthly_api_calls_used = 999
        
        # Call the middleware
        response = await middleware.dispatch(request, call_next)
        
        # Verify the response
        assert response is not None
        assert call_next.called
