from fastapi import Request
from fastapi.responses import JSONResponse
from src.core.exceptions import (
    NotFoundError,
    ValidationError,
    PermissionDeniedError,
    ConflictError,
    RateLimitExceededError,
    InternalServerError
)
from src.utils.base_logger import log_error


async def handle_not_found(exc: NotFoundError) -> JSONResponse:
    """Manejador para errores de tipo 'recurso no encontrado'."""
    log_error(f"Not Found Error: {exc.detail}")
    return JSONResponse(
        status_code=404,
        content={"detail": exc.detail}
    )


async def handle_validation_error(exc: ValidationError) -> JSONResponse:
    """Manejador para errores de validación."""
    log_error(f"Validation Error: {exc.detail}")
    return JSONResponse(
        status_code=422,
        content={
            "validation_error": True,
            "detail": exc.detail
        }
    )


async def handle_permission_denied(exc: PermissionDeniedError) -> JSONResponse:
    """Manejador para errores de permisos denegados."""
    log_error(f"Permission Denied: {exc.detail}")
    return JSONResponse(
        status_code=403,
        content={"detail": exc.detail}
    )


async def handle_conflict(exc: ConflictError) -> JSONResponse:
    """Manejador para errores de conflicto."""
    log_error(f"Conflict Error: {exc.detail}")
    return JSONResponse(
        status_code=409,
        content={"detail": exc.detail}
    )


async def handle_rate_limit(exc: RateLimitExceededError) -> JSONResponse:
    """Manejador para errores de límite de tasa excedido."""
    log_error(f"Rate Limit Exceeded: {exc.detail}")
    return JSONResponse(
        status_code=429,
        content={"detail": exc.detail}
    )


async def handle_internal_error(exc: InternalServerError) -> JSONResponse:
    """Manejador para errores internos del servidor."""
    log_error(f"Internal Server Error: {exc.detail}")
    return JSONResponse(
        status_code=500,
        content={
            "internal_error": True,
            "detail": exc.detail
        }
    )


def register_exception_handlers(app):
    """Registra los manejadores de excepciones en la aplicación."""
    app.add_exception_handler(NotFoundError, handle_not_found)
    app.add_exception_handler(ValidationError, handle_validation_error)
    app.add_exception_handler(PermissionDeniedError, handle_permission_denied)
    app.add_exception_handler(ConflictError, handle_conflict)
    app.add_exception_handler(RateLimitExceededError, handle_rate_limit)
    app.add_exception_handler(InternalServerError, handle_internal_error) 