from sqlalchemy import <PERSON>umn, Integer, Float, DateTime, Foreign<PERSON>ey, func, String
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import relationship
from src.db.base import Base
from .mixins import TenantMixin, ACCOUNT_ID_FK, ACCOUNT_RANGE


class AccountUsageMetrics(Base, TenantMixin):
    __tablename__ = "account_usage_metrics"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)

    # Basic usage metrics
    api_calls_count = Column(Integer, default=0)
    storage_used = Column(Integer, default=0)  # en bytes

    # Detailed API usage metrics
    endpoint_usage = Column(JSONB, default=dict)  # Counts per endpoint
    training_count = Column(Integer, default=0)  # Number of model trainings
    recommendation_count = Column(
        Integer, default=0
    )  # Number of recommendation requests

    # Performance metrics
    avg_response_time = Column(Float, default=0.0)  # Average response time in seconds
    p95_response_time = Column(Float, default=0.0)  # 95th percentile response time
    p99_response_time = Column(Float, default=0.0)  # 99th percentile response time

    # Error metrics
    error_count = Column(Integer, default=0)  # Total number of errors
    error_rate = Column(Float, default=0.0)  # Error rate (errors/total calls)
    error_types = Column(JSONB, default=dict)  # Counts per error type

    # Time tracking
    last_reset = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now())
    last_billing_cycle = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())  # For billing purposes

    # Current billing period metrics
    billing_period_api_calls = Column(
        Integer, default=0
    )  # API calls in current billing period
    billing_period_storage = Column(
        Integer, default=0
    )  # Storage used in current billing period

    __table_args__ = ({"postgresql_partition_by": ACCOUNT_RANGE},)

    account = relationship("Account", back_populates="usage_metrics")


class EndpointMetrics(Base, TenantMixin):
    __tablename__ = "endpoint_metrics"

    account_id = Column(Integer, ForeignKey(ACCOUNT_ID_FK), primary_key=True)
    endpoint = Column(String, primary_key=True)  # Endpoint path
    method = Column(String, primary_key=True)  # HTTP method

    # Usage metrics
    call_count = Column(Integer, default=0)  # Number of calls to this endpoint

    # Performance metrics
    avg_response_time = Column(Float, default=0.0)  # Average response time
    p95_response_time = Column(Float, default=0.0)  # 95th percentile
    p99_response_time = Column(Float, default=0.0)  # 99th percentile
    min_response_time = Column(Float, default=0.0)  # Minimum response time
    max_response_time = Column(Float, default=0.0)  # Maximum response time

    # Error metrics
    error_count = Column(Integer, default=0)  # Number of errors
    error_rate = Column(Float, default=0.0)  # Error rate for this endpoint

    # Time tracking
    last_called_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now())

    __table_args__ = ({"postgresql_partition_by": ACCOUNT_RANGE},)

    account = relationship("Account")
