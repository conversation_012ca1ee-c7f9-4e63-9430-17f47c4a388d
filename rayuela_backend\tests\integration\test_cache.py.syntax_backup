import pytest
from unittest.mock import AsyncMock, patch
from src.core.cache import (
    get_cache,
    invalidate_user_cache,
    invalidate_account_cache,
    invalidate_cache_after
)
from src.db.models import SystemUser, Account, Product

class TestCache:
    """Tests de integración para el sistema de caché."""
    
    @pytest.fixture
    def mock_redis(self):
        """Mock de Redis para pruebas."""
        with patch("src.core.cache.redis") as mock:
            mock.delete = AsyncMock()
            mock.get = AsyncMock()
            mock.set = AsyncMock()
            return mock
    
@pytest.mark.asyncio
    async def test_invalidate_user_cache(
        self,
        mock_redis,
        test_users
    ):
        """Test para invalidación de caché de usuario."""
        user = test_users[0]
        
        # Invalidar caché
        await invalidate_user_cache(user.id)
        
        # Verificar que se eliminaron las claves correctas
        mock_redis.delete.assert_any_call(f"user:{user.id}")
        mock_redis.delete.assert_any_call(f"user_permissions:{user.id}")
        mock_redis.delete.assert_any_call(f"user_roles:{user.id}")
    
    async def test_invalidate_account_cache(
        self,
        mock_redis,
        test_accounts
    ):
        """Test para invalidación de caché de cuenta."""
        account = test_accounts[0]
        
        # Invalidar caché
        await invalidate_account_cache(account.id)
        
        # Verificar que se eliminaron las claves correctas
        mock_redis.delete.assert_any_call(f"account:{account.id}")
        mock_redis.delete.assert_any_call(f"account_limits:{account.id}")
        mock_redis.delete.assert_any_call(f"account_settings:{account.id}")
    
    async def test_invalidate_cache_after_decorator(
        self,
        mock_redis,
        db_session,
        test_accounts
    ):
        """Test para el decorador de invalidación de caché."""
        account = test_accounts[0]
        
        # Función decorada
        @invalidate_cache_after("account")
        async def update_account(account_id: int, data: dict):
            account = await db_session.get(Account, account_id)
            for key, value in data.items():
                setattr(account, key, value)
            await db_session.commit()
            return account
        
        # Ejecutar función
        await update_account(account.id, {"name": "Updated Name"})
        
        # Verificar que se invalidó la caché
        mock_redis.delete.assert_any_call(f"account:{account.id}")
    
    async def test_cache_invalidation_on_cascade(
        self,
        mock_redis,
        db_session,
        test_accounts,
        test_users
    ):
        """Test para invalidación de caché en cascada."""
        account = test_accounts[0]
        user = test_users[0]
        
        # Simular actualización de cuenta que afecta a usuarios
        @invalidate_cache_after("account", cascade=["users"])
        async def update_account_settings(account_id: int, settings: dict):
            account = await db_session.get(Account, account_id)
            account.settings = settings
            await db_session.commit()
            return account
        
        # Ejecutar función
        await update_account_settings(account.id, {"new_setting": "value"})
        
        # Verificar que se invalidó la caché de la cuenta y sus usuarios
        mock_redis.delete.assert_any_call(f"account:{account.id}")
        mock_redis.delete.assert_any_call(f"user:{user.id}")
    
    async def test_cache_invalidation_on_soft_delete(
        self,
        mock_redis,
        db_session,
        test_products
    ):
        """Test para invalidación de caché en soft delete."""
        product = test_products[0]
        
        # Soft delete de producto
        @invalidate_cache_after("product")
        async def soft_delete_product(product_id: int):
            product = await db_session.get(Product, product_id)
            product.is_deleted = True
            await db_session.commit()
            return product
        
        # Ejecutar función
        await soft_delete_product(product.id)
        
        # Verificar que se invalidó la caché
        mock_redis.delete.assert_any_call(f"product:{product.id}")
    
    async def test_cache_invalidation_on_bulk_operations(
        self,
        mock_redis,
        db_session,
        test_products
    ):
        """Test para invalidación de caché en operaciones bulk."""
        products = test_products[:3]
        
        # Actualización bulk de productos
        @invalidate_cache_after("product", bulk=True)
        async def bulk_update_products(product_ids: list[int], data: dict):
            for product_id in product_ids:
                product = await db_session.get(Product, product_id)
                for key, value in data.items():
                    setattr(product, key, value)
            await db_session.commit()
        
        # Ejecutar función
        await bulk_update_products(
            [p.id for p in products],
            {"price": 100.0}
        )
        
        # Verificar que se invalidó la caché de todos los productos
        for product in products:
            mock_redis.delete.assert_any_call(f"product:{product.id}")
    
    async def test_cache_invalidation_with_conditions(
        self,
        mock_redis,
        db_session,
        test_products
    ):
        """Test para invalidación de caché con condiciones."""
        product = test_products[0]
        
        # Actualización condicional
        @invalidate_cache_after("product", condition=lambda p: p.price > 50)
        async def update_product_price(product_id: int, price: float):
            product = await db_session.get(Product, product_id)
            product.price = price
            await db_session.commit()
            return product
        
        # Ejecutar con precio que no invalida caché
        await update_product_price(product.id, 30.0)
        mock_redis.delete.assert_not_called()
        
        # Ejecutar con precio que invalida caché
        await update_product_price(product.id, 60.0)
        mock_redis.delete.assert_called_with(f"product:{product.id}") 