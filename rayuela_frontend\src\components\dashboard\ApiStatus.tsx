"use client";

import { useState, useEffect } from 'react';
import { CheckCircle2, AlertCircle, ServerCrash } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface ApiStatusProps {
  className?: string;
}

type StatusType = 'operational' | 'degraded' | 'outage' | 'loading';

export function ApiStatus({ className = '' }: ApiStatusProps) {
  const [status, setStatus] = useState<StatusType>('loading');
  const [lastChecked, setLastChecked] = useState<Date | null>(null);

  useEffect(() => {
    const checkApiStatus = async () => {
      try {
        const response = await fetch('/health');
        if (response.ok) {
          const data = await response.json();
          setStatus(data.status === 'healthy' ? 'operational' : 'degraded');
        } else {
          setStatus('degraded');
        }
      } catch (error) {
        console.error('Error checking API status:', error);
        setStatus('outage');
      }
      setLastChecked(new Date());
    };

    checkApiStatus();
    // Verificar el estado cada 5 minutos
    const interval = setInterval(checkApiStatus, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, []);

  const getStatusInfo = () => {
    switch (status) {
      case 'operational':
        return {
          label: 'API Operativa',
          icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
          description: 'Todos los sistemas funcionando correctamente',
          className: 'text-green-600 dark:text-green-400',
        };
      case 'degraded':
        return {
          label: 'Rendimiento Degradado',
          icon: <AlertCircle className="h-4 w-4 text-amber-500" />,
          description: 'Algunos servicios pueden experimentar lentitud',
          className: 'text-amber-600 dark:text-amber-400',
        };
      case 'outage':
        return {
          label: 'Servicio Interrumpido',
          icon: <ServerCrash className="h-4 w-4 text-red-500" />,
          description: 'Estamos experimentando problemas técnicos',
          className: 'text-red-600 dark:text-red-400',
        };
      case 'loading':
      default:
        return {
          label: 'Verificando estado...',
          icon: <AlertCircle className="h-4 w-4 text-gray-400 animate-pulse" />,
          description: 'Obteniendo información del estado de la API',
          className: 'text-gray-600 dark:text-gray-400',
        };
    }
  };

  const statusInfo = getStatusInfo();
  const formattedTime = lastChecked 
    ? lastChecked.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }) 
    : '';

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className={`flex items-center gap-1.5 ${className}`}>
            {statusInfo.icon}
            <span className={`text-xs font-medium ${statusInfo.className}`}>
              {statusInfo.label}
            </span>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom">
          <div className="text-sm">
            <p className="font-medium">{statusInfo.label}</p>
            <p className="text-xs text-gray-500 dark:text-gray-400">{statusInfo.description}</p>
            {lastChecked && (
              <p className="text-xs text-gray-500 mt-1">Última verificación: {formattedTime}</p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
