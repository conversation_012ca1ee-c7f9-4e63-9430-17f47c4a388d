#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar duplicados de order_number antes de aplicar la migración.

Este script debe ejecutarse ANTES de aplicar la migración fix_order_number_unique_constraint
para identificar y resolver cualquier order_number duplicado existente.

Uso:
    python -m scripts.check_order_duplicates --database-url postgresql://user:pass@host:port/db
    python -m scripts.check_order_duplicates --check-only  # Solo verificar, no resolver
"""

import argparse
import asyncio
import sys
from typing import List, Dict, Any
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import text

from src.core.config import settings


class OrderDuplicateChecker:
    """Clase para verificar y resolver duplicados de order_number."""
    
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.engine = create_async_engine(database_url, echo=False)
        self.async_session = sessionmaker(
            self.engine, class_=AsyncSession, expire_on_commit=False
        )

    async def check_duplicates(self) -> List[Dict[str, Any]]:
        """Verificar duplicados de order_number dentro de la misma cuenta."""
        
        async with self.async_session() as session:
            result = await session.execute(text("""
                SELECT account_id, order_number, COUNT(*) as count,
                       array_agg(id ORDER BY id) as order_ids,
                       array_agg(created_at ORDER BY id) as created_dates
                FROM orders 
                GROUP BY account_id, order_number 
                HAVING COUNT(*) > 1
                ORDER BY account_id, order_number
            """))
            
            duplicates = []
            for row in result.fetchall():
                duplicates.append({
                    "account_id": row.account_id,
                    "order_number": row.order_number,
                    "count": row.count,
                    "order_ids": row.order_ids,
                    "created_dates": row.created_dates
                })
            
            return duplicates

    async def get_order_details(self, account_id: int, order_ids: List[int]) -> List[Dict[str, Any]]:
        """Obtener detalles de las órdenes duplicadas."""
        
        async with self.async_session() as session:
            order_ids_str = ",".join(map(str, order_ids))
            result = await session.execute(text(f"""
                SELECT id, order_number, user_id, status, total_amount, currency,
                       created_at, updated_at
                FROM orders 
                WHERE account_id = :account_id AND id IN ({order_ids_str})
                ORDER BY created_at
            """), {"account_id": account_id})
            
            orders = []
            for row in result.fetchall():
                orders.append({
                    "id": row.id,
                    "order_number": row.order_number,
                    "user_id": row.user_id,
                    "status": row.status,
                    "total_amount": row.total_amount,
                    "currency": row.currency,
                    "created_at": row.created_at,
                    "updated_at": row.updated_at
                })
            
            return orders

    async def resolve_duplicate(self, account_id: int, order_number: str, order_ids: List[int], dry_run: bool = True) -> List[str]:
        """Resolver duplicados renombrando order_numbers."""
        
        changes = []
        
        async with self.async_session() as session:
            # Keep the first order (oldest), rename the others
            for i, order_id in enumerate(order_ids[1:], start=2):
                new_order_number = f"{order_number}_dup_{i}"
                
                # Ensure the new order number doesn't already exist
                attempt = 1
                while True:
                    check_result = await session.execute(text("""
                        SELECT COUNT(*) FROM orders 
                        WHERE account_id = :account_id AND order_number = :order_number
                    """), {"account_id": account_id, "order_number": new_order_number})
                    
                    if check_result.scalar() == 0:
                        break
                    
                    attempt += 1
                    new_order_number = f"{order_number}_dup_{i}_{attempt}"
                
                change_description = f"Order {order_id}: '{order_number}' → '{new_order_number}'"
                changes.append(change_description)
                
                if not dry_run:
                    # Actually update the order
                    await session.execute(text("""
                        UPDATE orders 
                        SET order_number = :new_order_number,
                            updated_at = NOW()
                        WHERE account_id = :account_id AND id = :order_id
                    """), {
                        "new_order_number": new_order_number,
                        "account_id": account_id,
                        "order_id": order_id
                    })
            
            if not dry_run:
                await session.commit()
        
        return changes

    async def run_check(self, resolve: bool = False, dry_run: bool = True) -> bool:
        """Ejecutar verificación completa de duplicados."""
        
        print("🔍 Verificando duplicados de order_number...")
        print(f"📊 Base de datos: {self.database_url}")
        print()
        
        try:
            duplicates = await self.check_duplicates()
            
            if not duplicates:
                print("✅ No se encontraron order_number duplicados dentro de cuentas")
                print("🎉 La base de datos está lista para la migración")
                return True
            
            print(f"⚠️  ENCONTRADOS {len(duplicates)} grupos de order_number duplicados:")
            print()
            
            total_affected_orders = 0
            all_changes = []
            
            for dup in duplicates:
                account_id = dup["account_id"]
                order_number = dup["order_number"]
                count = dup["count"]
                order_ids = dup["order_ids"]
                
                total_affected_orders += count
                
                print(f"📋 Cuenta {account_id}: order_number '{order_number}'")
                print(f"   🔢 Aparece {count} veces en órdenes: {order_ids}")
                
                # Get detailed information about the orders
                order_details = await self.get_order_details(account_id, order_ids)
                for detail in order_details:
                    print(f"   📝 ID {detail['id']}: {detail['status']}, ${detail['total_amount']} {detail['currency']}, creado: {detail['created_at']}")
                
                if resolve:
                    print(f"   🔧 Resolviendo duplicados...")
                    changes = await self.resolve_duplicate(account_id, order_number, order_ids, dry_run)
                    all_changes.extend(changes)
                    
                    for change in changes:
                        status = "SIMULADO" if dry_run else "APLICADO"
                        print(f"   ✅ {status}: {change}")
                
                print()
            
            print(f"📊 RESUMEN:")
            print(f"   🔢 Grupos de duplicados: {len(duplicates)}")
            print(f"   📦 Órdenes afectadas: {total_affected_orders}")
            
            if resolve:
                if dry_run:
                    print(f"   🧪 Cambios simulados: {len(all_changes)}")
                    print()
                    print("💡 Para aplicar los cambios realmente, ejecuta:")
                    print("   python -m scripts.check_order_duplicates --resolve --no-dry-run")
                else:
                    print(f"   ✅ Cambios aplicados: {len(all_changes)}")
                    print("🎉 Duplicados resueltos exitosamente")
                    print("🚀 La base de datos está lista para la migración")
            else:
                print()
                print("💡 Para resolver automáticamente los duplicados, ejecuta:")
                print("   python -m scripts.check_order_duplicates --resolve")
                print()
                print("⚠️  IMPORTANTE: Antes de aplicar la migración, debes resolver estos duplicados")
                print("   La migración fallará si existen order_number duplicados dentro de la misma cuenta")
            
            return not duplicates or (resolve and not dry_run)
            
        except Exception as e:
            print(f"❌ Error durante la verificación: {e}")
            return False
        finally:
            await self.engine.dispose()


async def main():
    parser = argparse.ArgumentParser(description="Verificar duplicados de order_number antes de migración")
    parser.add_argument("--database-url", help="URL de la base de datos")
    parser.add_argument("--resolve", action="store_true", help="Resolver duplicados automáticamente")
    parser.add_argument("--no-dry-run", action="store_true", help="Aplicar cambios realmente (no simulación)")
    parser.add_argument("--check-only", action="store_true", help="Solo verificar, no resolver")
    
    args = parser.parse_args()
    
    # Determine database URL
    if args.database_url:
        database_url = args.database_url
    else:
        # Use settings from config
        database_url = settings.get_database_url()
    
    if not database_url:
        print("❌ Error: No se pudo determinar la URL de la base de datos")
        print("   Proporciona --database-url o configura las variables de entorno")
        sys.exit(1)
    
    # Determine operation mode
    resolve = args.resolve and not args.check_only
    dry_run = not args.no_dry_run
    
    if resolve and dry_run:
        print("🧪 MODO SIMULACIÓN: Los cambios no se aplicarán realmente")
        print()
    elif resolve and not dry_run:
        print("⚠️  MODO APLICACIÓN: Los cambios se aplicarán a la base de datos")
        print()
    
    checker = OrderDuplicateChecker(database_url)
    success = await checker.run_check(resolve=resolve, dry_run=dry_run)
    
    if success:
        print("\n🎉 Verificación completada exitosamente")
        sys.exit(0)
    else:
        print("\n❌ Verificación falló o se encontraron problemas")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
