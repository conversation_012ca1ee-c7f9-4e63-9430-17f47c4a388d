from typing import List, Dict, Any, Optional, Union
import re
from src.db.schemas import Filter, FilterGroup, FilterOperator, LogicalOperator
from src.utils.base_logger import log_info, log_error, log_warning


def apply_filter(item: Dict[str, Any], filter_obj: Filter) -> bool:
    """
    Aplica un filtro individual a un item.
    
    Args:
        item: Diccionario que representa el item a filtrar
        filter_obj: Objeto Filter con el filtro a aplicar
        
    Returns:
        True si el item pasa el filtro, False en caso contrario
    """
    field = filter_obj.field
    op = filter_obj.op
    value = filter_obj.value
    
    # Si el campo no existe en el item, no pasa el filtro
    if field not in item:
        return False
    
    item_value = item[field]
    
    # Convertir a tipos comparables si es necesario
    if isinstance(item_value, (int, float)) and isinstance(value, str):
        try:
            value = type(item_value)(value)
        except (ValueError, TypeError):
            # Si no se puede convertir, usar el valor original
            pass
    elif isinstance(item_value, str) and isinstance(value, (int, float)):
        try:
            item_value = type(value)(item_value)
        except (ValueError, TypeError):
            # Si no se puede convertir, usar el valor original
            pass
    
    # Aplicar el operador correspondiente
    if op == FilterOperator.EQUALS:
        return item_value == value
    elif op == FilterOperator.NOT_EQUALS:
        return item_value != value
    elif op == FilterOperator.GREATER_THAN:
        return item_value > value
    elif op == FilterOperator.GREATER_THAN_EQUALS:
        return item_value >= value
    elif op == FilterOperator.LESS_THAN:
        return item_value < value
    elif op == FilterOperator.LESS_THAN_EQUALS:
        return item_value <= value
    elif op == FilterOperator.IN:
        return item_value in value
    elif op == FilterOperator.NOT_IN:
        return item_value not in value
    elif op == FilterOperator.CONTAINS:
        # Convertir a string para búsqueda de substring
        return str(value).lower() in str(item_value).lower()
    elif op == FilterOperator.STARTS_WITH:
        return str(item_value).lower().startswith(str(value).lower())
    elif op == FilterOperator.ENDS_WITH:
        return str(item_value).lower().endswith(str(value).lower())
    else:
        log_warning(f"Operador de filtro no soportado: {op}")
        return True  # Si no se reconoce el operador, no filtrar


def apply_filter_group(item: Dict[str, Any], filter_group: FilterGroup) -> bool:
    """
    Aplica un grupo de filtros a un item.
    
    Args:
        item: Diccionario que representa el item a filtrar
        filter_group: Objeto FilterGroup con los filtros a aplicar
        
    Returns:
        True si el item pasa todos los filtros, False en caso contrario
    """
    logic = filter_group.logic
    filters = filter_group.filters
    
    # Si no hay filtros, el item pasa
    if not filters:
        return True
    
    # Aplicar cada filtro según el operador lógico
    if logic == LogicalOperator.AND:
        for filter_obj in filters:
            if isinstance(filter_obj, FilterGroup):
                if not apply_filter_group(item, filter_obj):
                    return False
            else:
                if not apply_filter(item, filter_obj):
                    return False
        return True
    elif logic == LogicalOperator.OR:
        for filter_obj in filters:
            if isinstance(filter_obj, FilterGroup):
                if apply_filter_group(item, filter_obj):
                    return True
            else:
                if apply_filter(item, filter_obj):
                    return True
        return False
    else:
        log_warning(f"Operador lógico no soportado: {logic}")
        return True  # Si no se reconoce el operador, no filtrar


def apply_structured_filters(items: List[Dict[str, Any]], filter_group: FilterGroup) -> List[Dict[str, Any]]:
    """
    Aplica filtros estructurados a una lista de items.
    
    Args:
        items: Lista de diccionarios que representan los items a filtrar
        filter_group: Objeto FilterGroup con los filtros a aplicar
        
    Returns:
        Lista filtrada de items
    """
    if not filter_group or not items:
        return items
    
    filtered_items = []
    for item in items:
        if apply_filter_group(item, filter_group):
            filtered_items.append(item)
    
    log_info(f"Filtrado estructurado: {len(items)} -> {len(filtered_items)} items")
    return filtered_items


def parse_simple_filter_string(filter_str: str) -> Optional[FilterGroup]:
    """
    Parsea una cadena de filtro simple en formato field:value,field2:value2
    
    Args:
        filter_str: Cadena de filtro en formato simple
        
    Returns:
        Objeto FilterGroup con los filtros parseados, o None si hay error
    """
    if not filter_str:
        return None
    
    try:
        filters = []
        filter_parts = filter_str.split(',')
        
        for part in filter_parts:
            if ':' not in part:
                continue
                
            field, value = part.split(':', 1)
            field = field.strip()
            value = value.strip()
            
            # Detectar operadores en el campo
            op = FilterOperator.EQUALS
            if field.endswith('_gt'):
                field = field[:-3]
                op = FilterOperator.GREATER_THAN
            elif field.endswith('_gte'):
                field = field[:-4]
                op = FilterOperator.GREATER_THAN_EQUALS
            elif field.endswith('_lt'):
                field = field[:-3]
                op = FilterOperator.LESS_THAN
            elif field.endswith('_lte'):
                field = field[:-4]
                op = FilterOperator.LESS_THAN_EQUALS
            elif field.endswith('_ne'):
                field = field[:-3]
                op = FilterOperator.NOT_EQUALS
            
            # Convertir valor a tipo adecuado si es posible
            if value.lower() == 'true':
                value = True
            elif value.lower() == 'false':
                value = False
            elif value.isdigit():
                value = int(value)
            elif re.match(r'^-?\d+(\.\d+)?$', value):
                value = float(value)
            
            filters.append(Filter(field=field, op=op, value=value))
        
        if not filters:
            return None
            
        return FilterGroup(logic=LogicalOperator.AND, filters=filters)
    except Exception as e:
        log_error(f"Error parseando cadena de filtro: {str(e)}")
        return None
