"use client";

import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Copy, Check, Save, Download, AlertTriangle, Code, Terminal } from "lucide-react";
import { toast } from "sonner";
import { Checkbox } from "@/components/ui/checkbox";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface InitialApiKeyModalProps {
  apiKey: string;
  onClose: () => void;
}

const InitialApiKeyModal: React.FC<InitialApiKeyModalProps> = ({ apiKey, onClose }) => {
  const [copied, setCopied] = useState(false);
  const [codeCopied, setCodeCopied] = useState(false);
  const [confirmed, setConfirmed] = useState(false);
  const [downloadedOrCopied, setDownloadedOrCopied] = useState(false);
  const [showWarning, setShowWarning] = useState(false);

  // Get API base URL for the code snippet
  const apiBaseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

  // Hello World code snippet
  const helloWorldSnippet = `curl -X GET "${apiBaseUrl}/health/auth" \\
  -H "X-API-Key: ${apiKey}"`;

  const pythonSnippet = `import requests

response = requests.get(
    "${apiBaseUrl}/health/auth",
    headers={"X-API-Key": "${apiKey}"}
)
print(response.json())`;

  // Mostrar advertencia si el usuario intenta cerrar sin confirmar
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (!confirmed) {
        e.preventDefault();
        e.returnValue = '';
        return '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [confirmed]);

  const handleCopy = () => {
    navigator.clipboard.writeText(apiKey)
      .then(() => {
        setCopied(true);
        setDownloadedOrCopied(true);
        toast.success("¡API Key copiada al portapapeles!");
        setTimeout(() => setCopied(false), 2000);
      })
      .catch(err => {
        toast.error("Error al copiar la API Key.");
        console.error("Error al copiar al portapapeles:", err);
      });
  };

  const handleCopyCode = (code: string, type: string) => {
    navigator.clipboard.writeText(code)
      .then(() => {
        setCodeCopied(true);
        toast.success(`¡Código ${type} copiado al portapapeles!`);
        setTimeout(() => setCodeCopied(false), 2000);
      })
      .catch(err => {
        toast.error("Error al copiar el código.");
        console.error("Error al copiar al portapapeles:", err);
      });
  };

  const handleDownload = () => {
    try {
      // Crear un archivo de texto con la API Key
      const blob = new Blob([
        `API Key de Rayuela\n`,
        `----------------\n`,
        `Fecha: ${new Date().toLocaleString()}\n`,
        `API Key: ${apiKey}\n\n`,
        `IMPORTANTE: Guarda este archivo en un lugar seguro. Esta clave no se mostrará completa nuevamente.`
      ], { type: 'text/plain' });

      // Crear un enlace para descargar el archivo
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'rayuela-api-key.txt';
      document.body.appendChild(a);
      a.click();

      // Limpiar
      setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
      }, 0);

      setDownloadedOrCopied(true);
      toast.success("API Key descargada como archivo de texto");
    } catch (err) {
      toast.error("Error al descargar la API Key");
      console.error("Error al descargar:", err);
    }
  };

  const handleCloseAttempt = () => {
    if (!downloadedOrCopied) {
      setShowWarning(true);
      return;
    }

    onClose();
  };

  return (
    <Dialog open={true} onOpenChange={(isOpen) => !isOpen && handleCloseAttempt()}>
      <DialogContent className="sm:max-w-[650px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold flex items-center gap-2">
            <span className="text-2xl">🔑</span> Tu API Key está lista
          </DialogTitle>
          <DialogDescription className="py-2">
            <p className="mb-3">¡Bienvenido a Rayuela! Tu API Key se ha generado automáticamente. Úsala para autenticar todas tus solicitudes a la API.</p>

            <div className="bg-amber-50 border-l-4 border-amber-500 rounded-md p-3 my-3 dark:bg-amber-900/20 dark:border-amber-500">
              <div className="flex gap-2">
                <AlertTriangle className="h-5 w-5 text-amber-600 dark:text-amber-400 shrink-0 mt-0.5" />
                <div>
                  <p className="font-semibold text-amber-800 dark:text-amber-300 text-sm">⚠️ Solo se muestra una vez</p>
                  <p className="text-amber-700 dark:text-amber-400 text-sm">
                    Copia y guarda tu API Key ahora. No podrás verla completa nuevamente.
                  </p>
                </div>
              </div>
            </div>
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col space-y-4 mt-2">
          {/* API Key Section */}
          <div>
            <label className="text-sm font-medium mb-2 block">Tu API Key:</label>
            <div className="flex items-center space-x-2">
              <Input
                id="apiKey"
                readOnly
                value={apiKey}
                className="flex-1 font-mono text-sm bg-gray-50 dark:bg-gray-800 border-2"
              />
              <Button type="button" size="sm" onClick={handleCopy} className="min-w-[90px]">
                {copied ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                {copied ? "¡Copiado!" : "Copiar"}
              </Button>
            </div>
          </div>

          {/* Hello World Section */}
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 dark:bg-green-900/20 dark:border-green-700">
            <div className="flex items-center gap-2 mb-3">
              <Terminal className="h-5 w-5 text-green-600 dark:text-green-400" />
              <h4 className="font-semibold text-green-800 dark:text-green-300">🚀 Prueba tu API Key ahora</h4>
            </div>
            <p className="text-sm text-green-700 dark:text-green-400 mb-3">
              Ejecuta este comando para verificar que tu API Key funciona:
            </p>

            <div className="bg-gray-900 rounded-md p-3 relative">
              <pre className="text-green-400 text-xs overflow-x-auto">
                <code>{helloWorldSnippet}</code>
              </pre>
              <Button
                type="button"
                size="sm"
                variant="ghost"
                onClick={() => handleCopyCode(helloWorldSnippet, 'cURL')}
                className="absolute top-2 right-2 h-8 w-8 p-0 text-gray-400 hover:text-white"
              >
                {codeCopied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
              </Button>
            </div>

            <p className="text-xs text-green-600 dark:text-green-400 mt-2">
              ✅ Respuesta esperada: <code className="bg-green-100 dark:bg-green-800 px-1 rounded">{"status": "ok"}</code>
            </p>
          </div>

          {/* Confirmation Section */}
          <div className="flex items-center space-x-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <Checkbox
              id="confirm-saved"
              checked={confirmed}
              onCheckedChange={(checked: boolean) => setConfirmed(checked)}
            />
            <label
              htmlFor="confirm-saved"
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              ✅ He guardado mi API Key en un lugar seguro
            </label>
          </div>

          {/* Quick Next Steps */}
          <div className="border rounded-md p-3 bg-slate-50 dark:bg-slate-900/20">
            <h4 className="font-medium text-slate-800 dark:text-slate-300 mb-2 text-sm">📋 Próximos pasos:</h4>
            <ul className="space-y-1 text-xs text-slate-600 dark:text-slate-400">
              <li>• Ejecuta el comando de prueba arriba</li>
              <li>• Explora el dashboard para configurar tu cuenta</li>
              <li>• Consulta la documentación para integrar la API</li>
            </ul>
          </div>

          {/* Advertencia si intenta cerrar sin copiar/descargar */}
          {showWarning && (
            <div className="bg-red-50 border-l-4 border-red-500 p-3 rounded-md dark:bg-red-900/20">
              <div className="flex gap-2">
                <AlertTriangle className="h-4 w-4 text-red-600 dark:text-red-400 shrink-0 mt-0.5" />
                <p className="text-sm text-red-700 dark:text-red-300">
                  ¡No has copiado tu API Key! Asegúrate de guardarla antes de continuar.
                </p>
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="mt-4 flex flex-col sm:flex-row gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={handleDownload}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Descargar
          </Button>

          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex-1">
                  <Button
                    type="button"
                    onClick={onClose}
                    className="w-full bg-green-600 hover:bg-green-700 text-white font-medium"
                    disabled={!confirmed}
                  >
                    <Save className="h-4 w-4 mr-2" />
                    Continuar al Dashboard
                  </Button>
                </div>
              </TooltipTrigger>
              {!confirmed && (
                <TooltipContent>
                  <p>Confirma que has guardado tu API Key para continuar</p>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InitialApiKeyModal;
