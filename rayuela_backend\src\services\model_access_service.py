"""
Service for validating model access based on subscription plans.
"""
from typing import List, Optional, Dict, Any
from fastapi import HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.db import enums
from src.services.subscription_service import SubscriptionService
from src.utils.base_logger import log_info, log_error, log_warning


class ModelAccessService:
    """Service for validating model access based on subscription plans."""

    def __init__(self, db: AsyncSession, account_id: int):
        """
        Initialize the model access service.
        
        Args:
            db: Database session
            account_id: Account ID
        """
        self.db = db
        self.account_id = account_id
        self._subscription_service = SubscriptionService(db)
        
    async def validate_model_access(self, model_type: str) -> bool:
        """
        Validate if the account has access to the specified model type.
        
        Args:
            model_type: Type of model to validate access for
            
        Returns:
            True if access is allowed, False otherwise
            
        Raises:
            HTTPException: If the model is not available for the account's plan
        """
        try:
            # Get the subscription for the account
            subscription = await self._subscription_service.get_subscription(self.account_id)
            
            if not subscription:
                log_warning(f"No subscription found for account {self.account_id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="No active subscription found"
                )
            
            # Get the plan type
            plan_type = subscription.plan_type
            
            # Get the available models for the plan
            plan_limits = enums.PLAN_LIMITS.get(plan_type, {})
            available_models = plan_limits.get("available_models", [])
            
            # Check if the model is available for the plan
            if model_type not in available_models:
                log_warning(
                    f"Model {model_type} not available for account {self.account_id} "
                    f"with plan {plan_type}. Available models: {available_models}"
                )
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Model '{model_type}' is not available for your subscription plan. "
                           f"Please upgrade to a plan that includes this model."
                )
            
            log_info(f"Model access validated for account {self.account_id}, model {model_type}")
            return True
            
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            log_error(f"Error validating model access: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error validating model access: {str(e)}"
            )
    
    async def get_available_models(self) -> List[str]:
        """
        Get the list of models available for the account's subscription plan.
        
        Returns:
            List of available model types
        """
        try:
            # Get the subscription for the account
            subscription = await self._subscription_service.get_subscription(self.account_id)
            
            if not subscription:
                return []
            
            # Get the plan type
            plan_type = subscription.plan_type
            
            # Get the available models for the plan
            plan_limits = enums.PLAN_LIMITS.get(plan_type, {})
            available_models = plan_limits.get("available_models", [])
            
            return available_models
            
        except Exception as e:
            log_error(f"Error getting available models: {str(e)}")
            return []
