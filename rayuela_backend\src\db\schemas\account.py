from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime
from src.db.enums import SubscriptionPlan
# Ya no necesitamos importar SubscriptionPlan


class AccountBase(BaseModel):
    name: str = Field(..., min_length=1)


class AccountCreate(AccountBase):
    api_key_hash: Optional[str] = None
    api_key_prefix: Optional[str] = None
    api_key_last_chars: Optional[str] = None
    api_key_created_at: Optional[datetime] = None
    api_key_revealed: bool = False
    # stripe_customer_id eliminado
    mercadopago_customer_id: Optional[str] = None
    is_active: bool = True
    # Considera añadir email/password del primer usuario admin aquí si se crea junto
    # initial_admin_email: EmailStr
    # initial_admin_password: str


class AccountUpdate(BaseModel):
    name: Optional[str] = None  # Permitir actualizar solo el nombre
    # stripe_customer_id eliminado
    mercadopago_customer_id: Optional[str] = None
    is_active: Optional[bool] = None


class SubscriptionBasicInfo(BaseModel):
    """Información básica de suscripción para incluir en las respuestas de cuenta"""
    plan: str
    is_active: bool
    expires_at: Optional[datetime] = None


class AccountResponse(AccountBase):
    account_id: int
    name: str
    api_key: Optional[str] = (
        None  # La API key en texto plano solo se muestra una vez al generarla
    )
    api_key_prefix: Optional[str] = (
        None  # Prefijo de la API key (para mostrar en la UI)
    )
    api_key_last_chars: Optional[str] = (
        None  # Últimos caracteres de la API key (para mostrar en la UI)
    )
    api_key_created_at: Optional[datetime] = None  # Cuándo se creó la API key
    api_key_revealed: bool = False  # Indica si la API Key inicial ya fue mostrada al usuario
    # stripe_customer_id eliminado
    mercadopago_customer_id: Optional[str] = None  # ID del cliente en Mercado Pago
    created_at: datetime
    updated_at: datetime
    is_active: bool
    deleted_at: Optional[datetime] = None
    # Información básica sobre la suscripción
    subscription: Optional[SubscriptionBasicInfo] = None

    class ConfigDict:
        from_attributes = True  # Habilitar compatibilidad con ORM
