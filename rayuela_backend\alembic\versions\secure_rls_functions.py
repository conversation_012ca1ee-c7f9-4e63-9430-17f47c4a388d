"""secure_rls_functions

Revision ID: secure_rls_functions
Revises: add_mercadopago_fields
Create Date: 2025-05-20 10:00:00.000000

"""
from typing import Sequence, Union
from alembic import op
import sqlalchemy as sa
import logging

# revision identifiers, used by Alembic.
revision = 'secure_rls_functions'
down_revision = 'add_mercadopago_fields'  # Update this to the latest migration
branch_labels = None
depends_on = None

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def upgrade() -> None:
    """Upgrade database schema with secure RLS functions."""
    logger.info("Upgrading RLS functions with secure implementations")
    
    # Drop existing functions
    _drop_existing_functions()
    
    # Create new secure functions
    _create_secure_cleanup_function()
    _create_secure_bypass_function()
    
    # Update permissions
    _update_permissions()
    
    logger.info("RLS functions upgraded successfully")

def downgrade() -> None:
    """Downgrade database schema to previous RLS functions."""
    logger.info("Downgrading to original RLS functions")
    
    # Drop secure functions
    _drop_secure_functions()
    
    # Recreate original functions
    _recreate_original_functions()
    
    logger.info("RLS functions downgraded successfully")

def _drop_existing_functions() -> None:
    """Drop existing RLS functions."""
    logger.info("Dropping existing RLS functions")
    
    # Drop cleanup_old_data function
    op.execute("DROP FUNCTION IF EXISTS cleanup_old_data(integer, integer)")
    
    # Drop bypass_rls function
    op.execute("DROP FUNCTION IF EXISTS bypass_rls()")

def _create_secure_cleanup_function() -> None:
    """Create a secure version of the cleanup_old_data function."""
    logger.info("Creating secure cleanup_old_data function")
    
    op.execute("""
        CREATE OR REPLACE FUNCTION cleanup_old_data(
            p_account_id integer,
            p_days_old integer
        )
        RETURNS json AS $$
        DECLARE
            v_deleted_interactions integer := 0;
            v_archived_products integer := 0;
            v_inactivated_users integer := 0;
            v_result json;
            v_table_exists boolean;
            v_cutoff_date timestamp with time zone;
        BEGIN
            -- Validate input parameters
            IF p_account_id IS NULL OR p_account_id <= 0 THEN
                RAISE EXCEPTION 'Invalid account_id: %. Must be a positive integer.', p_account_id;
            END IF;
            
            IF p_days_old IS NULL OR p_days_old < 1 OR p_days_old > 3650 THEN
                RAISE EXCEPTION 'Invalid days_old: %. Must be between 1 and 3650.', p_days_old;
            END IF;
            
            -- Calculate cutoff date using a safe method
            v_cutoff_date := NOW() - (p_days_old * INTERVAL '1 day');
            
            -- Log operation for audit purposes
            RAISE NOTICE 'Cleanup operation started for account_id=% with days_old=%', p_account_id, p_days_old;
            
            -- Execute as superuser to bypass RLS
            SET SESSION AUTHORIZATION DEFAULT;
            
            -- Check if interactions table exists and clean up old interactions
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'interactions'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                WITH deleted AS (
                    DELETE FROM interactions
                    WHERE account_id = p_account_id
                    AND created_at < v_cutoff_date
                    RETURNING *
                )
                SELECT COUNT(*) INTO v_deleted_interactions FROM deleted;
                
                RAISE NOTICE 'Deleted % old interactions for account_id=%', v_deleted_interactions, p_account_id;
            END IF;
            
            -- Check if products table exists and archive inactive products
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'products'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                WITH updated AS (
                    UPDATE products
                    SET status = 'archived'
                    WHERE account_id = p_account_id
                    AND last_interaction_at < v_cutoff_date
                    AND status != 'archived'
                    RETURNING *
                )
                SELECT COUNT(*) INTO v_archived_products FROM updated;
                
                RAISE NOTICE 'Archived % inactive products for account_id=%', v_archived_products, p_account_id;
            END IF;
            
            -- Check if system_users table exists and inactivate inactive users
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'system_users'
            ) INTO v_table_exists;
            
            IF v_table_exists THEN
                WITH updated AS (
                    UPDATE system_users
                    SET status = 'inactive'
                    WHERE account_id = p_account_id
                    AND last_login_at < v_cutoff_date
                    AND status != 'inactive'
                    RETURNING *
                )
                SELECT COUNT(*) INTO v_inactivated_users FROM updated;
                
                RAISE NOTICE 'Inactivated % inactive users for account_id=%', v_inactivated_users, p_account_id;
            END IF;
            
            -- Create result JSON
            v_result := json_build_object(
                'account_id', p_account_id,
                'days_old', p_days_old,
                'deleted_interactions', v_deleted_interactions,
                'archived_products', v_archived_products,
                'inactivated_users', v_inactivated_users,
                'timestamp', NOW()
            );
            
            RETURN v_result;
        EXCEPTION
            WHEN OTHERS THEN
                -- Log error and re-raise
                RAISE NOTICE 'Error in cleanup_old_data: %', SQLERRM;
                RAISE;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """)
    
    # Add comment to function
    op.execute("""
        COMMENT ON FUNCTION cleanup_old_data(integer, integer) IS 
        'Securely cleans up old data for a specific account. 
        Requires maintenance_role privileges. 
        Parameters: 
        - p_account_id: The account ID to clean up data for
        - p_days_old: Number of days to keep data (1-3650)
        Returns a JSON object with operation results.';
    """)

def _create_secure_bypass_function() -> None:
    """Create a secure version of the bypass_rls function."""
    logger.info("Creating secure bypass_rls function")
    
    op.execute("""
        CREATE OR REPLACE FUNCTION bypass_rls(
            p_account_id integer
        )
        RETURNS void AS $$
        DECLARE
            v_original_user text;
        BEGIN
            -- Validate input parameter
            IF p_account_id IS NULL OR p_account_id <= 0 THEN
                RAISE EXCEPTION 'Invalid account_id: %. Must be a positive integer.', p_account_id;
            END IF;
            
            -- Store original user for restoration
            SELECT current_user INTO v_original_user;
            
            -- Log operation for audit purposes
            RAISE NOTICE 'RLS bypass started for account_id=% by user=%', p_account_id, v_original_user;
            
            -- Execute as superuser to bypass RLS
            SET SESSION AUTHORIZATION DEFAULT;
            
            -- Set tenant context to ensure operations only affect the specified tenant
            PERFORM set_config('app.tenant_id', p_account_id::text, false);
            
            -- Note: The caller is responsible for restoring the session when done
            -- by calling RESET SESSION AUTHORIZATION;
        EXCEPTION
            WHEN OTHERS THEN
                -- Log error and re-raise
                RAISE NOTICE 'Error in bypass_rls: %', SQLERRM;
                RAISE;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """)
    
    # Add comment to function
    op.execute("""
        COMMENT ON FUNCTION bypass_rls(integer) IS 
        'Securely bypasses RLS for a specific account. 
        Requires maintenance_role privileges. 
        Parameters: 
        - p_account_id: The account ID to bypass RLS for
        IMPORTANT: Caller must execute RESET SESSION AUTHORIZATION when done.';
    """)

def _update_permissions() -> None:
    """Update permissions for the secure functions."""
    logger.info("Updating permissions for secure RLS functions")
    
    # Grant execute permissions to maintenance_role
    op.execute("GRANT EXECUTE ON FUNCTION cleanup_old_data(integer, integer) TO maintenance_role")
    op.execute("GRANT EXECUTE ON FUNCTION bypass_rls(integer) TO maintenance_role")
    
    # Revoke execute permissions from other roles
    op.execute("REVOKE EXECUTE ON FUNCTION cleanup_old_data(integer, integer) FROM PUBLIC")
    op.execute("REVOKE EXECUTE ON FUNCTION bypass_rls(integer) FROM PUBLIC")

def _drop_secure_functions() -> None:
    """Drop secure RLS functions."""
    logger.info("Dropping secure RLS functions")
    
    # Drop secure functions
    op.execute("DROP FUNCTION IF EXISTS cleanup_old_data(integer, integer)")
    op.execute("DROP FUNCTION IF EXISTS bypass_rls(integer)")

def _recreate_original_functions() -> None:
    """Recreate original RLS functions."""
    logger.info("Recreating original RLS functions")
    
    # Recreate original bypass_rls function
    op.execute("""
        CREATE OR REPLACE FUNCTION bypass_rls()
        RETURNS void AS $$
        BEGIN
            SET SESSION AUTHORIZATION DEFAULT;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """)
    
    # Recreate original cleanup_old_data function
    op.execute("""
        CREATE OR REPLACE FUNCTION cleanup_old_data(
            p_account_id integer,
            p_days_old integer
        )
        RETURNS void AS $$
        BEGIN
            -- Ejecutar como superusuario para bypass RLS
            SET SESSION AUTHORIZATION DEFAULT;

            -- Limpiar datos antiguos
            DELETE FROM interactions
            WHERE account_id = p_account_id
            AND created_at < NOW() - (p_days_old || ' days')::interval;

            -- Archivar productos inactivos
            UPDATE products
            SET status = 'archived'
            WHERE account_id = p_account_id
            AND last_interaction_at < NOW() - (p_days_old || ' days')::interval;

            -- Inactivar usuarios inactivos
            UPDATE users
            SET status = 'inactive'
            WHERE account_id = p_account_id
            AND last_login_at < NOW() - (p_days_old || ' days')::interval;
        END;
        $$ LANGUAGE plpgsql
        SECURITY DEFINER;
    """)
    
    # Grant execute permissions
    op.execute("GRANT EXECUTE ON FUNCTION cleanup_old_data(integer, integer) TO maintenance_role")
    op.execute("GRANT EXECUTE ON FUNCTION bypass_rls() TO maintenance_role")
