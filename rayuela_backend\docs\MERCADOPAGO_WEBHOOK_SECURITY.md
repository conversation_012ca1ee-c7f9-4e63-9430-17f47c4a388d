# Seguridad de Webhooks de Mercado Pago

Este documento describe cómo configurar y asegurar los webhooks de Mercado Pago en Rayuela.

## Importancia de la Verificación de Firmas

Los webhooks de Mercado Pago son una forma en que Mercado Pago notifica a nuestra aplicación sobre eventos importantes, como pagos exitosos, suscripciones canceladas, etc. Sin embargo, estos webhooks son vulnerables a ataques de falsificación si no se verifican adecuadamente.

Un atacante podría enviar webhooks falsos a nuestra API para:
- Simular pagos exitosos sin realizar un pago real
- Obtener acceso a planes pagos sin pagar
- Manipular el estado de las suscripciones
- Corromper los datos de facturación

Por lo tanto, es **crítico** verificar criptográficamente la autenticidad de cada webhook entrante.

## Cómo Funciona la Verificación de Firmas

Mercado Pago incluye una firma en cada webhook que envía. Esta firma se basa en:
1. El ID del evento
2. El ID de la solicitud
3. Un timestamp
4. Un secreto compartido entre Mercado Pago y nuestra aplicación

Nuestra aplicación debe:
1. Extraer estos valores de los headers y parámetros de la solicitud
2. Reconstruir la cadena que se firmó
3. Calcular el HMAC-SHA256 usando nuestro secreto compartido
4. Comparar el resultado con la firma recibida

Si las firmas coinciden, el webhook es auténtico y puede ser procesado. Si no coinciden, debe ser rechazado.

## Configuración en GCP Secret Manager

Para mantener seguro el secreto compartido, lo almacenamos en Google Cloud Secret Manager:

```bash
# Crear el secreto
gcloud secrets create mercadopago-webhook-secret --replication-policy=automatic

# Añadir la versión del secreto (reemplazar YOUR_SECRET con el secreto real de Mercado Pago)
echo -n 'YOUR_SECRET' | gcloud secrets versions add mercadopago-webhook-secret --data-file=-

# Verificar que se ha creado correctamente
gcloud secrets describe mercadopago-webhook-secret
```

## Configuración en Cloud Run

Al desplegar en Cloud Run, inyectar el secreto como variable de entorno:

```bash
gcloud run deploy rayuela-api-service \
    --image YOUR_REGION-docker.pkg.dev/YOUR_PROJECT_ID/YOUR_REPO_NAME/rayuela-api:latest \
    --set-secrets="MERCADOPAGO_WEBHOOK_SECRET=mercadopago-webhook-secret:latest" \
    # Otros parámetros...
```

## Implementación en el Código

La verificación de firmas está implementada en:
- `src/utils/mercadopago_utils.py`: Contiene la lógica de verificación de firmas
- `src/api/v1/endpoints/billing.py`: Utiliza la verificación en el endpoint de webhook

## Pruebas

Se han implementado pruebas unitarias y de integración para la verificación de firmas en:
- `tests/utils/test_mercadopago_utils.py`: Pruebas unitarias para la lógica de verificación
- `tests/api/v1/endpoints/test_billing_mercadopago_webhook.py`: Pruebas de integración para el endpoint de webhook

## Obtener el Secreto de Mercado Pago

Para obtener el secreto de webhook de Mercado Pago:

1. Inicia sesión en tu cuenta de Mercado Pago
2. Ve a la sección de Desarrolladores > Webhooks
3. Configura un nuevo webhook para tu aplicación
4. El secreto se mostrará una vez durante la configuración
5. Guarda este secreto en GCP Secret Manager como se describió anteriormente

## Monitoreo y Alertas

Se recomienda configurar alertas para:
- Intentos fallidos de verificación de firma (posibles ataques)
- Errores en el procesamiento de webhooks

## Mejores Prácticas de Seguridad

1. **Rotación de Secretos**: Rota periódicamente el secreto del webhook
2. **Acceso Limitado**: Restringe el acceso al secreto en GCP Secret Manager
3. **Logging**: Registra todos los intentos fallidos de verificación
4. **Timeouts**: Implementa timeouts para prevenir ataques de denegación de servicio
5. **Rate Limiting**: Limita la cantidad de webhooks que se pueden procesar por IP/tiempo
