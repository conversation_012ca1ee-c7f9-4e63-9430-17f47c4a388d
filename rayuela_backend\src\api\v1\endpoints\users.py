from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from typing import List
from fastapi_cache.decorator import cache
from src.db import schemas
from src.db.session import get_db
from src.core.deps import get_current_account, get_limit_service
from src.services import LimitService
from src.utils.pagination import get_pagination_params
from src.db.models.end_user import EndUser
from sqlalchemy.exc import SQLAlchemyError
from src.utils.base_logger import log_error
from src.utils.security import verify_resource_ownership
from sqlalchemy import func
from src.core.exceptions import LimitExceededError

router = APIRouter()


@router.post("/", response_model=schemas.EndUser)
async def create_end_user(
    end_user: schemas.EndUserCreate,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    limit_service: LimitService = Depends(get_limit_service),
):
    # Validar límites de usuarios
    try:
        await limit_service.validate_user_limit()
    except LimitExceededError as e:
        raise HTTPException(status_code=429, detail=str(e))

    try:
        async with db.begin():
            # Use model_dump instead of artifact_dump
            db_end_user = EndUser(
                **end_user.model_dump(), account_id=account.account_id
            )
            db.add(db_end_user)
            await db.refresh(db_end_user)
            return db_end_user
    except SQLAlchemyError as e:
        log_error(f"Error creating end user: {str(e)}")
        raise HTTPException(status_code=500, detail="Error creating end user")


@router.get("/", response_model=schemas.PaginatedResponse[schemas.EndUser])
@cache(
    expire=300,
    key_builder=lambda f, *args, **kwargs: f"users:{kwargs['account'].account_id}:{kwargs['pagination'][0]}:{kwargs['pagination'][1]}",
)
async def read_end_users(
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
    pagination: tuple = Depends(get_pagination_params),
):
    skip, limit = pagination
    query = (
        select(EndUser)
        .where(EndUser.account_id == account.account_id)
        .offset(skip)
        .limit(limit)
    )
    result = await db.execute(query)
    end_users = result.scalars().all()
    total_query = (
        select(func.count())
        .select_from(EndUser)
        .where(EndUser.account_id == account.account_id)
    )
    total_result = await db.execute(total_query)
    total = total_result.scalar_one()
    return {"items": end_users, "total": total, "skip": skip, "limit": limit}


@router.get("/{user_id}", response_model=schemas.EndUser)
async def read_end_user(
    user_id: int,
    account: schemas.AccountResponse = Depends(get_current_account),
    db: AsyncSession = Depends(get_db),
):
    """Get an end user by ID."""
    # Usar la función de verificación de propiedad
    end_user = await verify_resource_ownership(
        db=db,
        model=EndUser,
        resource_id=user_id,
        account_id=account.account_id,
        error_class=lambda resource_type, resource_id: HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="End user not found"
        ),
    )
    return end_user
