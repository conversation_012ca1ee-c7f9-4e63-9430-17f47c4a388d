# Implementación del Endpoint de Registro Combinado

## Descripción

Se ha implementado un endpoint de registro combinado que permite crear una cuenta y un usuario administrador en una sola transacción. Este endpoint simplifica el proceso de registro desde el frontend.

## Endpoint

```
POST /api/v1/auth/register
```

## Esquema de Solicitud

```python
class RegisterRequest(BaseModel):
    """Schema para el registro completo de una cuenta con usuario administrador."""

    # Datos de la cuenta
    account_name: str = Field(..., min_length=1, description="Nombre de la cuenta")
    subscription_plan: SubscriptionPlan = Field(..., description="Plan de suscripción")
    
    # Datos del usuario administrador inicial
    email: EmailStr = Field(..., description="Email del usuario administrador")
    password: str = Field(..., min_length=8, description="Contraseña del usuario administrador")
    first_name: Optional[str] = Field(None, description="Nombre del usuario administrador")
    last_name: Optional[str] = Field(None, description="Apellido del usuario administrador")
```

## Esquema de Respuesta

```python
class AccountResponse(BaseModel):
    account_id: int
    name: str
    subscription_plan: SubscriptionPlan
    api_key: Optional[str] = None
    is_active: bool
```

## Implementación

El endpoint realiza las siguientes acciones en una sola transacción:

1. Crea una cuenta con los datos proporcionados
2. Genera una API key para la cuenta
3. Crea un usuario administrador con los datos proporcionados
4. Crea un rol de administrador si no existe
5. Asigna el rol de administrador al usuario
6. Devuelve la información de la cuenta con la API key

## Código

```python
@router.post("/register", response_model=schemas.AccountResponse)
async def register(
    register_data: schemas.RegisterRequest,
    db: AsyncSession = Depends(get_db),
):
    """Registra una nueva cuenta con un usuario administrador inicial.
    
    Este endpoint permite crear una cuenta y un usuario administrador en una sola llamada,
    facilitando el proceso de registro desde el frontend.
    """
    try:
        # Crear la cuenta y el usuario en una transacción
        async with db.begin():
            # 1. Crear la cuenta
            account_repo = AccountRepository(db)
            account_data = schemas.AccountCreate(
                name=register_data.account_name,
                subscription_plan=register_data.subscription_plan,
                is_active=True
            )
            account = await account_repo.create(account_data)
            
            # 2. Generar API Key para la cuenta
            api_key, api_key_hash = generate_api_key()
            api_key_parts = api_key.split("_")
            api_key_prefix = api_key_parts[0] if len(api_key_parts) > 0 else ""
            api_key_last_chars = api_key[-6:] if len(api_key) >= 6 else api_key
            
            # Actualizar la cuenta con la API Key
            setattr(account, 'api_key_hash', api_key_hash)
            setattr(account, 'api_key_prefix', api_key_prefix)
            setattr(account, 'api_key_last_chars', api_key_last_chars)
            # Guardar la API key para incluirla en la respuesta
            api_key_for_response = api_key
            
            # 3. Crear el usuario administrador
            # Obtener el account_id como un valor entero
            account_id_value = account.account_id
            # Convertir a entero si es posible
            try:
                account_id_value = int(account_id_value)
            except (TypeError, ValueError):
                # Si no se puede convertir, usar el valor original
                pass
                
            hashed_password = get_password_hash(register_data.password)
            
            user_data = {
                "email": register_data.email,
                "hashed_password": hashed_password,
                "is_active": True,
                "is_admin": True,
                "account_id": account.account_id
            }
            
            # Añadir nombre y apellido si se proporcionaron
            if register_data.first_name:
                user_data["first_name"] = register_data.first_name
            if register_data.last_name:
                user_data["last_name"] = register_data.last_name
                
            user = SystemUser(**user_data)
            db.add(user)
            await db.flush()  # Para obtener el ID del usuario
            
            # 4. Crear rol de administrador si no existe
            role_repo = RoleRepository(db, account_id=account_id_value)
            admin_role = await role_repo.create(
                RoleCreate(name=RoleType.ADMIN, description="Administrator role with full access")
            )
            
            # 5. Asignar rol de administrador al usuario
            user_role_repo = SystemUserRoleRepository(db, account_id=account_id_value)
            await user_role_repo.assign_role(user.id, admin_role.id)
            
        # La transacción se ha completado, preparar respuesta
        response = schemas.AccountResponse.model_validate(account)
        response.api_key = api_key_for_response  # Incluir API key en la respuesta (solo se mostrará una vez)
        
        logger.info(f"Account {account.account_id} created with admin user {user.email}")
        return response
        
    except Exception as e:
        logger.error(f"Error during registration: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error during registration: {str(e)}"
        )
```

## Pruebas

Se han creado varios scripts de prueba para verificar el funcionamiento del endpoint:

1. `test_register_endpoint.py`: Prueba el endpoint directamente usando la función de registro
2. `test_register_http.py`: Prueba el endpoint a través de HTTP
3. `register_simple.py`: Prueba la funcionalidad de registro usando SQLite

## Conclusión

El endpoint de registro combinado proporciona una forma sencilla y eficiente de crear una cuenta y un usuario administrador en una sola llamada, lo que simplifica el proceso de registro desde el frontend.
