"""Unify user ID nomenclature: rename end_user_id to user_id

Revision ID: unify_user_id_nomenclature
Revises: unify_entity_id_types
Create Date: 2025-01-24 12:00:00.000000

This migration resolves the inconsistent nomenclature between:
- API schemas that use 'user_id'
- Database models that use 'end_user_id' in interactions, recommendations, and searches

The migration:
1. Renames end_user_id to user_id in interactions, recommendations, and searches tables
2. Updates foreign key constraints to use the new column names
3. Updates indexes to use the new column names
4. Maintains data integrity throughout the process

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'unify_user_id_nomenclature'
down_revision: Union[str, None] = 'unify_entity_id_types'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema to unify user ID nomenclature."""
    
    print("Starting user ID nomenclature unification...")
    
    # Step 1: Drop existing foreign key constraints
    print("Step 1: Dropping existing foreign key constraints...")
    
    try:
        op.drop_constraint('fk_interaction_end_user', 'interactions', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_interaction_end_user: {e}")
    
    try:
        op.drop_constraint('fk_interaction_user', 'interactions', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_interaction_user: {e}")
    
    try:
        op.drop_constraint('fk_recommendation_end_user', 'recommendations', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_recommendation_end_user: {e}")
    
    try:
        op.drop_constraint('fk_recommendation_user', 'recommendations', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_recommendation_user: {e}")
    
    try:
        op.drop_constraint('fk_search_end_user', 'searches', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_search_end_user: {e}")
    
    try:
        op.drop_constraint('fk_search_user', 'searches', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_search_user: {e}")
    
    # Step 2: Drop existing indexes
    print("Step 2: Dropping existing indexes...")
    
    try:
        op.drop_index('idx_interaction_account_user', table_name='interactions')
    except Exception as e:
        print(f"  Warning: Could not drop idx_interaction_account_user: {e}")
    
    try:
        op.drop_index('idx_recommendation_user', table_name='recommendations')
    except Exception as e:
        print(f"  Warning: Could not drop idx_recommendation_user: {e}")
    
    try:
        op.drop_index('idx_search_account_user', table_name='searches')
    except Exception as e:
        print(f"  Warning: Could not drop idx_search_account_user: {e}")
    
    # Step 3: Rename columns
    print("Step 3: Renaming columns...")
    
    # Rename end_user_id to user_id in interactions table
    try:
        op.alter_column('interactions', 'end_user_id', new_column_name='user_id')
        print("  ✅ Renamed interactions.end_user_id to user_id")
    except Exception as e:
        print(f"  ⚠️  Could not rename interactions.end_user_id: {e}")
    
    # Rename end_user_id to user_id in recommendations table
    try:
        op.alter_column('recommendations', 'end_user_id', new_column_name='user_id')
        print("  ✅ Renamed recommendations.end_user_id to user_id")
    except Exception as e:
        print(f"  ⚠️  Could not rename recommendations.end_user_id: {e}")
    
    # Rename end_user_id to user_id in searches table
    try:
        op.alter_column('searches', 'end_user_id', new_column_name='user_id')
        print("  ✅ Renamed searches.end_user_id to user_id")
    except Exception as e:
        print(f"  ⚠️  Could not rename searches.end_user_id: {e}")
    
    # Step 4: Recreate indexes with new column names
    print("Step 4: Recreating indexes...")
    
    op.create_index('idx_interaction_account_user', 'interactions', ['account_id', 'user_id'])
    op.create_index('idx_recommendation_user', 'recommendations', ['account_id', 'user_id'])
    op.create_index('idx_search_account_user', 'searches', ['account_id', 'user_id'])
    
    # Step 5: Recreate foreign key constraints with new column names
    print("Step 5: Recreating foreign key constraints...")
    
    # Interactions -> EndUsers
    op.create_foreign_key(
        'fk_interaction_user',
        'interactions',
        'end_users',
        ['account_id', 'user_id'],
        ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    
    # Recommendations -> EndUsers
    op.create_foreign_key(
        'fk_recommendation_user',
        'recommendations',
        'end_users',
        ['account_id', 'user_id'],
        ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    
    # Searches -> EndUsers
    op.create_foreign_key(
        'fk_search_user',
        'searches',
        'end_users',
        ['account_id', 'user_id'],
        ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    
    print("✅ User ID nomenclature unification completed successfully!")


def downgrade() -> None:
    """Downgrade schema - rename user_id back to end_user_id."""
    
    print("Starting user ID nomenclature downgrade...")
    
    # Step 1: Drop foreign key constraints
    print("Step 1: Dropping foreign key constraints...")
    
    try:
        op.drop_constraint('fk_interaction_user', 'interactions', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_interaction_user: {e}")
    
    try:
        op.drop_constraint('fk_recommendation_user', 'recommendations', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_recommendation_user: {e}")
    
    try:
        op.drop_constraint('fk_search_user', 'searches', type_='foreignkey')
    except Exception as e:
        print(f"  Warning: Could not drop fk_search_user: {e}")
    
    # Step 2: Drop indexes
    print("Step 2: Dropping indexes...")
    
    try:
        op.drop_index('idx_interaction_account_user', table_name='interactions')
    except Exception as e:
        print(f"  Warning: Could not drop idx_interaction_account_user: {e}")
    
    try:
        op.drop_index('idx_recommendation_user', table_name='recommendations')
    except Exception as e:
        print(f"  Warning: Could not drop idx_recommendation_user: {e}")
    
    try:
        op.drop_index('idx_search_account_user', table_name='searches')
    except Exception as e:
        print(f"  Warning: Could not drop idx_search_account_user: {e}")
    
    # Step 3: Rename columns back
    print("Step 3: Renaming columns back...")
    
    op.alter_column('interactions', 'user_id', new_column_name='end_user_id')
    op.alter_column('recommendations', 'user_id', new_column_name='end_user_id')
    op.alter_column('searches', 'user_id', new_column_name='end_user_id')
    
    # Step 4: Recreate indexes with old column names
    print("Step 4: Recreating indexes...")
    
    op.create_index('idx_interaction_account_user', 'interactions', ['account_id', 'end_user_id'])
    op.create_index('idx_recommendation_user', 'recommendations', ['account_id', 'end_user_id'])
    op.create_index('idx_search_account_user', 'searches', ['account_id', 'end_user_id'])
    
    # Step 5: Recreate foreign key constraints with old column names
    print("Step 5: Recreating foreign key constraints...")
    
    op.create_foreign_key(
        'fk_interaction_end_user',
        'interactions',
        'end_users',
        ['account_id', 'end_user_id'],
        ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    
    op.create_foreign_key(
        'fk_recommendation_end_user',
        'recommendations',
        'end_users',
        ['account_id', 'end_user_id'],
        ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    
    op.create_foreign_key(
        'fk_search_end_user',
        'searches',
        'end_users',
        ['account_id', 'end_user_id'],
        ['account_id', 'user_id'],
        ondelete='CASCADE'
    )
    
    print("✅ User ID nomenclature downgrade completed successfully!")
