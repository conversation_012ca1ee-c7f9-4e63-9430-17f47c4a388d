"""
Tests de integración para el pipeline de recomendación con el modelo LTR.
"""
import pytest
import pytest_asyncio
import numpy as np
import pandas as pd
from unittest.mock import patch, MagicMock, AsyncMock
from sqlalchemy.ext.asyncio import AsyncSession

from src.ml_pipeline.serving_engine import ServingEngine
from src.ml_pipeline.learning_to_rank import LearningToRankModel
from src.ml_pipeline.model_artifact_manager import ModelArtifactManager
from src.ml_pipeline.post_processing_service import PostProcessingService


class TestRecommendationWithLTR:
    """Tests de integración para el pipeline de recomendación con el modelo LTR."""

    @pytest.fixture
    def mock_artifact_manager(self):
        """Fixture para mockear el gestor de artefactos."""
        mock = MagicMock(spec=ModelArtifactManager)
        mock.load_artifacts = AsyncMock(return_value={
            "collaborative": {"model": MagicMock()},
            "content": {"model": MagicMock()},
            "ltr": {"model": MagicMock()}
        })
        return mock

    @pytest.fixture
    def mock_ltr_model(self):
        """Fixture para mockear el modelo LTR."""
        mock = MagicMock(spec=LearningToRankModel)
        # Simular que el modelo reordena las recomendaciones basándose en relevancia real
        mock.predict.side_effect = lambda recs: sorted(
            recs, 
            key=lambda x: x.get("target_relevance", 0.0), 
            reverse=True
        )
        return mock

    @pytest.fixture
    def serving_engine(self, mock_artifact_manager):
        """Fixture para crear el motor de servicio."""
        return ServingEngine(artifact_manager=mock_artifact_manager)

    @pytest.fixture
    def post_processing_service(self, mock_ltr_model):
        """Fixture para crear el servicio de post-procesamiento."""
        return PostProcessingService(ltr_model=mock_ltr_model)

    @pytest.mark.asyncio
    async def test_recommendations_use_ltr_model(self, serving_engine, post_processing_service):
        """Test que verifica que las recomendaciones usan el modelo LTR entrenado con relevancia real."""
        # Crear recomendaciones de prueba
        test_recommendations = [
            {
                "user_id": 1,
                "item_id": 101,
                "score": 0.8,
                "model_type": "collaborative",
                "target_relevance": 0.0  # Baja relevancia real
            },
            {
                "user_id": 1,
                "item_id": 102,
                "score": 0.7,
                "model_type": "collaborative",
                "target_relevance": 1.0  # Alta relevancia real
            },
            {
                "user_id": 1,
                "item_id": 103,
                "score": 0.6,
                "model_type": "content",
                "target_relevance": 0.5  # Relevancia media
            }
        ]
        
        # Mockear el método get_recommendations del serving_engine
        with patch.object(serving_engine, 'get_recommendations', AsyncMock(return_value=test_recommendations)):
            # Aplicar post-procesamiento con LTR
            processed_recs = await post_processing_service.apply_post_processing(
                recommendations=test_recommendations,
                user_id=1,
                account_id=1
            )
            
            # Verificar que las recomendaciones se reordenaron según la relevancia real
            assert len(processed_recs) == 3
            assert processed_recs[0]["item_id"] == 102  # El ítem con mayor relevancia real
            assert processed_recs[1]["item_id"] == 103  # El ítem con relevancia media
            assert processed_recs[2]["item_id"] == 101  # El ítem con menor relevancia real
            
            # Verificar que se llamó al método predict del modelo LTR
            post_processing_service.ltr_model.predict.assert_called_once()

    @pytest.mark.asyncio
    async def test_end_to_end_recommendation_flow(self, serving_engine, post_processing_service):
        """Test que verifica el flujo completo de recomendación con LTR."""
        # Mockear el método get_candidate_recommendations del serving_engine
        with patch.object(serving_engine, 'get_candidate_recommendations', AsyncMock(return_value=[
            {"item_id": 101, "score": 0.8},
            {"item_id": 102, "score": 0.7},
            {"item_id": 103, "score": 0.6}
        ])):
            # Mockear el método enrich_recommendations del serving_engine
            with patch.object(serving_engine, 'enrich_recommendations', AsyncMock(return_value=[
                {"item_id": 101, "score": 0.8, "name": "Producto 1"},
                {"item_id": 102, "score": 0.7, "name": "Producto 2"},
                {"item_id": 103, "score": 0.6, "name": "Producto 3"}
            ])):
                # Mockear el método apply_post_processing para simular el reordenamiento por LTR
                with patch.object(post_processing_service, 'apply_post_processing', AsyncMock(return_value=[
                    {"item_id": 102, "score": 0.9, "name": "Producto 2", "ranked_by": "ltr"},
                    {"item_id": 103, "score": 0.8, "name": "Producto 3", "ranked_by": "ltr"},
                    {"item_id": 101, "score": 0.7, "name": "Producto 1", "ranked_by": "ltr"}
                ])):
                    # Crear una sesión de base de datos mock
                    db_mock = AsyncMock(spec=AsyncSession)
                    
                    # Llamar al método get_recommendations del serving_engine
                    recommendations = await serving_engine.get_recommendations(
                        db=db_mock,
                        account_id=1,
                        user_id=1,
                        n_recommendations=3,
                        post_processor=post_processing_service
                    )
                    
                    # Verificar que se obtuvieron las recomendaciones reordenadas por LTR
                    assert len(recommendations) == 3
                    assert recommendations[0]["item_id"] == 102
                    assert recommendations[0]["ranked_by"] == "ltr"
                    assert recommendations[1]["item_id"] == 103
                    assert recommendations[2]["item_id"] == 101
                    
                    # Verificar que se llamaron todos los métodos necesarios
                    serving_engine.get_candidate_recommendations.assert_called_once()
                    serving_engine.enrich_recommendations.assert_called_once()
                    post_processing_service.apply_post_processing.assert_called_once()
