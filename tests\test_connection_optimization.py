"""
Test suite to verify database connection optimization settings.
"""

import pytest
import os
import sys
from unittest.mock import patch, MagicMock

# Add the src directory to the path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', 'src'))

def test_sqlalchemy_pool_settings():
    """Test that SQLAlchemy pool settings are optimized for startup environment."""
    
    # Mock the settings to avoid database connection during test
    with patch('src.core.config.settings') as mock_settings:
        mock_settings.database_url = "postgresql+asyncpg://test:test@localhost:5432/test"
        
        # Import after mocking to ensure the mock is used
        from src.db.session import DatabaseConnectionManager
        
        # Create a connection manager instance
        manager = DatabaseConnectionManager()
        
        # Mock create_async_engine to capture the arguments
        with patch('src.db.session.create_async_engine') as mock_create_engine:
            mock_engine = MagicMock()
            mock_create_engine.return_value = mock_engine
            
            # Mock the configure_mappers method to avoid import issues
            with patch.object(manager, 'configure_mappers'):
                # Call create_engine
                import asyncio
                asyncio.run(manager.create_engine())
            
            # Verify the engine was created with optimized settings
            mock_create_engine.assert_called_once()
            call_args = mock_create_engine.call_args
            
            # Check that pool_size is optimized (should be 8, not 20)
            assert call_args.kwargs['pool_size'] == 8, f"Expected pool_size=8, got {call_args.kwargs['pool_size']}"
            
            # Check that max_overflow is optimized (should be 5, not 10)
            assert call_args.kwargs['max_overflow'] == 5, f"Expected max_overflow=5, got {call_args.kwargs['max_overflow']}"
            
            # Check other important settings
            assert call_args.kwargs['pool_pre_ping'] is True
            assert call_args.kwargs['pool_timeout'] == 30
            assert call_args.kwargs['pool_recycle'] == 1800
            assert call_args.kwargs['pool_reset_on_return'] is True


def test_gunicorn_worker_connections_default():
    """Test that Gunicorn worker_connections default is optimized."""
    
    # Test the default value when environment variable is not set
    with patch.dict(os.environ, {}, clear=True):
        # Import gunicorn_conf to get the default value
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "gunicorn_conf", 
            os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', 'gunicorn_conf.py')
        )
        gunicorn_conf = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gunicorn_conf)
        
        # Check that worker_connections is optimized (should be 200, not 1500)
        assert gunicorn_conf.worker_connections == 200, f"Expected worker_connections=200, got {gunicorn_conf.worker_connections}"


def test_gunicorn_worker_connections_configurable():
    """Test that Gunicorn worker_connections can be configured via environment variable."""
    
    # Test with custom environment variable
    with patch.dict(os.environ, {'GUNICORN_WORKER_CONNECTIONS': '150'}):
        # Import gunicorn_conf to get the configured value
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "gunicorn_conf", 
            os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', 'gunicorn_conf.py')
        )
        gunicorn_conf = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gunicorn_conf)
        
        # Check that worker_connections respects the environment variable
        assert gunicorn_conf.worker_connections == 150, f"Expected worker_connections=150, got {gunicorn_conf.worker_connections}"


def test_connection_load_calculation():
    """Test that the total connection load is within reasonable limits."""
    
    # Import gunicorn configuration
    with patch.dict(os.environ, {}, clear=True):
        import importlib.util
        spec = importlib.util.spec_from_file_location(
            "gunicorn_conf", 
            os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', 'gunicorn_conf.py')
        )
        gunicorn_conf = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(gunicorn_conf)
    
    # Calculate maximum theoretical connections
    max_workers = 4  # From gunicorn_conf.py: min(cpu_count + 1, 4)
    worker_connections = gunicorn_conf.worker_connections
    
    # SQLAlchemy pool settings
    pool_size = 8
    max_overflow = 5
    
    # Calculate different connection scenarios
    theoretical_max = max_workers * worker_connections
    sqlalchemy_max = max_workers * (pool_size + max_overflow)
    
    # Verify that connections are within reasonable limits for a startup
    assert theoretical_max <= 1000, f"Theoretical max connections too high: {theoretical_max}"
    assert sqlalchemy_max <= 100, f"SQLAlchemy max connections too high: {sqlalchemy_max}"
    
    # Verify significant reduction from previous settings
    # Previous: 4 workers * 1500 connections = 6000 theoretical max
    # Previous: 4 workers * (20 + 10) = 120 SQLAlchemy max
    previous_theoretical = 4 * 1500
    previous_sqlalchemy = 4 * (20 + 10)
    
    reduction_theoretical = (previous_theoretical - theoretical_max) / previous_theoretical
    reduction_sqlalchemy = (previous_sqlalchemy - sqlalchemy_max) / previous_sqlalchemy
    
    assert reduction_theoretical >= 0.8, f"Theoretical connection reduction insufficient: {reduction_theoretical:.2%}"
    assert reduction_sqlalchemy >= 0.5, f"SQLAlchemy connection reduction insufficient: {reduction_sqlalchemy:.2%}"


def test_environment_variables_documented():
    """Test that new environment variables are documented in example files."""
    
    # Check .env.example
    env_example_path = os.path.join(os.path.dirname(__file__), '..', 'rayuela_backend', '.env.example')
    with open(env_example_path, 'r') as f:
        env_content = f.read()
    
    assert 'GUNICORN_WORKER_CONNECTIONS' in env_content, "GUNICORN_WORKER_CONNECTIONS not found in .env.example"
    assert 'GUNICORN_WORKERS' in env_content, "GUNICORN_WORKERS not found in .env.example"
    assert 'GUNICORN_TIMEOUT' in env_content, "GUNICORN_TIMEOUT not found in .env.example"
    
    # Check production config example
    prod_config_path = os.path.join(os.path.dirname(__file__), '..', 'config', 'config-production.env.example')
    with open(prod_config_path, 'r') as f:
        prod_content = f.read()
    
    assert 'GUNICORN_WORKER_CONNECTIONS' in prod_content, "GUNICORN_WORKER_CONNECTIONS not found in production config"
    assert 'GUNICORN_WORKERS' in prod_content, "GUNICORN_WORKERS not found in production config"


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
