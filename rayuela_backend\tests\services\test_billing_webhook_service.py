"""
Tests for the BillingWebhookService.
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime, timezone

from src.services.billing_webhook_service import BillingWebhookService
from src.db.models import Subscription, Account
from src.db.enums import SubscriptionPlan


@pytest.fixture
def mock_db():
    """Mock database session."""
    db = AsyncMock()
    db.commit = AsyncMock()
    db.add = MagicMock()
    return db


@pytest.fixture
def mock_mercadopago_service():
    """Mock MercadoPagoService."""
    service = MagicMock()
    service._get_plan_from_price_id.return_value = SubscriptionPlan.STARTER
    service.sdk = MagicMock()
    service.sdk.payment.return_value.get.return_value = {"response": {"status": "approved"}}
    service.sdk.preapproval.return_value.get.return_value = {"response": {"status": "authorized"}}
    return service


@pytest.fixture
def webhook_service(mock_db, mock_mercadopago_service):
    """Create a BillingWebhookService with mocked dependencies."""
    service = BillingWebhookService(mock_db)
    service.mercadopago_service = mock_mercadopago_service
    return service


@pytest.mark.asyncio
async def test_handle_mercadopago_payment_approved(webhook_service, mock_db):
    """Test handling an approved payment from Mercado Pago."""
    # Arrange
    payment = {
        "metadata": {
            "account_id": "123",
            "price_id": "price_123"
        },
        "subscription_id": "sub_123"
    }
    
    # Mock the repository
    mock_subscription_repo = AsyncMock()
    mock_subscription_repo.get_by_account.return_value = None
    
    with patch("src.services.billing_webhook_service.SubscriptionRepository", return_value=mock_subscription_repo):
        # Act
        await webhook_service._handle_mercadopago_payment_approved(payment)
        
        # Assert
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()


@pytest.mark.asyncio
async def test_handle_mercadopago_payment_approved_existing_subscription(webhook_service, mock_db):
    """Test handling an approved payment with an existing subscription."""
    # Arrange
    payment = {
        "metadata": {
            "account_id": "123",
            "price_id": "price_123"
        },
        "subscription_id": "sub_123"
    }
    
    # Create a mock subscription
    mock_subscription = MagicMock(spec=Subscription)
    
    # Mock the repository
    mock_subscription_repo = AsyncMock()
    mock_subscription_repo.get_by_account.return_value = mock_subscription
    
    with patch("src.services.billing_webhook_service.SubscriptionRepository", return_value=mock_subscription_repo):
        # Act
        await webhook_service._handle_mercadopago_payment_approved(payment)
        
        # Assert
        mock_db.add.assert_not_called()
        mock_db.commit.assert_called_once()
        assert mock_subscription.plan_type == SubscriptionPlan.STARTER
        assert mock_subscription.is_active is True


@pytest.mark.asyncio
async def test_handle_mercadopago_payment_failed(webhook_service):
    """Test handling a failed payment from Mercado Pago."""
    # Arrange
    payment = {
        "metadata": {
            "account_id": "123"
        }
    }
    
    # Act
    await webhook_service._handle_mercadopago_payment_failed(payment)
    
    # Assert - no exceptions should be raised


@pytest.mark.asyncio
async def test_handle_mercadopago_subscription_created(webhook_service, mock_db):
    """Test handling a subscription created in Mercado Pago."""
    # Arrange
    subscription = {
        "id": "sub_123",
        "metadata": {
            "account_id": "123",
            "price_id": "price_123"
        }
    }
    
    # Mock the repository
    mock_subscription_repo = AsyncMock()
    mock_subscription_repo.get_by_account.return_value = None
    
    with patch("src.services.billing_webhook_service.SubscriptionRepository", return_value=mock_subscription_repo):
        # Act
        await webhook_service._handle_mercadopago_subscription_created(subscription)
        
        # Assert
        mock_db.add.assert_called_once()
        mock_db.commit.assert_called_once()


@pytest.mark.asyncio
async def test_handle_stripe_webhook(webhook_service):
    """Test handling a Stripe webhook event."""
    # Arrange
    event_type = "checkout.session.completed"
    event_data = {
        "object": {
            "metadata": {
                "account_id": "123"
            },
            "subscription": "sub_123",
            "customer": "cus_123"
        }
    }
    
    # Mock the methods
    webhook_service._handle_stripe_checkout_completed = AsyncMock()
    
    # Act
    await webhook_service.handle_stripe_webhook(event_type, event_data)
    
    # Assert
    webhook_service._handle_stripe_checkout_completed.assert_called_once_with(event_data)
