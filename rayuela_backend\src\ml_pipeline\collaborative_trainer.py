from typing import Dict, Any, <PERSON><PERSON>
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from implicit.als import AlternatingLeastSquares
from src.ml_pipeline.base_trainer import BaseTrainer
from src.utils.base_logger import log_info, log_error
from src.core.config import settings


class CollaborativeTrainer(BaseTrainer):
    """Trainer para modelos de filtrado colaborativo"""

    def __init__(self, account_id: int):
        super().__init__(account_id)
        self.default_params = {
            "factors": settings.COLLABORATIVE_FACTORS,
            "regularization": settings.COLLABORATIVE_REGULARIZATION,
            "iterations": settings.COLLABORATIVE_ITERATIONS,
            "alpha": 40,
        }

    def get_required_columns(self) -> list:
        """Retorna las columnas requeridas para el entrenamiento"""
        return ["user_id", "item_id", "value"]

    def prepare_data(self, data: pd.DataFrame) -> <PERSON><PERSON>:
        """Prepara los datos para el entrenamiento"""
        if not self.validate_data(data):
            raise ValueError("Datos inválidos para entrenamiento")

        user_ids = data["user_id"].unique()
        item_ids = data["item_id"].unique()

        user_to_idx = {user: idx for idx, user in enumerate(user_ids)}
        item_to_idx = {item: idx for idx, item in enumerate(item_ids)}

        rows = data["user_id"].map(user_to_idx)
        cols = data["item_id"].map(item_to_idx)
        values = data["value"]

        shape = (len(user_ids), len(item_ids))

        return (rows, cols, values, shape, user_to_idx, item_to_idx)

    def train_model(
        self, data: pd.DataFrame, params: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Entrena el modelo ALS"""
        try:
            model_params = {**self.default_params, **(params or {})}

            rows, cols, values, shape, user_map, item_map = self.prepare_data(data)

            model = AlternatingLeastSquares(**model_params)
            model.fit(rows, cols, values, shape=shape)

            self.model = model

            train_data, test_data = train_test_split(data, test_size=0.2)
            metrics = self._calculate_metrics(
                test_data, user_map=user_map, item_map=item_map
            )

            return {
                "model": model,
                "user_map": user_map,
                "item_map": item_map,
                "metrics": metrics,
                "parameters": model_params,
            }

        except Exception as e:
            log_error(f"Error en entrenamiento para cuenta {self.account_id}: {str(e)}")
            raise

    def _calculate_metrics(self, test_data: pd.DataFrame, **kwargs) -> Dict[str, float]:
        """Calcula métricas básicas del modelo"""
        try:
            user_map = kwargs.get("user_map", {})
            item_map = kwargs.get("item_map", {})
            precision_at_k = []
            recall_at_k = []

            for user_id in test_data["user_id"].unique():
                if user_id in user_map:
                    user_idx = user_map[user_id]
                    actual_items = set(
                        test_data[test_data["user_id"] == user_id]["item_id"]
                    )

                    recommended_items = self.model.recommend(
                        user_idx, None, N=10, filter_already_liked_items=True
                    )

                    recommended_ids = set(
                        [item_map[rec[0]] for rec in recommended_items]
                    )

                    precision = len(actual_items & recommended_ids) / len(
                        recommended_ids
                    )
                    recall = len(actual_items & recommended_ids) / len(actual_items)

                    precision_at_k.append(precision)
                    recall_at_k.append(recall)

            return {
                "precision@10": np.mean(precision_at_k),
                "recall@10": np.mean(recall_at_k),
            }

        except Exception as e:
            log_error(f"Error calculando métricas: {str(e)}")
            return {}
