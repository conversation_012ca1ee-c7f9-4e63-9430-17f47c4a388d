#!/bin/bash

set -e

DOMAIN="rayuela.ai"
SUBDOMAIN="api.rayuela.ai"

echo "🔍 === VERIFICACIÓN DE ESTADO DEL DOMINIO ==="
echo ""

# 1. Verificar verificación en Google
echo "📋 1. Verificando si el dominio está verificado en Google..."
VERIFIED_DOMAINS=$(gcloud domains list-user-verified --format="value(domain)" 2>/dev/null || echo "")

if [[ "$VERIFIED_DOMAINS" =~ "$DOMAIN" ]]; then
    echo "✅ Dominio $DOMAIN está verificado en Google"
else
    echo "❌ Dominio $DOMAIN NO está verificado en Google"
    echo "   Ejecuta: gcloud domains verify $DOMAIN"
    echo "   O verifica manualmente en: https://search.google.com/search-console/welcome"
fi

echo ""

# 2. Verificar resolución DNS
echo "🌐 2. Verificando resolución DNS..."
echo "   Verificando $DOMAIN..."
if nslookup $DOMAIN > /dev/null 2>&1; then
    echo "✅ $DOMAIN resuelve correctamente"
    nslookup $DOMAIN | grep -A 2 "Non-authoritative answer:"
else
    echo "❌ $DOMAIN no resuelve"
    echo "   Asegúrate de configurar los nameservers en Namecheap:"
    echo "   - ns-cloud-e1.googledomains.com"
    echo "   - ns-cloud-e2.googledomains.com"
    echo "   - ns-cloud-e3.googledomains.com"
    echo "   - ns-cloud-e4.googledomains.com"
fi

echo ""

# 3. Verificar nameservers configurados
echo "📡 3. Verificando nameservers configurados..."
NS_SERVERS=$(dig +short NS $DOMAIN 2>/dev/null || echo "")
if [ -n "$NS_SERVERS" ]; then
    echo "✅ Nameservers encontrados:"
    echo "$NS_SERVERS" | sed 's/^/   - /'
    
    # Verificar si son los correctos de Google Cloud
    if echo "$NS_SERVERS" | grep -q "googledomains.com"; then
        echo "✅ Nameservers de Google Cloud configurados correctamente"
    else
        echo "⚠️ Los nameservers no son de Google Cloud"
    fi
else
    echo "❌ No se encontraron nameservers"
fi

echo ""

# 4. Verificar zona DNS en GCP
echo "🗂️ 4. Verificando zona DNS en GCP..."
if gcloud dns managed-zones describe rayuela-ai-zone > /dev/null 2>&1; then
    echo "✅ Zona DNS 'rayuela-ai-zone' existe en GCP"
    echo "   Nameservers de la zona:"
    gcloud dns managed-zones describe rayuela-ai-zone --format="value(nameServers[])" | sed 's/^/   - /'
else
    echo "❌ Zona DNS 'rayuela-ai-zone' no existe en GCP"
fi

echo ""

# 5. Verificar mappings existentes
echo "🔗 5. Verificando mappings de dominio existentes..."
EXISTING_MAPPINGS=$(gcloud beta run domain-mappings list --region=us-central1 --format="value(name)" 2>/dev/null || echo "")
if [ -n "$EXISTING_MAPPINGS" ]; then
    echo "✅ Mappings existentes:"
    echo "$EXISTING_MAPPINGS" | sed 's/^/   - /'
else
    echo "ℹ️ No hay mappings de dominio configurados aún"
fi

echo ""

# 6. Estado de servicios Cloud Run
echo "🚀 6. Verificando servicios Cloud Run..."
echo "   Servicios disponibles:"
gcloud run services list --region=us-central1 --format="table(name,status.url)" | sed 's/^/   /'

echo ""
echo "📊 === RESUMEN ==="
echo ""

# Determinar si está listo para configurar
READY=true

if [[ ! "$VERIFIED_DOMAINS" =~ "$DOMAIN" ]]; then
    echo "❌ Dominio no verificado en Google"
    READY=false
fi

if ! nslookup $DOMAIN > /dev/null 2>&1; then
    echo "❌ DNS no configurado/propagado"
    READY=false
fi

if [ "$READY" = true ]; then
    echo "🎉 ¡El dominio está listo para configurar!"
    echo "   Ejecuta: ./scripts/setup-custom-domain.sh"
else
    echo "⏳ El dominio aún no está listo. Completa los pasos pendientes."
fi

echo "" 