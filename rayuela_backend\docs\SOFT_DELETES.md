# Implementación de Soft Deletes

Este documento describe la implementación de soft deletes (eliminaciones lógicas) en la aplicación, incluyendo las mejoras recientes para cumplir con requisitos de auditoría y GDPR.

## Concepto de Soft Delete

El soft delete es una técnica que permite "eliminar" registros sin borrarlos físicamente de la base de datos. En lugar de ejecutar una operación `DELETE`, se marca el registro como eliminado mediante uno o más campos específicos. Esto proporciona varias ventajas:

1. **Recuperación de datos**: Los datos pueden ser restaurados si se eliminaron por error.
2. **Auditoría**: Se mantiene un historial completo de los datos para fines de auditoría.
3. **Integridad referencial**: Se evitan problemas con relaciones y claves foráneas.
4. **Cumplimiento normativo**: Facilita el cumplimiento de regulaciones como GDPR que requieren mantener registros de cuándo se "eliminaron" los datos.

## Implementación Actual

### Campos de Soft Delete

Los modelos que soportan soft delete incluyen los siguientes campos:

1. **is_active**: Un campo booleano que indica si el registro está activo (no eliminado).
2. **deleted_at**: Un campo de fecha y hora que registra cuándo se realizó la eliminación lógica.

Ejemplo de definición de modelo:

```python
class Account(Base):
    __tablename__ = "accounts"
    
    account_id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    # ... otros campos ...
    
    created_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True)
    deleted_at = Column(DateTime(timezone=True), nullable=True)
```

### Modelos con Soporte para Soft Delete

Los siguientes modelos incluyen soporte para soft delete:

- `Account`: Cuentas de clientes
- `SystemUser`: Usuarios del sistema
- `EndUser`: Usuarios finales (consumidores)
- `Product`: Productos

### Implementación en el Repositorio Base

La lógica de soft delete está implementada en la clase `BaseRepository`, que proporciona métodos genéricos para todas las operaciones CRUD:

```python
async def prepare_soft_delete(self, id: int) -> Optional[ModelType]:
    """Prepara el objeto para soft delete. Retorna el objeto o None."""
    obj = await self.get_by_id(id)
    if obj and hasattr(obj, "is_active"):
        obj.is_active = False
        if hasattr(obj, "deleted_at"):
            obj.deleted_at = datetime.now(timezone.utc)
        log_info(
            f"Object {self.model.__name__} ID {id} prepared for soft delete, awaiting commit."
        )
        return obj
    return None

async def delete(self, id: int) -> bool:
    """Eliminar un objeto (hard delete o soft delete)."""
    try:
        await self._validate_tenant_access()

        # Intentar soft delete primero
        obj = await self.prepare_soft_delete(id)
        if obj:
            return True

        # Si no se pudo hacer soft delete, intentar hard delete
        obj = await self.get_by_id(id)
        if not obj:
            return False

        await self.db.delete(obj)
        return True
    except SQLAlchemyError as e:
        await self._handle_error(f"deleting {self.model.__name__}", e)
        return False
```

### Filtrado de Elementos Eliminados

Los métodos de consulta en `BaseRepository` filtran automáticamente los elementos eliminados:

```python
# Excluir elementos eliminados (soft delete) si aplica
if not include_deleted:
    if hasattr(self.model, "is_active"):
        query = query.filter(self.model.is_active == True)
    if hasattr(self.model, "deleted_at"):
        query = query.filter(self.model.deleted_at == None)
```

## Uso en la Aplicación

### Eliminación de Registros

Para eliminar un registro usando soft delete:

```python
# En un controlador
user_repo = SystemUserRepository(db, account_id)
success = await user_repo.delete(user_id)
```

### Consulta Incluyendo Elementos Eliminados

Para incluir elementos eliminados en las consultas:

```python
# En un controlador
user_repo = SystemUserRepository(db, account_id)
users = await user_repo.get_by_filters(
    filters={},
    include_deleted=True
)
```

## Beneficios para GDPR

La implementación de `deleted_at` proporciona varios beneficios para el cumplimiento del GDPR:

1. **Registro de eliminación**: Se mantiene un registro de cuándo se "eliminaron" los datos, lo que es útil para demostrar cumplimiento.

2. **Políticas de retención**: Facilita la implementación de políticas de retención de datos, permitiendo eliminar físicamente los datos después de un período específico.

3. **Derecho al olvido**: Permite implementar el "derecho al olvido" en dos fases:
   - Fase 1: Soft delete (inmediato)
   - Fase 2: Hard delete (después de un período de retención)

## Migración y Actualización

Se ha creado una migración Alembic para añadir el campo `deleted_at` a las tablas existentes:

```python
def upgrade():
    # Añadir columna deleted_at a la tabla accounts
    op.add_column('accounts', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    
    # Añadir columna deleted_at a la tabla system_users
    op.add_column('system_users', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    
    # Añadir columna deleted_at a la tabla end_users
    op.add_column('end_users', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
    
    # Añadir columna deleted_at a la tabla products
    op.add_column('products', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
```

## Consideraciones Futuras

### Restauración de Elementos Eliminados

Se podría implementar un método `restore` en `BaseRepository` para restaurar elementos eliminados:

```python
async def restore(self, id: int) -> Optional[ModelType]:
    """Restaurar un objeto eliminado mediante soft delete."""
    obj = await self.get_by_id(id, include_deleted=True)
    if obj and hasattr(obj, "is_active") and not obj.is_active:
        obj.is_active = True
        if hasattr(obj, "deleted_at"):
            obj.deleted_at = None
        await self.db.flush()
        await self.db.refresh(obj)
        return obj
    return None
```

### Eliminación Física Programada

Se podría implementar un proceso periódico para eliminar físicamente los registros que han sido marcados como eliminados durante un período específico:

```python
async def hard_delete_expired_records(days_retention: int = 90):
    """Eliminar físicamente registros que han sido soft-deleted hace más de X días."""
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_retention)
    
    for model in [Account, SystemUser, EndUser, Product]:
        query = delete(model).where(
            and_(
                model.deleted_at.is_not(None),
                model.deleted_at < cutoff_date
            )
        )
        await db.execute(query)
    
    await db.commit()
```

### Auditoría de Eliminaciones

Se podría mejorar el sistema de auditoría para registrar específicamente las operaciones de soft delete:

```python
async def delete(self, id: int) -> bool:
    """Eliminar un objeto (hard delete o soft delete)."""
    try:
        # ... código existente ...
        
        # Registrar la eliminación en el log de auditoría
        if obj and hasattr(obj, "is_active") and not obj.is_active:
            await write_audit_log_to_db(
                db=self.db,
                account_id=self.account_id,
                user_id=current_user.id,
                action="DELETE",
                entity_type=self.model.__name__,
                entity_id=str(id),
                details={"soft_delete": True, "deleted_at": obj.deleted_at.isoformat()},
                success=True
            )
        
        return True
    except SQLAlchemyError as e:
        # ... código existente ...
```

## Conclusión

La implementación de soft deletes con `is_active` y `deleted_at` proporciona una solución robusta para la gestión de eliminaciones de datos, cumpliendo con requisitos de auditoría y normativas como GDPR. La estructura actual permite una fácil extensión para implementar funcionalidades adicionales como restauración de datos y eliminación física programada.
