"""
Celery tasks for database partition management.
"""

from typing import Dict, Any
from datetime import datetime, timezone
from asgiref.sync import async_to_sync
import asyncio

from src.workers.celery_app import celery_app
from src.utils.base_logger import log_info, log_error


@celery_app.task(name="manage_partitions_task", bind=True, max_retries=3, default_retry_delay=300, queue="maintenance")
def manage_partitions_task(self) -> Dict[str, Any]:
    """
    Celery task to manage database partitions.

    This task ensures that there are enough partitions for high-volume tables
    based on the current maximum account_id and a buffer for future growth.

    Returns:
        Dictionary with partition management results
    """
    try:
        task_id = self.request.id or "unknown"
        log_info(f"[Task {task_id}] Starting partition management")

        # Import here to avoid circular imports
        from scripts.manage_partitions import manage_partitions

        # Run the partition management function
        start_time = datetime.now(timezone.utc)
        async_to_sync(manage_partitions)()
        duration = (datetime.now(timezone.utc) - start_time).total_seconds()

        log_info(f"[Task {task_id}] Completed partition management in {duration:.2f}s")

        return {
            "task_id": task_id,
            "status": "completed",
            "duration_seconds": duration,
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
    except Exception as e:
        log_error(f"[Task {self.request.id or 'unknown'}] Error in manage_partitions task: {str(e)}")
        # Retry the task with exponential backoff
        try:
            self.retry(exc=e, countdown=self.default_retry_delay * (2 ** self.request.retries))
        except Exception as retry_error:
            log_error(f"[Task {self.request.id or 'unknown'}] Max retries exceeded for manage_partitions: {str(retry_error)}")

        return {
            "task_id": self.request.id or "unknown",
            "status": "failed",
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }
