#!/usr/bin/env python
"""
Script para verificar que las importaciones funcionan correctamente.
Este script intenta importar los módulos principales de Rayuela para verificar que el PYTHONPATH está configurado correctamente.
"""

import os
import sys
import importlib

# Agregar el directorio raíz al PYTHONPATH
current_dir = os.path.dirname(os.path.abspath(__file__))
root_dir = os.path.dirname(current_dir)
sys.path.append(root_dir)

# Lista de módulos a importar
modules_to_import = [
    "src.core.config",
    "src.db.base",
    "src.api.v1.api",
    "src.utils.base_logger",
    "src.core.security.api_key",
    "src.core.security.jwt",
    "src.db.models",
    "src.db.repositories",
    "src.services",
]

def test_imports():
    """Verificar que los módulos se pueden importar correctamente."""
    print("Verificando importaciones...")
    
    success = True
    for module_name in modules_to_import:
        try:
            module = importlib.import_module(module_name)
            print(f"✅ Importación exitosa: {module_name}")
        except ImportError as e:
            print(f"❌ Error al importar {module_name}: {e}")
            success = False
    
    if success:
        print("\n✅ Todas las importaciones fueron exitosas.")
        return 0
    else:
        print("\n❌ Algunas importaciones fallaron. Verifica el PYTHONPATH.")
        return 1

if __name__ == "__main__":
    sys.exit(test_imports())
