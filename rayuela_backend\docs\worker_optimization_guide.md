# Celery Worker Optimization Guide

This guide provides information on how to optimize Celery workers for different types of tasks in the application.

## Worker Types and Resource Allocation

The application uses different worker types optimized for specific task categories:

| Worker Type | Queue | Memory | CPU | Concurrency | Max Tasks Per Child | Use Case |
|-------------|-------|--------|-----|-------------|---------------------|----------|
| Training | `training` | 8GB | 2 cores | 1 | 1 | ML model training tasks |
| Batch Processing | `batch_processing` | 6GB | 2 cores | 2 | 5 | Data ingestion and batch processing |
| Maintenance | `maintenance` | 4GB | 1 core | 4 | 10 | Cleanup, monitoring, and maintenance tasks |
| Default | `default` | 3GB | 1 core | 2 | 10 | Miscellaneous tasks |

## Queue Configuration

Tasks are automatically routed to the appropriate queue based on their type:

- **Training Queue**: ML model training tasks
  - `train_model`
  - `train_model_for_job`

- **Batch Processing Queue**: Data ingestion and batch processing tasks
  - `process_batch_data`

- **Maintenance Queue**: Cleanup, monitoring, and maintenance tasks
  - `cleanup_old_audit_logs`
  - `cleanup_old_interactions`
  - `monitor_high_volume_tables`
  - `manage_partitions_task`

- **Default Queue**: All other tasks

## Deployment Configuration

The Docker Compose configuration includes separate services for each worker type:

```yaml
# Worker for training tasks (high memory, low concurrency)
worker-training:
  build:
    context: .
    dockerfile: docker/worker.Dockerfile
  command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=1 --max-tasks-per-child=1 --queues=training --hostname=training@%h
  mem_limit: 8g
  mem_reservation: 6g
  deploy:
    resources:
      limits:
        cpus: '2.0'

# Worker for batch processing tasks (medium memory, medium concurrency)
worker-batch:
  build:
    context: .
    dockerfile: docker/worker.Dockerfile
  command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=5 --queues=batch_processing --hostname=batch@%h
  mem_limit: 6g
  mem_reservation: 4g
  deploy:
    resources:
      limits:
        cpus: '2.0'

# Worker for maintenance tasks (low memory, high concurrency)
worker-maintenance:
  build:
    context: .
    dockerfile: docker/worker.Dockerfile
  command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=4 --max-tasks-per-child=10 --queues=maintenance --hostname=maintenance@%h
  mem_limit: 4g
  mem_reservation: 2g
  deploy:
    resources:
      limits:
        cpus: '1.0'

# Worker for default tasks (low memory, medium concurrency)
worker-default:
  build:
    context: .
    dockerfile: docker/worker.Dockerfile
  command: celery -A src.workers.celery_app worker --loglevel=info --concurrency=2 --max-tasks-per-child=10 --queues=default --hostname=default@%h
  mem_limit: 3g
  mem_reservation: 2g
  deploy:
    resources:
      limits:
        cpus: '1.0'
```

## Monitoring Worker Performance

The application includes tools for monitoring worker performance:

### Using the Monitor Script

The `scripts/monitor_workers.py` script provides information about active workers, their queues, and resource usage:

```bash
# Basic monitoring
python scripts/monitor_workers.py

# Continuous monitoring every 5 seconds
python scripts/monitor_workers.py --interval 5 --count 0

# Output in JSON format
python scripts/monitor_workers.py --json
```

### Using the Management Script

The `scripts/manage_workers.py` script provides utilities to start, stop, and scale workers:

```bash
# Show worker status
python scripts/manage_workers.py status

# Start a worker for a specific queue
python scripts/manage_workers.py start training --concurrency 1 --max-tasks 1

# Stop a worker
python scripts/manage_workers.py stop training@hostname

# Scale workers for a queue based on load
python scripts/manage_workers.py scale batch_processing --min 1 --max 5
```

## Best Practices for Task Design

To make the most of the worker optimization:

1. **Task Categorization**: Ensure tasks are properly categorized and routed to the appropriate queue.

2. **Resource Estimation**: Estimate memory and CPU requirements for tasks and adjust worker configuration accordingly.

3. **Task Duration**: For long-running tasks, use a lower concurrency and max_tasks_per_child to prevent resource exhaustion.

4. **Monitoring**: Regularly monitor worker performance and adjust configuration as needed.

5. **Task Chunking**: For large batch operations, consider chunking tasks into smaller pieces to improve parallelism and resource utilization.

6. **Error Handling**: Implement proper error handling and retries in tasks to ensure robustness.

7. **Task Priority**: Use task priorities to ensure critical tasks are processed first.

## Scaling Strategies

### Vertical Scaling

Increase resources (memory, CPU) for workers handling resource-intensive tasks:

```yaml
worker-training:
  mem_limit: 12g  # Increased from 8g
  mem_reservation: 8g  # Increased from 6g
  deploy:
    resources:
      limits:
        cpus: '4.0'  # Increased from 2.0
```

### Horizontal Scaling

Add more worker instances for queues with high throughput requirements:

```yaml
# Add more batch processing workers
worker-batch-1:
  # Same configuration as worker-batch
  
worker-batch-2:
  # Same configuration as worker-batch
```

### Dynamic Scaling

Use the management script to dynamically scale workers based on queue length:

```bash
# Set up a cron job to run every minute
* * * * * python /path/to/scripts/manage_workers.py scale batch_processing --min 1 --max 5
```

## Troubleshooting

### Memory Issues

If workers are being killed due to memory issues:

1. Decrease concurrency to reduce the number of simultaneous tasks.
2. Increase memory limit for the worker.
3. Reduce max_tasks_per_child to recycle worker processes more frequently.
4. Check for memory leaks in task code.

### CPU Issues

If workers are CPU-bound:

1. Increase CPU limit for the worker.
2. Optimize task code to reduce CPU usage.
3. Consider using a worker pool with more CPU resources.

### Queue Backlog

If tasks are piling up in queues:

1. Add more worker instances for the affected queue.
2. Increase concurrency for existing workers.
3. Check for bottlenecks in task code.
4. Consider chunking large tasks into smaller pieces.

## Advanced Configuration

### Task Time Limits

Set time limits for tasks to prevent them from running indefinitely:

```python
@celery_app.task(name="train_model", queue="training", time_limit=3600*4, soft_time_limit=3600*3.5)
def train_model(account_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
    # Task code
```

### Task Priorities

Set priorities for tasks to ensure critical tasks are processed first:

```python
task = train_model.apply_async(
    args=[account_id, data],
    queue="training",
    priority=9  # Higher priority (0-9, 9 being highest)
)
```

### Task Rate Limiting

Limit the rate at which tasks are processed:

```python
@celery_app.task(name="process_batch_data", queue="batch_processing", rate_limit="10/m")
def process_batch_data(account_id: int, job_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
    # Task code
```

## Conclusion

By optimizing worker configuration for different task types, you can improve resource utilization, throughput, and reliability of the application. Regularly monitor worker performance and adjust configuration as needed to ensure optimal operation.
