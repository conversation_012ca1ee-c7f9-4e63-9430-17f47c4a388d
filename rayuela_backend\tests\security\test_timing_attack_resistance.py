"""
Tests para verificar la resistencia a ataques de tiempo en la validación de API keys.
"""

import pytest
import time
import statistics
from typing import List
from unittest.mock import AsyncMock, MagicMock

from src.core.security.api_key import generate_api_key, verify_api_key, hash_api_key
from src.services.api_key_service import Api<PERSON>eyService
from src.middleware.usage_meter_middleware import UsageMeterMiddleware
from src.db.models import Account
from src.db.repositories.account import AccountRepository


class TestTimingAttackResistance:
    """Test suite para verificar resistencia a ataques de tiempo."""

    def test_verify_api_key_timing_safety(self):
        """Verifica que verify_api_key use comparación de tiempo constante."""
        # Generate a valid API key and hash
        api_key, api_key_hash = generate_api_key()
        
        # Test with correct API key
        assert verify_api_key(api_key, api_key_hash) is True
        
        # Test with incorrect API key
        wrong_key = "ray_wrong_key_12345678901234567890"
        assert verify_api_key(wrong_key, api_key_hash) is False
        
        # Test timing consistency - this is a basic check
        # In a real timing attack test, we'd need more sophisticated timing analysis
        correct_times = []
        incorrect_times = []
        
        # Measure timing for correct key
        for _ in range(100):
            start = time.perf_counter()
            verify_api_key(api_key, api_key_hash)
            end = time.perf_counter()
            correct_times.append(end - start)
        
        # Measure timing for incorrect key
        for _ in range(100):
            start = time.perf_counter()
            verify_api_key(wrong_key, api_key_hash)
            end = time.perf_counter()
            incorrect_times.append(end - start)
        
        # The timing should be relatively consistent
        # This is a basic test - real timing attack analysis would be more sophisticated
        correct_avg = statistics.mean(correct_times)
        incorrect_avg = statistics.mean(incorrect_times)
        
        # The difference should be minimal (within reasonable variance)
        # This is not a perfect test but gives us confidence in timing safety
        time_diff_ratio = abs(correct_avg - incorrect_avg) / max(correct_avg, incorrect_avg)
        assert time_diff_ratio < 0.5, f"Timing difference too large: {time_diff_ratio}"

    @pytest.mark.asyncio
    async def test_api_key_service_timing_safety(self):
        """Verifica que ApiKeyService use comparación segura."""
        # Mock database session
        mock_db = AsyncMock()
        service = ApiKeyService(mock_db)
        
        # Create mock account with API key
        api_key, api_key_hash = generate_api_key()
        mock_account = MagicMock()
        mock_account.api_key_hash = api_key_hash
        
        # Test correct API key
        result = await service.validate_api_key(api_key, mock_account)
        assert result is True
        
        # Test incorrect API key
        wrong_key = "ray_wrong_key_12345678901234567890"
        result = await service.validate_api_key(wrong_key, mock_account)
        assert result is False
        
        # Test with account without API key hash
        mock_account_no_key = MagicMock()
        mock_account_no_key.api_key_hash = None
        result = await service.validate_api_key(api_key, mock_account_no_key)
        assert result is False

    @pytest.mark.asyncio
    async def test_account_repository_timing_safety(self):
        """Verifica que AccountRepository use comparación segura."""
        # Mock database session and base repository methods
        mock_db = AsyncMock()
        repo = AccountRepository(mock_db)
        
        # Create mock accounts
        api_key1, api_key_hash1 = generate_api_key()
        api_key2, api_key_hash2 = generate_api_key()
        
        mock_account1 = MagicMock()
        mock_account1.api_key_hash = api_key_hash1
        mock_account1.is_active = True
        mock_account1.deleted_at = None
        
        mock_account2 = MagicMock()
        mock_account2.api_key_hash = api_key_hash2
        mock_account2.is_active = True
        mock_account2.deleted_at = None
        
        # Mock the execute_with_partition method
        mock_result = MagicMock()
        mock_result.scalars.return_value.all.return_value = [mock_account1, mock_account2]
        repo.execute_with_partition = AsyncMock(return_value=mock_result)
        
        # Test finding correct account
        found_account = await repo.get_by_api_key(api_key1)
        assert found_account == mock_account1
        
        # Test with non-existent API key
        wrong_key = "ray_wrong_key_12345678901234567890"
        found_account = await repo.get_by_api_key(wrong_key)
        assert found_account is None

    @pytest.mark.asyncio
    async def test_middleware_timing_safety(self):
        """Verifica que los middlewares usen comparación segura con optimización."""
        from unittest.mock import patch

        # Test UsageMeterMiddleware (the consolidated middleware)
        usage_meter = UsageMeterMiddleware()

        # Mock database and API key/account
        mock_db = AsyncMock()
        api_key, api_key_hash = generate_api_key()

        # Mock API key model
        mock_api_key_model = MagicMock()
        mock_api_key_model.id = 1
        mock_api_key_model.account_id = 123
        mock_api_key_model.api_key_hash = api_key_hash
        mock_api_key_model.is_active = True

        # Mock account
        mock_account = MagicMock()
        mock_account.account_id = 123
        mock_account.is_active = True
        mock_account.deleted_at = None

        # Mock the repositories
        with patch('src.db.repositories.api_key.ApiKeyRepository') as mock_api_key_repo_class, \
             patch('src.db.repositories.account.AccountRepository') as mock_account_repo_class:

            # Configure API key repository mock
            mock_api_key_repo = AsyncMock()
            mock_api_key_repo.get_by_api_key_hash.return_value = mock_api_key_model
            mock_api_key_repo.update_last_used = AsyncMock()
            mock_api_key_repo_class.return_value = mock_api_key_repo

            # Configure account repository mock
            mock_account_repo = AsyncMock()
            mock_account_repo.get_by_id.return_value = mock_account
            mock_account_repo_class.return_value = mock_account_repo

            # Test correct API key
            found_account = await usage_meter._get_account_from_api_key(mock_db, api_key)
            assert found_account == mock_account

            # Verify the repositories were called correctly
            mock_api_key_repo.get_by_api_key_hash.assert_called_once()
            mock_account_repo.get_by_id.assert_called_once_with(123)

            # Test incorrect API key (API key not found)
            mock_api_key_repo.get_by_api_key_hash.return_value = None
            wrong_key = "ray_wrong_key_12345678901234567890"
            found_account = await usage_meter._get_account_from_api_key(mock_db, wrong_key)
            assert found_account is None

    def test_hash_consistency(self):
        """Verifica que el hash de API keys sea consistente."""
        api_key = "ray_test_key_12345678901234567890"
        
        # Hash should be consistent
        hash1 = hash_api_key(api_key)
        hash2 = hash_api_key(api_key)
        assert hash1 == hash2
        
        # Different keys should have different hashes
        different_key = "ray_different_key_12345678901234567890"
        hash3 = hash_api_key(different_key)
        assert hash1 != hash3

    def test_api_key_generation_security(self):
        """Verifica que la generación de API keys sea segura."""
        # Generate multiple API keys
        keys = []
        hashes = []
        
        for _ in range(10):
            api_key, api_key_hash = generate_api_key()
            keys.append(api_key)
            hashes.append(api_key_hash)
        
        # All keys should be unique
        assert len(set(keys)) == len(keys)
        assert len(set(hashes)) == len(hashes)
        
        # Keys should have proper format
        for key in keys:
            assert key.startswith("sk_")
            assert len(key) > 20  # Should be reasonably long
        
        # Hashes should be hex strings
        for hash_val in hashes:
            assert isinstance(hash_val, str)
            assert len(hash_val) == 64  # SHA-256 hex string length
            # Should be valid hex
            int(hash_val, 16)

    def test_edge_cases(self):
        """Verifica casos extremos en la validación de API keys."""
        api_key, api_key_hash = generate_api_key()
        
        # Empty strings
        assert verify_api_key("", api_key_hash) is False
        assert verify_api_key(api_key, "") is False
        assert verify_api_key("", "") is False
        
        # None values should not crash
        assert verify_api_key(None, api_key_hash) is False
        assert verify_api_key(api_key, None) is False
        
        # Very long strings
        long_key = "ray_" + "x" * 1000
        assert verify_api_key(long_key, api_key_hash) is False
        
        # Special characters
        special_key = "ray_!@#$%^&*()_+"
        assert verify_api_key(special_key, api_key_hash) is False
