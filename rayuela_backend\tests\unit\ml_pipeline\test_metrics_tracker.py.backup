import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from src.ml_pipeline.metrics_tracker import MetricsTracker
from src.db.models import (
    Product,
    EndUser,
    Interaction,
    ModelMetric,
    TrainingMetrics
)

@pytest.fixture
async def metrics_tracker(db_session: AsyncSession):
    """Fixture para crear una instancia de MetricsTracker."""
    return MetricsTracker(db_session, account_id=1)

@pytest.fixture
async def sample_data(db_session: AsyncSession):
    """Fixture para crear datos de muestra."""
    # Crear usuarios
    users = []
    for i in range(10):
        user = EndUser(
            account_id=1,
            email=f"user{i}@example.com",
            name=f"User {i}"
        )
        db_session.add(user)
        users.append(user)
    
    # Crear productos
    products = []
    for i in range(20):
        product = Product(
            account_id=1,
            name=f"Product {i}",
            description=f"Description {i}",
            price=10.0,
            category=f"Category {i % 5}"  # 5 categorías diferentes
        )
        db_session.add(product)
        products.append(product)
    
    await db_session.commit()
    
    # Crear interacciones
    interactions = []
    for user in users:
        for product in products[:5]:  # Cada usuario interactúa con 5 productos
            interaction = Interaction(
                account_id=1,
                end_user_id=user.id,
                product_id=product.id,
                interaction_type="view",
                timestamp=datetime.utcnow() - timedelta(days=np.random.randint(0, 30))
            )
            db_session.add(interaction)
            interactions.append(interaction)
    
    await db_session.commit()
    return users, products, interactions

async def test_track_training_metrics(metrics_tracker: MetricsTracker, sample_data):
    """Test para registrar métricas de entrenamiento."""
    users, products, interactions = sample_data
    
    # Métricas de entrenamiento
    metrics = {
        "accuracy": 0.85,
        "precision": 0.82,
        "recall": 0.88,
        "f1": 0.85,
        "training_time": 120.5,
        "model_size_mb": 50.2
    }
    
    # Registrar métricas
    await metrics_tracker.track_training_metrics(metrics)
    
    # Verificar que se guardaron las métricas
    saved_metrics = await metrics_tracker.get_latest_training_metrics()
    assert isinstance(saved_metrics, TrainingMetrics)
    assert saved_metrics.account_id == 1
    assert saved_metrics.accuracy == 0.85
    assert saved_metrics.precision == 0.82
    assert saved_metrics.recall == 0.88
    assert saved_metrics.f1 == 0.85
    assert saved_metrics.training_time == 120.5
    assert saved_metrics.model_size_mb == 50.2

async def test_track_model_metrics(metrics_tracker: MetricsTracker, sample_data):
    """Test para registrar métricas del modelo."""
    users, products, interactions = sample_data
    
    # Métricas del modelo
    metrics = {
        "ndcg": 0.75,
        "diversity": 0.65,
        "novelty": 0.70,
        "coverage": 0.80,
        "latency_ms": 150.0
    }
    
    # Registrar métricas
    await metrics_tracker.track_model_metrics(metrics)
    
    # Verificar que se guardaron las métricas
    saved_metrics = await metrics_tracker.get_latest_model_metrics()
    assert isinstance(saved_metrics, ModelMetric)
    assert saved_metrics.account_id == 1
    assert saved_metrics.ndcg == 0.75
    assert saved_metrics.diversity == 0.65
    assert saved_metrics.novelty == 0.70
    assert saved_metrics.coverage == 0.80
    assert saved_metrics.latency_ms == 150.0

async def test_get_metrics_history(metrics_tracker: MetricsTracker, sample_data):
    """Test para obtener historial de métricas."""
    users, products, interactions = sample_data
    
    # Registrar múltiples métricas
    for i in range(5):
        training_metrics = {
            "accuracy": 0.8 + i * 0.01,
            "precision": 0.8 + i * 0.01,
            "recall": 0.8 + i * 0.01,
            "f1": 0.8 + i * 0.01,
            "training_time": 120.0,
            "model_size_mb": 50.0
        }
        await metrics_tracker.track_training_metrics(training_metrics)
    
    # Obtener historial
    history = await metrics_tracker.get_metrics_history(days=30)
    
    # Verificar historial
    assert len(history) == 5
    for i, metrics in enumerate(history):
        assert isinstance(metrics, TrainingMetrics)
        assert metrics.account_id == 1
        assert abs(metrics.accuracy - (0.8 + i * 0.01)) < 0.001

async def test_get_metrics_summary(metrics_tracker: MetricsTracker, sample_data):
    """Test para obtener resumen de métricas."""
    users, products, interactions = sample_data
    
    # Registrar métricas
    metrics = {
        "accuracy": 0.85,
        "precision": 0.82,
        "recall": 0.88,
        "f1": 0.85,
        "training_time": 120.5,
        "model_size_mb": 50.2
    }
    await metrics_tracker.track_training_metrics(metrics)
    
    # Obtener resumen
    summary = await metrics_tracker.get_metrics_summary()
    
    # Verificar resumen
    assert isinstance(summary, dict)
    assert "training" in summary
    assert "model" in summary
    assert "performance" in summary
    assert all(isinstance(v, dict) for v in summary.values())

async def test_track_performance_metrics(metrics_tracker: MetricsTracker, sample_data):
    """Test para registrar métricas de rendimiento."""
    users, products, interactions = sample_data
    
    # Métricas de rendimiento
    metrics = {
        "cpu_usage": 75.5,
        "memory_usage_mb": 1024.0,
        "gpu_usage": 90.0,
        "batch_size": 32,
        "throughput": 100.0
    }
    
    # Registrar métricas
    await metrics_tracker.track_performance_metrics(metrics)
    
    # Verificar que se guardaron las métricas
    saved_metrics = await metrics_tracker.get_latest_performance_metrics()
    assert isinstance(saved_metrics, dict)
    assert saved_metrics["cpu_usage"] == 75.5
    assert saved_metrics["memory_usage_mb"] == 1024.0
    assert saved_metrics["gpu_usage"] == 90.0
    assert saved_metrics["batch_size"] == 32
    assert saved_metrics["throughput"] == 100.0

async def test_cleanup_old_metrics(metrics_tracker: MetricsTracker, sample_data):
    """Test para limpiar métricas antiguas."""
    users, products, interactions = sample_data
    
    # Registrar métricas antiguas
    old_date = datetime.utcnow() - timedelta(days=60)
    metrics = {
        "accuracy": 0.85,
        "precision": 0.82,
        "recall": 0.88,
        "f1": 0.85,
        "training_time": 120.5,
        "model_size_mb": 50.2,
        "created_at": old_date
    }
    await metrics_tracker.track_training_metrics(metrics)
    
    # Registrar métricas recientes
    recent_metrics = {
        "accuracy": 0.86,
        "precision": 0.83,
        "recall": 0.89,
        "f1": 0.86,
        "training_time": 120.0,
        "model_size_mb": 50.0
    }
    await metrics_tracker.track_training_metrics(recent_metrics)
    
    # Limpiar métricas antiguas
    await metrics_tracker.cleanup_old_metrics(days=30)
    
    # Verificar que solo quedan las métricas recientes
    history = await metrics_tracker.get_metrics_history(days=30)
    assert len(history) == 1
    assert abs(history[0].accuracy - 0.86) < 0.001

async def test_get_metrics_trends(metrics_tracker: MetricsTracker, sample_data):
    """Test para obtener tendencias de métricas."""
    users, products, interactions = sample_data
    
    # Registrar métricas con tendencia
    for i in range(5):
        metrics = {
            "accuracy": 0.8 + i * 0.01,
            "precision": 0.8 + i * 0.01,
            "recall": 0.8 + i * 0.01,
            "f1": 0.8 + i * 0.01,
            "training_time": 120.0,
            "model_size_mb": 50.0
        }
        await metrics_tracker.track_training_metrics(metrics)
    
    # Obtener tendencias
    trends = await metrics_tracker.get_metrics_trends()
    
    # Verificar tendencias
    assert isinstance(trends, dict)
    assert "accuracy" in trends
    assert "precision" in trends
    assert "recall" in trends
    assert "f1" in trends
    assert all(isinstance(v, list) for v in trends.values())
    assert all(len(v) == 5 for v in trends.values())
    assert all(all(isinstance(x, float) for x in v) for v in trends.values()) 