"""
Repository for API Key operations.
"""

from typing import List, Optional
from sqlalchemy import select, update, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError

from src.db import models
from src.db.repositories.base import BaseRepository
from src.utils.base_logger import log_error


class ApiKeyRepository(BaseRepository[models.ApiKey]):
    """Repository for API Key CRUD operations."""

    def __init__(self, db: AsyncSession):
        super().__init__(models.ApiKey, db)

    async def get_by_account_id(self, account_id: int, include_inactive: bool = False) -> List[models.ApiKey]:
        """
        Get all API keys for a specific account.
        
        Args:
            account_id: The account ID to get API keys for
            include_inactive: Whether to include inactive API keys
            
        Returns:
            List of API keys for the account
        """
        try:
            query = select(models.ApiKey).filter(
                models.ApiKey.account_id == account_id
            )
            
            if not include_inactive:
                query = query.filter(models.ApiKey.is_active == True)
                
            query = query.order_by(models.ApiKey.created_at.desc())
            
            result = await self.execute_with_partition(query)
            return result.scalars().all()
            
        except SQLAlchemyError as e:
            await self._handle_error("fetching API keys by account ID", e)

    async def get_by_api_key_hash(self, api_key_hash: str) -> Optional[models.ApiKey]:
        """
        Get an API key by its hash.
        
        Args:
            api_key_hash: The SHA-256 hash of the API key
            
        Returns:
            The API key if found and active, None otherwise
        """
        try:
            query = select(models.ApiKey).filter(
                and_(
                    models.ApiKey.api_key_hash == api_key_hash,
                    models.ApiKey.is_active == True
                )
            )
            
            result = await self.execute_with_partition(query)
            return result.scalars().first()
            
        except SQLAlchemyError as e:
            await self._handle_error("fetching API key by hash", e)

    async def create_api_key(
        self,
        account_id: int,
        name: Optional[str],
        api_key_hash: str,
        api_key_prefix: str,
        api_key_last_chars: str
    ) -> models.ApiKey:
        """
        Create a new API key.
        
        Args:
            account_id: The account ID this API key belongs to
            name: Optional descriptive name for the API key
            api_key_hash: SHA-256 hash of the API key
            api_key_prefix: Prefix of the API key for display
            api_key_last_chars: Last 6 characters for display
            
        Returns:
            The created API key
        """
        try:
            api_key = models.ApiKey(
                account_id=account_id,
                name=name,
                api_key_hash=api_key_hash,
                api_key_prefix=api_key_prefix,
                api_key_last_chars=api_key_last_chars,
                is_active=True
            )
            
            return await self.create(api_key)
            
        except SQLAlchemyError as e:
            await self._handle_error("creating API key", e)

    async def update_api_key(
        self,
        api_key_id: int,
        account_id: int,
        **update_data
    ) -> Optional[models.ApiKey]:
        """
        Update an API key.
        
        Args:
            api_key_id: The ID of the API key to update
            account_id: The account ID (for security)
            **update_data: Fields to update
            
        Returns:
            The updated API key if found, None otherwise
        """
        try:
            # First verify the API key belongs to the account
            api_key = await self.get_by_id(api_key_id)
            if not api_key or api_key.account_id != account_id:
                return None
                
            # Update the API key
            query = update(models.ApiKey).where(
                and_(
                    models.ApiKey.id == api_key_id,
                    models.ApiKey.account_id == account_id
                )
            ).values(**update_data)
            
            await self.execute_with_partition(query)
            await self.db.commit()
            
            # Return the updated API key
            return await self.get_by_id(api_key_id)
            
        except SQLAlchemyError as e:
            await self._handle_error("updating API key", e)

    async def revoke_api_key(self, api_key_id: int, account_id: int) -> bool:
        """
        Revoke (deactivate) an API key.
        
        Args:
            api_key_id: The ID of the API key to revoke
            account_id: The account ID (for security)
            
        Returns:
            True if the API key was revoked, False if not found
        """
        try:
            query = update(models.ApiKey).where(
                and_(
                    models.ApiKey.id == api_key_id,
                    models.ApiKey.account_id == account_id,
                    models.ApiKey.is_active == True
                )
            ).values(is_active=False)
            
            result = await self.execute_with_partition(query)
            await self.db.commit()
            
            return result.rowcount > 0
            
        except SQLAlchemyError as e:
            await self._handle_error("revoking API key", e)
            return False

    async def update_last_used(self, api_key_id: int) -> None:
        """
        Update the last_used timestamp for an API key.
        
        Args:
            api_key_id: The ID of the API key to update
        """
        try:
            from sqlalchemy.sql import func
            
            query = update(models.ApiKey).where(
                models.ApiKey.id == api_key_id
            ).values(last_used=func.now())
            
            await self.execute_with_partition(query)
            await self.db.commit()
            
        except SQLAlchemyError as e:
            # Don't raise error for last_used updates to avoid breaking API calls
            log_error(f"Error updating API key last_used: {str(e)}")

    async def count_active_keys_for_account(self, account_id: int) -> int:
        """
        Count the number of active API keys for an account.
        
        Args:
            account_id: The account ID
            
        Returns:
            Number of active API keys
        """
        try:
            from sqlalchemy import func
            
            query = select(func.count(models.ApiKey.id)).filter(
                and_(
                    models.ApiKey.account_id == account_id,
                    models.ApiKey.is_active == True
                )
            )
            
            result = await self.execute_with_partition(query)
            return result.scalar() or 0
            
        except SQLAlchemyError as e:
            await self._handle_error("counting active API keys", e)
            return 0
