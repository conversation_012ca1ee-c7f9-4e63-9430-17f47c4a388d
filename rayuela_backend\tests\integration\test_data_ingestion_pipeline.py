"""Integration tests for the data ingestion pipeline."""

import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi import Fast<PERSON><PERSON>
from httpx import AsyncClient
from httpx import AsyncClient
import json
from datetime import datetime, timedelta

from src.main import app
from src.api.v1.endpoints.data_ingestion import (
    batch_data_ingestion,
    get_batch_job_status,
)
from src.db.models import BatchI<PERSON><PERSON><PERSON><PERSON>, Account, SystemUser
from src.db.enums import BatchIngestionJobStatus
from src.core.deps import get_db, get_current_account, get_limit_service
from src.services import LimitService, DataIngestionService

from tests.integration.conftest import mock_celery_tasks, execute_mock_tasks
from tests.conftest import (
    db_session,
    override_get_db,
    test_account,
    test_admin_user,
    test_products,
    test_end_users,
    test_batch_job,
)


@pytest.fixture
def client(override_get_db):
    """Create a test client with overridden dependencies."""
    app.dependency_overrides[get_db] = override_get_db
    with Async<PERSON>lient(app=app, base_url="http://test") as client:
        return client
    app.dependency_overrides = {}


@pytest_asyncio.fixture(scope="function")
async def async_client(override_get_db, test_account, test_admin_user):
    """Create an async test client with overridden dependencies."""

    # Override dependencies
    async def override_get_current_account():
        return {"account_id": test_account.id, "name": test_account.name}

    async def override_get_limit_service(db=None):
        if db is None:
            db = override_get_db()
        return LimitService(db=db, account_id=test_account.id)

    app.dependency_overrides[get_db] = override_get_db
    app.dependency_overrides[get_current_account] = override_get_current_account
    app.dependency_overrides[get_limit_service] = override_get_limit_service

    async with AsyncClient(app=app, base_url="http://test") as client:
        return client

    app.dependency_overrides = {}


@pytest.mark.asyncio
async def test_batch_data_ingestion_endpoint(
    async_client, db_session, test_account, mock_celery_tasks
):
    """Test the batch_data_ingestion endpoint."""
    # Prepare batch data
    batch_data = {
        "users": [
            {"external_id": "user_1", "metadata": {"age": 25, "gender": "male"}},
            {"external_id": "user_2", "metadata": {"age": 30, "gender": "female"}},
        ],
        "products": [
            {
                "external_id": "product_1",
                "name": "Test Product 1",
                "description": "Description for Test Product 1",
                "category": "Category 1",
                "price": 10.0,
                "metadata": {"color": "red", "size": "small"},
            },
            {
                "external_id": "product_2",
                "name": "Test Product 2",
                "description": "Description for Test Product 2",
                "category": "Category 2",
                "price": 20.0,
                "metadata": {"color": "blue", "size": "medium"},
            },
        ],
        "interactions": [
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_1",
                "interaction_type": "view",
                "rating": 4.5,
                "metadata": {"timestamp": "2023-01-01T12:00:00Z", "device": "mobile"},
            },
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_2",
                "interaction_type": "purchase",
                "rating": 5.0,
                "metadata": {"timestamp": "2023-01-02T12:00:00Z", "device": "desktop"},
            },
            {
                "end_user_external_id": "user_2",
                "product_external_id": "product_1",
                "interaction_type": "like",
                "rating": 3.5,
                "metadata": {"timestamp": "2023-01-03T12:00:00Z", "device": "tablet"},
            },
        ],
    }

    # Call the endpoint
    response = await async_client.post("/api/v1/data/batch", json=batch_data)

    # Check response
    assert response.status_code == 202
    data = response.json()
    assert data["status"] == "processing"
    assert "job_id" in data
    assert "task_id" in data
    assert data["total_users"] == 2
    assert data["total_products"] == 2
    assert data["total_interactions"] == 3

    # Execute the mock Celery task
    job_id = data["job_id"]
    await execute_mock_tasks(mock_celery_tasks, db_session)

    # Check the batch job status
    response = await async_client.get(f"/api/v1/data/batch/{job_id}")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"

    # Verify that the data was ingested
    # Check users
    response = await async_client.get("/api/v1/users")
    assert response.status_code == 200
    users_data = response.json()
    assert len(users_data) >= 2

    # Check products
    response = await async_client.get("/api/v1/products")
    assert response.status_code == 200
    products_data = response.json()
    assert len(products_data) >= 2

    # Check interactions
    response = await async_client.get("/api/v1/interactions")
    assert response.status_code == 200
    interactions_data = response.json()
    assert len(interactions_data) >= 3


@pytest.mark.asyncio
async def test_batch_data_ingestion_with_invalid_data(
    async_client, db_session, test_account
):
    """Test the batch_data_ingestion endpoint with invalid data."""
    # Prepare invalid batch data (missing required fields)
    invalid_batch_data = {
        "users": [{"metadata": {"age": 25, "gender": "male"}}],  # Missing external_id
        "products": [
            {
                "external_id": "product_1",
                "description": "Description for Test Product 1",
                "category": "Category 1",
                "price": 10.0,
                "metadata": {"color": "red", "size": "small"},
            }  # Missing name
        ],
        "interactions": [
            {
                "end_user_external_id": "user_1",
                "product_external_id": "product_1",
                "interaction_type": "view",
                "metadata": {"timestamp": "2023-01-01T12:00:00Z", "device": "mobile"},
            }  # Missing rating
        ],
    }

    # Call the endpoint
    response = await async_client.post("/api/v1/data/batch", json=invalid_batch_data)

    # Check response (should be a validation error)
    assert response.status_code == 422
    data = response.json()
    assert "detail" in data


@pytest.mark.asyncio
async def test_get_batch_job_status_endpoint(
    async_client, db_session, test_account, test_batch_job
):
    """Test the get_batch_job_status endpoint."""
    # Update the batch job status
    test_batch_job.status = BatchIngestionJobStatus.PROCESSING
    test_batch_job.started_at = datetime.now()
    test_batch_job.processed_count = {
        "users": 5,
        "products": 10,
        "interactions": 20,
        "total": 35,
        "errors": 2,
    }
    await db_session.commit()

    # Call the endpoint
    response = await async_client.get(f"/api/v1/data/batch/{test_batch_job.id}")

    # Check response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "processing"
    assert data["job_id"] == test_batch_job.id
    assert data["processed_count"]["users"] == 5
    assert data["processed_count"]["products"] == 10
    assert data["processed_count"]["interactions"] == 20
    assert data["processed_count"]["errors"] == 2

    # Update the batch job status to completed
    test_batch_job.status = BatchIngestionJobStatus.COMPLETED
    test_batch_job.completed_at = datetime.now()
    test_batch_job.processed_count = {
        "users": 10,
        "products": 20,
        "interactions": 50,
        "total": 80,
        "errors": 3,
        "error_details": {
            "users": ["Invalid user data"],
            "products": ["Invalid product data"],
            "interactions": ["Invalid interaction data"],
        },
    }
    await db_session.commit()

    # Call the endpoint again
    response = await async_client.get(f"/api/v1/data/batch/{test_batch_job.id}")

    # Check response
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "completed"
    assert data["processed_count"]["users"] == 10
    assert data["processed_count"]["products"] == 20
    assert data["processed_count"]["interactions"] == 50
    assert data["processed_count"]["errors"] == 3
    assert "error_details" in data["processed_count"]
    assert "success_rate" in data
    assert data["success_rate"] > 0


@pytest.mark.asyncio
async def test_get_batch_job_status_not_found(async_client, db_session, test_account):
    """Test the get_batch_job_status endpoint with a non-existent job ID."""
    # Call the endpoint with a non-existent job ID
    response = await async_client.get("/api/v1/data/batch/999")

    # Check response
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "not found" in data["detail"].lower()


@pytest.mark.asyncio
async def test_batch_data_ingestion_with_limit_exceeded(
    async_client, db_session, test_account
):
    """Test the batch_data_ingestion endpoint with limits exceeded."""
    # Mock the limit service to simulate a limit exceeded
    original_validate_user_limit = LimitService.validate_user_limit

    async def mock_validate_user_limit(self):
        from src.core.exceptions import LimitExceededError

        raise LimitExceededError("Users limit exceeded for your subscription plan")

    # Apply the mock
    LimitService.validate_user_limit = mock_validate_user_limit

    try:
        # Prepare batch data
        batch_data = {
            "users": [
                {"external_id": "user_1", "metadata": {"age": 25, "gender": "male"}},
                {"external_id": "user_2", "metadata": {"age": 30, "gender": "female"}},
            ],
            "products": [],
            "interactions": [],
        }

        # Call the endpoint
        response = await async_client.post("/api/v1/data/batch", json=batch_data)

        # Check response
        assert response.status_code == 429
        data = response.json()
        assert "detail" in data
        assert "users limit exceeded" in data["detail"].lower()

    finally:
        # Restore the original method
        LimitService.validate_user_limit = original_validate_user_limit
