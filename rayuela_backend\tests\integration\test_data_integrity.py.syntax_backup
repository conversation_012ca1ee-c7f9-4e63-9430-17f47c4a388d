import pytest
from datetime import datetime
from src.db.repositories.base import BaseRepository
from src.db.models import Product, Account, SystemUser
from src.core.exceptions import ValidationError, ConflictError, NotFoundError

class TestDataIntegrity:
    """Tests de integración para la integridad de datos."""
    
    @pytest.fixture
    def product_repository(self, db_session):
        """Crear instancia del repositorio de productos."""
        return BaseRepository(db_session, Product)
    
@pytest.mark.asyncio
    async def test_robust_upsert(
        self,
        product_repository,
        test_accounts
    ):
        """Test para el método robust_upsert."""
        account = test_accounts[0]
        
        # Datos iniciales
        data = {
            "name": "Test Product",
            "price": 100.0,
            "account_id": account.id
        }
        
        # Crear producto
        product = await product_repository.robust_upsert(
            account_id=account.id,
            data=data,
            unique_fields=["name"]
        )
        assert product.name == data["name"]
        assert product.price == data["price"]
        
        # Actualizar producto existente
        updated_data = {
            "name": "Test Product",  # Mismo nombre
            "price": 200.0,  # Precio actualizado
            "account_id": account.id
        }
        updated_product = await product_repository.robust_upsert(
            account_id=account.id,
            data=updated_data,
            unique_fields=["name"]
        )
        assert updated_product.id == product.id
        assert updated_product.price == 200.0
        
        # Intentar crear producto con nombre duplicado en otro tenant
        other_account = test_accounts[1]
        with pytest.raises(ConflictError):
            await product_repository.robust_upsert(
                account_id=other_account.id,
                data=data,
                unique_fields=["name"]
            )
    
    async def test_soft_delete(
        self,
        product_repository,
        test_accounts
    ):
        """Test para soft delete."""
        account = test_accounts[0]
        
        # Crear producto
        product = await product_repository.create(
            account_id=account.id,
            data={
                "name": "Test Product",
                "price": 100.0
            }
        )
        
        # Soft delete
        await product_repository.soft_delete(product.id)
        
        # Verificar que el producto está marcado como eliminado
        deleted_product = await product_repository.get(product.id)
        assert deleted_product.is_deleted
        assert deleted_product.deleted_at is not None
        
        # Verificar que no aparece en listados normales
        products = await product_repository.list(account_id=account.id)
        assert product.id not in [p.id for p in products]
        
        # Verificar que aparece en listados incluyendo eliminados
        all_products = await product_repository.list(
            account_id=account.id,
            include_deleted=True
        )
        assert product.id in [p.id for p in all_products]
    
    async def test_partitioning(
        self,
        product_repository,
        test_accounts
    ):
        """Test para particionamiento de datos."""
        account_a = test_accounts[0]
        account_b = test_accounts[1]
        
        # Crear productos para cada tenant
        product_a = await product_repository.create(
            account_id=account_a.id,
            data={
                "name": "Product A",
                "price": 100.0
            }
        )
        
        product_b = await product_repository.create(
            account_id=account_b.id,
            data={
                "name": "Product B",
                "price": 200.0
            }
        )
        
        # Verificar que cada tenant solo ve sus productos
        products_a = await product_repository.list(account_id=account_a.id)
        assert len(products_a) == 1
        assert products_a[0].id == product_a.id
        
        products_b = await product_repository.list(account_id=account_b.id)
        assert len(products_b) == 1
        assert products_b[0].id == product_b.id
        
        # Verificar que no se puede acceder a productos de otro tenant
        with pytest.raises(NotFoundError):
            await product_repository.get(
                product_b.id,
                account_id=account_a.id
            )
    
    async def test_data_validation(
        self,
        product_repository,
        test_accounts
    ):
        """Test para validación de datos."""
        account = test_accounts[0]
        
        # Test validación de campos requeridos
        with pytest.raises(ValidationError):
            await product_repository.create(
                account_id=account.id,
                data={"name": ""}  # Falta precio
            )
        
        # Test validación de tipos de datos
        with pytest.raises(ValidationError):
            await product_repository.create(
                account_id=account.id,
                data={
                    "name": "Test",
                    "price": "invalid"  # Precio debe ser número
                }
            )
        
        # Test validación de valores
        with pytest.raises(ValidationError):
            await product_repository.create(
                account_id=account.id,
                data={
                    "name": "Test",
                    "price": -10.0  # Precio no puede ser negativo
                }
            )
    
    async def test_cascade_delete(
        self,
        db_session,
        test_accounts
    ):
        """Test para eliminación en cascada."""
        account = test_accounts[0]
        
        # Crear usuario del sistema
        system_user = SystemUser(
            email="<EMAIL>",
            hashed_password="password_hash",
            account_id=account.id
        )
        db_session.add(system_user)
        await db_session.flush()
        
        # Crear productos asociados al usuario
        product1 = Product(
            name="Product 1",
            price=100.0,
            account_id=account.id,
            created_by=system_user.id
        )
        product2 = Product(
            name="Product 2",
            price=200.0,
            account_id=account.id,
            created_by=system_user.id
        )
        db_session.add_all([product1, product2])
        await db_session.commit()
        
        # Verificar la relación created_products
        await db_session.refresh(system_user)
        assert len(system_user.created_products) == 2
        assert any(p.id == product1.id for p in system_user.created_products)
        assert any(p.id == product2.id for p in system_user.created_products)
        
        # Eliminar usuario
        await db_session.delete(system_user)
        await db_session.commit()
        
        # Verificar que los productos se eliminaron en cascada
        deleted_product1 = await db_session.get(Product, (account.id, product1.id))
        assert deleted_product1 is None
        
        deleted_product2 = await db_session.get(Product, (account.id, product2.id))
        assert deleted_product2 is None
    
    async def test_data_consistency(
        self,
        product_repository,
        test_accounts
    ):
        """Test para consistencia de datos."""
        account = test_accounts[0]
        
        # Crear producto
        product = await product_repository.create(
            account_id=account.id,
            data={
                "name": "Test Product",
                "price": 100.0
            }
        )
        
        # Actualizar producto en múltiples operaciones
        await product_repository.update(
            product.id,
            {"price": 200.0}
        )
        await product_repository.update(
            product.id,
            {"name": "Updated Product"}
        )
        
        # Verificar que los cambios se aplicaron correctamente
        updated_product = await product_repository.get(product.id)
        assert updated_product.price == 200.0
        assert updated_product.name == "Updated Product"
        
        # Verificar que los timestamps se actualizaron
        assert updated_product.updated_at > updated_product.created_at 