#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para configurar todos los secretos en Google Cloud Secret Manager.

Este script ayuda a migrar todas las variables sensibles desde variables de entorno
a Google Cloud Secret Manager para mejorar la seguridad.

Uso:
    python -m scripts.setup_secrets --project-id your-project-id [--env production]
"""

import argparse
import os
import sys
import subprocess
import json
from typing import Dict, List, Optional
from pathlib import Path


class SecretManagerSetup:
    """Clase para configurar secretos en Google Cloud Secret Manager."""
    
    def __init__(self, project_id: str, env: str = "production"):
        self.project_id = project_id
        self.env = env
        
        # Definir todos los secretos que deben estar en Secret Manager
        self.secrets_config = {
            # Credenciales de base de datos
            "DB_PASSWORD": {
                "description": "PostgreSQL database password",
                "required": True,
                "env_var": "POSTGRES_PASSWORD"
            },
            "POSTGRES_SERVER": {
                "description": "PostgreSQL server host/IP",
                "required": True,
                "env_var": "POSTGRES_SERVER"
            },
            "POSTGRES_USER": {
                "description": "PostgreSQL username",
                "required": True,
                "env_var": "POSTGRES_USER"
            },
            "POSTGRES_DB": {
                "description": "PostgreSQL database name",
                "required": True,
                "env_var": "POSTGRES_DB"
            },
            "POSTGRES_PORT": {
                "description": "PostgreSQL port",
                "required": True,
                "env_var": "POSTGRES_PORT"
            },
            "APP_DB_PASSWORD": {
                "description": "Application database role password",
                "required": False,
                "env_var": "APP_DB_PASSWORD"
            },
            "MAINTENANCE_DB_PASSWORD": {
                "description": "Maintenance database role password",
                "required": False,
                "env_var": "MAINTENANCE_DB_PASSWORD"
            },
            
            # Credenciales de Redis
            "REDIS_PASSWORD": {
                "description": "Redis password",
                "required": True,
                "env_var": "REDIS_PASSWORD"
            },
            "REDIS_HOST": {
                "description": "Redis host/IP",
                "required": True,
                "env_var": "REDIS_HOST"
            },
            "REDIS_PORT": {
                "description": "Redis port",
                "required": True,
                "env_var": "REDIS_PORT"
            },
            "REDIS_URL": {
                "description": "Complete Redis connection URL",
                "required": True,
                "env_var": "REDIS_URL"
            },
            
            # Configuración de seguridad
            "SECRET_KEY": {
                "description": "JWT secret key for token signing",
                "required": True,
                "env_var": "SECRET_KEY"
            },
            
            # Configuración de Google Cloud
            "GCS_BUCKET_NAME": {
                "description": "Google Cloud Storage bucket name",
                "required": True,
                "env_var": "GCS_BUCKET_NAME"
            },
            
            # Credenciales de MercadoPago
            "MERCADOPAGO_ACCESS_TOKEN": {
                "description": "MercadoPago access token",
                "required": False,
                "env_var": "MERCADOPAGO_ACCESS_TOKEN"
            },
            "MERCADOPAGO_PUBLIC_KEY": {
                "description": "MercadoPago public key",
                "required": False,
                "env_var": "MERCADOPAGO_PUBLIC_KEY"
            },
            "MERCADOPAGO_WEBHOOK_SECRET": {
                "description": "MercadoPago webhook secret",
                "required": False,
                "env_var": "MERCADOPAGO_WEBHOOK_SECRET"
            },
            
            # Credenciales de Stripe (legacy)
            "STRIPE_API_KEY": {
                "description": "Stripe API key (legacy)",
                "required": False,
                "env_var": "STRIPE_API_KEY"
            },
            "STRIPE_WEBHOOK_SECRET": {
                "description": "Stripe webhook secret (legacy)",
                "required": False,
                "env_var": "STRIPE_WEBHOOK_SECRET"
            },
        }

    def check_gcloud_auth(self) -> bool:
        """Verificar que gcloud esté autenticado."""
        try:
            result = subprocess.run(
                ["gcloud", "auth", "list", "--filter=status:ACTIVE", "--format=value(account)"],
                capture_output=True,
                text=True,
                check=True
            )
            if result.stdout.strip():
                print(f"✅ Autenticado como: {result.stdout.strip()}")
                return True
            else:
                print("❌ No hay cuentas autenticadas en gcloud")
                return False
        except subprocess.CalledProcessError:
            print("❌ Error al verificar autenticación de gcloud")
            return False

    def check_secret_exists(self, secret_name: str) -> bool:
        """Verificar si un secreto ya existe en Secret Manager."""
        try:
            subprocess.run(
                ["gcloud", "secrets", "describe", secret_name, "--project", self.project_id],
                capture_output=True,
                check=True
            )
            return True
        except subprocess.CalledProcessError:
            return False

    def create_secret(self, secret_name: str, description: str) -> bool:
        """Crear un secreto en Secret Manager."""
        try:
            subprocess.run([
                "gcloud", "secrets", "create", secret_name,
                "--replication-policy=automatic",
                "--project", self.project_id,
                f"--labels=env={self.env},component=rayuela-backend"
            ], check=True)
            print(f"✅ Secreto creado: {secret_name}")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Error creando secreto {secret_name}: {e}")
            return False

    def set_secret_value(self, secret_name: str, value: str) -> bool:
        """Establecer el valor de un secreto."""
        try:
            process = subprocess.Popen([
                "gcloud", "secrets", "versions", "add", secret_name,
                "--data-file=-",
                "--project", self.project_id
            ], stdin=subprocess.PIPE, text=True)
            process.communicate(input=value)
            
            if process.returncode == 0:
                print(f"✅ Valor establecido para: {secret_name}")
                return True
            else:
                print(f"❌ Error estableciendo valor para: {secret_name}")
                return False
        except Exception as e:
            print(f"❌ Error estableciendo valor para {secret_name}: {e}")
            return False

    def load_env_file(self) -> Dict[str, str]:
        """Cargar variables de entorno desde archivo .env."""
        env_file = Path(f".env.{self.env}")
        env_vars = {}
        
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        env_vars[key] = value
            print(f"✅ Cargadas {len(env_vars)} variables desde {env_file}")
        else:
            print(f"⚠️ Archivo {env_file} no encontrado")
        
        return env_vars

    def setup_all_secrets(self, interactive: bool = True) -> bool:
        """Configurar todos los secretos en Secret Manager."""
        print(f"🔐 Configurando secretos para proyecto: {self.project_id}")
        print(f"📁 Entorno: {self.env}")
        print()
        
        # Verificar autenticación
        if not self.check_gcloud_auth():
            print("Por favor, ejecuta: gcloud auth login")
            return False
        
        # Cargar variables de entorno existentes
        env_vars = self.load_env_file()
        
        success_count = 0
        total_count = len(self.secrets_config)
        
        for secret_name, config in self.secrets_config.items():
            print(f"\n📝 Procesando: {secret_name}")
            print(f"   Descripción: {config['description']}")
            
            # Verificar si el secreto ya existe
            if self.check_secret_exists(secret_name):
                print(f"   ℹ️ El secreto ya existe")
                if interactive:
                    response = input(f"   ¿Actualizar valor? (y/N): ").lower()
                    if response != 'y':
                        print(f"   ⏭️ Saltando {secret_name}")
                        continue
            else:
                # Crear el secreto
                if not self.create_secret(secret_name, config['description']):
                    continue
            
            # Obtener el valor
            env_var = config['env_var']
            value = env_vars.get(env_var) or os.getenv(env_var)
            
            if not value:
                if config['required']:
                    if interactive:
                        value = input(f"   🔑 Ingresa valor para {secret_name}: ").strip()
                    if not value:
                        print(f"   ❌ Valor requerido para {secret_name}")
                        continue
                else:
                    print(f"   ⏭️ Valor opcional no proporcionado para {secret_name}")
                    continue
            
            # Establecer el valor
            if self.set_secret_value(secret_name, value):
                success_count += 1
        
        print(f"\n🎉 Configuración completada: {success_count}/{total_count} secretos")
        
        if success_count == total_count:
            print("✅ Todos los secretos configurados exitosamente")
            return True
        else:
            print("⚠️ Algunos secretos no se pudieron configurar")
            return False


def main():
    parser = argparse.ArgumentParser(description="Configurar secretos en Google Cloud Secret Manager")
    parser.add_argument("--project-id", required=True, help="ID del proyecto de Google Cloud")
    parser.add_argument("--env", default="production", help="Entorno (default: production)")
    parser.add_argument("--non-interactive", action="store_true", help="Modo no interactivo")
    
    args = parser.parse_args()
    
    setup = SecretManagerSetup(args.project_id, args.env)
    success = setup.setup_all_secrets(interactive=not args.non_interactive)
    
    if success:
        print("\n🔗 Próximos pasos:")
        print("1. Verificar que todos los secretos estén configurados:")
        print(f"   gcloud secrets list --project {args.project_id}")
        print("2. Desplegar la aplicación para que use los nuevos secretos")
        print("3. Verificar que la aplicación funcione correctamente")
        sys.exit(0)
    else:
        print("\n❌ Configuración incompleta. Revisa los errores anteriores.")
        sys.exit(1)


if __name__ == "__main__":
    main()
