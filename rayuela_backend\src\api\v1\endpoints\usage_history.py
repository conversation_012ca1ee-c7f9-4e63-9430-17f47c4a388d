"""
Endpoints for historical usage data.
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, List, Optional
from datetime import datetime

from src.core.deps import (
    get_current_active_user,
    get_db,
)
from src.db.models import SystemUser
from src.services.usage_service import UsageService
from src.utils.base_logger import log_error

router = APIRouter()

@router.get("/history", response_model=List[Dict[str, Any]])
async def get_usage_history(
    start_date: Optional[datetime] = Query(None, description="Start date for historical data"),
    end_date: Optional[datetime] = Query(None, description="End date for historical data"),
    current_user: SystemUser = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db),
):
    """
    Get historical usage data for the current account.
    
    Args:
        start_date: Optional start date for filtering data
        end_date: Optional end date for filtering data
        
    Returns:
        List of daily usage data points
    """
    try:
        usage_service = UsageService(db)
        return await usage_service.get_usage_history(
            account_id=current_user.account_id,
            start_date=start_date,
            end_date=end_date
        )
    
    except Exception as e:
        log_error(f"Error retrieving usage history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving usage history: {str(e)}",
        )
