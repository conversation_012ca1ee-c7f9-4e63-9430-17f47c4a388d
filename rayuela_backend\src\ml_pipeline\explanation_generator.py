from typing import Dict, Any, List, Union, Optional, Set
from src.db import schemas
from src.utils.base_logger import log_info, log_warning


class ExplanationGenerator:
    """
    Clase responsable de generar explicaciones para las recomendaciones.
    """
    
    def generate_explanation(
        self,
        item_id: int,
        user_id: int,
        recommendation_details: Dict[str, Any],
        model_artifacts: Optional[Dict[str, Any]] = None,
        explanation_level: schemas.ExplanationLevel = schemas.ExplanationLevel.SIMPLE,
        user_history: Optional[Dict[int, float]] = None,
    ) -> Union[str, schemas.DetailedExplanation]:
        """
        Genera una explicación para una recomendación basada en su tipo.
        
        Args:
            item_id: ID del ítem recomendado
            user_id: ID del usuario
            recommendation_details: Detalles de la recomendación
            model_artifacts: Artefactos del modelo utilizado
            explanation_level: Nivel de detalle de la explicación
            user_history: Historial de interacciones del usuario
            
        Returns:
            Explicación como string o como objeto DetailedExplanation
        """
        model_type = recommendation_details.get("model_type", "hybrid")
        
        # Determinar el tipo de explicación a generar
        if model_type == "collaborative":
            return self.generate_collaborative_explanation(
                item_id=item_id,
                score=recommendation_details.get("score", 0.0),
                user_id=user_id,
                model_type=recommendation_details.get("collaborative_model", "als"),
                explanation_level=explanation_level,
                user_history=user_history,
                collab_artifacts=model_artifacts.get("collaborative") if model_artifacts else None
            )
        elif model_type == "content":
            return self.generate_content_explanation(
                item_id=item_id,
                score=recommendation_details.get("score", 0.0),
                user_id=user_id,
                is_semantic_model=recommendation_details.get("is_semantic_model", False),
                category_weights=recommendation_details.get("category_weights"),
                content_artifacts=model_artifacts.get("content") if model_artifacts else None,
                explanation_level=explanation_level,
                user_history=user_history
            )
        elif model_type == "hybrid":
            # Para explicaciones híbridas, necesitamos explicaciones de ambos modelos
            collab_explanation = self.generate_collaborative_explanation(
                item_id=item_id,
                score=recommendation_details.get("collab_score", 0.0),
                user_id=user_id,
                model_type=recommendation_details.get("collaborative_model", "als"),
                explanation_level=explanation_level,
                user_history=user_history,
                collab_artifacts=model_artifacts.get("collaborative") if model_artifacts else None
            )
            
            content_explanation = self.generate_content_explanation(
                item_id=item_id,
                score=recommendation_details.get("content_score", 0.0),
                user_id=user_id,
                is_semantic_model=recommendation_details.get("is_semantic_model", False),
                category_weights=recommendation_details.get("category_weights"),
                content_artifacts=model_artifacts.get("content") if model_artifacts else None,
                explanation_level=explanation_level,
                user_history=user_history
            )
            
            return self.generate_hybrid_explanation(
                item_id=item_id,
                collab_score=recommendation_details.get("collab_score", 0.0),
                content_score=recommendation_details.get("content_score", 0.0),
                collab_weight=recommendation_details.get("collab_weight", 0.5),
                content_weight=recommendation_details.get("content_weight", 0.5),
                collab_explanation=collab_explanation,
                content_explanation=content_explanation,
                explanation_level=explanation_level
            )
        elif model_type == "fallback":
            return self.generate_fallback_explanation(
                item_id=item_id,
                reason=recommendation_details.get("fallback_reason", "popular"),
                explanation_level=explanation_level
            )
        else:
            # Explicación genérica para tipos desconocidos
            log_warning(f"Tipo de modelo desconocido para explicación: {model_type}")
            return "Recomendado basado en tus preferencias" if explanation_level == schemas.ExplanationLevel.SIMPLE else schemas.DetailedExplanation(
                item_id=item_id,
                user_id=user_id,
                primary_reason="Recomendado basado en tus preferencias",
                score=recommendation_details.get("score", 0.0),
                model_type="unknown",
                factors=[]
            )
    
    def generate_collaborative_explanation(
        self,
        item_id: int,
        score: float,
        user_id: int,
        model_type: str = "als",
        explanation_level: schemas.ExplanationLevel = schemas.ExplanationLevel.SIMPLE,
        user_history: Optional[Dict[int, float]] = None,
        collab_artifacts: Optional[Dict[str, Any]] = None
    ) -> Union[str, schemas.DetailedExplanation]:
        """
        Genera una explicación para una recomendación basada en filtrado colaborativo.
        
        Args:
            item_id: ID del ítem recomendado
            score: Puntuación de la recomendación
            user_id: ID del usuario
            model_type: Tipo de modelo colaborativo
            explanation_level: Nivel de detalle de la explicación
            user_history: Historial de interacciones del usuario
            collab_artifacts: Artefactos del modelo colaborativo
            
        Returns:
            Explicación como string o como objeto DetailedExplanation
        """
        # Explicación simple (string)
        if explanation_level == schemas.ExplanationLevel.SIMPLE:
            if model_type == "als":
                return "Recomendado porque usuarios similares a ti disfrutaron este producto"
            elif model_type == "svd":
                return "Producto popular entre usuarios con gustos similares a los tuyos"
            elif model_type == "knn":
                return "Usuarios que compraron productos similares a los tuyos también compraron este"
            else:
                return "Recomendado basado en preferencias de usuarios similares"
        
        # Explicación detallada
        primary_reason = "Recomendado basado en las preferencias de usuarios similares"
        secondary_reason = None
        factors = []
        confidence = min(1.0, max(0.1, score / 5.0))  # Normalizar score a un valor entre 0.1 y 1.0
        
        # Si tenemos artefactos del modelo y el historial del usuario, podemos generar una explicación más detallada
        if collab_artifacts and user_history:
            model = collab_artifacts.get("model")
            similar_users = collab_artifacts.get("similar_users", {}).get(str(user_id), [])
            
            # Factores basados en el historial del usuario
            if model and len(user_history) > 0:
                # Buscar ítems similares en el historial del usuario
                item_similarities = {}
                if hasattr(model, "item_factors") and item_id in model.item_factors:
                    target_item_factors = model.item_factors[item_id]
                    
                    # Calcular similitud con otros ítems que el usuario ha interactuado
                    for hist_item_id, rating in user_history.items():
                        if hist_item_id in model.item_factors:
                            import numpy as np
                            from sklearn.metrics.pairwise import cosine_similarity
                            
                            hist_item_factors = model.item_factors[hist_item_id]
                            sim = cosine_similarity(
                                np.array(target_item_factors).reshape(1, -1),
                                np.array(hist_item_factors).reshape(1, -1)
                            )[0][0]
                            
                            item_similarities[hist_item_id] = {
                                "similarity": float(sim),
                                "rating": float(rating)
                            }
                    
                    # Ordenar por similitud y añadir como factores
                    top_similar_items = sorted(
                        item_similarities.items(),
                        key=lambda x: x[1]["similarity"] * x[1]["rating"],
                        reverse=True
                    )[:3]  # Top 3
                    
                    for i, (sim_item_id, data) in enumerate(top_similar_items):
                        item_name = f"producto {sim_item_id}"
                        similarity_pct = int(data["similarity"] * 100)
                        
                        if similarity_pct > 70:
                            strength_text = "muy similar"
                        elif similarity_pct > 40:
                            strength_text = "similar"
                        else:
                            strength_text = "algo similar"
                            
                        factor = schemas.ExplanationFactor(
                            type="similar_item",
                            name=item_name,
                            value=str(sim_item_id),
                            strength=data["similarity"],
                            description=f"Este producto es {strength_text} a {item_name} que te gustó"
                        )
                        factors.append(factor)
            
            # Factores basados en usuarios similares
            if similar_users:
                similar_user_count = len(similar_users)
                if similar_user_count > 0:
                    factor = schemas.ExplanationFactor(
                        type="similar_users",
                        name="usuarios similares",
                        value=str(similar_user_count),
                        strength=min(1.0, similar_user_count / 10),  # Normalizar
                        description=f"{similar_user_count} usuarios con gustos similares disfrutaron este producto"
                    )
                    factors.append(factor)
                    secondary_reason = f"{similar_user_count} usuarios con gustos similares disfrutaron este producto"
                
        # Si no pudimos generar factores específicos, agregar un factor genérico
        if not factors:
            factor = schemas.ExplanationFactor(
                type="generic",
                name="usuarios similares",
                value="similitud",
                strength=confidence,
                description="Basado en patrones de preferencia de usuarios similares"
            )
            factors.append(factor)
        
        return schemas.DetailedExplanation(
            item_id=item_id,
            user_id=user_id,
            primary_reason=primary_reason,
            secondary_reason=secondary_reason,
            score=score,
            confidence=confidence,
            model_type="collaborative",
            model_subtype=model_type,
            factors=factors
        )
    
    def generate_content_explanation(
        self,
        item_id: int,
        score: float,
        user_id: int,
        is_semantic_model: bool = False,
        category_weights: Optional[Dict[str, float]] = None,
        content_artifacts: Optional[Dict[str, Any]] = None,
        explanation_level: schemas.ExplanationLevel = schemas.ExplanationLevel.SIMPLE,
        user_history: Optional[Dict[int, float]] = None
    ) -> Union[str, schemas.DetailedExplanation]:
        """
        Genera una explicación para una recomendación basada en contenido.
        
        Args:
            item_id: ID del ítem recomendado
            score: Puntuación de la recomendación
            user_id: ID del usuario
            is_semantic_model: Si es un modelo semántico o no
            category_weights: Pesos por categoría
            content_artifacts: Artefactos del modelo basado en contenido
            explanation_level: Nivel de detalle de la explicación
            user_history: Historial de interacciones del usuario
            
        Returns:
            Explicación como string o como objeto DetailedExplanation
        """
        # Explicación simple (string)
        if explanation_level == schemas.ExplanationLevel.SIMPLE:
            if is_semantic_model:
                return "Producto similar a otros que has comprado o visualizado"
            else:
                return "Basado en tus intereses y preferencias de categorías"
        
        # Explicación detallada
        primary_reason = "Recomendado basado en tus intereses y preferencias"
        secondary_reason = None
        factors = []
        confidence = min(1.0, max(0.1, score / 5.0))  # Normalizar score a un valor entre 0.1 y 1.0
        
        # Si tenemos artefactos del modelo y/o categorías, podemos generar una explicación más detallada
        if content_artifacts:
            model = content_artifacts.get("model")
            item_metadata = content_artifacts.get("item_metadata", {}).get(str(item_id), {})
            
            # Si tenemos metadatos del ítem, usarlos para la explicación
            if item_metadata:
                category = item_metadata.get("category", "")
                if category:
                    category_interest = 0.7  # Valor por defecto
                    
                    # Si tenemos pesos por categoría, usar el peso real
                    if category_weights and category in category_weights:
                        category_interest = category_weights[category]
                    
                    factor = schemas.ExplanationFactor(
                        type="category",
                        name="categoría",
                        value=category,
                        strength=category_interest,
                        description=f"Te interesan productos de la categoría '{category}'"
                    )
                    factors.append(factor)
                    secondary_reason = f"Te interesan productos de la categoría '{category}'"
                    
                # Añadir atributos relevantes
                attributes = item_metadata.get("attributes", {})
                for attr_name, attr_value in attributes.items():
                    if attr_name.startswith("attr_") and attr_value:
                        clean_name = attr_name.replace("attr_", "")
                        factor = schemas.ExplanationFactor(
                            type="attribute",
                            name=clean_name,
                            value=str(attr_value),
                            strength=0.6,  # Valor por defecto
                            description=f"Te interesan productos con {clean_name}: {attr_value}"
                        )
                        factors.append(factor)
        
            # Si tenemos modelo semántico y user_profile
            if model and hasattr(model, "user_profiles") and user_id in model.user_profiles:
                if is_semantic_model:
                    factor = schemas.ExplanationFactor(
                        type="semantic",
                        name="similitud semántica",
                        value="alta",
                        strength=confidence,
                        description="Este producto tiene características similares a otros que te han gustado"
                    )
                    factors.append(factor)
                
                # Si tenemos acceso al perfil de usuario y a los vectores de ítems
                if hasattr(model, "item_vectors") and item_id in model.item_vectors:
                    # Calcular qué dimensiones del perfil de usuario contribuyen más a la recomendación
                    user_profile = model.user_profiles[user_id]
                    item_vector = model.item_vectors[item_id]
                    
                    if len(user_profile) == len(item_vector):
                        # Calcular contribución por dimensión
                        contributions = []
                        for i, (u, v) in enumerate(zip(user_profile, item_vector)):
                            contributions.append((i, u * v))
                        
                        # Ordenar por contribución
                        top_dims = sorted(contributions, key=lambda x: x[1], reverse=True)[:2]  # Top 2
                        
                        for dim_idx, contrib in top_dims:
                            if contrib > 0:
                                factor = schemas.ExplanationFactor(
                                    type="dimension",
                                    name=f"característica {dim_idx+1}",
                                    value=str(dim_idx),
                                    strength=min(1.0, max(0.0, contrib)),
                                    description=f"Tu perfil coincide fuertemente en la característica {dim_idx+1}"
                                )
                                factors.append(factor)
        
        # Si no pudimos generar factores específicos, agregar un factor genérico
        if not factors:
            factor = schemas.ExplanationFactor(
                type="generic",
                name="intereses",
                value="perfil",
                strength=confidence,
                description="Basado en tu perfil de intereses"
            )
            factors.append(factor)
        
        return schemas.DetailedExplanation(
            item_id=item_id,
            user_id=user_id,
            primary_reason=primary_reason,
            secondary_reason=secondary_reason,
            score=score,
            confidence=confidence,
            model_type="content",
            model_subtype="semantic" if is_semantic_model else "standard",
            factors=factors
        )
    
    def generate_hybrid_explanation(
        self,
        item_id: int,
        collab_score: float,
        content_score: float,
        collab_weight: float,
        content_weight: float,
        collab_explanation: Union[str, schemas.DetailedExplanation],
        content_explanation: Union[str, schemas.DetailedExplanation],
        explanation_level: schemas.ExplanationLevel = schemas.ExplanationLevel.SIMPLE
    ) -> Union[str, schemas.DetailedExplanation]:
        """
        Genera una explicación híbrida combinando explicaciones colaborativas y basadas en contenido.
        
        Args:
            item_id: ID del ítem recomendado
            collab_score: Puntuación del modelo colaborativo
            content_score: Puntuación del modelo basado en contenido
            collab_weight: Peso del modelo colaborativo
            content_weight: Peso del modelo basado en contenido
            collab_explanation: Explicación del modelo colaborativo
            content_explanation: Explicación del modelo basado en contenido
            explanation_level: Nivel de detalle de la explicación
            
        Returns:
            Explicación como string o como objeto DetailedExplanation
        """
        # Normalizar pesos
        total_weight = collab_weight + content_weight
        if total_weight > 0:
            collab_norm = collab_weight / total_weight
            content_norm = content_weight / total_weight
        else:
            collab_norm = content_norm = 0.5
            
        # Determinar qué modelo contribuyó más
        collab_contribution = collab_score * collab_norm
        content_contribution = content_score * content_norm
        
        # Explicación simple (string)
        if explanation_level == schemas.ExplanationLevel.SIMPLE:
            if collab_contribution >= content_contribution:
                if isinstance(collab_explanation, str):
                    return collab_explanation
                else:
                    return collab_explanation.primary_reason
            else:
                if isinstance(content_explanation, str):
                    return content_explanation
                else:
                    return content_explanation.primary_reason
        
        # Explicación detallada
        factors = []
        
        # Obtener factores de ambas explicaciones
        if isinstance(collab_explanation, schemas.DetailedExplanation):
            # Añadir factor para indicar contribución del modelo colaborativo
            factor = schemas.ExplanationFactor(
                type="model_contribution",
                name="filtrado colaborativo",
                value=f"{int(collab_norm * 100)}%",
                strength=collab_norm,
                description=f"El {int(collab_norm * 100)}% de la recomendación se basa en preferencias de usuarios similares"
            )
            factors.append(factor)
            
            # Añadir factores de la explicación colaborativa
            for f in collab_explanation.factors:
                # Ajustar la fuerza según el peso
                f.strength = f.strength * collab_norm
                factors.append(f)
        
        if isinstance(content_explanation, schemas.DetailedExplanation):
            # Añadir factor para indicar contribución del modelo basado en contenido
            factor = schemas.ExplanationFactor(
                type="model_contribution",
                name="basado en contenido",
                value=f"{int(content_norm * 100)}%",
                strength=content_norm,
                description=f"El {int(content_norm * 100)}% de la recomendación se basa en tus intereses y preferencias"
            )
            factors.append(factor)
            
            # Añadir factores de la explicación basada en contenido
            for f in content_explanation.factors:
                # Ajustar la fuerza según el peso
                f.strength = f.strength * content_norm
                factors.append(f)
        
        # Determinar razones primaria y secundaria
        if collab_contribution >= content_contribution:
            primary_reason = collab_explanation.primary_reason if isinstance(collab_explanation, schemas.DetailedExplanation) else "Recomendado por usuarios similares"
            secondary_reason = content_explanation.primary_reason if isinstance(content_explanation, schemas.DetailedExplanation) else "Coincide con tus intereses"
        else:
            primary_reason = content_explanation.primary_reason if isinstance(content_explanation, schemas.DetailedExplanation) else "Coincide con tus intereses"
            secondary_reason = collab_explanation.primary_reason if isinstance(collab_explanation, schemas.DetailedExplanation) else "Recomendado por usuarios similares"
        
        # Calcular score combinado y confianza
        combined_score = (collab_score * collab_norm) + (content_score * content_norm)
        confidence = min(1.0, max(0.1, combined_score / 5.0))  # Normalizar a un valor entre 0.1 y 1.0
        
        return schemas.DetailedExplanation(
            item_id=item_id,
            user_id=0,  # Se establecerá después
            primary_reason=primary_reason,
            secondary_reason=secondary_reason,
            score=combined_score,
            confidence=confidence,
            model_type="hybrid",
            model_subtype=f"collab_{int(collab_norm*100)}_content_{int(content_norm*100)}",
            factors=factors
        )
    
    def generate_fallback_explanation(
        self,
        item_id: int,
        reason: str = "popular",
        explanation_level: schemas.ExplanationLevel = schemas.ExplanationLevel.SIMPLE
    ) -> Union[str, schemas.DetailedExplanation]:
        """
        Genera una explicación para recomendaciones de fallback.
        
        Args:
            item_id: ID del ítem recomendado
            reason: Razón del fallback
            explanation_level: Nivel de detalle de la explicación
            
        Returns:
            Explicación como string o como objeto DetailedExplanation
        """
        # Explicación simple (string)
        if explanation_level == schemas.ExplanationLevel.SIMPLE:
            if reason == "popular":
                return "Producto popular entre nuestros usuarios"
            elif reason == "trending":
                return "Producto tendencia en este momento"
            elif reason == "new_arrival":
                return "Producto recién añadido a nuestro catálogo"
            elif reason == "category_popular":
                return "Producto popular en esta categoría"
            elif reason == "staff_pick":
                return "Recomendado por nuestro equipo"
            else:
                return "Producto destacado"
        
        # Explicación detallada
        primary_reason = ""
        secondary_reason = None
        factors = []
        confidence = 0.6  # Valor por defecto para fallbacks
        
        if reason == "popular":
            primary_reason = "Producto popular entre nuestros usuarios"
            factor = schemas.ExplanationFactor(
                type="popularity",
                name="popularidad",
                value="alta",
                strength=0.8,
                description="Este producto es muy popular entre nuestros usuarios"
            )
            factors.append(factor)
            
        elif reason == "trending":
            primary_reason = "Producto tendencia en este momento"
            factor = schemas.ExplanationFactor(
                type="trending",
                name="tendencia",
                value="creciente",
                strength=0.9,
                description="Este producto está ganando popularidad rápidamente"
            )
            factors.append(factor)
            
        elif reason == "new_arrival":
            primary_reason = "Producto recién añadido a nuestro catálogo"
            factor = schemas.ExplanationFactor(
                type="recency",
                name="novedad",
                value="reciente",
                strength=0.7,
                description="Este producto es una adición reciente a nuestro catálogo"
            )
            factors.append(factor)
            
        elif reason == "category_popular":
            primary_reason = "Producto popular en esta categoría"
            factor = schemas.ExplanationFactor(
                type="category_popularity",
                name="popularidad en categoría",
                value="alta",
                strength=0.75,
                description="Este producto es popular dentro de su categoría"
            )
            factors.append(factor)
            
        elif reason == "staff_pick":
            primary_reason = "Recomendado por nuestro equipo"
            factor = schemas.ExplanationFactor(
                type="curated",
                name="recomendación editorial",
                value="seleccionado",
                strength=0.8,
                description="Este producto ha sido seleccionado por nuestro equipo editorial"
            )
            factors.append(factor)
            
        else:
            primary_reason = "Producto destacado"
            factor = schemas.ExplanationFactor(
                type="generic",
                name="destacado",
                value="relevante",
                strength=0.6,
                description="Este producto ha sido destacado para tu consideración"
            )
            factors.append(factor)
        
        return schemas.DetailedExplanation(
            item_id=item_id,
            user_id=0,  # Se establecerá después
            primary_reason=primary_reason,
            secondary_reason=secondary_reason,
            score=0.0,  # Sin score real para fallbacks
            confidence=confidence,
            model_type="fallback",
            model_subtype=reason,
            factors=factors
        )
        
    async def get_item_explanation(
        self,
        account_id: int,
        user_id: int,
        item_id: int,
        model_artifacts: Optional[Dict[str, Any]] = None,
        db_session = None
    ) -> schemas.DetailedExplanation:
        """
        Obtiene una explicación detallada para un ítem específico.
        
        Args:
            account_id: ID de la cuenta
            user_id: ID del usuario
            item_id: ID del ítem
            model_artifacts: Artefactos del modelo (opcional)
            db_session: Sesión de base de datos (opcional)
            
        Returns:
            Explicación detallada
        """
        log_info(f"Generando explicación para item_id={item_id}, user_id={user_id}, account_id={account_id}")
        
        # Placeholder para obtener datos de interacción si no tenemos artefactos
        user_history = {}
        
        # Si tenemos una sesión de DB, podemos obtener información adicional
        if db_session:
            from sqlalchemy import select
            from src.db.models import Interaction
            
            # 1. Obtener historial del usuario
            query = select(Interaction).where(
                (Interaction.account_id == account_id) &
                (Interaction.user_id == user_id)
            ).limit(100)
            
            result = await db_session.execute(query)
            interactions = result.scalars().all()
            
            for interaction in interactions:
                user_history[interaction.item_id] = interaction.rating or 1.0
                
            # 2. Obtener información del ítem específico
            from src.db.models import Product
            
            query = select(Product).where(
                (Product.account_id == account_id) &
                (Product.id == item_id)
            )
            
            result = await db_session.execute(query)
            product = result.scalar_one_or_none()
            
            if product:
                # Crear detalles para la recomendación
                recommendation_details = {
                    "item_id": item_id,
                    "name": product.name,
                    "category": product.category,
                    "price": product.price,
                    "model_type": "hybrid",  # Por defecto
                    "score": 0.8,  # Score ficticio
                    "collab_score": 0.7,
                    "content_score": 0.9,
                    "collab_weight": 0.4,
                    "content_weight": 0.6
                }
                
                # Generar explicación
                explanation = self.generate_explanation(
                    item_id=item_id,
                    user_id=user_id,
                    recommendation_details=recommendation_details,
                    model_artifacts=model_artifacts,
                    explanation_level=schemas.ExplanationLevel.DETAILED,
                    user_history=user_history
                )
                
                if isinstance(explanation, str):
                    # Convertir a explicación detallada si recibimos un string
                    return schemas.DetailedExplanation(
                        item_id=item_id,
                        user_id=user_id,
                        primary_reason=explanation,
                        score=recommendation_details.get("score", 0.0),
                        model_type="hybrid",
                        factors=[]
                    )
                else:
                    # Asegurar que se establezca el user_id correcto
                    explanation.user_id = user_id
                    return explanation
        
        # Si no tenemos sesión de DB o no encontramos el producto, devolver explicación genérica
        log_warning(f"Generando explicación genérica para item_id={item_id} (sin datos adicionales)")
        
        explanation = schemas.DetailedExplanation(
            item_id=item_id,
            user_id=user_id,
            primary_reason="Producto que podría interesarte",
            score=0.5,  # Score ficticio
            confidence=0.6,
            model_type="hybrid",
            factors=[
                schemas.ExplanationFactor(
                    type="generic",
                    name="recomendación",
                    value="general",
                    strength=0.6,
                    description="Basado en tu perfil general de intereses"
                )
            ]
        )
        
        return explanation 