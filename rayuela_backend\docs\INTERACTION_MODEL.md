# Modelo de Interacciones

Este documento explica el diseño y las consideraciones del modelo de interacciones en nuestra aplicación.

## Cambios Recientes

### Eliminación de la Restricción de Unicidad en Interacciones

Se ha eliminado la restricción de unicidad (`UniqueConstraint`) en la tabla `interactions` que anteriormente impedía registrar múltiples interacciones del mismo tipo para el mismo usuario y producto. Esta restricción estaba definida en las columnas:
- `account_id`
- `end_user_id`
- `product_id`
- `interaction_type`

#### Razón del Cambio

La restricción de unicidad impedía casos de uso comunes y necesarios en un sistema de recomendación, como:

1. **Múltiples visualizaciones**: Un usuario puede ver el mismo producto varias veces en diferentes momentos.
2. **Múltiples clics**: Un usuario puede hacer clic en el mismo producto varias veces.
3. **Añadir/quitar del carrito**: Un usuario puede añadir y quitar un producto del carrito varias veces.
4. **Historial de interacciones**: Es importante mantener un historial completo de todas las interacciones para análisis y mejora de las recomendaciones.

#### Implementación del Cambio

El cambio se implementó en dos partes:

1. **Modificación del modelo**: Se eliminó la restricción de unicidad del modelo `Interaction` en `src/db/models/interaction.py`.
2. **Migración de la base de datos**: Se creó una migración Alembic (`remove_unique_interaction_constraint.py`) para eliminar la restricción de la base de datos.

Para aplicar este cambio a una base de datos existente, ejecuta:

```bash
# Si tienes alembic instalado globalmente
alembic upgrade head

# O si prefieres usar Python directamente
python -m alembic upgrade head

# Si estás usando Docker
docker-compose exec api alembic upgrade head
```

## Estructura del Modelo de Interacciones

El modelo `Interaction` representa las interacciones de los usuarios con los productos en el sistema. Cada interacción tiene los siguientes campos:

- `account_id`: ID de la cuenta (tenant)
- `id`: ID único de la interacción
- `end_user_id`: ID del usuario final que realizó la interacción
- `product_id`: ID del producto con el que se interactuó
- `interaction_type`: Tipo de interacción (VIEW, LIKE, PURCHASE, CART, etc.)
- `value`: Valor opcional asociado a la interacción (por ejemplo, una calificación)
- `timestamp`: Fecha y hora en que ocurrió la interacción

## Tipos de Interacciones

Los tipos de interacciones están definidos en el enum `InteractionType` en `src/db/enums.py`:

- `VIEW`: El usuario vio el producto
- `LIKE`: El usuario indicó que le gusta el producto
- `PURCHASE`: El usuario compró el producto
- `CART`: El usuario añadió el producto al carrito
- `RATING`: El usuario calificó el producto
- `WISHLIST`: El usuario añadió el producto a su lista de deseos
- `CLICK`: El usuario hizo clic en el producto
- `SEARCH`: El usuario encontró el producto a través de una búsqueda
- `FAVORITE`: El usuario marcó el producto como favorito

## Consideraciones para el Uso

### Registro de Interacciones

Al registrar interacciones, es importante incluir:

1. El ID del usuario final
2. El ID del producto
3. El tipo de interacción
4. Un valor (opcional, dependiendo del tipo de interacción)

### Consulta de Interacciones

Al consultar interacciones, puedes filtrar por:

1. Usuario específico
2. Producto específico
3. Tipo de interacción
4. Rango de fechas

### Uso en el Sistema de Recomendación

Las interacciones son la base del sistema de recomendación colaborativo. El sistema utiliza el historial de interacciones para:

1. Identificar patrones de comportamiento
2. Encontrar usuarios similares
3. Recomendar productos basados en interacciones pasadas
4. Calcular la relevancia de los productos para cada usuario

## Mejores Prácticas

1. **Registrar todas las interacciones**: No filtrar o limitar el registro de interacciones, ya que cada una proporciona información valiosa.
2. **Incluir timestamps precisos**: Asegurarse de que cada interacción tenga un timestamp preciso.
3. **Usar el tipo correcto**: Elegir el tipo de interacción que mejor represente la acción del usuario.
4. **Considerar el valor**: Para interacciones como RATING, asegurarse de incluir el valor correspondiente.
